image: docker:20

services:
  - name: docker:20-dind

variables:
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_CERTDIR: '/certs'
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: '$DOCKER_TLS_CERTDIR/client'
  RELEASE_VERSION: 'PROD.2024.10.10'
  K8S_NAMESPACE: 'nginx'
stages:
  - node-build
  - docker-build
  - notify

node-build:
  stage: node-build
  image: node:14-alpine
  cache:
    key: ${CI_PROJECT_NAME}
    paths:
      - node_modules/
  script:
    - npm config set registry https://registry.npmmirror.com/
    - npm config set sass_binary_site https://npm.taobao.org/mirrors/node-sass/
    - npm install
    - npm run build:prod
  artifacts:
    paths:
      - pv-h5
  only:
    - master

docker-build:
  stage: docker-build
  script:
    - docker build -t ${HARBOR_HOST}/${HARBOR_PROJECT}/${CI_PROJECT_NAME}:${RELEASE_VERSION} .
    - docker login ${HARBOR_HOST} -u ${HARBOR_USERNAME} -p ${HARBOR_PASSWORD}
    - docker push ${HARBOR_HOST}/${HARBOR_PROJECT}/${CI_PROJECT_NAME}:${RELEASE_VERSION}
  only:
    - master
# 构建成功时的通知消息
notify-success:
  stage: notify
  script:
    - |
      sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories && \
      apk add --update curl && \
      curl "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${WECHAT_CP_NOTIFY_KEY}" \
        -H "Content-Type:application/json" \
        -d "{\"msgtype\":\"markdown\",\"markdown\":{\"content\":\"**项目构建结果**：<font color='info'>成功</font> \n >页面访问地址：[https://qdcds.pharmbrain.com](https://qdcds.pharmbrain.com) \n >构建触发者：<font color='comment'>${GITLAB_USER_NAME}</font> \n >项目名称：<font color='comment'>${CI_PROJECT_NAME}</font> \n >提交号：<font color='comment'>${CI_COMMIT_SHA}</font> \n >提交日志：<font color='comment'>${CI_COMMIT_MESSAGE}</font> \n >构建分支：<font color='comment'>${CI_COMMIT_BRANCH}</font> \n >流水线地址：[${CI_PIPELINE_URL}](${CI_PIPELINE_URL})\"}}"
  when: on_success
  only:
    - master

# 构建失败时的通知消息
notify-fail:
  stage: notify
  script:
    - |
      sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories && \
      apk add --update curl && \
      curl "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${WECHAT_CP_NOTIFY_KEY}" \
        -H "Content-Type:application/json" \
        -d "{\"msgtype\":\"markdown\",\"markdown\":{\"content\":\"**项目构建结果**：<font color='warning'>失败</font> \n >构建触发者：<font color='comment'>${GITLAB_USER_NAME}</font> \n >项目名称：<font color='comment'>${CI_PROJECT_NAME}</font> \n >提交号：<font color='comment'>${CI_COMMIT_SHA}</font> \n >提交日志：<font color='comment'>${CI_COMMIT_MESSAGE}</font> \n >构建分支：<font color='comment'>${CI_COMMIT_BRANCH}</font> \n >流水线地址：[${CI_PIPELINE_URL}](${CI_PIPELINE_URL})\"}}"
  when: on_failure
  only:
    - master
