import autoImport from 'unplugin-auto-import/vite';

export default function createAutoImport() {
	return autoImport({
		imports: [
			'vue',
			'vue-router',
			'pinia',
			{
				'@/hooks/web/useProgress': ['useNProgress'],
				'@/hooks/web/useTitle': ['useTitle'],
				'@/hooks/web/useScrollCache': ['useScrollCache'],
				'@/hooks/web/useCard': ['useCard'],
				'@/hooks/web/useDefaultValue': ['useDefaultValue'],
				'@/hooks/web/useReport': ['useReport'],
				'@/hooks/web/useDDI': ['useDDI'],
				'@/hooks/web/useChatCard': ['useChatCard'],
				'@/hooks/web/useMyCard': ['useMyCard'],
				'@/hooks/web/useOrg': ['useOrg'],
				'@/hooks/web/useOrg1': ['useOrg1'],
				'@/hooks/web/useCardLibraryLocalStorage': ['useCardLibraryLocalStorage'],
				'@/hooks/web/useCascader': ['useCascader'],
				'@/hooks/web/useParams': ['useParams'],
				'@/hooks/web/useMessage': ['useMessage'],
				'@/hooks/web/useMessage': ['useMessage'],
				'@/hooks/web/useAssistant': ['useAssistant'],
			},
		],
		dts: true,
	});
}
