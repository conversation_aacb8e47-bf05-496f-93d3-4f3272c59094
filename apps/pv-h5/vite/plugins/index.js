import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';

import createEslintPlugin from './eslint-plugin';
import createAutoImport from './auto-import';
import createSvgIcon from './svg-icon';
import createCompression from './compression';
import createSetupExtend from './setup-extend';
import createVisualizer from './visualizer';
import unpluginComponents from './unplugin-components';
import createComponentsName from './components-name';
import progress from 'vite-plugin-progress';
import { createStyleImportPlugin, VxeTableResolve } from 'vite-plugin-style-import';

export default function createVitePlugins(viteEnv, isBuild = false) {
	const vitePlugins = [vue(), progress(), vueJsx()];
	vitePlugins.push(createEslintPlugin());
	vitePlugins.push(createAutoImport());
	vitePlugins.push(createSetupExtend());
	vitePlugins.push(createSvgIcon(isBuild));
	vitePlugins.push(createVisualizer());
	vitePlugins.push(unpluginComponents());
	vitePlugins.push(createComponentsName());
	vitePlugins.push(
		createStyleImportPlugin({
			resolves: [VxeTableResolve()],
		})
	);
	isBuild && vitePlugins.push(...createCompression(viteEnv));
	return vitePlugins;
}
