module.exports = {
	root: true,
	env: {
		browser: true,
		es2021: true,
		node: true,
	},
	extends: ['eslint:recommended', 'plugin:vue/essential', 'plugin:vue/vue3-essential', 'eslint-config-prettier', 'plugin:prettier/recommended'],
	parser: 'vue-eslint-parser',
	parserOptions: {
		ecmaVersion: 'latest',
		sourceType: 'module',
		ecmaFeatures: {
			jsx: true,
		},
	},
	plugins: ['vue', 'prettier'],
	globals: {
		defineProps: 'readonly',
		defineEmits: 'readonly',
		defineExpose: 'readonly',
		withDefaults: 'readonly',
	},
	rules: {
		'no-undef': 'off',
		'no-useless-escape': 'off',
		'vue/multi-word-component-names': 'off',
		// 禁止或强制在单行代码块中使用空格
		'block-spacing': [2, 'always'],
		// 强制使用一致的缩进 第二个参数为 "tab" 时，会使用tab
		'brace-style': [
			2,
			'1tbs',
			{
				allowSingleLine: true,
			},
		],
		camelcase: [
			0,
			{
				properties: 'always',
			},
		],
		// 文件末尾强制换行
		'eol-last': 2,
		eqeqeq: ['error', 'always', { null: 'ignore' }],
		// 强制在 JSX 属性中一致地使用双引号或单引号
		'jsx-quotes': [2, 'prefer-single'],
		// 禁止使用多个空格
		'no-multi-spaces': 2,
		// 禁止使用多行字符串，在 JavaScript 中，可以在新行之前使用斜线创建多行字符串
		'no-multi-str': 2,
		// 不允许多个空行
		'no-multiple-empty-lines': [
			2,
			{
				max: 1,
			},
		],
		// 要求操作符周围有空格
		'space-infix-ops': 2,
		'vue/no-v-model-argument': 'off',
		'no-unreachable': 'off', //不能有无法执行的代码
		'no-unused-vars': 'off',
		'new-cap': 'off', // 不要求构造函数首字母大写
		'no-inner-declarations': 'off',
		'vue/no-parsing-error': 'off',
		'vue/no-v-for-template-key': 'off',
	},
};
