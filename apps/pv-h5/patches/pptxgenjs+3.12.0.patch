diff --git a/node_modules/pptxgenjs/dist/pptxgen.cjs.js b/node_modules/pptxgenjs/dist/pptxgen.cjs.js
index f849abe..96e9726 100644
--- a/node_modules/pptxgenjs/dist/pptxgen.cjs.js
+++ b/node_modules/pptxgenjs/dist/pptxgen.cjs.js
@@ -5961,7 +5961,7 @@ function genXmlTextRunProperties(opts, isDefault) {
             throw new Error('ERROR: \'hyperlink requires either `url` or `slide`\'');
         else if (opts.hyperlink.url) {
             // runProps += '<a:uFill>'+ genXmlColorSelection('0000FF') +'</a:uFill>'; // Breaks PPT2010! (Issue#74)
-            runProps += "<a:hlinkClick r:id=\"rId".concat(opts.hyperlink._rId, "\" invalidUrl=\"\" action=\"\" tgtFrame=\"\" tooltip=\"").concat(opts.hyperlink.tooltip ? encodeXmlEntities(opts.hyperlink.tooltip) : '', "\" history=\"1\" highlightClick=\"0\" endSnd=\"0\"").concat(opts.color ? '>' : '/>');
+            runProps += "<a:hlinkClick r:id=\"rId".concat(opts.hyperlink._rId, "\" invalidUrl=\"\" action=\"ppaction://hlinkfile\" tgtFrame=\"\" tooltip=\"").concat(opts.hyperlink.tooltip ? encodeXmlEntities(opts.hyperlink.tooltip) : '', "\" history=\"1\" highlightClick=\"0\" endSnd=\"0\"").concat(opts.color ? '>' : '/>');
         }
         else if (opts.hyperlink.slide) {
             runProps += "<a:hlinkClick r:id=\"rId".concat(opts.hyperlink._rId, "\" action=\"ppaction://hlinksldjump\" tooltip=\"").concat(opts.hyperlink.tooltip ? encodeXmlEntities(opts.hyperlink.tooltip) : '', "\"").concat(opts.color ? '>' : '/>');
diff --git a/node_modules/pptxgenjs/dist/pptxgen.es.js b/node_modules/pptxgenjs/dist/pptxgen.es.js
index 0ccf431..a19b78f 100644
--- a/node_modules/pptxgenjs/dist/pptxgen.es.js
+++ b/node_modules/pptxgenjs/dist/pptxgen.es.js
@@ -5955,7 +5955,7 @@ function genXmlTextRunProperties(opts, isDefault) {
             throw new Error('ERROR: \'hyperlink requires either `url` or `slide`\'');
         else if (opts.hyperlink.url) {
             // runProps += '<a:uFill>'+ genXmlColorSelection('0000FF') +'</a:uFill>'; // Breaks PPT2010! (Issue#74)
-            runProps += "<a:hlinkClick r:id=\"rId".concat(opts.hyperlink._rId, "\" invalidUrl=\"\" action=\"\" tgtFrame=\"\" tooltip=\"").concat(opts.hyperlink.tooltip ? encodeXmlEntities(opts.hyperlink.tooltip) : '', "\" history=\"1\" highlightClick=\"0\" endSnd=\"0\"").concat(opts.color ? '>' : '/>');
+            runProps += "<a:hlinkClick r:id=\"rId".concat(opts.hyperlink._rId, "\" invalidUrl=\"\" action=\"ppaction://hlinkfile\" tgtFrame=\"\" tooltip=\"").concat(opts.hyperlink.tooltip ? encodeXmlEntities(opts.hyperlink.tooltip) : '', "\" history=\"1\" highlightClick=\"0\" endSnd=\"0\"").concat(opts.color ? '>' : '/>');
         }
         else if (opts.hyperlink.slide) {
             runProps += "<a:hlinkClick r:id=\"rId".concat(opts.hyperlink._rId, "\" action=\"ppaction://hlinksldjump\" tooltip=\"").concat(opts.hyperlink.tooltip ? encodeXmlEntities(opts.hyperlink.tooltip) : '', "\"").concat(opts.color ? '>' : '/>');
