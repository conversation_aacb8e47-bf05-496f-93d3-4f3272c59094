server {
    server_tokens off;
    gzip on;
    gzip_static on;
    gzip_min_length 10k;
    gzip_types text/javascript application/javascript text/css application/json;
    gzip_proxied any;
    gzip_vary on;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;

    listen       80;

    # Main location
    location / {
        add_header Cache-Control no-cache;
        add_header Pragma no-cache;
        add_header Expires 0;

        root /usr/share/nginx/html;
        index index.html index.htm;
    }

    # Handling /app/mobile route
    location ^~/app/mobile {
        add_header Cache-Control no-cache;
        add_header Pragma no-cache;
        add_header Expires 0;

        alias  /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

}
