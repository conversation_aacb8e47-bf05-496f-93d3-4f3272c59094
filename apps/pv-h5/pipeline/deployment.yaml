apiVersion: v1
kind: Service
metadata:
  name: {{POD_NAME}}
  namespace: {{NAMESPACE}}
spec:
  selector:
    app: {{POD_NAME}}
  type: ClusterIP
  ports:
    - name: {{POD_PORT}}tcp{{POD_PORT}}1
      port: {{POD_PORT}}
      protocol: TCP
      targetPort: {{POD_PORT}}

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{POD_NAME}}
  namespace: {{NAMESPACE}}
  labels:
    app: {{POD_NAME}}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{POD_NAME}}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: {{POD_NAME}}
    spec:
      containers:
        - image: {{IMAGE}}
          imagePullPolicy: Always
          name: {{POD_NAME}}
          ports:
            - containerPort: {{POD_PORT}}
              name: {{POD_PORT}}tcp{{POD_PORT}}1
              protocol: TCP
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: {{IMAGE_PULL_SECRETS}}
      restartPolicy: Always
