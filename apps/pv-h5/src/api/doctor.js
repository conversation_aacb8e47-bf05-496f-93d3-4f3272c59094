import request from '@/utils/request';

// 查询医院
export function findHospital(data) {
	return request({
		url: '/API-DOCTOR/v2/doctor/get/hospital/list',
		method: 'post',
		data: data,
		svgType: '1',
	});
}

// 获取所有科室
export function getDepartment(params) {
	return request({
		url: '/API-DOCTOR/v2/standard/department/current',
		method: 'get',
		params,
	});
}

//获取医生列表新
export function getDoctorListNew(data) {
	return request({
		url: 'API-DOCTOR/v2/doctor/page/search',
		method: 'post',
		svgType: '1',
		data,
	});
}
// 领域
export function findFieldList() {
	return request({
		url: '/API-SYSTEM/field/all',
		method: 'get',
	});
}

//查询专业职称
export function getProfessionalTitle(qyId) {
	if (qyId === 'muqiao') {
		return request({
			url: '/API-DOCTOR/v2/standard/title/current',
			method: 'get',
		});
	} else {
		return request({
			url: '/API-DOCTOR/v2/standard/doctor/title',
			method: 'get',
		});
	}
}
//查询行政职称
export function getAdministrativeTitle() {
	return request({
		url: '/API-DOCTOR/v2/standard/administrative/position',
		method: 'get',
	});
}

// 关注医生
export function focusDoctor(data) {
	return request({
		url: '/API-DOCTOR/v2/doctor/focus',
		method: 'post',
		svgType: '1',
		data,
	});
}

//添加目标医生
export function updateDoctorCoverage(params) {
	return request({
		url: `/API-DOCTOR/v2/sales/target/${params.id}`,
		method: 'put',
		params,
	});
}

// 查询个人标签
export function personalTag(params) {
	return request({
		url: '/API-DOCTOR/v2/tag/personal/find',
		method: 'get',
		params,
	});
}

// 查询用户标签列表
export function getTagList(params) {
	return request({
		url: '/API-DOCTOR/v2/tag/term/find/user',
		method: 'get',
		params,
	});
}

// 新建个人标签
export function setTag(data, data1) {
	return request({
		url: `/API-DOCTOR/v2/tag/personal/add/${data1.termValue}`,
		method: 'post',
		data,
		svgType: '1',
	});
}

// 根据标签id查询未打标签医生列表
export function doctorLabelList(params, params1) {
	return request({
		url: `/API-DOCTOR/v2/doctor/find/notTag/${params1.tagTermId}`,
		method: 'get',
		params,
	});
}

// 批量给医生打标签
export function addDocTag(tagTermId, data, data1) {
	return request({
		url: `/API-DOCTOR/v2/customer/tag/add/${tagTermId}?addedValue=${data.addedValue}&mobileTagType=${data.mobileTagType}`,
		method: 'post',
		data: data1,
		svgType: '1',
	});
}

// 查询全部医生列表
export function allDoctor(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/find/page',
		method: 'get',
		params,
	});
}

// 修改个人标签
export function updatePersonTag(val) {
	return request({
		url: '/API-DOCTOR/v2/tag/personal',
		method: 'put',
		data: val,
		svgType: '1',
	});
}

// 删除个人标签
export function deletePersonTag(id) {
	return request({
		url: '/API-DOCTOR/v2/tag/personal',
		method: 'delete',
		params: {
			id,
		},
	});
}

// 根据标签id查询已打标签医生列表
export function tagDoctorList(params) {
	return request({
		url: `/API-DOCTOR/v2/doctor/find/${params.tagTermId}`,
		method: 'get',
		params,
	});
}

// 批量删除打标签的医生
export function removeDocTag(data) {
	return request({
		url: `/API-DOCTOR/v2/customer/tag/delete/${data.tagTermId}?mobileTagType=${data.mobileTagType}`,
		method: 'post',
		svgType: '1',
		data: data.arr,
	});
}

/**
 * @description: 发送动态批量上传图片
 */
export function uploadDynamicImg(data, bucket) {
	return request({
		url: `/API-MINIO/minio/${bucket}/upload`,
		method: 'post',
		data,
		svgType: '2',
	});
}

//新建医生
export function addDoctor(data) {
	return request({
		url: '/API-DOCTOR/v2/doctor/add',
		method: 'post',
		data,
		svgType: '1',
	});
}
//新建医生
export function updataDoctor(data) {
	return request({
		url: '/API-DOCTOR/v2/archive/add',
		method: 'post',
		data,
		svgType: '1',
	});
}

export function getHospitalList(data, page, size) {
	return request({
		url: `/API-DOCTOR/v2/hospital/all?page=${page}&size=${size}&sort=id,asc`,
		method: 'post',
		svgType: '1',
		data,
	});
}

// 查询id查询医生详情
export function findByDoctorId(params) {
	return request({
		url: `/API-DOCTOR/v2/doctor/get/one/${params.doctorId}`,
		method: 'get',
	});
}

// 维护医生手机号
export function postdoctorinfo(params) {
	return request(
		{
			url: '/API-DOCTOR/v2/channel/set',
			method: 'post',
			svgType: '1',
			data: params,
		},
		{
			code_message_show: false,
			error_message_show: false,
		}
	);
}

//同步外部联系人列表
export function refreshExternal(userId, relam) {
	if (relam) {
		return request(
			{
				url: `/API-DOCTOR/v2/tp/bind/sync/external/contact/single`,
				method: 'get',
			},
			{
				code_message_show: false,
			}
		);
	} else {
		return request(
			{
				url: `/API-DOCTOR/v2/bind/friend/list/refresh/${userId}`,
				method: 'get',
			},
			{
				code_message_show: false,
			}
		);
	}
}

//同步代表绑定信息
export function refreshChannel(doctorId) {
	return request({
		url: `/API-DOCTOR/v2/bind/refresh/channel/${doctorId}`,
		method: 'put',
	});
}

//解绑
export function unbindingInterface(data, relam) {
	if (relam) {
		return request({
			url: `/API-DOCTOR/v2/tp/bind/doctor/unbind/${data.doctorId}`,
			method: 'delete',
		});
	} else {
		return request({
			url: `/API-DOCTOR/v2/bind/doctor/unbind/${data.doctorId}`,
			method: 'delete',
		});
	}
}

// 查询医生联系渠道
export function getcontent(id) {
	return request({
		url: `/API-DOCTOR/v2/channel/find/${id}`,
		method: 'get',
	});
}

//查询产品分级
export function findProduct(doctorID) {
	return request({
		url: `/API-DOCTOR/v2/doctor/get/product/leve/${doctorID}`,
		method: 'get',
	});
}

//根据医生id查询相关同事
export function findRelatedColleagues(doctorId) {
	return request({
		url: `/API-DOCTOR/v2/doctor/get/other/works/${doctorId}`,
		method: 'get',
	});
}

//查询外部联系人列表
export function externalList(userId, isBind, relam) {
	if (relam) {
		return request({
			url: `/API-DOCTOR/v2/tp/bind/external/user/list/${isBind}`,
			method: 'get',
		});
	} else {
		return request({
			url: `/API-DOCTOR/v2/bind/friend/list/${userId}/${isBind}`,
			method: 'get',
		});
	}
}

//绑定外部联系人
export function bindExternal(data, relam) {
	if (relam) {
		return request({
			url: '/API-DOCTOR/v2/tp/bind/external/user',
			method: 'post',
			svgType: '1',
			data,
		});
	} else {
		return request({
			url: '/API-DOCTOR/v2/bind/external/user',
			method: 'post',
			svgType: '1',
			data,
		});
	}
}

// 根据医生id查询已打的标签
export function doctorChooseTagInterface(doctorID, params) {
	return request({
		url: `/API-DOCTOR/v2/doctor/find/${doctorID}/system`,
		method: 'get',
		params: params,
	});
}

// 根据医生id查询个人标签
export function doctorSelfTagInterface(doctorID) {
	return request({
		url: `/API-DOCTOR/v2/doctor/find/${doctorID}/personal`,
		method: 'get',
	});
}

// 根据医生id查询代表有权限修改的标签
export function doctorPerTagInterface(doctorID, data) {
	return request({
		url: `/API-DOCTOR/v2/doctor/find/staff/${doctorID}`,
		method: 'get',
		params: data,
	});
}

// 根据医生id修改医生系统标签
export function upDoctorTag(doctorID, data, tagType) {
	return request({
		url: `/API-DOCTOR/v2/doctor/update/term/${doctorID}`,
		method: 'put',
		svgType: '1',
		params: tagType,
		data,
	});
}

// 根据医生id修改医生个人标签
export function upDoctorSelfTagInterface(doctorID, data) {
	return request({
		url: `/API-DOCTOR/v2/doctor/update/personalTerm/${doctorID}`,
		method: 'put',
		svgType: '1',
		data,
	});
}

// 根据医生id查询已打的标签
export function getRelationInterface(data) {
	return request({
		url: `/API-DOCTOR/v2/group/rules/find/relation/`,
		method: 'post',
		svgType: '1',
		data,
	});
}

// 查询学术文章
export function academicArticleInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/get/paper/info',
		method: 'get',
		params,
	});
}

// 查询学术信息标签
export function academicTabsInterface(params, doctorId) {
	return request({
		url: `/API-DOCTOR/v2/doctor/get/paper/range/${doctorId}`,
		method: 'get',
		params,
	});
}

// 查询临床指南
export function guideInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/get/guide/info',
		method: 'get',
		params,
	});
}

// 查询学术会议
export function meetingInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/get/meeting/info',
		method: 'get',
		params,
	});
}

// 查询学术直播
export function liveInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/get/live/info',
		method: 'get',
		params,
	});
}

// 查询发明专利项目
export function projectPatentInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/get/patent/info',
		method: 'get',
		params,
	});
}

// 查询临床课题研究
export function projectResearchInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/get/research/info',
		method: 'get',
		params,
	});
}
// 查询国家自然科学
export function projectFundInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/get/fund/info',
		method: 'get',
		params,
	});
}

// 查询项目信息tag
export function projectRange(params, doctorId) {
	return request({
		url: `/API-DOCTOR/v2/doctor/get/project/range/${doctorId}`,
		method: 'get',
		params,
	});
}

// 查询动态信息时间标签
export function dynamicTimeLabelInterface(params, type, doctorId) {
	return request({
		url: `/API-DOCTOR/v2/doctor/get/activity/range/${type}/${doctorId}`,
		method: 'get',
		params,
	});
}
// 查询动态信息
export function dynamicTabelInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/get/activity/info',
		method: 'get',
		params,
	});
}

// 查询合作分类tag
export function teamWorkTagListInterface(params, doctorId) {
	return request({
		url: `/API-DOCTOR/v2/relation/findRelationType/list/${doctorId}`,
		method: 'get',
		params,
	});
}
// 查询学术网络
export function networkInterface(params, doctorId) {
	return request({
		url: `/API-DOCTOR/v2/relation/findBy/list`,
		method: 'get',
		params,
	});
}

// 教育行为倾向
export function detailInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/action/detail',
		method: 'get',
		params,
	});
}

//获取互动一览明细类型
export function getDataType() {
	return request({
		url: '/API-DOCTOR/v2/doctor/action/find/by/list/dataType',
		method: 'get',
	});
}

// 查询医生活跃度
export function activenessInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/action/activeness',
		method: 'get',
		params,
	});
}
// 互动行为统计
export function statisticalInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/action/statistical',
		method: 'get',
		params,
	});
}
// 互动行为倾向
export function tendencyInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/action/tendency',
		method: 'get',
		params,
	});
}
// 教育行为倾向
export function eduTendencyInterface(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/action/educate/tendency',
		method: 'get',
		params,
	});
}
