import request from '@/utils/request';
import qs from 'qs';
//查询品牌信息
export function filterBrandInfo(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/brandInfo',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}

//查询sku信息 (全部产品筛选使用)
export function filterSkuInfo(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/skuInfo',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}
//查询sku信息 (全部产品筛选使用)树状结构
export function filterSkuInfoTree(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/findCategory',
			method: 'get',
			params,
			paramsSerializer: (params) => {
				return qs.stringify(params, { arrayFormat: 'repeat' });
			},
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}
//查询产品信息 (拜访筛选使用)
export function filterProductInfo(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/productInfo',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}

//查询产品信息 (穆桥拜访筛选使用)
export function filterProductInfoMq(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/visitProductInfo',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}

//查询ta
export function filterTa(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/getTa',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}

//查询组织tree
export function filterOrganizationTree(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/getOrganizationTree',
			method: 'get',
			params,
			paramsSerializer: (params) => {
				return qs.stringify(params, { arrayFormat: 'repeat' });
			},
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}

//查询DSM及MR组织tree
export function filterSimpleTree(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/getOrganizationSimpleTree',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}

//查询医院信息
export function filterHospital(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/findHospital',
			method: 'get',
			params,
			paramsSerializer: (params) => {
				return qs.stringify(params, { arrayFormat: 'repeat' });
			},
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}

// 查询省份
export function filterProvince(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/findProvince',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}

// 查询市场
export function filterMarketShare(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/findMarketShare',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}

export function getTeam(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/salesChannel/selectTeam',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}

export function getSkuCn(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/salesChannel/selectSkuCn',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}
export function getChannel(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/salesChannel/selectChannel',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}
//查询market下的医院
export function filterMarketHospital(params) {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/report/filter/findMarketShareHospital',
			method: 'get',
			params,
		},
		{
			error_message_show: false,
			code_message_show: false,
		}
	);
}
// /province/all/tree
export function getProvinceTree(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/filter/findProvinceCityCounty',
		method: 'get',
		params,
		paramsSerializer: (params) => {
			return qs.stringify(params, { arrayFormat: 'repeat' });
		},
	});
}
//获取问题
export function getQuestion(data, page) {
	return request({
		url: `/API-POCKETVIEW/pocketView/cardConfig/page?page=${page}&size=30`,
		method: 'post',
		svgType: '1',
		data,
	});
}
