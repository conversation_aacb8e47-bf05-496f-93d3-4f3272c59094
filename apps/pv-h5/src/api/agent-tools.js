import request from '@/utils/request';

export function xiaoiceAuth(params) {
	return request({
		url: '/API-AGENT-TOOLS/xiaoice/generate_auth_token',
		method: 'post',
		params,
	});
}
export function xiaoiceInvalidate(signature) {
	return request({
		url: '/API-AGENT-TOOLS/xiaoice/invalidate_auth_token',
		method: 'post',
		headers: {
			signature,
		},
		svgType: '1',
	});
}
//获取代表覆盖的医生动态数据
export function getDoctorDynamics(params) {
	return request({
		url: '/API-DOCTOR/v2/doctor/action/detail',
		method: 'get',
		params,
	});
}

export function getAgentListApi() {
	return request({
		url: '/API-APP/component/user/page',
		method: 'post',
		data: {
			page: 0,
			size: 100,
		},
		svgType: '1',
	});
}
//初始化会话ID
export function initChatId(data) {
	return request({
		url: '/API-AIP/chat/init',
		method: 'post',
		data,
		svgType: '1',
	});
}
//发送消息
export function sendMsgApi(data, options) {
	return request({
		url: '/API-AIP/chat',
		// url: 'http://**************:8000/sales-insight/chat',
		// headers: {
		// 	'x-realm': 'dev',
		// 	'x-username': 'yyds',
		// },
		method: 'post',
		data,
		svgType: '1',
		...options,
	});
}
//更新会话描述
export function updateChatDescApi(conversation_id, data) {
	return request({
		url: `/API-AIP/chat/${conversation_id}/description`,
		method: 'put',
		data,
		svgType: '1',
	});
}
//获取历史记录
export function getHistoryApi(params) {
	return request({
		url: `/API-AIP/chat/history/list`,
		method: 'get',
		params,
	});
}
//根据会话ID获取会话详情
export function getChatDetailApi(conversation_id, params) {
	return request({
		url: `/API-AIP/chat/${conversation_id}/history`,
		method: 'get',
		params,
	});
}
//根据消息id更新消息内容
export function updateMsgApi(message_id, data) {
	return request({
		url: `/API-AIP/chat/messages/${message_id}`,
		method: 'put',
		data,
		svgType: '1',
	});
}
//根据消息id删除消息
export function deleteMsgApi(data) {
	return request({
		url: `/API-AIP/chat/messages/batch-delete`,
		method: 'post',
		data,
		svgType: '1',
	});
}
//点赞消息
export function likeMsgApi(message_id) {
	return request({
		url: `/API-AIP/chat/messages/${message_id}/like`,
		method: 'post',
		svgType: '1',
	});
}
//取消点赞
export function cancelLikeMsgApi(message_id) {
	return request({
		url: `/API-AIP/chat/messages/${message_id}/like`,
		method: 'delete',
		svgType: '1',
	});
}
//点踩消息
export function dislikeMsgApi(message_id) {
	return request({
		url: `/API-AIP/chat/messages/${message_id}/dislike`,
		method: 'post',
		svgType: '1',
	});
}

//取消点踩
export function cancelDislikeMsgApi(message_id) {
	return request({
		url: `/API-AIP/chat/messages/${message_id}/dislike`,
		method: 'delete',
		svgType: '1',
	});
}
//点踩反馈
export function dislikeFeedbackApi(message_id, data) {
	return request({
		url: `/API-AIP/chat/messages/${message_id}/feedback`,
		method: 'post',
		data,
		svgType: '1',
	});
}
//解读接口
export function getInterpretationApi(data, options) {
	return request({
		url: '/API-AIP/chat/analyze',
		method: 'post',
		data,
		svgType: '1',
		...options,
	});
}
//猜你想问
export function getGuessQuestionApi(data, options) {
	return request({
		url: '/API-AIP/chat/generic_analysis',
		method: 'post',
		data,
		svgType: '1',
		...options,
	});
}
//添加消息
export function addMsgApi(data, options) {
	return request({
		url: '/API-AIP/chat/messages',
		method: 'post',
		data,
		svgType: '1',
		...options,
	});
}

// llm模型接口
export function getChatDirectApi(data, options) {
	return request({
		url: '/API-AIP/chat/direct',
		method: 'post',
		data,
		svgType: '1',
		...options,
	});
}
