import useUserStore from '@/store/modules/user';
import request from '@/utils/request';
import router from '@/router';
import usePremissionStore from '@/store/modules/premission';
export const getAgentId = async () => {
	const route = router.currentRoute.value;
	let agentId = route.query.agent_id;
	if (!agentId) {
		console.log(route);
		const { GET_USER_AGENT } = usePremissionStore();
		const list = await GET_USER_AGENT();
		const agent = list.find((item) => {
			return item.attributes.url.indexOf(route.path.split('/')[1]) !== -1;
		});
		console.log(list, agent, route.path.split('/')[1]);
		if (agent.attributes?.agentType === 'nurture') {
			agentId = agent.attributes.agent;
		} else {
			agentId = agent.id;
		}
	}
	return agentId || '002';
};
export async function getKnowledgeTotal(params) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/total',
		method: 'get',
		params: {
			agent_id: String(agent_id),
			...params,
		},
		data: [],
	});
}
export async function getKnowledgeTotalSimple(params) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/total/simple',
		method: 'get',
		params: {
			agent_id,
			...params,
		},
	});
}
export async function getKnowledgeStatistics(data) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/api/session/history/message_statistics',
		method: 'post',
		params: {
			agent_id,
			...data,
		},
	});
}
export async function getKnowledgeCollections(data) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/get_collections',
		method: 'post',
		data: {
			agent_id,
			...data,
		},
		svgType: '1',
	});
}
export async function getKnowledgeList(params) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/get_data_list',
		method: 'get',
		params: {
			agent_id,
			...params,
		},
	});
}

export async function getKnowledgeDetail(params) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/get_data',
		method: 'get',
		params: {
			agent_id,
			...params,
		},
	});
}

export async function putKnowledge(params) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/update_data',
		method: 'put',
		params: {
			agent_id,
			...params,
		},
		data: [],
		svgType: '1',
	});
}
export async function postKnowledge(data) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/add_pending_data',
		method: 'post',
		data: {
			agent_id,
			...data,
		},
		svgType: '1',
	});
}

export async function putKnowledgeCollection(data) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/update_collection',
		method: 'put',
		data: {
			agent_id,
			...data,
		},
		svgType: '1',
	});
}

export async function deleteKnowledgeCollection(params) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/del_collection',
		method: 'delete',
		params: {
			agent_id,
			...params,
		},
	});
}

export async function getKnowledgeMissionList(params) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/get/missed/list',
		method: 'get',
		params: {
			agent_id: params.agent_id || (await getAgentId()),
			skip: (params.page_num - 1) * params.page_size,
			limit: params.page_size,
			is_confirmed: params.is_confirmed,
			search_text: params.search_text,
		},
	});
}

export async function postKnowledgeMissionList(params) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/api/session/history/agent_no_answer_list',
		method: 'post',
		params: {
			agent_id: params.agent_id || (await getAgentId()),
			skip: (params.page_num - 1) * params.page_size,
			limit: params.page_size,
			query: params.search_text,
		},
	});
}

export async function getKnowledgeMissionDetail(message_id, agent_id) {
	agent_id = agent_id || (await getAgentId());
	return request({
		url: '/API-OPENGPT/knowledge/get/missed/info',
		method: 'get',
		params: {
			agent_id,
			message_id,
		},
	});
}

export async function putKnowledgeMission(data) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/update/' + data.id,
		method: 'put',
		data: {
			agent_id,
			...data,
		},
		svgType: '1',
	});
}

export async function postKnowledgeFile(file, dataset_id) {
	const agent_id = await getAgentId();
	const formData = new FormData();
	formData.append('file', file);
	return request({
		url: '/API-OPENGPT/knowledge/create_file_collection',
		method: 'post',
		params: {
			dataset_id: dataset_id,
			agent_id,
		},
		data: formData,
		svgType: '2',
	});
}

export async function checkKnowledge(message) {
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/knowledge/conversation/check',
		method: 'get',
		params: {
			agent_id,
			message,
		},
	});
}

export async function postKnowledgeHistory(data) {
	const userStore = useUserStore();
	const userId = userStore.userInfo.id;
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/api/session/history/create?userId=' + userId,
		method: 'post',
		data: {
			agent_id,
			...data,
		},
		svgType: '1',
	});
}
export async function getKnowledgeHistory(params) {
	const userStore = useUserStore();
	const userId = userStore.userInfo.id;
	const agent_id = await getAgentId();
	return request({
		url: '/API-OPENGPT/api/session/history/user/page',
		method: 'get',
		params: {
			userId,
			agent_id,
			...params,
		},
	});
}

export function postApplyCreate(data) {
	return request({
		url: '/API-OPENGPT/api/apply/create',
		method: 'post',
		data,
		svgType: '1',
	});
}

export function getApplyList(params) {
	return request({
		url: '/API-OPENGPT/api/apply/page',
		method: 'get',
		params: {
			status: params.status,
			limit: params.page_size,
			skip: (params.page_num - 1) * params.page_size,
		},
		svgType: '1',
	});
}

export async function postEditData(data) {
	return request({
		url: '/API-OPENGPT/knowledge/edit_data',
		method: 'post',
		data: {
			agent_id: await getAgentId(),
			...data,
		},
		svgType: '1',
	});
}
