import request from '@/utils/request';
import { filterEmpty } from '@/utils/index';
import qs from 'qs';
//各产品达成情况分析
export function getAchievementAnalysis(params) {
	return request({
		url: '/API-SEMANTIC/inventory_management/product_achievement_contribution',
		method: 'get',
		params: filterEmpty(params),
		// paramsSerializer: (params) => {
		// 	return Object.keys(params)
		// 		.map((key) => {
		// 			const value = params[key];
		// 			if (Array.isArray(value)) {
		// 				// 手动编码：[ 变成 %5B，] 变成 %5D，, 变成 %2C
		// 				const encodedArray = `%5B${value.join('%2C')}%5D`;
		// 				return `${encodeURIComponent(key)}=${encodedArray}`;
		// 			}
		// 			return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
		// 		})
		// 		.join('&');
		// },
	});
}
//各团队销售完成分析
export function getTeamSalesAnalysis(params) {
	return request({
		url: '/API-SEMANTIC/inventory_management/team_achievement_contribution',
		method: 'get',
		params: filterEmpty(params),
		paramsSerializer: (params) => {
			return Object.keys(params)
				.map((key) => {
					const value = params[key];
					if (Array.isArray(value)) {
						// 手动编码：[ 变成 %5B，] 变成 %5D，, 变成 %2C
						const encodedArray = `%5B${value.join('%2C')}%5D`;
						return `${encodeURIComponent(key)}=${encodedArray}`;
					}
					return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
				})
				.join('&');
		},
	});
}
//进销存趋势分析
export function getInventoryTrendAnalysis(params) {
	return request({
		url: '/API-SEMANTIC/inventory_management/inventory_trend_analysis',
		method: 'get',
		params: filterEmpty(params),
		paramsSerializer: (params) => {
			return Object.keys(params)
				.map((key) => {
					const value = params[key];
					if (Array.isArray(value)) {
						// 手动编码：[ 变成 %5B，] 变成 %5D，, 变成 %2C
						const encodedArray = `%5B${value.join('%2C')}%5D`;
						return `${encodeURIComponent(key)}=${encodedArray}`;
					}
					return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
				})
				.join('&');
		},
	});
}
//各渠道发出分析
export function getChannelAnalysis(params) {
	return request({
		url: '/API-SEMANTIC/inventory_management/channel_shipment_analysis',
		method: 'get',
		params: filterEmpty(params),
		paramsSerializer: (params) => {
			return Object.keys(params)
				.map((key) => {
					const value = params[key];
					if (Array.isArray(value)) {
						// 手动编码：[ 变成 %5B，] 变成 %5D，, 变成 %2C
						const encodedArray = `%5B${value.join('%2C')}%5D`;
						return `${encodeURIComponent(key)}=${encodedArray}`;
					}
					return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
				})
				.join('&');
		},
	});
}
//销售情况
export function getSalesAnalysis(params) {
	return request({
		url: '/API-SEMANTIC/inventory_management/sales_situation_analysis',
		method: 'get',
		params: filterEmpty(params),
		paramsSerializer: (params) => {
			return Object.keys(params)
				.map((key) => {
					const value = params[key];
					if (Array.isArray(value)) {
						// 手动编码：[ 变成 %5B，] 变成 %5D，, 变成 %2C
						const encodedArray = `%5B${value.join('%2C')}%5D`;
						return `${encodeURIComponent(key)}=${encodedArray}`;
					}
					return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
				})
				.join('&');
		},
	});
}
//省份销售趋势
export function getProvinceSalesAnalysis(params) {
	return request({
		url: '/API-SEMANTIC/inventory_management/provincial_sales_analysis',
		method: 'get',
		params: filterEmpty(params),
		paramsSerializer: (params) => {
			return Object.keys(params)
				.map((key) => {
					const value = params[key];
					if (Array.isArray(value)) {
						// 手动编码：[ 变成 %5B，] 变成 %5D，, 变成 %2C
						const encodedArray = `%5B${value.join('%2C')}%5D`;
						return `${encodeURIComponent(key)}=${encodedArray}`;
					}
					return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
				})
				.join('&');
		},
	});
}
