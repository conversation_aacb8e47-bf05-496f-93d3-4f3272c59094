import request from '@/utils/request';

//校验用户是否存在
export function findUserIsExistPV() {
	return request(
		{
			url: '/API-POCKETVIEW/pocketView/user/check',
			method: 'get',
		},
		{
			response_auth_redirect: false,
			error_message_show: false,
			code_message_show: false,
		}
	);
}

//查询用户是否存在
export function findUserIsExist() {
	return request(
		{
			url: `/API-SYSTEM/user/check/exist/${encodeURIComponent(localStorage.getItem('pv-email'))}`,
			method: 'get',
		},
		{ excludeAuthInfo: true }
	);
}
//sso用户同步
export function asyncUser() {
	return request(
		{
			url: `/API-POCKETVIEW/pocketView/user/sync`,
			method: 'post',
		},
		{ excludeAuthInfo: true }
	);
}
//获取用户信息
export function getUserInfo() {
	return request({
		url: `/API-SYSTEM/user/info?isBrief=false`,
		method: 'get',
	});
}

// 获取目标医生
export function getTargetDoctor(data) {
	return request({
		url: '/API-DOCTOR/v2/doctor/page/search',
		method: 'post',
		data,
		svgType: '1',
	});
}

// 获取问题类型
export function getQuestionType(data) {
	return request({
		url: '/API-DOCTOR/v2/question/find/type',
		method: 'post',
		svgType: '1',
	});
}

// 问题类型下的列表
export function getQesTypeList(data) {
	return request({
		url: '/API-DOCTOR/v2/question/find/detail',
		method: 'post',
		data,
		svgType: '1',
	});
}

// ai推送
export function getAiPush(params) {
	return request({
		url: `/API-GPT/chat/hcp_data_insight?question=${params.question}&userId=${params.userId}&doctorId=${params.doctorId}`,
		method: 'post',
		data: params,
		svgType: '1',
	});
}

// 获取文章列表
export function getArticleInterface(params) {
	return request({
		url: '/API-CONTENT/article/list/by/example/pageable/user',
		method: 'get',
		params,
	});
}

//获取文章token
export function getArticleToken(realme) {
	return request({
		url: '/API-CONTENT/cms/token' + '/' + realme,
		method: 'get',
	});
}

// 更新用户信息
export function updateBySelf(data) {
	return request({
		url: '/API-SYSTEM/user/update',
		method: 'put',
		data,
		svgType: '1',
	});
}
// 图片上传
export function uploadImg(data) {
	return request({
		url: '/API-MINIO/minio/public/path',
		method: 'post',
		data,
		svgType: '2',
	});
}

// 获取当前登录的组织结构
export function subDeptList() {
	return request({
		url: '/API-SYSTEM/user/sub/groups',
		method: 'get',
	});
}

// 常见问题
export function getCommonQuestion(data, params) {
	return request(
		{
			url: '/API-TECHPOOL/question/find/common',
			method: 'post',
			data,
			params,
			svgType: '1',
		},
		{ excludeAuthInfo: true }
	);
}
//获取治疗领域
export function getTreatmentArea(params) {
	return request({
		url: '/API-SYSTEM/field/page',
		method: 'get',
		params,
	});
}
/**
 * @description: 用户信息拓展字段变更
 */
export function updateUserAttributes(data) {
	return request({
		url: '/API-SYSTEM/user/update/attributes',
		method: 'put',
		svgType: '1',
		data,
	});
}

//查询省份
export function queryProvince() {
	return request({
		url: '/API-SYSTEM/province/all',
		method: 'get',
	});
}

export function queryCity(data) {
	return request({
		url: '/API-SYSTEM/province/city/by/provinceIds',
		method: 'post',
		data,
		svgType: '1',
	});
}

export function queryQx(data) {
	return request({
		url: '/API-SYSTEM/province/area/cityIds',
		method: 'post',
		data,
		svgType: '1',
	});
}

export function getTerritoryCodeLevel(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/filter/getTerritoryCodeLevel',
		method: 'get',
		params,
	});
}
export function getParentTerritoryInfo(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/filter/getParentTerritoryInfo',
		method: 'get',
		params,
	});
}
