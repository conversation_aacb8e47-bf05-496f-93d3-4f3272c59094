/* eslint-disable prettier/prettier */
/* eslint-disable no-undef */
/* eslint-disable no-empty */
/**
 * 微信js-sdk
 * 参考文档：https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421141115
 */
import useEnterStore from '@/store/modules/enterprise';
const wxApi = {
	/**
	 * [wxRegister 微信Api初始化]
	 * @param  {Function} data [ready回调函数]
	 */
	wxRegister(data) {
		return new Promise((resolve, reject) => {
			wx.config({
				beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
				debug: false, // 开启调试模式
				appId: data.appId, // 必填，公众号的唯一标识
				timestamp: data.timestamp, // 必填，生成签名的时间戳
				nonceStr: data.nonceStr, // 必填，生成签名的随机串
				signature: data.signature, // 必填，签名，见附录1
				jsApiList: ['agentConfig', 'openEnterpriseChat', 'shareToExternalContact', 'previewFile', 'hideMenuItems'],
			});
			wx.error((res) => {
				console.log('认证失败', res);
				reject(res);
			});
			wx.ready(() => {
				console.log('认证成功');
				resolve();
			});
		});
	},

	/**
	 * [registerAgentConfig 微信Api agentConfig初始化]
	 * @param  {Function} option [ready回调函数]
	 */
	registerAgentConfig(option) {
		const enterStore = useEnterStore();
		const isProxy = enterStore.enterInfo.isProxy;
		const agentId = enterStore.enterInfo.agentId;
		return new Promise((resolve, reject) => {
			wx.agentConfig({
				corpid: isProxy === true || isProxy === 'true' ? option.corpid : option.appId,
				agentid: agentId,
				timestamp: option.timestamp,
				nonceStr: option.nonceStr,
				signature: option.signature,
				jsApiList: ['getCurExternalContact', 'sendChatMessage', 'shareToExternalChat', 'getContext', 'getCurExternalChat', 'shareToExternalContact', 'openEnterpriseChat', 'setClipboardData'], // 必填，传入需要使用的接口名称
				success: (res) => {
					// 回调
					resolve(res);
					// option.success(res);
					console.log('agentConfig成功');
				},
				fail: (res) => {
					reject(res);
					// option.error(res);
				},
			});
		});
	},
	previewFile(data) {
		wx.previewFile({
			url: data.url, // 需要预览文件的地址(必填，可以使用相对路径)
			name: data.name, // 需要预览文件的文件名，必须有带文件格式的后缀，例如.doc(不填的话取url的最后部分，最后部分是个包含格式后缀的文件名)
			size: data.size, // 需要预览文件的字节大小(必填，而且大小必须正确，否则会打开失败)
			success: (res) => {
				console.log('预览成功', res);
			},
			fail: (err) => {
				console.log('预览失败', err);
			},
		});
	},
	hideAllNonBaseMenuItem() {
		// wx.hideAllNonBaseMenuItem();
		wx.hideMenuItems({
			menuList: ['menuItem:share:appMessage', 'menuItem:share:timeline', 'menuItem:share:wechat', 'menuItem:favorite', 'menuItem:copyUrl', 'menuItem:share:email'], // 要隐藏的菜单项
		});
	},
	// 创建或打开企业会话
	openEnterpriseChat(data) {
		return new Promise((resolve, reject) => {
			wx.openEnterpriseChat({
				userIds: data.userIds, // 企业成员的userid列表，注意是数组
				externalUserIds: data.externalUserIds, // 企业会话名称
				chatName: data.chatName, // 企业会话名称
				chatId: data.chatId, // 企业会话id
				success: (res) => {
					console.log('创建或打开企业会话成功', res);
					resolve(res);
				},
				fail: (res) => {
					console.log('创建或打开企业会话失败', res);
					reject(res);
				},
			});
		});
	},

	/**
	 * [shareToExternalChat 企业微信下 群发给客户]
	 * @param {[Object]} option [分享信息]
	 * @param {[Function]} option.success [转发成功的回调函数]
	 * @param {[Function]} option.error [转发失败的回调函数]
	 */
	shareToExternalContact(option) {
		return new Promise((resolve, reject) => {
			wx.invoke(
				'shareToExternalContact',
				{
					externalUserIds: option.externalUserIds, // 企业成员的userid列表，注意是数组
					attachments: [
						{
							msgtype: 'miniprogram', // 消息类型，必填
							miniprogram: {
								appid: option.appid, // 小程序的appid
								title: option.title, // 小程序消息的title
								imgUrl: option.imgUrl, //小程序消息的封面图。必须带http或者https协议头
								page: option.page, //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
							},
						},
					],
				},
				(res) => {
					if (res.err_msg === 'shareToExternalContact:ok') {
						resolve(res);
					} else {
						reject(res);
					}
				}
			);
		});
	},
};
export default wxApi;
