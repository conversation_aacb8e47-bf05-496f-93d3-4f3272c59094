import request from '@/utils/request';
import { filterEmpty } from '@/utils/index';
//销量达成
export function salesAchievementStatus(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/achievedInTheSameMonthAndQuarter',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

// 销售排名与分布
export function salesRankingDistribution(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/rankingDistribution',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

// 销售趋势
export function salesTrends(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/salesTrends',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}
export function timeDimensionTrendAnalysis(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/listing/timeDimensionTrendAnalysis',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}
// 销售铺点
export function salesShop(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/salesPoint',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

//Sales分医院销售概览Amount
export function branchHospitalSalesOverviewAmount(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/branchHospitalSalesOverviewAmount',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}
// 省份准入
export function multidimensionalTrendAnalysiProvince(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/listing/multidimensionalTrendAnalysiProvince',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}
//城市准入
export function multidimensionalTrendAnalysiCity(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/listing/multidimensionalTrendAnalysiCity',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}
// Sales分医院销售概览 amount-2
export function branchHospitalSalesOverviewAmountT(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/data/sales/branchHospitalSalesOverviewAmount',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

//DDI 分析图
export function ddiTransactionChart(params, relam) {
	// if (relam === 'muqiao' || relam === 'roche') {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/transaction/constructAnalysisChart/new',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
	// } else {
	// 	return request({
	// 		url: '/API-POCKETVIEW/pocketView/report/card/transaction/constructAnalysisChart',
	// 		method: 'post',
	// 		data: filterEmpty(params),
	// 		svgType: '1',
	// 	});
	// }
}

//DDI 销售进展（按人员）
export function ddiTransactionSalesPeople(params, relam) {
	// if (relam === 'muqiao' || relam === 'roche') {
	return request({
		url: `/API-POCKETVIEW/pocketView/report/card/transaction/transactionSalesPeopleNew?page=${params.page}&size=${params.size}`,
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
	// } else {
	// 	return request({
	// 		url: `/API-POCKETVIEW/pocketView/report/card/transaction/transactionSalesPeople?page=${params.page}&size=${params.size}`,
	// 		method: 'post',
	// 		data: filterEmpty(params),
	// 		svgType: '1',
	// 	});
	// }
}
//DDI 销售进展（按医院）
export function ddiTransactionSalesHospital(params, relam) {
	// if (relam === 'muqiao' || relam === 'roche') {
	return request({
		url: `/API-POCKETVIEW/pocketView/report/card/transaction/groupByHospitalNew?page=${params.page}&size=${params.size}`,
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
	// } else {
	// 	return request({
	// 		url: `/API-POCKETVIEW/pocketView/report/card/transaction/groupByHospital?page=${params.page}&size=${params.size}`,
	// 		method: 'post',
	// 		data: filterEmpty(params),
	// 		svgType: '1',
	// 	});
	// }
}

//DDI 销售进展
export function ddiTransactionSales(params, relam) {
	// if (relam === 'muqiao' || relam === 'roche') {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/transaction/transactionSales/new',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
	// } else {
	// 	return request({
	// 		url: '/API-POCKETVIEW/pocketView/report/card/transaction/transactionSales',
	// 		method: 'post',
	// 		data: filterEmpty(params),
	// 		svgType: '1',
	// 	});
	// }
}

//DDI 销售明细
export function ddiTransactionDetails(params) {
	return request({
		url: `/API-POCKETVIEW/pocketView/report/card/transaction/transactionDetails?page=${params.page}&size=${params.size}`,
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

// ddi明细下载
export function ddiInfoDownload(data, params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/transaction/detail/send',
		method: 'post',
		data: filterEmpty(data),
		params: filterEmpty(params),
		svgType: '1',
	});
}

// 准入情况
export function accessStatus(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/listing/accessStatus',
		method: 'get',
		params,
	});
}

// 准入情况
export function accessStatusInfo(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/listing/accessStatusInfo',
		method: 'get',
		params,
	});
}
//各级医院准入情况
export function accessStatusLevel(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/listing/hospitalAccessStatus',
		method: 'get',
		params,
	});
}
// 准入 listing
export function salesListing(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/listing/listing',
		method: 'get',
		params,
	});
}

//MarketShare 产品销售月趋势
export function marketShareTrends(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/marketShare/productSalesMonthlyTrends',
		mrthod: 'get',
		params: filterEmpty(params),
	});
}
//市场趋势季度
export function marketShareTrendsQuarter(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/marketShare/productSalesQuarterTrends',
		mrthod: 'get',
		params: filterEmpty(params),
	});
}
//MarketShare 药品市场占比分析
export function marketShareAnalysis(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/marketShare/drugMarketShare',
		mrthod: 'get',
		params: filterEmpty(params),
	});
}

//查询列表
export function queryList(data) {
	return request({
		url: '/API-POCKETVIEW/report/card/all',
		method: 'post',
		data,
		svgType: '1',
	});
}

// 根据卡片名字查询对应的卡片
export function queryCardByName(list) {
	return request({
		url: `/API-POCKETVIEW/report/card/find/by/reportCd`,
		method: 'post',
		data: list,
		svgType: '1',
	});
}

// 根据报告分组id查询对应报告
export function queryReportById(list) {
	return request({
		url: `/API-POCKETVIEW/report/card/find`,
		method: 'post',
		data: list,
		svgType: '1',
	});
}

//关注
export function focusChange(reportCd) {
	return request({
		url: `/API-POCKETVIEW/collect/concern/${reportCd}`,
		method: 'post',
		svgType: '1',
	});
}

//查询关注列表
export function queryFollowList(data) {
	return request({
		url: `/API-POCKETVIEW/collect/page/follow`,
		method: 'post',
		data,
		svgType: '1',
	});
}
export function getSalesAchievement(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/public/domeData',
		method: 'get',
		params,
	});
}

export function getMyAgent(data) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/productTarget',
		method: 'post',
		data,
		svgType: '1',
	});
}

export function getChannelTrends(data) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/salesChannel/salesChannelTrends',
		method: 'post',
		data: filterEmpty(data),
		svgType: '1',
	});
}

export function getChannelComparison(data) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/salesChannel/salesComparisonAnalysis',
		method: 'post',
		data: filterEmpty(data),
		svgType: '1',
	});
}

// 获取supersettoken
export function getSupersetToken() {
	return request({
		url: 'API-TOOLS/superset/token',
		method: 'get',
	});
}
//根据人员id获取销售达成
export function getPeopleSales(data) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/transaction/findSalesPeople',
		method: 'post',
		data,
		svgType: '1',
	});
}
//各产品销售趋势分析
export function getSalesTrends(data) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/productSalesTrendAnalysis',
		method: 'post',
		data: filterEmpty(data),
		svgType: '1',
	});
}
//各品牌销售趋势分析
export function getBrandSalesTrends(data) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/brandSalesTrendAnalysis',
		method: 'post',
		data: filterEmpty(data),
		svgType: '1',
	});
}
//各团队累计销售趋势分析
export function getTeamSalesTrends(data) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/cumulativeSalesAnalysisOfEachTeam',
		method: 'post',
		data: filterEmpty(data),
		svgType: '1',
	});
}

// 下载明细
export function salesInfoDownload(query) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/exportDataToEmail',
		method: 'post',
		data: filterEmpty(query),
		svgType: '1',
	});
}

// 热力图下载
export function salesHeatmapDownload(query) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/exportHospitalSalesOverview',
		method: 'post',
		data: filterEmpty(query),
		svgType: '1',
	});
}

// 销售总览下载
export function salesAchievementDownload(query) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/exportToEmail',
		method: 'post',
		data: filterEmpty(query),
		svgType: '1',
	});
}

// 城市销售分析
export function branchCitySalesOverviewAmount(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/branchCitySalesOverviewAmount',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

// 省份销售分析
export function branchProvinceSalesOverviewAmount(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/branchProvinceSalesOverviewAmount',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

export function getApi(params, url) {
	return request({
		url,
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}
//准入销量
export function getAdmissionSales(data) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/admissionOfficialSales/findHospital',
		method: 'post',
		data: filterEmpty(data),
		svgType: '1',
	});
}
// 团队准入分析
export function getTeamAdmissionAnalysis(data) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/listing/multidimensionalTrendAnalysiTeam',
		method: 'post',
		data: filterEmpty(data),
		svgType: '1',
	});
}

// cubejs接口
export function getcubejsData(params) {
	return request({
		url: '/API-AGENTBOX/endpoints/query-cubejs',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}
export function getcubejsHierarchicalData(params) {
	return request({
		url: '/API-AGENTBOX/endpoints/query-cubejs-hierarchical',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}
