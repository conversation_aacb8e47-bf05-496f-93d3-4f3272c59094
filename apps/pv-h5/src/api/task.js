import request from '@/utils/request';

export function getTaskList(params, data) {
	return request({
		url: '/API-TASK/task/page',
		method: 'post',
		params,
		data,
		svgType: '1',
	});
}

// 关闭任务
export function closeTaskInterface(params, data) {
	return request({
		url: '/API-TASK/task/batch/close',
		method: 'put',
		svgType: '1',
		params,
		data,
	});
}

// 查询同事信息
export function colleagueList(id, params) {
	return request({
		url: 'API-SYSTEM/user/colleagues/all/' + id,
		method: 'get',
		params,
	});
}

// 转移给其他同事
export function taskTransferIn(data) {
	return request({
		url: '/API-TASK/task/batch/shift',
		method: 'put',
		svgType: '1',
		data,
	});
}

// 新建任务
export function taskCreate(data) {
	return request({
		url: '/API-TASK/task/create',
		method: 'post',
		svgType: '1',
		data,
	});
}

// 修改任务信息
export function taskUpdateInfo(data, id) {
	return request({
		url: '/API-TASK/task/update/' + id,
		method: 'put',
		svgType: '1',
		data,
	});
}

// 任务流转记录
export function taskInfoLog(data, id) {
	return request({
		url: '/API-TASK/task/log/find/' + id,
		method: 'post',
		svgType: '1',
		params: data,
	});
}

// 查询产品列表
export function findProductAll() {
	return request({
		url: '/API-SYSTEM/product/user/product/all',
		method: 'get',
	});
}
