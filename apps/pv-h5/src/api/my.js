import request from '@/utils/request';
import { filterEmpty } from '@/utils/index';

//每日报告总结
export function dailyReportSummary() {
	return request({
		url: '/API-POCKETVIEW/pocketView/summarize/report/sum',
		method: 'get',
	});
}

// 每日达成总结
export function achievementSum() {
	return request({
		url: '/API-POCKETVIEW/pocketView/summarize/achievement/sum',
		method: 'get',
	});
}

// 拜访汇总
export function visitSummaryInfo(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/data/findVisitSummaryInfo',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

// 个人总结
export function myVisitSummary(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/my/visit/personalSummary',
		method: 'get',
		params,
	});
}
export function myList(params) {
	return request({
		url: '/API-POCKETVIEW/report/card/find/my',
		method: 'post',
		params,
	});
}

// 问卷列表
export function findSurveyList(params) {
	return request({
		url: '/API-DOCTOR/v2/doctorSurveyInfo/find',
		method: 'get',
		params,
	});
}

// 新建问卷
export function createSurvey(data) {
	return request({
		url: '/API-DOCTOR/v2/doctorSurveyInfo/add',
		method: 'post',
		data,
		svgType: '1',
	});
}

// 修改
export function updateSurvey(data) {
	return request({
		url: '/API-DOCTOR/v2/doctorSurveyInfo/update',
		method: 'put',
		data,
		svgType: '1',
	});
}

// 获取已经填写的医生集合
export function getMyDoctors(params) {
	return request({
		url: '/API-DOCTOR/v2/doctorSurveyInfo/findSurveyDoctorId',
		method: 'get',
		params,
	});
}

// 删除问卷
export function deleteSurvey(id) {
	return request({
		url: '/API-DOCTOR/v2/doctorSurveyInfo/delete/' + id,
		method: 'delete',
	});
}
