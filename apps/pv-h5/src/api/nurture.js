import request from '@/utils/request';

export function componentSearch(data) {
	return request({
		url: '/API-APP/component/page?page=0&size=1000',
		method: 'post',
		data,
		svgType: '1',
	});
}
// /component/category/count
export function getComponentCategoryCount(params) {
	return request({
		url: '/API-APP/component/category/count/' + params.type,
		method: 'get',
		params,
		svgType: '1',
	});
}
export function getScopeList() {
	return request({
		url: '/API-APP/scope/all',
		method: 'get',
		svgType: '1',
	});
}

export function componentDetail(id) {
	return request({
		url: `/API-APP/component/${id}`,
		method: 'get',
		svgType: '1',
	});
}

export function componentUpdate(data) {
	return request({
		url: '/API-APP/component',
		method: 'put',
		data,
		svgType: '1',
	});
}
