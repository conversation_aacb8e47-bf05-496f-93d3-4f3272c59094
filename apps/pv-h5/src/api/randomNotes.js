import request from '@/utils/request';

// 随手记列表
export function notesList() {
	return request({
		url: '/API-DOCTOR/v2/random/notes/all',
		method: 'post',
	});
}
// 新增随手记
export function addNotes(data) {
	return request({
		url: '/API-DOCTOR/v2/random/notes/create',
		method: 'post',
		data,
		svgType: '1',
	});
}

// 删除随手记
export function deleteNotes(id) {
	return request({
		url: '/API-DOCTOR/v2/random/notes/delete/' + id,
		method: 'delete',
	});
}

// 修改随手记
export function updateNotes(data) {
	return request({
		url: '/API-DOCTOR/v2/random/notes/update',
		method: 'put',
		data,
		svgType: '1',
	});
}

export function findVisitReportList(data) {
	return request({
		url: `/API-DOCTOR/v2/visit/find/my/group`,
		method: 'post',
		data,
		svgType: '1',
	});
}

// 删除报告
export function deleteVisitReport(visitId) {
	return request({
		url: `/API-DOCTOR/v2/visit/delete`,
		method: 'delete',
		params: {
			id: visitId,
		},
	});
}

//查询拜访报告详情
export function findVisitReportDetail(visitId) {
	return request({
		url: `/API-DOCTOR/v2/visit/find/${visitId}`,
		method: 'get',
		params: { ownerDetail: true },
	});
}

// 新建拜访报告
export function createVisitReport(data) {
	return request({
		url: '/API-DOCTOR/v2/visit/add',
		method: 'post',
		data,
		svgType: '1',
	});
}

// 修改报告
export function updateVisitReport(data) {
	return request({
		url: '/API-DOCTOR/v2/visit/updateVisit',
		method: 'put',
		data,
		svgType: '1',
	});
}

// 获取医生详情
export function getDoctorDetail(data) {
	return request({
		url: '/API-DOCTOR/v2/doctor/get/one/' + data,
		method: 'get',
	});
}
