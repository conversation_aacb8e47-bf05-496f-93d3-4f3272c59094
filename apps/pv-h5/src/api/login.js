import request from '@/utils/request';
import { getReToken } from '@/utils/auth';
import useEnterStore from '@/store/modules/enterprise';
// 获取企业信息
export function getEnterpriseInfo(baseURL, domain) {
	return request(
		{
			url: '/API-SYSTEM/enterprise/find',
			baseURL,
			method: 'get',
			params: { domain },
		},
		{
			excludeAuthInfo: true,
		}
	);
}

export function getUserInfo() {
	return request(
		{
			url: '/API-SYSTEM/user/info',
			method: 'get',
			params: { isBrief: false },
		},
		{
			response_auth_redirect: true,
			error_message_show: false,
			code_message_show: false,
		}
	);
}
//登录
export function login(data, baseURL, realm) {
	return request(
		{
			baseURL,
			url: `/auth/realms/${realm}/protocol/openid-connect/token`,
			method: 'post',
			data,
		},
		{ excludeAuthInfo: true }
	);
}
export function refreshToken(baseURL, realm, clientId) {
	return request(
		{
			url: `/auth/realms/${realm}/protocol/openid-connect/token`,
			method: 'post',
			baseURL,
			data: {
				client_id: clientId,
				grant_type: 'refresh_token',
				refresh_token: getReToken(),
			},
		},
		{ excludeAuthInfo: true, error_message_show: false }
	);
}

/* 分享-企业-应用 */
export function wechatAgent(data) {
	const enterStore = useEnterStore();
	// 获取企业 isProxy
	const isProxy = enterStore.enterInfo.isProxy;
	if (isProxy === 'true' || isProxy === true) {
		return request({
			url: '/API-WECHAT-TP/wechat/tp/' + enterStore.enterInfo.dkSiteId + `/create/corp/agent/ticket/${enterStore.enterInfo.appId}/${enterStore.enterInfo.agentId}`,
			method: 'post',
			data,
		});
	} else {
		return request({
			url: '/API-WECHAT-CP/wx/cp/' + enterStore.enterInfo.appId + '/' + enterStore.enterInfo.agentId + '/create/agent/jsapi/ticket',
			method: 'post',
			data,
		});
	}
}

//企稳认证信息
export function wechatCrop(data) {
	const enterStore = useEnterStore();
	// 获取企业 isProxy
	const isProxy = enterStore.enterInfo.isProxy;
	if (isProxy === 'true' || isProxy === true) {
		return request({
			url: `/API-WECHAT-TP/wechat/tp/${enterStore.enterInfo.dkSiteId}/create/corp/jsapi/ticket/${enterStore.enterInfo.appId}/${enterStore.enterInfo.agentId}`,
			method: 'post',
			data,
		});
	} else {
		return request({
			url: '/API-WECHAT-CP/wx/cp/' + enterStore.enterInfo.appId + '/' + enterStore.enterInfo.agentId + '/create/corp/jsapi/ticket',
			method: 'post',
			data,
		});
	}
}

/**
 * @description: 上传ppt
 */
export function uploadMinio(data, bucket) {
	return request({
		url: `/API-MINIO/minio/${bucket}/path`,
		method: 'post',
		data,
		svgType: '2',
	});
}

// 查询首页轮播图
export function findSwipe(params) {
	return request({
		url: '/API-SYSTEM/carousel/all',
		method: 'get',
		params,
	});
}

// 应用权限
export function appManage(params) {
	return request({
		url: '/API-APP/app/manage/user/all',
		method: 'get',
		params,
	});
}

// 根据用户查询当前组织
export function getOrgByUser(id) {
	return request({
		url: `/API-SYSTEM/group/${id}/user/all/true/true`,
		method: 'get',
	});
}

// 路由列表
export function permissionList() {
	return request({
		url: '/API-SYSTEM/menu/all/tree',
		method: 'get',
		params: {
			ignorePermission: false,
			view: 'mobile',
		},
	});
}

// 拜访数据
export function dictData(data) {
	return request({
		url: '/API-SYSTEM/dict/data/find',
		method: 'post',
		data,
		svgType: '1',
	});
}

// 退出登录
export function userLogout() {
	return request({
		url: '/API-SYSTEM/user/logout',
		method: 'post',
	});
}
