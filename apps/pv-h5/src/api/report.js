import request from '@/utils/request';
import { filterEmpty } from '@/utils/index';

//查询所有标签
export function getLabels(params) {
	return request({
		url: 'API-POCKETVIEW/report/card/find/label',
		method: 'post',
		params,
		svgType: '1',
	});
}

//查询报告分页
export function getReports(params) {
	return request({
		url: 'API-POCKETVIEW/report/card/find',
		method: 'post',
		data: params,
		svgType: '1',
	});
}

//创建报告
export function createReport(data) {
	return request({
		url: 'API-POCKETVIEW/collect/create',
		method: 'post',
		data,
		svgType: '1',
	});
}

//删除报告
export function deleteReport(reportId) {
	return request({
		url: `API-POCKETVIEW/collect/delete/${reportId}`,
		method: 'delete',
	});
}

//查询我的报告列表
export function getMyReport(params) {
	return request({
		url: 'API-POCKETVIEW/collect/page/mine',
		method: 'post',
		params,
		svgType: '1',
	});
}

//纯销分析
export function pureSellingAnalysis(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/data/pureSellingAnalysis',
		method: 'get',
		params,
	});
}

//修改我的报告信息
export function editMyPort(data) {
	return request({
		url: '/API-POCKETVIEW/collect/update',
		method: 'put',
		data,
		svgType: '1',
	});
}
//TOP医院分析
export function topHospital(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/data/top20Hospital',
		method: 'get',
		params,
	});
}

//Bu 销售表现及人员生产力
export function salesPerformance(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/data/salesPerformance',
		method: 'get',
		params,
	});
}

// 拜访汇总
export function visitSummaryInfo(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/data/findVisitSummaryInfo',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

// 拜访明细
export function visitInfo(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/data/findVisitInfo',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

// 拜访明细下载
export function visitInfoDownload(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/data/exportVisitInfo',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

// 拜访管理及表现
export function visitManagement(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/data/visitManagementPerformance',
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}

// 个人总结
export function myVisitSummary(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/my/visit/personalSummary',
		method: 'get',
		params,
	});
}

// 个人总结
export function mySalesSummary(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/my/sales/personalSummary',
		method: 'get',
		params,
	});
}

export function sendEmail(data, html) {
	return request({
		url: `/API-BEACON/email/true/attachment?mailDTO=${encodeURIComponent(JSON.stringify(html))}`,
		method: 'post',
		data,
		svgType: '2',
	});
}

//团队销售趋势分析
export function teamSalesTrendAnalysis(params) {
	return request({
		url: '/API-POCKETVIEW/pocketView/report/card/sales/teamSalesTrendAnalysisReport?sort=' + params.sort,
		method: 'post',
		data: filterEmpty(params),
		svgType: '1',
	});
}
