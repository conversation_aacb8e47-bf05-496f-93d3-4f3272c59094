import PptxGenJS from 'pptxgenjs';
import useUserStore from '@/store/modules/user.js';
let userStore = useUserStore();
export default function exportChartsToPPT(groupsOfImages, names) {
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async (resolve, reject) => {
		const pptx = new PptxGenJS();

		// // 添加默认图片的幻灯片的封装函数
		const addImageSlide = (imageData) => {
			const slide = pptx.addSlide();
			slide.addImage({
				path: imageData,
				x: 0, // 从幻灯片的最左边开始
				y: 0, // 从幻灯片的最顶部开始
				w: '100%', // 图片宽度填充整个幻灯片宽度
				h: '100%', // 图片高度填充整个幻灯片高度
			});
		};
		//添加水印
		try {
			const watermarkText = `${userStore.userInfo.username}`; // 水印文本
			const rotationAngle = (-15 * Math.PI) / 200; // 水印旋转角度
			// 在这里可以对添加水印后的图片数组进行处理，例如下载或展示等

			let processedImages = await addWatermarkToImages(groupsOfImages, rotationAngle, watermarkText);

			processedImages.forEach((images, index, arr) => {
				let total = arr.length - 3;
				let current = index - 1;
				const slide = pptx.addSlide();
				slide.addImage({
					data: images,
					x: 0, // 横向放置两张图
					y: 0, // 纵向排列，每行两张
					w: '100%', // 图片宽度填充整个幻灯片宽度
					h: '100%', // 图片高度填充整个幻灯片高度
				});
				// 添加页码
				if (index > 1 && index < arr.length - 1) {
					slide.addText(`第${current}页/共${total}页`, {
						x: '0%',
						y: '96%',
						w: '100%', // 图片宽度填充整个幻灯片宽度
						h: '5%', // 图片高度填充整个幻灯片高度
						fontSize: 7,
						color: '999999',
						align: 'center',
						// fill: { color: '0088CC' }, // 添加半透明白色背景
					});
				}
			});
			// 保存PPT文件
			const filename = `${names}.pptx`;
			// 将 PPT 文件保存到本地
			const pptBlob = await pptx.write('blob');
			// if (process.env.NODE_ENV === 'development') {
			// 将 Blob 对象转换为 URL
			const pptUrl = URL.createObjectURL(pptBlob);
			console.log(pptBlob);
			// 获取 Blob 对象的字节大小
			const pptBlobSize = pptBlob.size;
			console.log('Blob 的字节大小为：', pptBlobSize, '字节');

			// 创建下载链接并设置 href 属性
			let downloadPPT = async () => {
				await pptx.writeFile({ fileName: filename });
				// const downloadLink = document.createElement('a');
				// downloadLink.href = pptUrl;
				// downloadLink.download = filename;
				// downloadLink.click();
			};

			// }
			resolve({ pptBlob, filename, pptBlobSize, downloadPPT, pptUrl });
		} catch (error) {
			reject('导出失败');
		}
	});
}

function addWatermarkToImages(imageBase64Array, rotationAngle, watermarkText) {
	// 创建一个新的 Image 对象用于加载图片
	const loadImage = (src) =>
		new Promise((resolve, reject) => {
			const img = new Image();
			img.onload = () => resolve(img);
			img.onerror = reject;
			img.src = src;
		});

	// 添加水印并返回新的图片 base64 字符串
	const addWatermark = async (imageBase64) => {
		try {
			const img = await loadImage(imageBase64);
			const canvas = document.createElement('canvas');
			const ctx = canvas.getContext('2d');
			canvas.width = img.width;
			canvas.height = img.height;
			ctx.drawImage(img, 0, 0);

			ctx.font = 'bold 30px Arial';
			ctx.fillStyle = 'rgba(200, 200, 200, 0.3)';
			ctx.textAlign = 'center';
			ctx.textBaseline = 'middle';

			// 旋转角度转弧度
			const angle = rotationAngle;

			// 计算水印在水平和垂直方向上的间隔
			const horizontalSpacing = canvas.width / 4;
			const verticalSpacing = canvas.height / 3;

			// 添加水印
			for (let i = 0; i < 4; i++) {
				for (let j = 0; j < 3; j++) {
					ctx.save();
					ctx.translate(horizontalSpacing * (i + 0.5), verticalSpacing * (j + 0.5));
					ctx.rotate(angle);
					ctx.fillText(watermarkText, 0, 0);
					ctx.restore();
				}
			}

			return canvas.toDataURL('image/jpeg');
		} catch (error) {
			console.error('Error adding watermark:', error);
			return null;
		}
	};
	// 对每个图片进行添加水印操作
	const addWatermarkToAllImages = async () => {
		const processedImages = [];
		for (const imageBase64 of imageBase64Array) {
			const processedImageBase64 = await addWatermark(imageBase64);
			processedImages.push(processedImageBase64);
		}
		return processedImages;
	};

	// 调用添加水印方法，并返回处理后的图片数组
	return addWatermarkToAllImages();
}
