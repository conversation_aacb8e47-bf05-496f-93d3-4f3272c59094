import { isPhoneType } from '@/utils/index';
const commandHandler = {
	// 改变页面标题
	titleApp: (title) => {
		try {
			if (isPhoneType()) {
				// 注释ios修改标题方法
				// window.webkit.messageHandlers.setAppTitle.postMessage(title)
			} else {
				window.androids.setAppTitle(title);
			}
		} catch (error) {
			console.log(error);
		}
	},
	// 获取用户的token信息
	getAppToken: () => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.getAppToken.postMessage('getToken');
			} else {
				window.androids.getAppToken('getToken');
			}
		} catch (error) {
			console.log(error);
		}
	},
	// 需要打开的新的链接
	openAppView: (url) => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.openAppView.postMessage(url);
			} else {
				window.androids.openAppView(JSON.stringify(url));
			}
		} catch (error) {
			console.log(error);
		}
	},
	// token 失效
	sendAppTokenFali: (token) => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.sendAppTokenFali.postMessage(token);
			} else {
				window.androids.sendAppTokenFali(token);
			}
		} catch (error) {
			console.log(error);
		}
	},
	// 分享相关
	shareAppWechat: (data) => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.shareAppWechat.postMessage(data);
			} else {
				window.androids.shareAppWechat(JSON.stringify(data));
			}
		} catch (error) {
			console.log(error);
		}
	},
	// 企业微信分享
	shareAppWeCom: (data) => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.shareAppWeCom.postMessage(data);
			} else {
				window.androids.shareAppWeCom(JSON.stringify(data));
			}
		} catch (error) {
			console.log(error);
		}
	},
	// 上传用户头像到App端
	sendAppUserAvater: (url) => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.sendAppUserAvater.postMessage(url);
			} else {
				window.androids.sendAppUserAvater(url);
			}
		} catch (error) {
			console.log(error);
		}
	},
	// app 回到首页
	openAppHome: (data) => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.openAppHome.postMessage(data);
			} else {
				window.androids.openAppHome(data);
			}
		} catch (error) {
			console.log(error);
		}
	},
	// 关闭当前webView或刷新webview
	/*
	 * path: string, // 页面名称
	 * bool: boolean, // 是否刷新
	 * isClose: boolean, // 是否关闭页面
	 */
	closeWebView: (type) => {
		// 兼容新增isClose参数
		if (type === '0') {
			type = {
				isClose: true,
			};
		} else if (type.isClose === undefined) {
			type.isClose = true;
		}
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.closeWebView.postMessage(type);
			} else {
				window.androids.closeWebView(JSON.stringify(type));
			}
		} catch (error) {
			console.log(error);
		}
	},
	// 打开浏览器
	openAppBrowser: (url) => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.openAppBrowser.postMessage({ url: url });
			} else {
				window.androids.openAppBrowser(JSON.stringify({ url: url }));
			}
		} catch (error) {
			console.log(error);
		}
	},
	// 隐藏导航栏
	pushToNoNav: () => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.pushToNoNav.postMessage('2');
			} else {
				window.androids.pushToNoNav('2');
			}
		} catch (error) {
			console.log(error);
		}
	},
	// 显示导航栏
	goBackToNative: () => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.goBackToNative.postMessage('1');
			} else {
				window.androids.goBackToNative('1');
			}
		} catch (error) {
			console.log(error);
		}
	},
	// 通知app点了分享
	shareRecommendToTop: () => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.shareRecommendToTop.postMessage('3');
			} else {
				window.androids.shareRecommendToTop('3');
			}
		} catch (err) {
			console.log(err);
		}
	},
	// 通知app唤醒键盘
	wakeUpKeyboard: () => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.wakeUpKeyboard.postMessage();
			} else {
				window.androids.wakeUpKeyboard();
			}
		} catch (err) {
			console.log(err);
		}
	},
	// 通知app收起键盘
	stowKeyboard: () => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.stowKeyboard.postMessage();
			} else {
				window.androids.stowKeyboard();
			}
		} catch (err) {
			console.log(err);
		}
	},

	// 刷新页面
	refreshAppView: (data) => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.refreshAppView.postMessage(data);
			} else {
				window.androids.refreshAppView(data);
			}
		} catch (err) {
			console.log(err);
		}
	},
	// 改变原声导航栏的title
	updateAppTitle: (data) => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.updateAppTitle.postMessage(data);
			} else {
				window.androids.updateAppTitle(JSON.stringify(data));
			}
		} catch (err) {
			console.log(err);
		}
	},
	sendAppParameters: (data) => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.sendAppParameters.postMessage(data);
			} else {
				window.androids.sendAppParameters(JSON.stringify(data));
			}
		} catch (err) {
			console.log(err);
		}
	},
	// 通知app发送步数
	getSteps: () => {
		try {
			if (isPhoneType()) {
				window.webkit.messageHandlers.getSteps.postMessage('1');
			} else {
				window.androids.getSteps();
			}
		} catch (err) {
			console.log(err);
		}
	},
};
export default commandHandler;
