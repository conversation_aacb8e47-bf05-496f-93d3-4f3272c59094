import vueMatomo from '@/plugins/vue-matomo';
import useUserStore from '@/store/modules/user';
import useEnterStore from '@/store/modules/enterprise';
import dataAc from '@/plugins/dataAc';

export default (app, router) => {
	if (!app.config.globalProperties.$umeng) {
		app.use(dataAc);
	}
	let userStore = useUserStore();
	let enterStore = useEnterStore();
	// 阻止 matomo 重复注入到 app 实例中
	if (window._paq) return;
	window._paq = window._paq || [];
	window._paq.push(['setUserId', `${userStore.userInfo.username}`]); // 设置用户
	app.use(vueMatomo, {
		host: enterStore.enterInfo.siteId.split('?')[0],
		siteId: enterStore.enterInfo.siteId.split('?')[1].split('=')[1],
		enableHeartBeatTimer: true,
		debug: import.meta.env.VITE_APP_ENV === 'development' ? true : false,
		router,
	});
};
