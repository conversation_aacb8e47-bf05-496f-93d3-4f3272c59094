export function getToken() {
	if (import.meta.env.VITE_APP_ENV === 'development') {
		// return '*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
	}
	// 解析 URL，提取其中的参数部分
	const urlParams = new URLSearchParams(window.location.search);
	// 获取特定参数的值（例如，获取 token 参数的值）
	const token = urlParams.get('token');
	if (token) {
		setToken(token, 7200);
	}
	const tokenInfo = JSON.parse(localStorage.getItem('tokenInfo'));
	if (!tokenInfo) {
		return false;
	}
	const now = new Date().getTime();
	if (tokenInfo.expires && now - tokenInfo.startTime > tokenInfo.expires) {
		localStorage.removeItem('tokenInfo');
		return false;
	}
	return tokenInfo.token;
}
export function setToken(token, expires) {
	const obj = {
		token: token,
		expires: expires * 1000,
		startTime: new Date().getTime(), // 存入的时间
	};
	localStorage.setItem('tokenInfo', JSON.stringify(obj));
}
export function removeToken() {
	localStorage.removeItem('tokenInfo');
}
export function getReToken() {
	const reTokenInfo = JSON.parse(localStorage.getItem('reTokenInfo'));
	if (!reTokenInfo) {
		return false;
	}
	const now = new Date().getTime();
	if (reTokenInfo.expires && now - reTokenInfo.startTime > reTokenInfo.expires) {
		localStorage.removeItem('reTokenInfo');
		return false;
	}
	return reTokenInfo.token;
}
export function setReToken(token, expires) {
	const obj = {
		token: token,
		expires: expires * 1000,
		startTime: new Date().getTime(), // 存入的时间
	};
	localStorage.setItem('reTokenInfo', JSON.stringify(obj));
}
export function removeReToken() {
	localStorage.removeItem('reTokenInfo');
}
