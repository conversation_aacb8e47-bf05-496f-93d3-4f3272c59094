import axios from 'axios';
import { showToast as Toast } from 'vant';
import qs from 'qs';
import { getReToken, getToken, setReToken, setToken, removeReToken, removeToken } from '@/utils/auth';
import useUserStore from '@/store/modules/user';
import useEnterStore from '@/store/modules/enterprise';
import { refreshToken } from '@/api/login';
import router from '@/router/index';
// 数据缓存配置对象
const options = {
	storage: true, // 是否开始localStorage缓存
	storageKey: 'caCache',
	storage_exprise: 600000, // localStorage的存储有效时间 10min
	expire: 300000, // 存储接口数据的有效时间 5min
};

// 初始化 localStorage 数据, 当前时间减去存储数据的时间小于10分钟，数据还有效，不用清空
(function () {
	const cache = window.localStorage.getItem(options.storageKey);
	if (cache) {
		const { storageExpire } = JSON.parse(cache);
		if (storageExpire && getNowTime() - storageExpire < options.storage_exprise) {
			return;
		}
	}
	window.localStorage.setItem(options.storageKey, JSON.stringify({ data: {}, storageExpire: getNowTime() }));
})();

// 创建map集合，用于存放正在响应的请求
const pendingMap = new Map();
let isRefreshing = false;
let requests = [];
// 每次请求都会触发，生成axios实例，目的是每个请求相互之间都是隔离的没有关系
function request(axiosConfig, customOptions = {}) {
	const enterStore = useEnterStore();
	// 创建axios实例  并设置默认请求头 并设置请求超时时间
	const service = axios.create({
		baseURL: enterStore.enterInfo.interfaceAddress,
		timeout: 120000,
	});
	// 自定义配置
	const custom_options = Object.assign(
		{
			request_get_serializer: false, // 是否对get请求参数序列化
			request_get_token: true, // 是否需要获取token,默认为true
			repeat_request_cancel: false, // 是否开启取消重复请求, 默认为 true
			reduct_data_format: true, // 是否开启简洁的数据结构响应, 默认为true
			error_message_show: true, // 是否开启接口错误信息展示,默认为true
			code_message_show: true, // 是否开启code不为200的信息提示， 默认为true
			response_auth_redirect: true, // 是否校验接口403的情况，默认为true
			cache_request_local: false, // 是否开启数据缓存（适用于get请求），默认为false
			setExpireTime: 0, // 缓存数据的有效时间，默认是0，最大不能超过60000（method === get, cache_request_local === true生效）
		},
		customOptions
	);

	// 请求拦截器
	service.interceptors.request.use(
		(config) => {
			const userStore = useUserStore();
			// config.cancelToken ?? removePending(config);
			custom_options.repeat_request_cancel && addPending(config);
			// 请求是否缓存
			if (custom_options.cache_request_local && config.method.toLocaleLowerCase() === 'get') {
				requestInterceptor(config);
			}

			if (custom_options.request_get_token) {
				config.headers['code'] = localStorage.getItem('lbH5Code');
			}
			if (getToken() && !custom_options.excludeAuthInfo) {
				config.headers['Authorization'] = `Bearer ${getToken()}`;
			}
			if (['put', 'post', 'patch'].includes(config.method.toLocaleLowerCase())) {
				if (config.svgType === '1') {
					config.headers['Content-Type'] = 'application/json;utf-8';
				} else if (config.svgType === '2') {
					config.headers['Content-Type'] = 'multipart/form-data;boundary = ' + new Date().getTime();
				} else {
					config.data = qs.stringify({
						...config.data,
					});
				}
			}
			// get请求需要序列化参数
			if (custom_options.request_get_serializer && config.method.toLocaleLowerCase() === 'get') {
				config.url = config.url + '?' + qs.stringify({ ...config.params }, { arrayFormat: 'repeat' });
				config.params = undefined;
			}
			return config;
		},
		(error) => {
			return Promise.reject(error);
		}
	);

	// 响应拦截器
	service.interceptors.response.use(
		(res) => {
			if (res.config.method.toLocaleLowerCase() === 'get' && custom_options.cache_request_local) {
				cacheResInterceptor(res);
			}
			// 兼容agent-tools xiaoice 接口
			if ((res.data && res.data.signature) || res.config.url.includes('/xiaoice/invalidate_auth_token')) {
				return custom_options.reduct_data_format ? res.data : res;
			}
			//兼容cubejs接口
			if ((res.data && res.data.signature) || res.config.url.includes('/API-AGENTBOX/endpoints/query-cubejs')) {
				return custom_options.reduct_data_format ? res.data : res;
			}
			if (res.data && res.data.code !== 200 && res.data.status !== 200 && !res.data.access_token) {
				if (custom_options.code_message_show) {
					Toast({
						message: res.data.msg ? res.data.msg : res.data.message ? res.data.message : '接口请求失败',
						position: 'top',
						duration: 2000,
					});
				}
				// 上传 matomo
				try {
					if (window._paq) {
						window._paq.push([
							'trackEvent',
							'API Error',
							`Status ${res.data.code || res.data.status || '其他'}`,
							res.config.url + ' | ' + (res.data.msg || res.data.message || '接口请求失败') + ' | ' + res.config.method + ' | params:' + JSON.stringify(res.config.params || {}) + ' | data:' + JSON.stringify(res.config.data || {}),
						]);
					}
				} catch (error) {
					console.log(error);
				}

				return Promise.reject(res.data.msg); // code不等于200, 页面具体逻辑就不执行了
			}
			return custom_options.reduct_data_format ? res.data : res;
		},
		(error) => {
			const { config } = error;
			if (error.response?.status === 401) {
				if (getReToken()) {
					if (!isRefreshing) {
						// 控制重复获取token
						isRefreshing = true;
						refreshToken(enterStore.enterInfo.authAddress, enterStore.enterInfo.id, enterStore.enterInfo.clientId)
							.then((res) => {
								isRefreshing = false;
								const { access_token, expires_in, refresh_token, refresh_expires_in } = res;
								setToken(access_token, expires_in);
								setReToken(refresh_token, refresh_expires_in);
								requests.forEach((cb) => {
									cb(access_token);
								});
							})
							.catch(() => {
								isRefreshing = false;
								// 换取新token失败，跳转到登出页面
								location.href = '/app/mobile/logout';
								return Promise.reject(error);
							});
					}
					// 将其他接口缓存起来
					const retryRequest = new Promise((resolve) => {
						requests.push((access_token) => {
							config.headers.Authorization = 'Bearer ' + access_token;
							console.log(config);
							resolve(service(config));
						});
					});
					return retryRequest;
				}
			}
			if (custom_options.response_auth_redirect && error.response?.status === 403) {
				removeReToken();
				removeToken();
				return router.push('/401');
			}

			// config && removePending(config);
			// 处理被取消的请求 和 被缓存的请求
			// if (axios.isCancel(error)) {
			// 	if (custom_options.cache_request_local && error.message.data && error.message.data.config.method.toLocaleLowerCase() === 'get') {
			// 		// 缓存的请求 返回数据结果
			// 		return Promise.resolve(error.message.data.data);
			// 	}
			// 	// 取消的请求 在浏览器打印出信息
			// 	return console.error('请求的重复请求：' + error.message);
			// }
			// 其他错误 在浏览器打印出信息
			custom_options.error_message_show && httpErrorStatusHandle(error); // 处理错误状态码

			if (!custom_options.error_message_show) {
				return Promise.reject();
			}
			return Promise.reject(error); // 错误继续返回给到具体页面onsole.error(error)
		}
	);
	return service(axiosConfig);
}
export default request;

/**
 * 处理异常，显示错误信息
 * @param error
 */
function httpErrorStatusHandle(error) {
	console.log(error);
	// 获取 http 的状态码
	const errCode = error.response.status;
	const url = error.response.config.url;
	const method = error.response.config.method;
	const params = JSON.stringify(error.response.config.params || {});
	const data = JSON.stringify(error.response.config.data || {});
	let message = error.response.data.message || '';
	if (!message) {
		switch (errCode) {
			case 302:
				message = '接口重定向了！';
				break;
			case 400:
				message = '参数不正确！';
				break;
			case 403:
				message = '您没有权限操作！';
				break;
			case 404:
				message = `请求地址出错: ${error.response.config.url}`;
				break;
			case 500:
				message = '服务器内部错误！';
				break;
			case 503:
				message = '服务不可用！';
				break;
			case 504:
				message = '服务暂时无法访问，请稍后再试！';
				break;
			case 0:
				message = '请求超时，请稍后再试！';
				break;
			default:
				message = '异常问题，请联系管理员！';
				break;
		}
	}
	Toast({
		message: message || '接口请求失败',
		position: 'top',
	});
	try {
		// 上传matomo
		if (window._paq) {
			window._paq.push(['trackEvent', 'API Error', `Status ${errCode || '其他'}`, url + ' | ' + message + ' | ' + method + ' | params:' + params + ' | data:' + data]);
		}
	} catch (error) {
		console.log(error);
	}
}

/**
 * 删除重复的请求
 * @param {*} config
 */
function removePending(config) {
	const pendingKey = getPendingKey(config);
	if (pendingMap.has(pendingKey)) {
		const cancelToken = pendingMap.get(pendingKey);
		cancelToken(pendingKey);
		pendingMap.delete(pendingKey);
	}
}

/**
 * 生成唯一的每个请求的key
 * @param {*} config
 *  @returns
 */
function getPendingKey(config) {
	let { url, method, params, data } = config;
	// response里面返回的config.data是个字符串对象
	if (typeof data === 'string') {
		data = data ? JSON.parse(data) : '';
	}
	return [url, method, JSON.stringify(params), JSON.stringify(data)].join('&');
}

/**
 * 储存每个请求的唯一cancel（取消）回调，以此为标示
 * @param config
 */
function addPending(config) {
	const pendingKey = getPendingKey(config);
	if (pendingMap.has(pendingKey)) {
		return;
	}
	config.cancelToken =
		config.cancelToken ||
		new axios.CancelToken((cancel) => {
			pendingMap.set(pendingKey, cancel);
		});
}

/**
 * 开启缓存，存入数据
 * @returns {*} res
 */
function cacheResInterceptor(res) {
	if (res && (res.data.code === 200 || res.data.status === 200)) {
		const data = {
			expire: getNowTime(),
			data: res,
		};
		CACHES[`${getPendingKey(res.config)}`] = data;
	}
}

/**
 * 开启缓存则保存数据和cancel函数
 * @param {*} config
 */
function requestInterceptor(config) {
	const data = CACHES[`${getPendingKey(config)}`];
	// 这里用于存储是默认时间还是用户传递过来的时间
	let setExpireTime;
	options.setExpireTime ? (setExpireTime = options.setExpireTime) : (setExpireTime = options.expire);
	// 判断缓存数据是否存在 存在的话 是否过期 没过期就返回
	if (data && getNowTime() - data.expire < setExpireTime) {
		config.cancelToken = new axios.CancelToken((cancel) => {
			cancel(data);
		});
	}
}

/**
 * 读取缓存的数据
 * @param {String} key
 * @returns {Object} data[key] || null
 */
function getCacheItem(key) {
	const cache = window.localStorage.getItem(options.storageKey);
	const { data } = JSON.parse(cache);
	return (data && data[key]) || null;
}

/**
 * 获取当前时间
 * @returns {number}
 */
function getNowTime() {
	return new Date().getTime();
}

/**
 * @description 写入缓存数据
 * @param {String} key
 * @param {Object} value
 */
function setCacheItem(key, value) {
	const cache = window.localStorage.getItem(options.storageKey);
	const { data, storageExpire } = JSON.parse(cache);
	data[key] = value;
	window.localStorage.setItem(options.storageKey, JSON.stringify({ data, storageExpire }));
}

// 通过 proxy 代理 处理读取和写入缓存数据
const _CACHES = {};
const cacheHandler = {
	get: (target, key) => {
		let value = target[key];
		if (options.storage && !value) {
			value = getCacheItem(key);
		}
		return value;
	},
	set: (target, key, value) => {
		target[key] = value;
		if (options.storage) {
			setCacheItem(key, value);
		}

		return true;
	},
};
const CACHES = new Proxy(_CACHES, cacheHandler);
