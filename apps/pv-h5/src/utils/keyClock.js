import VueKeycloakJs from '@dsb-norge/vue-keycloak-js';
import { setToken, setReToken } from './auth.js';
import useEnterStore from '@/store/modules/enterprise';
function clock(app) {
	const enterStore = useEnterStore();
	return new Promise((resolve, reject) => {
		app.use(VueKeycloakJs, {
			init: {
				onLoad: 'login-required',
				checkLoginIframe: false,
			},
			logout: {
				redirectUri: window.location.origin + '/logout',
			},
			config: {
				url: enterStore.enterInfo.authAddress + '/auth',
				clientId: enterStore.enterInfo.clientId,
				realm: enterStore.enterInfo.id,
			},
			onReady(keycloak) {
				alert(1);
				app.config.globalProperties.$keycloak = keycloak; // 挂载全局EventBus
				const reTokenParsed = parseInt(keycloak.refreshTokenParsed.exp - keycloak.refreshTokenParsed.iat);
				const tokenParsed = parseInt(keycloak.tokenParsed.exp - keycloak.tokenParsed.iat);
				localStorage.setItem('pv-email', keycloak.tokenParsed.email);
				// 替换 access_token
				setToken(keycloak.token, tokenParsed);
				// 替换 refresh_token
				setReToken(keycloak.refreshToken, reTokenParsed);
				// 写入keyCloak 信息
				// setKeycloak(keycloak);
				resolve();
			},
		});
	});
}

export default clock;
