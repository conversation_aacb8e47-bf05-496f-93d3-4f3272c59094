import { parseTime } from './pb';
import Decimal from 'decimal.js';
import dayjs from 'dayjs';
import usePremissionStore from '@/store/modules/premission';
/**
 * 表格时间格式化
 */
export function formatDate(cellValue) {
	if (cellValue == null || cellValue === '') return '';
	var date = new Date(cellValue);
	var year = date.getFullYear();
	var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
	var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
	var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
	var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
	var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
	return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
}

//时间处理
export function formattedTime() {
	let now = new Date();
	let hours = now.getHours().toString().padStart(2, '0');
	let minutes = now.getMinutes().toString().padStart(2, '0');
	return [hours, minutes];
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
	if (('' + time).length === 10) {
		time = parseInt(time) * 1000;
	} else {
		time = +time;
	}
	const d = new Date(time);
	const now = Date.now();

	const diff = (now - d) / 1000;

	if (diff < 30) {
		return '刚刚';
	} else if (diff < 3600) {
		// less 1 hour
		return Math.ceil(diff / 60) + '分钟前';
	} else if (diff < 3600 * 24) {
		return Math.ceil(diff / 3600) + '小时前';
	} else if (diff < 3600 * 24 * 2) {
		return '1天前';
	}
	if (option) {
		return parseTime(time, option);
	} else {
		return d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分';
	}
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
	url = url == null ? window.location.href : url;
	const search = url.substring(url.lastIndexOf('?') + 1);
	const obj = {};
	const reg = /([^?&=]+)=([^?&=]*)/g;
	search.replace(reg, (rs, $1, $2) => {
		const name = decodeURIComponent($1);
		let val = decodeURIComponent($2);
		val = String(val);
		obj[name] = val;
		return rs;
	});
	return obj;
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
	// returns the byte length of an utf8 string
	let s = str.length;
	for (var i = str.length - 1; i >= 0; i--) {
		const code = str.charCodeAt(i);
		if (code > 0x7f && code <= 0x7ff) s++;
		else if (code > 0x7ff && code <= 0xffff) s += 2;
		if (code >= 0xdc00 && code <= 0xdfff) i--;
	}
	return s;
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
	const newArray = [];
	for (let i = 0; i < actual.length; i++) {
		if (actual[i]) {
			newArray.push(actual[i]);
		}
	}
	return newArray;
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
	if (!json) return '';
	return cleanArray(
		Object.keys(json).map((key) => {
			if (json[key] === undefined) return '';
			return encodeURIComponent(key) + '=' + encodeURIComponent(json[key]);
		})
	).join('&');
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
	const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ');
	if (!search) {
		return {};
	}
	const obj = {};
	const searchArr = search.split('&');
	searchArr.forEach((v) => {
		const index = v.indexOf('=');
		if (index !== -1) {
			const name = v.substring(0, index);
			const val = v.substring(index + 1, v.length);
			obj[name] = val;
		}
	});
	return obj;
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
	const div = document.createElement('div');
	div.innerHTML = val;
	return div.textContent || div.innerText;
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
	if (typeof target !== 'object') {
		target = {};
	}
	if (Array.isArray(source)) {
		return source.slice();
	}
	Object.keys(source).forEach((property) => {
		const sourceProperty = source[property];
		if (typeof sourceProperty === 'object') {
			target[property] = objectMerge(target[property], sourceProperty);
		} else {
			target[property] = sourceProperty;
		}
	});
	return target;
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
	if (!element || !className) {
		return;
	}
	let classString = element.className;
	const nameIndex = classString.indexOf(className);
	if (nameIndex === -1) {
		classString += '' + className;
	} else {
		classString = classString.substr(0, nameIndex) + classString.substr(nameIndex + className.length);
	}
	element.className = classString;
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
	if (type === 'start') {
		return new Date().getTime() - 3600 * 1000 * 24 * 90;
	} else {
		return new Date(new Date().toDateString());
	}
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
	let timeout;
	return function (...args) {
		if (timeout) clearTimeout(timeout);
		if (immediate) {
			const callNow = !timeout;
			timeout = setTimeout(() => {
				timeout = null;
			}, wait);
			if (callNow) func.apply(this, args);
		} else {
			timeout = setTimeout(() => {
				func.apply(this, args);
			}, wait);
		}
	};
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
	if (!source && typeof source !== 'object') {
		throw new Error('error arguments', 'deepClone');
	}
	const targetObj = source.constructor === Array ? [] : {};
	Object.keys(source).forEach((keys) => {
		if (source[keys] && typeof source[keys] === 'object') {
			targetObj[keys] = deepClone(source[keys]);
		} else {
			targetObj[keys] = source[keys];
		}
	});
	return targetObj;
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
	return Array.from(new Set(arr));
}

/**
 * @returns {string}
 */
export function createUniqueString() {
	const timestamp = +new Date() + '';
	const randomNum = parseInt((1 + Math.random()) * 65536) + '';
	return (+(randomNum + timestamp)).toString(32);
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
	return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'));
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
	if (!hasClass(ele, cls)) ele.className += ' ' + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
	if (hasClass(ele, cls)) {
		const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)');
		ele.className = ele.className.replace(reg, ' ');
	}
}

export function makeMap(str, expectsLowerCase) {
	const map = Object.create(null);
	const list = str.split(',');
	for (let i = 0; i < list.length; i++) {
		map[list[i]] = true;
	}
	return expectsLowerCase ? (val) => map[val.toLowerCase()] : (val) => map[val];
}

// 首字母大小
export function titleCase(str) {
	return str.replace(/( |^)[a-z]/g, (L) => L.toUpperCase());
}

// 下划转驼峰
export function camelCase(str) {
	return str.replace(/_[a-z]/g, (str1) => str1.substr(-1).toUpperCase());
}

export function isNumberStr(str) {
	return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str);
}

/*
 * 获取url中参数值
 */
export function getQueryString(name) {
	if (window.location.href.indexOf('?') < 0) {
		return null;
	}
	const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
	const r = window.location.href.split(/\?(.*)$/, 2)[1].match(reg);
	if (r != null) {
		return decodeURIComponent(r[2]);
	}
	return null;
}

export function isPhoneType() {
	const u = navigator.userAgent;
	const isIOS = !!u.match(/Mac OS X/i);
	if (isIOS) {
		// 这个是ios操作系统
		return true;
	} else {
		// 除去ios系统 全部默认是安卓操作系统
		return false;
	}
}

// uuid
export function uuid() {
	const s = [];
	const hexDigits = '0123456789abcdef';
	for (let i = 0; i < 36; i++) {
		s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
	}
	s[14] = '4';
	s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
	s[8] = s[13] = s[18] = s[23] = '-';

	const uuid = s.join('');
	return uuid;
}

// 统一都处理成K，保留2位小数
export function formatKNumber(number) {
	if (!number) return 0;
	return `${new Decimal(number).dividedBy(1000).toDecimalPlaces(2)}`;
}

// 统一都处理成K，保留1位小数
export function formatKNumberT(number) {
	if (!number || number === 0 || number === '0') return 0;
	let num = `${new Decimal(number).dividedBy(1000).toDecimalPlaces(1).toNumber().toLocaleString()}`;
	if (num.indexOf('.') < 0) num = num + '.0';
	return num;
}

export function formatPrecentOne(number) {
	if (!number || number === 0 || number === '0') return 0;
	let num = new Decimal(number).toDecimalPlaces(1).toNumber().toLocaleString();
	if (num.indexOf('.') < 0) num = num + '.0';
	return num;
}

export function formatNumber(number) {
	if (!number) return 0;
	if (number >= 10000) {
		// 如果数值大于等于 1000000，将其除以 1000 并添加 "K" 后缀
		let num = `${new Decimal(number).dividedBy(1000).toDecimalPlaces(0).toNumber().toLocaleString()}`;
		return (num = num.split('.')[0] + 'K');
	} else {
		// 否则直接添加 "K" 后缀
		number = new Decimal(number).toDecimalPlaces(0);
		return `${number}`;
	}
}
export function formatNumberT(number) {
	if (!number || number === 0 || number === '0') return 0 + 'K';
	let num = `${new Decimal(number).dividedBy(1000).toDecimalPlaces(1).toNumber().toLocaleString()}`;
	if (num.indexOf('.') < 0) num = num + '.0';
	return (num = num + 'K');
}
export function formatNumberTH(number) {
	if (!number || number === 0 || number === '0') return 0;
	let num = `${new Decimal(number).dividedBy(1000).toDecimalPlaces(1).toNumber().toLocaleString()}`;
	if (num.indexOf('.') < 0) num = num + '.0';
	return num;
}
export function formatNumberWan(number) {
	if (!number) return 0;
	// 如果数值大于等于 1000000，将其除以 1000 并添加 "K" 后缀
	let num = `${new Decimal(number).dividedBy(10000).toNumber().toLocaleString()}`;
	return (num = num.split('.')[0] + '万');
}

/**
 * 两个日期之间所有的月份
 * @param {String} start
 * @param {String} end
 * @returns {Array}
 */
export function getMonthBetween(start, end) {
	var result = [];
	var s = start.split('-');
	var e = end.split('-');
	var min = new Date();
	var max = new Date();
	min.setFullYear(s[0], s[1]);
	max.setFullYear(e[0], e[1]);
	var curr = min;
	while (curr <= max) {
		var month = curr.getMonth();
		var str = (month === 0 ? curr.getFullYear() - 1 : curr.getFullYear()) + '-' + (month === 0 ? 12 : month < 10 ? '0' + month : month);
		var t = curr.getFullYear() + '-12';
		if (str === t) {
			str = curr.getFullYear() + '-12';
		}
		result.push(str);
		curr.setMonth(month + 1);
	}
	return result;
}

// 判断传入的三个月份是否在同一个季度
export function isInSameQuarter(month1, month2, month3) {
	// 定义每个季度的起始月份和结束月份
	var quarters = [
		{ startMonth: 1, endMonth: 3 }, // 第一季度
		{ startMonth: 4, endMonth: 6 }, // 第二季度
		{ startMonth: 7, endMonth: 9 }, // 第三季度
		{ startMonth: 10, endMonth: 12 }, // 第四季度
	];

	// 获取传入的月份所属的季度索引
	function getQuarterIndex(month) {
		for (var i = 0; i < quarters.length; i++) {
			if (quarters[i].startMonth <= month && month <= quarters[i].endMonth) {
				return i + 1;
			}
		}

		throw new Error('Invalid month');
	}

	try {
		var quarter1 = getQuarterIndex(month1);
		var quarter2 = getQuarterIndex(month2);
		var quarter3 = getQuarterIndex(month3);

		// 若三个月份都处于相同的季度中，则返回 true；否则返回 false
		return quarter1 === quarter2 && quarter2 === quarter3;
	} catch (error) {
		console.log(error);
		return false;
	}
}

// str : 2023-09-01
export function getFilterTime(str) {
	if (str && str.length > 6) {
		let newStr = str.replace(/-/g, '');
		return newStr.substring(0, 6);
	} else {
		return str;
	}
}

// 返回当前年月：202403
export function getCurrentYearMonth() {
	let month = new Date().getMonth() + 1;
	let year = new Date().getFullYear();
	return year + month.toString().padStart(2, '0');
}

// 返回当前时间上半年/上年的年月：202310
export function getYearMonthBefore(index) {
	const now = new Date();
	now.setMonth(now.getMonth() - index);
	const year = now.getFullYear();
	const month = now.getMonth() + 1;
	const paddedMonth = month < 10 ? '0' + month : month;
	return `${year}${paddedMonth}`;
}

// 通过ids查找对应的names
export function getNamesByIdsInArray(array, ids) {
	const idToNameMap = Object.fromEntries(array.map((obj) => [obj.id, obj.name]));
	return ids.map((id) => idToNameMap[id] || null);
}

export function generateRandomNumber() {
	const min = 10000; // 最小值（包括）
	const max = 99999; // 最大值（包括）

	// 生成随机数
	const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;

	return randomNumber;
}

export function translateFont(size) {
	// 获得当前页面宽度大小
	let clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
	if (!clientWidth) return;
	if (clientWidth > 600) clientWidth = 375;
	let fontSize = clientWidth / 375; // 2560 为字体大小显示为 size 时的页面宽度
	return size * fontSize;
}
export function saleTrendFilter(item) {
	if (item.seriesName === '指标' || item.seriesName === '销售') {
		return formatNumbeWT(item.value) + 'K';
	}
	if (
		item.seriesName === '指标额' ||
		item.seriesName === '销售额' ||
		item.seriesName === '同期' ||
		item.seriesName === '目标医院' ||
		item.seriesName === '准入数' ||
		item.seriesName === '正式进药' ||
		item.seriesName === '临时采购' ||
		item.seriesName === '院内决策药房' ||
		item.seriesName === '未准入' ||
		item.seriesName === '指标' ||
		item.seriesName === '销售' ||
		item.seriesName === '实际' ||
		item.seriesName === '目标' ||
		item.seriesName === '库存金额' ||
		item.seriesName === '发出金额' ||
		item.seriesName === '发出金额负' ||
		item.seriesName === '销售金额' ||
		item.seriesName === '发出' ||
		item.seriesName === '库存' ||
		item.seriesName === '客户数'
	) {
		return formatNumberT(item.value);
	}
	if (item.seriesName === '达成' || item.seriesName === '同比增长' || item.seriesName === '占比' || item.seriesName === '同比' || item.seriesName === '达成率' || item.seriesName === '环比' || item.seriesName === '库存周转率' || item.seriesName === '发出达成' || item.seriesName === '销售达成') {
		return formatPrecentOne(item.value) + '%';
	}
}
export function spaces(arr) {
	if (arr <= 6) {
		return 0;
	} else if (arr <= 12) {
		return 1;
	} else if (arr <= 18) {
		return 2;
	} else {
		return 3;
	}
}

// 输入日期字符串和目标月份的索引（从0开始计数）
export function getPreviousMonth(dateString, targetIndex) {
	// 将输入的日期字符串转换为日期对象
	var date = new Date(dateString);

	// 获取当前日期的月份
	var currentMonth = date.getMonth();

	// 计算目标月份的索引
	var targetMonth = currentMonth - targetIndex;

	// 如果目标月份小于1，说明需要向前跨年
	if (targetMonth < 0) {
		// 计算跨年后的年份
		var year = date.getFullYear() - Math.ceil(Math.abs(targetMonth) / 12);
		// 计算跨年后的月份
		targetMonth = 12 - (Math.abs(targetMonth) % 12);
		// 根据年份和月份创建新的日期对象
		var newDate = new Date(year, targetMonth - 1, 1);
		return formatDate1(newDate);
	} else {
		// 如果目标月份大于等于1，直接创建新的日期对象
		// eslint-disable-next-line no-redeclare
		var newDate = new Date(date.getFullYear(), targetMonth, 1);
		return formatDate1(newDate);
	}
}

// 格式化日期为 "YYYY-MM-DD"
function formatDate1(date) {
	var year = date.getFullYear();
	var month = (date.getMonth() + 1).toString().padStart(2, '0');
	var day = date.getDate().toString().padStart(2, '0');
	return year + '-' + month + '-' + day;
}

/**
 * [isWeChat 判断当前系统所处的环境]
 * @returns {[String]} [企业微信|微信|不在微信环境]
 */
export function isWeChat() {
	const ua = window.navigator.userAgent.toLowerCase();
	// eslint-disable-next-line eqeqeq
	if (ua.match(/MicroMessenger/i) == 'micromessenger' && ua.match(/wxwork/i) == 'wxwork') {
		return '企业微信';
		// eslint-disable-next-line eqeqeq
	} else if (ua.match(/micromessenger/i) == 'micromessenger') {
		return '微信';
	} else {
		return '不在微信环境';
	}
}
export function getTimePeriod(currentTime) {
	const hour = currentTime.getHours();
	if (hour >= 0 && hour < 12) {
		return '上午';
	} else if (hour >= 12 && hour < 18) {
		return '下午';
	} else {
		return '晚上';
	}
}

export function getNameByPath(path) {
	if (path.includes('/report/myReport')) {
		return '汇报-我的报告';
	} else if (path.includes('/report/reportDetail')) {
		return '汇报-报告详情-';
	} else if (path.includes('/observe/focus')) {
		return '洞察-关注-';
	} else if (path.includes('/observe/discovery')) {
		let index = sessionStorage.getItem('discovery-index');
		return `洞察-发现-${index}-`;
	} else {
		if (path.indexOf('/report') > -1) {
			try {
				const perStore = usePremissionStore();
				const subPath = path.split('/')[path.split('/').length - 1];
				const name = perStore.reportList.find((ele) => ele.path === subPath)?.name;
				return `汇报-${name}-`;
			} catch {
				return '';
			}
		} else {
			return '';
		}
	}

	//
	//
	// else if (path.includes('/report/standardReport')) {
	// 	return '汇报-医院团队-';
	// } else if (path.includes('/report/manageReport')) {
	// 	return '汇报-管理层-';
	// } else if (path.includes('/report/kyReport')) {
	// 	return '汇报-科园-';
	// } else if (path.includes('/report/cdReport')) {
	// 	return '汇报-C&D-';
	// }
}

export function formatNumberW(number, suffix, target) {
	// 判断参数是否为数字
	if (typeof number !== 'number' || isNaN(number)) {
		return '参数错误';
	}

	function formatNumber(count) {
		if (target < 1000) {
			return `${count}`;
		} else if (target >= 1000 && target < 10000) {
			// 向下取整并保留两位小数
			return `${(Math.floor((count / 1000) * 100) / 100).toFixed(2)}`;
		} else {
			// 向下取整并保留两位小数
			return `${(Math.floor((count / 10000) * 100) / 100).toFixed(2)}`;
		}
	}

	function formatNumberUnit(count, prefix, suffix) {
		if (count < 1000) {
			return `${count}${suffix}`;
		} else if (count >= 1000 && count < 10000) {
			// 向下取整并保留两位小数
			return `${(Math.floor((count / 1000) * 100) / 100).toFixed(2)}${prefix}千${suffix}`;
		} else {
			// 向下取整并保留两位小数
			return `${(Math.floor((count / 10000) * 100) / 100).toFixed(2)}${prefix}万${suffix}`;
		}
	}

	if (suffix !== '') {
		return formatNumberUnit(number, '', suffix);
	} else {
		return formatNumber(number);
	}

	// 处理数字大于等于万的情况
	// if (number >= 10000) {
	// 将数字转换为万并保留一位小数
	// return new Decimal(number).dividedBy(10000).toDecimalPlaces(2);
	// } else {
	// 	// 小于万的情况直接返回原数字
	// 	return number.toString();
	// }
}
export function filterEmpty(obj) {
	return Object.fromEntries(
		Object.entries(obj).filter(([key, value]) => {
			// 过滤掉属性值为空字符串或者空数组的情况
			return (typeof value !== 'string' || value.trim() !== '') && (!Array.isArray(value) || value.length > 0);
		})
	);
}

// 获取屏幕宽度识别是pc或者移动
export function isPc() {
	let num = document.documentElement.clientWidth;
	return num < 900 ? false : true;
}

// 生成chatId
export function getRandomString() {
	var str = 'abcdefghijklmnopqrstuvwxyz1234567890';
	var chars = str.split(''); // 将字符串转换成数组
	var randomString = '';
	var index;

	// 循环12次，每次随机选取一个字符
	for (var i = 0; i < 12; i++) {
		index = Math.floor(Math.random() * chars.length); // 生成随机索引
		randomString += chars[index]; // 将随机选取的字符添加到结果字符串中
	}

	return randomString; // 返回结果字符串
}
export function getFirstAndLastDayOfMonth() {
	const now = new Date();

	// 获取当前年份和月份
	const year = now.getFullYear();
	const month = now.getMonth();

	// 获取当前月份的第一天
	const firstDay = new Date(year, month, 1);

	// 获取当前月份的最后一天
	const lastDay = new Date(year, month + 1, 0);

	// 格式化日期为 yyyy-mm-dd
	const formatDate = (date) => {
		const y = date.getFullYear();
		const m = String(date.getMonth() + 1).padStart(2, '0'); // 补零
		const d = String(date.getDate()).padStart(2, '0'); // 补零
		return `${y}-${m}-${d}`;
	};

	return {
		firstDay: formatDate(firstDay),
		lastDay: formatDate(lastDay),
	};
}

export function formatParamsDates(arr) {
	for (const item of arr) {
		if (item.params && item.params.startTime && item.params.endTime) {
			// 分割 startTime 和 endTime，检查是否为 YYYY-MM 格式
			let startParts = item.params.startTime.split('-');
			let endParts = item.params.endTime.split('-');

			// 如果 startTime 是 YYYY-MM 格式，将其转换为 YYYY-MM-01
			if (startParts.length === 2) {
				item.params.startTime += '-01';
			}

			// 如果 endTime 是 YYYY-MM 格式，将其转换为 YYYY-MM-01
			if (endParts.length === 2) {
				item.params.endTime += '-01';
			}
		}
	}

	return arr;
}

function getFormattedDate(date) {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
	const day = String(date.getDate()).padStart(2, '0');

	return `${year}-${month}-${day}T00:00:00`;
}

export function getTodayAndYesterday() {
	const today = new Date();
	const yesterday = new Date(today);
	yesterday.setDate(today.getDate() - 1);

	const formattedToday = getFormattedDate(today);
	const formattedYesterday = getFormattedDate(yesterday);

	return { formattedToday, formattedYesterday };
}

export function formatDateToYearMonth(dateString) {
	// 使用split方法将日期字符串分割为数组
	const parts = dateString.split('-');

	// 检查是否分割成功，且数组长度是否为3（年-月-日）
	if (parts.length !== 3) {
		return '参数错误';
	}

	// 使用slice(0, 2)获取年和月，然后使用'年'和'月'进行拼接
	return `${parts[0]}年${parts[1].padStart(2, '0')}月`;
}

export const dateISO = (inputDateTime) => {
	// 将输入日期时间字符串转换为Date对象
	const date = new Date(inputDateTime);
	// 获取日期部分
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	// 获取时间部分
	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');
	const milliseconds = String(date.getMilliseconds()).padStart(3, '0');
	// 构建ISO 8601格式的日期时间字符串
	const isoDateTime = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}Z`;
	return isoDateTime; // 输出：2023-05-11T14:24:00.000Z
};

export function getFirstMonthStartDate(endDate) {
	const end = new Date(endDate);
	// 克隆日期对象
	const date = new Date(end);
	// 往前推五个月
	date.setMonth(end.getMonth() - 5);
	// 设置为该月的第一天
	date.setDate(1);
	// 返回日期字符串
	return date.toISOString().split('T')[0];
}
// 历史会话序列化
export function ruleAllList(allList, lang = '中文') {
	let lt = [];
	for (const item of allList) {
		// 不存在
		let sName = '';
		// 1.item.name的值不等与当年的时间
		if (item.name.split('-')[0] !== dayjs().format('YYYY')) {
			sName = item.name;
		} else {
			const num = dayjs(dayjs().format('YYYY-MM-DD')).diff(item.name, 'day');
			if (num === 0) {
				sName = lang === '中文' ? '今天' : 'Today';
			} else if (num === 1) {
				sName = lang === '中文' ? '昨天' : 'Yesterday';
			} else if (num === 2) {
				sName = lang === '中文' ? '2天前' : '2 days ago';
			} else if (num === 3) {
				sName = lang === '中文' ? '3天前' : '3 days ago';
			} else if (num === 4) {
				sName = lang === '中文' ? '4天前' : '4 days ago';
			} else if (num === 5) {
				sName = lang === '中文' ? '5天前' : '5 days ago';
			} else if (num === 6) {
				sName = lang === '中文' ? '6天前' : '6 days ago';
			} else if (num >= 7 && num < 14) {
				sName = lang === '中文' ? '上周' : 'last week';
			} else if (num >= 14 && num < 21) {
				sName = lang === '中文' ? '2周前' : '2 weeks ago';
			} else if (num >= 21 && num < 30) {
				sName = lang === '中文' ? '3周前' : '3 weeks ago';
			} else if (num >= 30 && num < 60) {
				sName = lang === '中文' ? '上个月' : 'last month';
			} else if (num >= 60 && num < 90) {
				sName = lang === '中文' ? '2月前' : '2 months ago';
			} else if (num >= 90 && num < 120) {
				sName = lang === '中文' ? '3月前' : '3 months ago';
			} else if (num >= 120 && num < 150) {
				sName = lang === '中文' ? '4月前' : '4 months ago';
			} else if (num >= 150 && num < 180) {
				sName = lang === '中文' ? '5月前' : '5 months ago';
			} else if (num >= 180 && num < 210) {
				sName = lang === '中文' ? '6月前' : '6 months ago';
			} else if (num >= 210 && num < 240) {
				sName = lang === '中文' ? '7月前' : '7 months ago';
			} else if (num >= 240 && num < 270) {
				sName = lang === '中文' ? '8月前' : '8 months ago';
			} else if (num >= 270 && num < 300) {
				sName = lang === '中文' ? '9月前' : '9 months ago';
			} else if (num >= 300 && num < 330) {
				sName = lang === '中文' ? '10月前' : '10 months ago';
			} else if (num >= 330 && num < 360) {
				sName = lang === '中文' ? '11月前' : '11 months ago';
			} else {
				sName = lang === '中文' ? '12月前' : '12 months ago';
			}
		}
		lt.push({ ...item, sName });
	}
	// 数组lt根据sName的值做合并
	return mergeByCategory(lt);
}
const mergeByCategory = (items) => {
	return items.reduce((accumulator, currentItem) => {
		const existingItem = accumulator.find((item) => item.sName === currentItem.sName);
		if (existingItem) {
			// 如果已存在，则合并对象
			existingItem.children = existingItem.children.concat(currentItem.children);
		} else {
			// 如果不存在，则添加到结果数组
			accumulator.push({ ...currentItem });
		}
		return accumulator;
	}, []);
};
export function addOrUpdateQueryParameter(url, key, value) {
	let fullUrl;

	// Check if the URL has a domain (contains '://' or starts with '//')
	if (url.includes('://') || url.startsWith('//')) {
		fullUrl = url;
	} else {
		// Get current page's origin (protocol + domain)
		const currentOrigin = window.location.origin;
		// Ensure the URL starts with a slash if it doesn't
		fullUrl = url.startsWith('/') ? `${currentOrigin}${url}` : `${currentOrigin}/${url}`;
	}

	// Create a URL object
	let urlObj = new URL(fullUrl);

	// Set or update the specified query parameter
	urlObj.searchParams.set(key, value);

	// Return the updated URL string
	return urlObj.toString();
}

export function formatNumberY(sale) {
	// 将字符串转换为数字
	const saleNumber = parseInt(sale, 10);

	// 检查转换是否成功
	if (isNaN(saleNumber)) {
		throw new Error('Invalid sale number');
	}

	// 将数字除以 1 亿（10^8），并保留两位小数
	const saleInBillions = (saleNumber / 1e8).toFixed(2);

	// 返回带单位的字符串
	return `${saleInBillions}亿`;
}

export function saleTrendFilterY(item) {
	if (item.seriesName === '月目标' || item.seriesName === '月实际') {
		return formatNumberY(item.value);
	}
	if (item.seriesName === '月达成' || item.seriesName === '月同比') {
		return new Decimal(item.value).toDecimalPlaces(0) + '%';
	}
}
export function formatNumbeW(number) {
	if (!number || number === 0 || number === '0') return 0;
	const isNegative = number < 0;
	const absoluteValue = Math.abs(number);
	if (absoluteValue < 1000) {
		const formatted = new Decimal(absoluteValue).toDecimalPlaces(2).toNumber().toLocaleString();
		return isNegative ? `-${formatted}` : formatted;
	} else {
		const formatted = `${new Decimal(absoluteValue).dividedBy(1000).toDecimalPlaces(2).toNumber().toLocaleString()}K`;
		return isNegative ? `-${formatted}` : formatted;
	}
}

export function formatNumbeWT(number) {
	if (!number || number === 0 || number === '0') return 0;
	const isNegative = number < 0;
	const absoluteValue = Math.abs(number);
	let formatted = `${new Decimal(absoluteValue).dividedBy(1000).toDecimalPlaces(1).toNumber().toLocaleString()}`;
	formatted = formatted.indexOf('.') < 0 ? `${formatted}.0` : formatted;
	return isNegative ? `-${formatted}` : formatted;
}
function generateMonths(start, end) {
	const result = [];
	const startYear = Math.floor(start / 100); // 起始年份
	const startMonth = start % 100; // 起始月份
	const endYear = Math.floor(end / 100); // 结束年份
	const endMonth = end % 100; // 结束月份

	for (let year = startYear; year <= endYear; year++) {
		const monthStart = year === startYear ? startMonth : 1;
		const monthEnd = year === endYear ? endMonth : 12;

		for (let month = monthStart; month <= monthEnd; month++) {
			const formattedMonth = month < 10 ? `0${month}` : month; // 补零
			result.push(`${year}${formattedMonth}`);
		}
	}
	return result;
}
//vxetable筛选
export const sortRule = ({ row, column }) => {
	// console.log(column.property);
	let y = generateMonths(202301, 202412);
	// 定义表格字段
	const tableFields = ['territoryCodeCn', 'salesY', 'targetY', 'achY', 'growthY', 'salesQ', 'targetQ', 'achQ', 'growthQ', 'salesM', 'targetM', 'achM', 'growthM', 'growthAch', 'momAch'];
	if (
		[
			'salesV',
			'targetV',
			'yearOnYearGrowth',
			'moMGrowth',
			'units',
			'unitsTarget',
			'unitsYearOnYearGrowth',
			'unitsMoMGrowth',
			'salesValue',
			'Total',
			...y,
			...tableFields,
			'shipments',
			'pure_sales',
			'warehouse_stock',
			'total_theoretical_inventory_v',
			'shipment_amount',
			'customer_count',
			'month_id',
		].includes(column.property)
	) {
		let value;
		if (typeof row[column.property] === 'string' && row[column.property].includes(',')) {
			value = row[column.property].replace(/,/g, '');
		} else {
			value = row[column.property];
		}
		if (typeof value === 'string' && value?.includes('W')) {
			return value.slice(0, -1) * 10000;
		} else {
			return value;
		}
	} else {
		//处理剩余参数都是带%结尾的，去掉% return
		return row[column.property]?.slice(0, -1);
	}
};
export function isPCTrue() {
	const userAgent = navigator.userAgent.toLowerCase();
	const isMobile = /android|iphone|ipad|ipod|windows phone|blackberry|opera mini|mobile/i.test(userAgent);
	return !isMobile; // 如果不是移动设备，认为是 PC
}

// 获取一个树形结构的数组是否有某一个属性的值
export function findElementsByPropertyValue(arr, property, value) {
	let result = [];

	function searchNode(node) {
		if (node[property] === value && node.enable) {
			result.push(node);
		}

		if (node.children && Array.isArray(node.children)) {
			node.children.forEach((child) => searchNode(child));
		}
	}

	arr.forEach((node) => searchNode(node));
	return result;
}

// 获取一个树形结构的数组是否有某一个属性的值
export function findChildrenById(arr, id) {
	for (let item of arr) {
		if (item.path === id) {
			return item.children || []; // 返回找到的children，若没有则返回空数组
		}
		if (item.children.length > 0) {
			const result = findChildrenById(item.children, id);
			if (result.length > 0) {
				return result; // 如果在子节点中找到了，返回结果
			}
		}
	}
	return []; // 如果没有找到，返回空数组
}

// 获取一个树形结构的数组是否有某一个属性的值，并返回其本身
export function findById(data, id) {
	for (const item of data) {
		// 如果找到匹配的 id，返回该项
		if (item.h === id) {
			return item;
		}
		// 如果有 children，则递归调用
		if (item.children) {
			const found = findById(item.children, id);
			if (found) {
				return found;
			}
		}
	}
	// 如果没有找到，返回 null
	return null;
}
export function findById1(data, id) {
	for (const item of data) {
		// 如果找到匹配的 id，返回该项
		if (item.territory_code === id) {
			return item;
		}
		// 如果有 children，则递归调用
		if (item.children) {
			const found = findById1(item.children, id);
			if (found) {
				return found;
			}
		}
	}
	// 如果没有找到，返回 null
	return null;
}
// 校验树形结构的数组是否有可用的元素
export function hasEnabled(treeList) {
	return treeList.some((item) => {
		if (item.enable === true) {
			return true;
		}
		// 如果有子节点，则递归检查子节点
		if (item.children && Array.isArray(item.children)) {
			return hasEnabled(item.children);
		}
		return false;
	});
}

function isSafari() {
	return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
}

function isIOS() {
	return /iPhone|iPad|iPod/i.test(navigator.userAgent);
}
// 是否是 Safari 浏览器
export function isSafariBrowser() {
	return (isSafari() || isIOS()) && !navigator.userAgent.includes('CriOS');
}
export function filterByLevel(data, levelThreshold) {
	const result = [];
	// 遍历 data 数组
	for (const item of data) {
		// 创建新节点
		const newItem = { ...item }; // 浅拷贝原始项
		// 如果有子节点，递归处理
		if (item.children && Array.isArray(item.children)) {
			newItem.children = filterByLevel(item.children, levelThreshold);
		}
		// 判断是否保留当前节点
		// 如果 level < threshold 或有符合条件的子节点，则保留
		if (item.level < levelThreshold || (newItem.children && newItem.children.length > 0)) {
			result.push(newItem);
		}
	}
	return result;
}

export function formatPercentage(val, maxThreshold = 20000, minThreshold = 0.01) {
	console.log(val);

	if (val > maxThreshold) return `>${maxThreshold}%`;
	if (val < -maxThreshold) return `<${-maxThreshold}%`;
	if (val > 0 && val < minThreshold) return `<${minThreshold}%`;
	if (val < 0 && val > -minThreshold) return `>${-minThreshold}%`;
	return `${val}%`;
}

/**
 * 遮罩层不能滚动
 */
function mo() {
	// e.preventDefault();
}
export function overflowYhiddern() {
	document.body.style.overflowY = 'hidden';
	document.getElementsByTagName('body')[0].style['touch-action'] = 'none';
	document.addEventListener('touchmove', mo(event), false);
}
export function overflowYauto() {
	document.body.style.overflowY = 'visible';
	document.getElementsByTagName('body')[0].style['touch-action'] = '';
	document.removeEventListener('touchmove', mo(event), false);
}

/**
 * 获取元素距离浏览器工作区顶端的距离
 */
export function scrollTop(id) {
	const hTop = document.getElementById(id).getBoundingClientRect().top;
	return hTop;
}
//立即执行版防抖
export function debounceSenior(func, wait) {
	let timeout;
	return function (...args) {
		if (timeout) clearTimeout(timeout);
		const callNow = !timeout;
		timeout = setTimeout(() => {
			timeout = null;
		}, wait);
		if (callNow) func.apply(this, args);
	};
}

/**
 * @description: 文件日期后缀格式
 * @return {*}  例如 20220109
 */
export function fileDate() {
	const d = new Date();
	const year = d.getFullYear();
	const mon = d.getMonth() + 1;
	const day = d.getDate();
	const t = year.toString() + (mon < 10 ? '0' + mon : mon) + (day < 10 ? '0' + day : day);
	return t;
}
/**
 * 日期格式处理 yyyy-MM-dd
 */
export function GMTToStr(time) {
	const date = new Date(time);
	const Str = date.getFullYear();
	let month = date.getMonth() + 1;
	let strDate = date.getDate();
	if (month >= 1 && month <= 9) {
		month = '0' + month;
	}
	if (strDate >= 0 && strDate <= 9) {
		strDate = '0' + strDate;
	}
	return Str + '-' + month + '-' + strDate;
}

/**
 * 获取n年前的时间  传入格式yyyy-MM-dd  num几年前
 */
export function dateBefore(time, num) {
	const arr = time.split('-');
	return Number(arr[0]) - num + '-' + arr[1] + '-' + arr[2];
}

/**
 * 获取n个月前的时间  传入格式yyyy-MM-dd  num 0
 */
export function dateMonthBefore(time, val) {
	const arr = time.split('-');
	const num = val;
	if (num === 1) {
		// 返回当月开始到今天的日期
		return Number(arr[0]) + '-' + arr[1] + '-' + '01';
	} else {
		const T = Number(arr[1]) - num;
		if (T <= 0) {
			if ((13 + T).toString().length > 1) {
				return Number(arr[0]) - 1 + '-' + (13 + T) + '-' + arr[2];
			} else {
				return Number(arr[0]) - 1 + '-0' + (13 + T) + '-' + arr[2];
			}
		} else {
			if (T.toString().length > 1) {
				return Number(arr[0]) + '-' + T + '-' + arr[2];
			} else {
				return Number(arr[0]) + '-0' + T + '-' + arr[2];
			}
		}
	}
}
export function findNodeByPathName(tree, path) {
	// 处理空输入
	if (!tree || !Array.isArray(tree) || !path || typeof path !== 'string') {
		return null;
	}
	// 使用递归查找节点
	function searchNode(nodes) {
		for (const node of nodes) {
			if (node.path === path) {
				return node; // 返回节点本身
			}
			if (node.children && node.children.length > 0) {
				const result = searchNode(node.children);
				if (result) return result;
			}
		}
		return null;
	}
	// 从根节点开始搜索
	return searchNode(tree);
}
// 获取当前月的第一天和最后一天（修复时区问题）
export function getCurrentMonthRange() {
	const now = new Date();
	// 当月第一天
	const start = new Date(now.getFullYear(), now.getMonth(), 1);
	const startLocalDate = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-${String(start.getDate()).padStart(2, '0')}`;
	// 当月最后一天
	const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
	const endLocalDate = `${end.getFullYear()}-${String(end.getMonth() + 1).padStart(2, '0')}-${String(end.getDate()).padStart(2, '0')}`;

	return { startLocalDate, endLocalDate };
}
export function findItemInTree(tree, code) {
	for (const item of tree) {
		if (item.code === code) {
			return item;
		}
		if (item.children && Array.isArray(item.children)) {
			const found = findItemInTree(item.children, code);
			if (found) {
				return found;
			}
		}
	}
	return null;
}
