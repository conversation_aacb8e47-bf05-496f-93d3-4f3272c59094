// utils/debugTool.js
import Vconsole from 'vconsole/dist/vconsole.min.js';

class DebugTool {
	constructor() {
		this.clickCount = 0;
		this.clickTimeout = null;
		this.debugArea = null; // 存储 debugArea 元素
		this.init();
	}

	loadVConsole() {
		new Vconsole();
		console.log('vConsole 已加载！');
	}

	createDebugArea() {
		// 创建点击区域
		this.debugArea = document.createElement('div');
		this.debugArea.id = 'debug-click-area';
		this.debugArea.style.position = 'fixed';
		this.debugArea.style.top = '0';
		this.debugArea.style.left = '70%';
		this.debugArea.style.width = '30vw';
		this.debugArea.style.height = '50px';
		this.debugArea.style.zIndex = '9999';
		this.debugArea.style.backgroundColor = 'transparent';
		this.debugArea.style.pointerEvents = 'none'; // 不干扰后面的元素
		document.body.appendChild(this.debugArea);
	}

	handleClick(e) {
		// console.log('点击事件触发，点击次数：', this.clickCount, '事件来源：', e.target);
		if (!this.debugArea) return;
		const rect = this.debugArea.getBoundingClientRect();
		const isInsideDebugArea = e.clientX >= rect.left && e.clientX <= rect.right && e.clientY >= rect.top && e.clientY <= rect.bottom;

		if (isInsideDebugArea) {
			this.clickCount++;

			// 清除之前的计时器
			if (this.clickTimeout) {
				clearTimeout(this.clickTimeout);
			}

			// 判断是否连续点击 5 次
			if (this.clickCount === 4) {
				this.loadVConsole();
				this.clickCount = 0; // 重置点击次数
			} else {
				// 如果 1 秒内没有达到 5 次点击，重置计数
				this.clickTimeout = setTimeout(() => {
					this.clickCount = 0;
					console.log('没到 5 次');
				}, 1000);
			}
		}
	}

	init() {
		// 创建点击区域
		this.createDebugArea();

		// 绑定全局点击事件
		document.addEventListener('click', this.handleClick.bind(this));
	}

	// 可选：提供销毁方法清理资源
	destroy() {
		if (this.debugArea) {
			this.debugArea.remove();
			this.debugArea = null;
		}
		if (this.clickTimeout) {
			clearTimeout(this.clickTimeout);
			this.clickTimeout = null;
		}
		document.removeEventListener('click', this.handleClick.bind(this));
		this.clickCount = 0;
	}
}

// 导出 DebugTool 类
export default DebugTool;
