export default {
	route: {
		chat: 'Chat',
		find: 'Discover',
		focus: 'Focus',
		standardReport: 'Hospital',
		cdReport: 'C&D',
		kyReport: 'KY',
		manageReport: 'Management',
		myReport: 'MyReport',
		reportDetail: 'ReportDetail',
		hasten: 'Hasten',
		customer: 'Customer',
	},
	common: {
		login: 'Login',
		logout: 'Logout',
		chat: 'Chat',
		observe: 'Insight',
		visit: 'Call',
		customer: 'Customer',
		report: 'Report',
		mine: 'My',
		notice: 'Please ensure compliance with the data usage security regulations statement, keep company trade secrets confidential, and protect data privacy and security when using.',
		querying: 'Querying',
		gdprfy: 'Generating daily performance reports for you',
		search: 'Search',
		nodata: 'No Data',
		pageNotFound: 'Page Not Found~~~',
		logoutSuccess: 'Your login has expired, please log in again~~~',
		goHome: 'Back Home',
		survey: 'Survey',
	},
	login: {
		username: 'Please enter username',
		password: 'Please enter password',
		loading: 'Logging in...',
		toast: 'Please enter username and password',
	},
	report: {
		all: 'ALL',
	},
	observe: {
		recommend: 'Recommend',
		saleas: 'Sales',
		access: 'Listing',
		market: 'Market',
		visit: 'Engage',
		ddi: 'DDI',
		charts: 'Charts',
		rank: 'Rank',
		overview: 'Overview',
		meeting: 'Meeting',
	},
	chat: {
		myAssistant: 'My Assistant',
		myAssistantDesc: 'Your 7 * 24-hour personal assistant',
		businessInsights: 'Business Insights',
		businessInsightsDesc: 'In depth analysis of core business indicators to support data-driven optimization decision-making',
		salesCustomerInsight: 'Smart Sales Engagement',
		salesCustomerInsightDesc: 'Sales Engagement Assistant and Recommendation by AI',
		medicalCustomerInsight: 'Smart Medical Engagement',
		medicalCustomerInsightDesc: 'Medical Engagement Assistant and Recommendation by AI',
		customerInsight1: 'Smart Engagement 2.0',
		customerInsight1Desc: 'AI assistant Engagement Recommendations and agents by AI',
		scriptAssistant: 'Sales Insights',
		scriptAssistantDesc: 'Instant business insights, efficient and accurate real-time decision-making',
		salesInsightNurturing: 'Sales Insight Cultivation',
		salesInsightNurturingDesc: 'Continuously adjust and optimize problems and KPIs to cultivate smarter sales insights',
		policyInsights: 'Company Policy Inquiry',
		policyInsightsDesc: 'Personnel regulations, reimbursement process, compliance policies, etc',
		medicineAgent: 'Insight into medical insurance and admission policies',
		medicineAgentDesc: 'Medical insurance policies, bidding admission, and hospital admission status are all under control',
		medicineAgentNurturing: 'Insight cultivation on medical insurance and admission policies',
		medicineAgentNurturingDesc: 'Continuously adjusting and optimizing policy knowledge, cultivating smarter policy insights',
		insurancePayment: 'Insight into Medical Insurance Payment Policies',
		insurancePaymentDesc: 'DRG/DIP Payment Reform Policy Assistant',
		productKnowledgQuery: 'Product and Medical Knowledge Inquiry',
		productKnowledgQueryDesc: 'Basic information, professional support, promotion strategies, etc',
		medicalKnowledgeMaintained: 'Product and Medical Knowledge Query Training',
		medicalKnowledgeMaintainedDesc: 'Continuously adjust and optimize common problems, cultivate smarter knowledge insights',
		medicalIntelligenceAccompanyingPractice: 'Medical intelligent training companion',
		medicalIntelligenceAccompanyingPracticeDesc: 'Your exclusive medical knowledge learning partner',
		salesForecast: 'Intelligent sales forecasting',
		salesForecastDesc: 'All the sales data you are concerned about are here',
		dkActivity: 'Insight from experts',
		dkActivityDesc: "Pay attention to KOL celebrities' trends, academic opinions, diagnosis and treatment tendencies, and learn from experts",
		intelligenceAccompanyingPractice: 'Quick Study, AI Q&A',
		intelligenceAccompanyingPracticeDesc: 'Good helper for career advancement',
		cloundAssistant: 'Intelligent Visiting Script',
		cloundAssistantDesc: 'Visiting techniques, article promotion techniques, etc',
		customerVisitSuggestions: 'Customer Visit Suggestions',
		maintenanceOfMedicalKnowledge: 'Maintenance of Medical Knowledge',
		ybzrwh: 'Medical insurance admission policies',
		all: 'All',
		market: 'Market',
		saleas: 'Saleas',
		medicine: 'Medicine',
		procure: 'Procure',
		finance: 'Finance',
		visitMateriaDisplayAndSharing: 'Visit material display and sharing',
		visitMateriaDisplayAndSharingDesc: 'Meetings, industry trends, regulations, and information sharing',
		khbfxxsj: 'Customer visit information collection',
		khbfxxsjDesc: 'Customer visit information collection',
		medicalVisitRecords: 'Medical visit records',
		salesVisitRecords: 'Sales visit records',
		medicalVisitRecordsDesc: 'Record key information anytime, anywhere',
		cjbfjh: 'Create a visit plan',
	},
	visit: {
		visit: 'Visit',
		log: 'Log',
		visitColl: 'Collaborative Visits',
		visitLog: 'Call Report',
		createLog: 'Create Report',
		createVisit: 'Create Call Report',
		editVisit: 'Edit Report',
		noVisit: 'No Call Report',
		doctorName: 'Doctor Name',
		medicalInsights: 'MedicalNotes',
		medicalInquiry: 'MedicalInquiry',
		notes: 'Notes',
	},
	manage: {
		selectLanguage: 'Select Language',
		setting: 'Setting',
		myCustomer: 'My Customer',
		myMetrics: 'My Metrics',
		planTask: 'PlanTask',
		workSummary: 'Summary',
		quickly: 'Quickly',
		metrics: 'Metrics',
		reach: 'Achievement',
		aboutAgentBox: 'About',
		languages: 'Languages',
		feedBack: 'Feedback',
		privacyPolicy: 'Privacy Policy',
		department: 'Dept',
		nickName: 'NickName',
	},
	summary: {
		workSummary: 'Work Summary',
		mySummary: 'My Summary',
		teamSummary: 'Team Summary',
		dailySummary: 'Daily Summary',
		weeklySummary: 'Weekly Summary',
		monthlySummary: 'Monthly Summary',
		noDaylySummary: 'No Daily Summary~',
		noWeeklySummary: 'No Weekly Summary~',
		noMonthlySummary: 'No Monthly Summary~',
		failSummary: 'Get Summary Failed~',
	},
	task: {
		create: 'Add',
		closeTask: 'Close Task',
		forward: 'Forward',
		excute: 'Execute',
		noMore: 'No More~',
		all: 'All',
		todo: 'To Do',
		completed: 'Completed',
		expired: 'Expired',
	},
	history: {
		noMore: 'No Data',
	},
	survey: {
		noSurvey: 'No Survey',
		createLog: 'Create Survey',
		surveyCreate: 'Create Survey',
	},
};
