import router from './router';
import { getToken, getReToken } from '@/utils/auth';
import watermark from '@/utils/watermark.js';
import useUserStore from '@/store/modules/user';
import useWxMenu from '@/store/modules/wxOptionMenu';
import usefilterStore from '@/store/modules/filter';
import usePremissionStore from '@/store/modules/premission';
// import clock from '@/utils/keyClock';
import registerMatomo from '@/utils/matomo';
import EnterInfo from '@/utils/enterpriseInfo';

// 配置免登录白名单
const whiteList = ['/authview', '/register', '/401', '/login', '/noExist', '/test', '/logout'];

let App = null;
export function getApp(app) {
	App = app;
}
let { start, done } = useNProgress();
let userStore;
let filterStore;
let useMenuStore;
let premissionStore;
router.beforeEach((to, from, next) => {
	userStore = useUserStore();
	filterStore = usefilterStore();
	premissionStore = usePremissionStore();
	start();
	EnterInfo().then(async () => {
		if (whiteList.includes(to.path)) {
			//在白名单
			await isApp();
			next();
		} else {
			if (getToken() || getReToken()) {
				await isApp();
				const isBool = await isUserInfo();
				if (isBool) {
					registerMatomo(App, router);
					if (window.parent === window) {
						const name = userStore.userInfo.username.indexOf('@') !== -1 ? userStore.userInfo.username.split('@')[0] : userStore.userInfo.username;
						watermark.set(`${name}-${userStore.userInfo.firstName}`);
					}
					userStore.getUserIsExist();
					getFilterInfo();
					next({ ...to, replace: true });
				} else {
					next();
				}
			} else {
				// 去除keycloak登录逻辑
				//  await clock(App);
				await isApp();
				next({ path: '/logout', replace: true });
			}
		}
	});
});
router.afterEach((to) => {
	// useTitle(to.meta.title);
	useMenuStore = useWxMenu();
	useMenuStore.SET_WX_MENU(to);
	done();
});

// vue 挂载 dom
function isApp() {
	return new Promise((resolve) => {
		if (App && App._container) {
			resolve();
		} else {
			setTimeout(() => {
				App.mount('#app');
				resolve();
			}, 500);
		}
	});
}

//获取用户信息并跳转
async function isUserInfo() {
	if (Object.keys(userStore.userInfo).length === 0) {
		//获取用户信息
		await userStore.GET_USERINFO();
		// 获取用户组织架构信息
		await filterStore.GET_ORGINFO();
		// 获取有权限的菜单
		await premissionStore.GET_PERMISSION();
		// 添加有权限的菜单
		premissionStore.addRouters.forEach((ele) => {
			router.addRoute(ele);
		});
		return true;
	}
	return false;
}

function getFilterInfo() {
	const promises = [];

	if (Object.keys(filterStore.skuInfo).length === 0) {
		promises.push(filterStore.GET_SKUINFO());
	}
	if (Object.keys(filterStore.skuInfoTree).length === 0) {
		promises.push(filterStore.GET_SKUINFOTREE());
	}
	if (Object.keys(filterStore.productInfo).length === 0) {
		promises.push(filterStore.GET_FILTERINFO());
	}
	if (Object.keys(filterStore.provinceList).length === 0) {
		promises.push(filterStore.GET_PROVINCE());
	}
	if (Object.keys(filterStore.msList).length === 0) {
		promises.push(filterStore.GET_MARKETSHARE());
	}
	if (Object.keys(filterStore.hospitalList).length === 0) {
		promises.push(filterStore.GET_HOSPITAL());
	}
	//cube
	if (Object.keys(filterStore.cubeOrgTreeInfo).length === 0) {
		// promises.push(filterStore.GET_ORG_FILTER());
	}
	if (Object.keys(filterStore.cubeSkuTreeInfo).length === 0) {
		// promises.push(filterStore.GET_SKU_FILTER());
	}
	if (Object.keys(filterStore.cubeProvinceCityTreeInfo).length === 0) {
		// promises.push(filterStore.GET_PC_FILTER());
	}
	// 等待所有异步操作完成
	Promise.all(promises);
}
