<template>
	<van-popup teleport="body" :safe-area-inset-bottom="true" class="q_custom-popup" :close-on-click-overlay="true" position="bottom" :style="{ height: '75%' }" v-model:show="show">
		<!-- header -->
		<div class="c-header" id="ap-c-header">
			<!-- title -->
			<span class="c-header__title" @click="reset">重置</span>
			<!-- 确认 -->
			<span @click="confirm" class="c-header__confirm">确认</span>
		</div>

		<div class="list" id="ap-list">
			<van-tabs v-model:active="active" shrink @click-tab="tabChange">
				<van-tab title="省份">
					<div class="search-content"><van-search v-model="searchValue" placeholder="搜索" /></div>
					<div class="data">
						<template v-for="item in provinceData" :key="item.name">
							<van-cell is-link @click.self="selectProvince(item)" v-if="!searchValue || item.name.includes(searchValue)">
								<van-checkbox shape="square" v-model:model-value="item.checked" @change="changeProvince(item)" icon-size="16" :name="item" :indeterminate="item.indeterminate">
									{{ item.name }}
								</van-checkbox>
							</van-cell>
						</template>
					</div>
				</van-tab>
				<van-tab :title="activeProvince.name || '请选择'" :disabled="!activeProvince.name">
					<div class="search-content"><van-search v-model="searchValue" placeholder="搜索" /></div>
					<div class="data">
						<template v-for="item in activeProvince.child" :key="item.name">
							<van-cell v-if="!searchValue || item.name.includes(searchValue)">
								<van-checkbox shape="square" @change="changeCity(item)" v-model:model-value="item.checked" icon-size="16" :name="item">
									{{ item.name }}
								</van-checkbox>
							</van-cell>
						</template>
					</div>
				</van-tab>
			</van-tabs>
		</div>
	</van-popup>
</template>
<script setup>
import { getProvinceTree } from '@/api/filter';
const emits = defineEmits(['_confirm', 'defaultValue']);
const props = defineProps(['defaultValue']);
const searchValue = ref('');
const active = ref(0);
const tabChange = () => {
	if (active.value === 0) {
		activeProvince.value = {};
	}
	searchValue.value = '';
};
const activeProvince = ref({});
const provinceData = ref([]);
const selectProvince = (item) => {
	activeProvince.value = item;
	nextTick(() => {
		active.value = 1;
	});
};
const changeProvince = (item) => {
	console.log(item);
	item.indeterminate = false;
	item.child.forEach((item1) => {
		item1.checked = item.checked;
	});
};
const changeCity = (item) => {
	const notAll = activeProvince.value.child.some((item1) => item1.checked !== item.checked);
	console.log(notAll, item.checked, item.child);
	activeProvince.value.checked = !notAll && item.checked;
	activeProvince.value.indeterminate = notAll;
};

const show = ref(false);
const reset = () => {
	provinceData.value.forEach((item) => {
		item.checked = false;
		item.indeterminate = false;
		item.child.forEach((item1) => {
			item1.checked = false;
		});
	});
};
const golbalInit = (data) => {
	reset();
	provinceData.value = data;
	//设置默认值
	if (props.defaultValue && Object.keys(props.defaultValue).length > 0) {
		initChecked(provinceData.value);
	}
};
const confirm = () => {
	let isAll = true;
	// const countyCode = [];
	const province = [];
	const city = [];
	let county = [];
	provinceData.value.forEach((item) => {
		console.log(item, 11);
		if (!item.checked) {
			isAll = false;
		}
		if (item.checked || item.indeterminate) {
			// if (item.checked) {
			province.push(item.name);
			// }
			item.child.forEach((item1) => {
				if (item1.checked) {
					city.push(item1.name);
					county = county.concat(item1.child);
				}
			});
		}
	});
	if (isAll) {
		emits('_confirm', { province: [], city: [], county: [], provinceData: provinceData.value });
	} else {
		emits('_confirm', { province, city, county, provinceData: provinceData.value });
	}
	show.value = false;
};

// 初始化勾选状态
function initChecked(treeData) {
	const { province, city } = props.defaultValue;
	console.log(province, city);

	treeData.forEach((prov) => {
		// 勾选默认省份
		prov.checked = province?.includes(prov.name);
		// 勾选默认城市
		prov.child.forEach((ct) => {
			ct.checked = city?.includes(ct.name);
		});
		// 计算省份的 checked 和 indeterminate
		updateProvinceState(prov);
	});
}

// 更新省份的 checked 和 indeterminate 状态
function updateProvinceState(province) {
	const allCities = province.child.length;
	const checkedCities = province.child.filter((c) => c.checked).length;

	if (checkedCities === 0) {
		province.checked = false;
		province.indeterminate = false;
	} else if (checkedCities === allCities) {
		province.checked = true;
		province.indeterminate = false;
	} else {
		province.checked = false;
		province.indeterminate = true;
	}
}
onMounted(() => {
	getProvinceTree().then((res) => {
		provinceData.value = res.result;
		//设置默认值
		if (props.defaultValue && Object.keys(props.defaultValue).length > 0) {
			initChecked(provinceData.value);
		}
	});
});
defineExpose({ show, golbalInit });
</script>
<style lang="scss" scoped>
@import '../../style/pc/pickerFilter/cityFilter.scss';
.c-header {
	padding: 20px 20px 6px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.c-header__title {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-tabbar-active);
	}

	.c-header__confirm {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-tabbar-active);
	}
}

.list {
	height: calc(100% - 50px);
	padding: 0 15px;
}

.search-content {
	padding: 8px 0px 0;
	flex: 1;

	&:deep(.van-search) {
		padding: 0;
		border-radius: 3px;
		overflow: hidden;
	}

	&:deep(.van-search__field) {
		height: 36px;
	}

	&:deep(.van-field__left-icon) {
		color: var(--pv-no-active-color);
	}

	&:deep(.van-field__control::-webkit-input-placeholder) {
		color: var(--pv-no-active-color);
	}
}

:deep(.van-tabs) {
	height: 100%;

	.van-tabs__wrap {
		border-bottom: 1px solid var(--pv-filter-bgc);
	}

	.van-tabs__content {
		height: calc(100% - 44px);
	}

	.van-tab__panel {
		height: 100%;
	}
}

.data {
	height: calc(100% - 60px);
	overflow-y: auto;

	:deep(.van-cell) {
		justify-content: space-between;

		.van-cell {
			&__value {
				flex: 0 1 auto;
			}
		}
	}
}
</style>
