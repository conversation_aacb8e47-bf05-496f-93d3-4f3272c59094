<template>
	<van-popup :lazy-render="false" teleport="body" :safe-area-inset-bottom="true" class="q_custom-popup" :close-on-click-overlay="true" position="bottom" :style="{ height: '75%' }" v-model:show="show">
		<!-- header -->
		<div class="c-header">
			<!-- title -->
			<span class="c-header__confirm" @click="reset">重置</span>
			<!-- 确认 -->
			<span @click="confirm" class="c-header__confirm">确认</span>
		</div>

		<div class="list">
			<VTreeSearch
				ref="vtree"
				v-model="checked"
				:data="data"
				checkable
				keyField="territory_code"
				titleField="employee_name"
				:showCheckAll="false"
				:showCheckedButton="false"
				:showFooter="false"
				searchPlaceholder="搜索"
				:nodeMinHeight="(53 * clientWitdh) / 375"
				:cascade="false"
				@check="checkedChange"
				@uncheck="resetChecked"
			>
				<template #search-input>
					<!-- 搜索 -->
					<div class="search-content"><van-search @update:model-value="searchEv" @clear="resetSearch" v-model="searchValue" placeholder="搜索" /></div>
				</template>
			</VTreeSearch>
		</div>
	</van-popup>
</template>
<script setup>
import { VTreeSearch } from '@wsfe/vue-tree';
const { proxy } = getCurrentInstance();
import { deepClone } from '@/utils/index.js';
const emits = defineEmits(['_confirm']);
const props = defineProps(['listData', 'path', 'defaultCheck']);
const clientWitdh = document.documentElement.clientWidth;
const vtree = ref(null);
const checked = ref([]);
const data = ref([]);
// 创建映射表
let nodeMap;
// 创建一个映射表，将 data.value 树的节点按 h 字段映射
const createNodeMap = (tree) => {
	const map = new Map();
	const buildMap = (nodes) => {
		nodes.forEach((node) => {
			map.set(node.territory_code, node);
			if (node.children && node.children.length > 0) {
				buildMap(node.children);
			}
		});
	};
	buildMap(tree);
	return map;
};
const handleData = (list) => {
	for (let item of list) {
		if (item.children) {
			handleData(item.children);
		}
		item.disabled = false;
	}
};
watch(
	() => props.listData,
	(n) => {
		handleData(n);
		console.log(n);
		data.value = deepClone(n);
		nodeMap = createNodeMap(data.value);
	}
);

const show = ref(false);
const searchValue = ref('');
// 搜索事件
const searchEv = (value) => {
	proxy.$umeng('搜索', `${props.path}-人员`, value);
	vtree.value.search(value);
};

// 重置搜索
const resetSearch = (num) => {
	vtree.value.search();
};
const reset = () => {
	searchValue.value = '';
	vtree.value.search();
	handleReset();
};
const findItemById = (tree, id) => {
	// 遍历树的每个节点
	for (const node of tree) {
		if (node.territory_code === id) {
			// 找到匹配的节点
			return node;
		}
		if (node.children && node.children.length > 0) {
			// 如果有子节点，递归查找
			const found = findItemById(node.children, id);
			if (found) {
				return found; // 找到后返回
			}
		}
	}
	return null; // 未找到返回 null
};
const checkedChange = (n) => {
	console.log(n);
	// 如果 n 有 children，则递归禁用所有后代节点
	const disableDescendants = (node) => {
		if (node?.children && node.children.length > 0) {
			node.children.forEach((child) => {
				// 禁用子节点
				vtree.value.updateNode(child.territory_code, { disabled: true, checked: false });
				// 递归处理子节点的子节点
				disableDescendants(child);
			});
		}
	};
	// 调用递归函数禁用所有后代节点（不包括当前节点）
	disableDescendants(n);
};
const resetChecked = (n) => {
	console.log(n);

	// 如果 n 有 children，则递归禁用所有后代节点
	const enableDescendants = (node) => {
		if (node?.children && node.children.length > 0) {
			node.children.forEach((child) => {
				// 使用 Map 来查找节点
				const item = nodeMap.get(child.territory_code);
				if (item) {
					vtree.value.updateNode(child.territory_code, { disabled: item.disabled, checked: false });
				}
				// 递归处理子节点的子节点
				enableDescendants(child);
			});
		}
	};

	// 调用递归函数禁用所有后代节点（不包括当前节点）
	enableDescendants(n);
};
watch(show, (n) => {
	if (n) {
		if (!props.defaultCheck) return;
		nextTick(() => {
			if (Array.isArray(props.defaultCheck)) {
				checked.value = props.defaultCheck;
				checked.value.forEach((ele) => {
					let node = vtree.value.getNode(ele);
					checkedChange(node);
				});
			} else {
				checked.value = [props.defaultCheck];
				let node = vtree.value.getNode(props.defaultCheck);
				checkedChange(node);
			}
			// 1. 展开默认选中的节点, 批量方法 setExpandKeys 不生效，需要逐个展开
			checked.value.forEach((ele) => {
				vtree.value.setExpand(ele, true);
			});
			// 2. 默认选中的第一个节点出现在可视范围内
			nextTick(() => {
				vtree.value.scrollTo(checked.value[0], 'center');
			});
		});
	}
});
const handleDefault = (n) => {
	nextTick(() => {
		if (Array.isArray(props.defaultCheck)) {
			checked.value = props.defaultCheck;
			checked.value.forEach((ele) => {
				let node = vtree.value.getNode(ele);
				checkedChange(node);
			});
		} else {
			checked.value = [props.defaultCheck];
			let node = vtree.value.getNode(props.defaultCheck);
			checkedChange(node);
		}
	});
};
const handleReset = () => {
	handleData(props.listData);
	data.value = deepClone(props.listData);
	nodeMap = createNodeMap(data.value);
	checked.value = [];
	vtree.value.clearChecked();
};
const confirm = () => {
	if (checked.value.length === vtree.value.getNodesCount()) {
		emits('_confirm', { userId: [], userName: [] });
	} else {
		const acName = [];
		const acId = [];
		const flatData = vtree.value.getFlatData();
		const checkedSet = new Set(checked.value);
		flatData.forEach((item) => {
			if (checkedSet.has(item.territory_code)) {
				acName.push(item.employee_name);
				acId.push(item.territory_code);
			}
		});
		console.log({ userId: acId, userName: acName });
		emits('_confirm', { userId: acId, userName: acName });
	}
	show.value = false;
};
const getFlatData = () => {
	return vtree.value.getFlatData();
};
defineExpose({ show, handleDefault, handleReset, confirm, getFlatData });
</script>
<style lang="scss" scoped>
.c-header {
	padding: 20px 20px 6px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.c-header__title {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-default-color);
	}

	.c-header__confirm {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-tabbar-active);
	}
}
.list {
	height: calc(100% - 50px);
	padding: 0 15px;
}
::v-deep(.vtree-tree-search__wrapper) {
	.vtree-tree-node__indent-wrapper {
		border-bottom: 1px solid #dfe1e6;
	}
	.vtree-tree-search__input-wrapper {
		padding-left: 0;
	}
	.vtree-tree-search__action-wrapper {
		display: none;
	}
	.vtree-tree-node__expand i {
		&:after {
			border: none;
			width: 8px;
			height: 10px;
			background-image: url('../../assets/img/expand.png');
			background-size: 100% 100%;
		}
	}
	.vtree-tree-node__title {
		font-size: 16px;
		color: #172b4d;
		margin-left: 16px;
		padding-left: 0;
	}
	.vtree-tree-node__square {
		width: 15px;
		height: 15px;
		.vtree-tree-node__checkbox {
			margin-left: 5px;
			width: 100%;
			height: 100%;
			border-radius: 2.5px;
			border: 1px solid #dfe1e6;
			background-color: initial;
		}
		.vtree-tree-node__checkbox_checked,
		.vtree-tree-node__checkbox_indeterminate {
			border-color: #0052cc;
			background-color: #0052cc;
		}
		.vtree-tree-node__checkbox_indeterminate:after {
			width: 9px;
			top: 50%;
			transform: translateY(-50%);
		}
		.vtree-tree-node__checkbox_checked:after {
			width: 5px;
			height: 10px;
		}
	}
	.vtree-tree-node__checkbox_disabled {
		border-color: #dcdee2 !important;
		background-color: #e8eaec !important;
	}
	.vtree-tree-node__title_disabled {
		color: #c5c8ce !important;
	}
}

.search-content {
	padding: 8px 0px 0;
	flex: 1;
	&:deep(.van-search) {
		padding: 0;
		border-radius: 3px;
		overflow: hidden;
	}
	&:deep(.van-search__field) {
		height: 36px;
	}
	&:deep(.van-field__left-icon) {
		color: var(--pv-no-active-color);
	}
	&:deep(.van-field__control::-webkit-input-placeholder) {
		color: var(--pv-no-active-color);
	}
}

/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	.c-header {
		padding: 20px 20px 6px !important;

		.c-header__title {
			font-size: 16px !important;
		}

		.c-header__confirm {
			font-size: 16px !important;
		}
	}
	.list {
		height: calc(100% - 50px) !important;
		padding: 0 15px !important;
	}
	::v-deep(.vtree-tree-search__search) {
		height: 42px !important;
	}
	::v-deep(.vtree-tree-search__wrapper) {
		.vtree-tree-node__indent-wrapper {
			border-bottom: 1px solid #dfe1e6 !important;
			min-height: 53px !important;
		}
		.vtree-tree-node__expand i {
			&:after {
				width: 8px !important;
				height: 10px !important;
			}
		}
		.vtree-tree-node__title {
			font-size: 16px !important;
			margin-left: 16px !important;
			height: 26px !important;
			line-height: 26px !important;
		}
		.vtree-tree-node__square {
			width: 15px !important;
			height: 15px !important;
			.vtree-tree-node__checkbox {
				margin-left: 5px !important;
				border-radius: 2.5px !important;
				border: 1px solid #dfe1e6 !important;
			}
			.vtree-tree-node__checkbox_indeterminate:after {
				width: 9px !important;
				height: 1px !important;
				left: 2px !important;
			}
			.vtree-tree-node__checkbox_checked:after {
				width: 5px !important;
				height: 10px !important;
				top: 0px !important;
				left: 4px !important;
			}

			.vtree-tree-node__checkbox:after {
				border-width: 1px !important;
			}
		}
	}

	.search-content {
		padding: 8px 0px 0 !important;
		&:deep(.van-search) {
			border-radius: 3px !important;
		}
		&:deep(.van-search__field) {
			height: 36px !important;
		}
	}
}
</style>
