<template>
	<div class="q-box">
		<div v-for="filter in filters" :key="filter.name" @click="handleClick(filter.name)" :class="['q-fileter-item', `q-width-${filter.size}`, { disabled: loading[filter.name] || filter.disabled }]">
			<span class="item-title ell">
				<span v-if="filter.name === 'time'">
					<span v-if="filter.single" class="item-title ell">{{ query.startDate }}</span>
					<span v-else>{{ query.startDate + ' - ' + query.endDate }}</span>
				</span>
				<span v-else-if="filter.name === 'monthName'">
					<span class="item-title ell">{{ query.monthName }}</span>
				</span>
				<span v-else-if="query[filter.queryName].includes('全部')">
					{{ filter.name === 'hospital' && query.hospitalName === '全部医院' ? '全部终端' : query[filter.queryName] }}
				</span>
				<span v-else-if="filter.name === 'market'">{{ query[filter.queryName] }}</span>
				<span v-else>
					<span>{{ query[filter.queryName].slice(0, 3) }}</span>
					<span>...</span>
					<span style="color: #0052cc">+{{ query[filter.queryName].split(',').length }}</span>
				</span>
			</span>
			<img v-if="loading[filter.name]" class="load" src="@/assets/img/filter-loading.png" alt="loading" />
			<svg-icon v-else class="icon" icon-class="down2"></svg-icon>
		</div>
	</div>
</template>
<script setup>
// 定义 props
const props = defineProps({
	filters: {
		type: Array,
		required: true,
		validator: (filters) => filters.every((f) => f.name && f.size && typeof f.name === 'string' && typeof f.size === 'string'),
	},
	query: {
		type: Object,
		required: true,
	},
	loading: {
		type: Object,
		default: () => ({}),
	},
});

// 定义 emits
const emit = defineEmits(['openTime', 'openOrg', 'openBrand', 'openCity', 'openHospital', 'openMarket']);
// 处理点击事件
const handleClick = (name) => {
	const eventMap = {
		time: 'openTime',
		org: 'openOrg',
		brand: 'openBrand',
		city: 'openCity',
		hospital: 'openHospital',
		market: 'openMarket',
		monthName: 'openMonthName',
	};
	const event = eventMap[name];
	if (event && !props.loading[name]) {
		emit(event);
	}
};
</script>
<style lang="scss" scoped>
.load {
	width: 10px;
	animation: spin 1s linear infinite;
}
/* 定义旋转动画 */
@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
/* 禁用状态样式 */
.disabled {
	// opacity: 0.5; /* 置灰效果 */
	pointer-events: none; /* 禁用点击 */
	cursor: not-allowed; /* 鼠标指针显示不可点击 */
	background-color: #dfe1e6;
	color: #6b778c;
}
</style>
