<template>
	<div id="ap-step-progress" class="step-progress">
		<van-popup v-model:show="progressShow" :close-on-click-overlay="false" class="popup-container">
			<div class="steps">
				<div class="title">正在生成PPT , 请勿关闭当前页面</div>
				<div class="imgs">
					<img src="@/assets/img/progress.gif" alt="" />
				</div>
				<div class="step-list" ref="list">
					<div class="step-item" v-for="(item, index) in steps" :key="item" :class="{ 'step-active': currentStep === index, 'step-completed': index < currentStep, 'step-no-active': index > currentStep }">
						<span v-if="currentStep >= index" class="step-bullet step-item-pc">
							<svg-icon icon-class="check"></svg-icon>
						</span>
						<span v-else class="step-item-pc">
							<svg-icon icon-class="loading" class="loading-icon"></svg-icon>
						</span>
						<span class="step-title">
							{{ item }}
						</span>
					</div>
				</div>
			</div>
		</van-popup>
	</div>
</template>

<script setup>
import { showSuccessToast } from 'vant';
import { ref, watch } from 'vue';
const props = defineProps({
	topThreeStepsTimes: {
		type: Number,
		default: 2000,
	},
	exportPPTComplate: {
		type: Boolean,
		default: false,
	},
});
const steps = ref(['获取数据', '数据准备', '数据处理', '图表生成', '拼装PPT', '马上就好']);
const currentStep = ref(0);
const progressShow = ref(false);
const list = ref(null);
let interval = null;
let interval2 = null;
let y = 0;
watch(progressShow, (n) => {
	if (n) {
		nextTick(() => {
			initCom();
			//控制前三步时间
			interval = setInterval(() => {
				if (currentStep.value > 1) {
					clearInterval(interval);
				} else {
					currentStep.value++;
					stepMove();
				}
			}, props.topThreeStepsTimes);
		});
	}
});
watch(
	() => props.exportPPTComplate,
	(n) => {
		if (!n) return;
		//控制后几步时间
		if (interval) {
			clearInterval(interval);
		}
		interval2 = setInterval(() => {
			if (currentStep.value === 5) {
				showSuccessToast('已完成');
				progressShow.value = false;
				clearInterval(interval2);
			} else {
				currentStep.value++;
				stepMove();
			}
		}, 500);
	}
);
const stepMove = () => {
	let listHeight = list.value.scrollHeight;
	console.log(listHeight);
	let itemHeight = listHeight / 6;
	y += itemHeight;
	list.value.scrollTo({
		top: y,
		behavior: 'smooth',
	});
};
const initCom = () => {
	y = 0;
	clearInterval(interval);
	clearInterval(interval2);
	currentStep.value = 0;
	list.value.scrollTo({ top: 0 });
};
defineExpose({
	progressShow,
});
</script>

<style lang="scss" scoped>
@import '../../style/pc/report/exportProgress.scss';
/* 外层容器样式 */
.step-progress {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16px;
}
/* 弹窗样式 */
:deep(.van-popup) {
	background-color: white;
	padding: 20px;
	border-radius: 10px;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 步骤标题 */
.title {
	font-size: 15px;
	color: #000;
	// margin-bottom: 8px;
	text-align: center;
}
.imgs {
	text-align: center;
}
/* 步骤列表 */
.step-list {
	display: flex;
	flex-direction: column;
	height: 50px;
	overflow-y: auto;
}
// step-list滚动条样式
.step-list::-webkit-scrollbar {
	width: 0;
}

/* 每个步骤的容器 */
.step-item {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
	padding: 2px 0;
	/* 步骤文本 */
	.step-title {
		font-size: 14px;
		color: #000;
	}
}
/* 步骤状态样式 */
.step-active {
	color: #000;
	.step-title,
	.step-bullet {
		font-size: 16px !important;
		transition: all 0.3s linear;
	}
	.step-bullet {
		color: #10be00;
	}
}

.step-completed,
.step-no-active {
	.step-title {
		color: #dfdfdf !important;
	}
	.step-bullet {
		color: #ccc;
	}
}
@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.loading-icon {
	display: inline-block; /* 确保图标是一个块元素 */
	animation: spin 1s linear infinite; /* 定义动画时长、缓动方式和无限循环 */
}
</style>
