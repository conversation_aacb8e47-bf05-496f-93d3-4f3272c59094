<template>
	<div class="switch">
		<van-switch v-model="checked" size="15" @change="changeStatus">
			<template #background>
				<div class="text" :class="{ on: checked, close: !checked }">{{ type }}</div>
			</template>
		</van-switch>
	</div>
</template>
<script setup>
const checked = ref(true);
watch(checked, (n) => {
	type.value = n ? 'RMB' : 'USD';
});
const type = ref('RMB');
const emit = defineEmits(['currencySwitching']);
const changeStatus = () => {
	emit('currencySwitching', type.value);
};
</script>
<style lang="scss" scoped>
.switch {
	--van-switch-width: 53px;
	--van-switch-height: 22px;
	--van-switch-node-size: 17px;
	--van-switch-on-background: linear-gradient(134.62deg, #f1f1f1 -0.67%, #cfcfcf) !important;
	--van-switch-background: linear-gradient(134.62deg, #f1f1f1 -0.67%, #cfcfcf) !important;
	.van-switch {
		display: flex;
		align-items: center;
	}
	.van-switch {
		box-shadow: inset 0.8vw 0.8vw 1.333vw hsla(190, 7%, 52%, 0.701), inset -0.8vw -0.8vw 1.067vw rgba(221, 227, 229, 0.649);
	}
	.text {
		color: #666 !important;
		font-size: 11px;
		transition: all 0.3s;
		width: 100%;
	}
	.text.on {
		padding: 0 0 0 7px;
	}
	.text.close {
		padding: 0 0 0 24px;
	}
}
</style>
