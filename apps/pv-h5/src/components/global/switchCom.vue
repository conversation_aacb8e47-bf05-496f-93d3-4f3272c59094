<template>
	<div v-if="isShow" id="sw1" class="switch" :class="{ disabled: props.inChat }">
		<van-switch v-model="checked" size="15" @change="changeStatus">
			<template #background>
				<div class="text" :class="{ on: checked, close: !checked }">{{ type }}</div>
			</template>
		</van-switch>
	</div>
</template>
<script setup>
import useOptionStore from '@/store/modules/option';
import useEnterStore from '@/store/modules/enterprise';
const props = defineProps(['defaultValue', 'inChat']);
const optionStore = useOptionStore();
let checked = ref(true);
const type = ref('金额');
const emit = defineEmits(['currencySwitching']);
let isShow = ref(false);
//处理默认值
watch(
	() => props.defaultValue,
	(n) => {
		if (n) {
			isShow.value = n.label === 'true';
			checked.value = n.description === 'money' ? true : false;
			type.value = checked.value ? '金额' : '数量';
		}
	},
	{ immediate: true }
);
watch(checked, (n) => {
	type.value = n ? '金额' : '数量';
	emit('currencySwitching', type.value);
});
watch(
	() => optionStore.type,
	(n) => {
		console.log(n);
		if (n === '数量') return (checked.value = false);
		checked.value = true;
	}
);

defineExpose({ type, checked });
const changeStatus = () => {};
</script>
<style lang="scss" scoped>
.disabled {
	pointer-events: none;
}
.switch {
	--van-switch-width: 53px;
	--van-switch-height: 22px;
	--van-switch-node-size: 17px;
	--van-switch-on-background: linear-gradient(134.62deg, #f1f1f1 -0.67%, #cfcfcf) !important;
	--van-switch-background: linear-gradient(134.62deg, #f1f1f1 -0.67%, #cfcfcf) !important;
	.van-switch {
		display: flex;
		align-items: center;
	}
	.van-switch {
		box-shadow: inset 0.8vw 0.8vw 1.333vw hsla(190, 7%, 52%, 0.701), inset -0.8vw -0.8vw 1.067vw rgba(221, 227, 229, 0.649);
	}
	.text {
		color: #666 !important;
		font-size: 11px;
		transition: all 0.3s;
		width: 100%;
	}
	.text.on {
		padding: 0 0 0 7px;
	}
	.text.close {
		padding: 0 0 0 24px;
	}
}
/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#sw1 {
		--van-switch-width: 53px !important;
		--van-switch-height: 22px !important;
		--van-switch-node-size: 17px !important;
		--van-switch-on-background: linear-gradient(134.62deg, #f1f1f1 -0.67%, #cfcfcf) !important;
		--van-switch-background: linear-gradient(134.62deg, #f1f1f1 -0.67%, #cfcfcf) !important;
		.van-switch {
			display: flex;
			align-items: center;
		}
		.van-switch {
			width: 53px !important;
			height: 22px !important;
		}
		:deep(.van-switch) {
			box-shadow: inset 2.4px 2.4px 4px hsla(190, 7%, 52%, 0.701), inset -2.4px -2.4px 4px rgba(221, 227, 229, 0.649);
			.van-switch__node {
				width: var(--van-switch-node-size) !important;
				height: var(--van-switch-node-size) !important;
				left: 4px !important;
			}
		}
		.text {
			color: #666 !important;
			font-size: 11px;
			transition: all 0.3s;
			width: 100%;
		}
		.text.on {
			padding: 0 0 0 6px;
		}
		.text.close {
			padding: 0 0 0 25px;
		}
	}
}
</style>
