<template>
	<div v-if="!route.query.hiddenTarbar" class="tabBar">
		<div class="tab-item" v-for="(item, index) in tabList" :class="{ active: currentIndex === item.text, hidden: item.isHidden }" :key="index" @click="changeTab(item)">
			<div class="fa">
				<svg-icon v-if="currentIndex === item.text" class="icon" :class="{ activeIcon: currentIndex === item.text }" :icon-class="item.icon"></svg-icon>
				<svg-icon v-else class="icon" :icon-class="item.defaultIcon"></svg-icon>
				<span>{{ item.text }}</span>
			</div>
		</div>
	</div>
</template>
<script setup>
const { proxy } = getCurrentInstance();
const props = defineProps(['num']);
import { useI18n } from 'vue-i18n';
import routerList from '../../router';
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
import useUserStore from '@/store/modules/user';
import usePremissionStore from '@/store/modules/premission';
const userStore = useUserStore();
const perStore = usePremissionStore();
const userId = userStore.userInfo.username;
import { showToast } from 'vant';

watch(route, (n) => {
	let name = n.matched[0].name;
	let f = tabList.value.find((ele) => ele.route === name);
	currentIndex.value = f.text;
});
let tabList = ref([]);
let currentIndex = ref();
let changeTab = (item) => {
	if (item.route === 'random' && userId === 'rsm_roche') {
		return showToast('暂无权限');
	}
	if (item.route === 'report') {
		// 跳转到 report 下有权限的第一个彩蛋
		router.push({
			name: perStore.reportChildren,
		});
	} else {
		router.push({ name: item.route });
	}
	proxy.$umeng('点击', '底部页签', item.text);
};

watch(
	() => props.num,
	(n) => {
		tabList.value.find((ele) => ele.text === t('common.chat')).msgMum = n;
	}
);
const init = () => {
	const list = routerList.getRoutes();
	list.forEach((item) => {
		if (item.meta.isTabar) {
			tabList.value.push({
				text: t(item.meta.title),
				route: item.name,
				icon: item.meta.icon,
				defaultIcon: item.meta.icon + '-default',
				isHidden: isHiddenFn(item.name),
			});
		}
	});
	let name = route.matched[0].name;
	let f = tabList.value.find((ele) => ele.route === name);
	currentIndex.value = f.text;
};

// 菜单是否显示
const isHiddenFn = (name) => {
	const item = perStore.asyncRouters.find((item) => item.path === name);
	return item ? item.isHidden : false;
};
onMounted(() => {
	init();
});
</script>
<style lang="scss" scoped>
.tabBar {
	height: 63px;
	display: flex;
	justify-content: space-around;
	align-items: start;
	border: 1px solid #dfe1e6;
	position: fixed;
	z-index: 99;
	bottom: 0;
	width: 100vw;
	background-color: #fff;
	.tab-item {
		font-size: 9px;
		flex: 1;
		color: var(--pv-no-active-color);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		transform: translateY(6px);
		position: relative;
		right: 1px;
		.fa {
			width: 100%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}
		.icon {
			font-size: 20px;
			margin-bottom: 4px;
			color: #6b778c;
		}
		.activeIcon {
			color: #0052cc;
		}
		img {
			height: 50px;
			width: 50px;
			transform: translateY(-6px);
		}
	}
	.active {
		color: var(--pv-tabbar-active);
	}
	.hidden {
		display: none;
	}
}
</style>
