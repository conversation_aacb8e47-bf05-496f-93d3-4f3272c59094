<template>
	<div class="update-box">
		<span>报告刷新时间：{{ refreshTime }}</span
		>&emsp;
		<span>数据更新至：{{ updateTime }}</span>
	</div>
</template>

<script setup>
let dataTime = ref('');
let props = defineProps(['updateTime', 'isYMD', 'refreshTime']);
if (props.updateTime?.length > 9) {
	if (props.isYMD) {
		dataTime.value = props.updateTime.slice(0, 10);
	} else {
		dataTime.value = props.updateTime.slice(0, 7);
	}
}
</script>

<style lang="scss" scoped>
.update-box {
	padding-top: 5px;
	font-size: 10px;
	font-weight: 400;
	text-align: center;
	color: var(--pv-no-active-color);
}
@media screen and (min-width: 600px) {
	.update-box {
		font-size: 10px !important;
		padding-top: 5px !important;
	}
}
</style>
