<template>
	<div class="loading">
		<span v-if="str" class="msg">{{ str }}</span>
		<span class="loader"></span>
		<span v-if="str" class="block1"></span>
	</div>
</template>
<script setup>
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps({
	text: {
		type: String,
	},
});
let str = ref('');
let timer = null;
onMounted(() => {
	if (props.text) {
		str.value = props.text;
		return;
	}
	timer = setTimeout(() => {
		str.value = t('common.querying');
	}, 5000);
});
onUnmounted(() => {
	clearTimeout(timer);
	// timer = null;
});
</script>
<style lang="scss" scoped>
.loading {
	padding-left: 12px;
	padding-top: 3px;
	display: flex;
	align-items: center;
	color: #172b4d;
	.msg {
		margin-left: -12px;
		margin-right: 16px;
	}
	.block1 {
		width: 25px;
	}
}
.loader {
	display: block;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background-color: #6b778c;
	box-shadow: 12px 0 #6b778c, -12px 0 #6b778c;
	position: relative;
	animation: flash 1.2s linear infinite alternate;
}

@keyframes flash {
	0% {
		background-color: transparent;
		box-shadow: 12px 0 transparent, -12px 0 #6b778c;
	}
	50% {
		background-color: #6b778c;
		box-shadow: 12px 0 transparent, -12px 0 transparent;
	}
	100% {
		background-color: transparent;
		box-shadow: 12px 0 #6b778c, -12px 0 transparent;
	}
}

/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	.loading {
		padding-left: 12px;
		padding-top: 3px;
		height: auto !important;
		margin-top: 0 !important;
		.msg {
			margin-left: -12px;
			margin-right: 16px;
		}
		.block1 {
			width: 25px;
		}
	}
	.loader {
		width: 6px;
		height: 6px;
		box-shadow: 12px 0 #6b778c, -12px 0 #6b778c;
	}

	@keyframes flash {
		0% {
			box-shadow: 12px 0 transparent, -12px 0 #6b778c;
		}
		50% {
			box-shadow: 12px 0 transparent, -12px 0 transparent;
		}
		100% {
			box-shadow: 12px 0 #6b778c, -12px 0 transparent;
		}
	}
}
</style>
