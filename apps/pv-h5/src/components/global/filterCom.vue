<template>
	<div class="f-f">
		<div class="filter" ref="navListRef">
			<div v-if="title" class="filter-item filter-item-desc">{{ title }}</div>
			<div v-else class="filter-item" :class="{ active: item === currentIndex, activefontcolor: item === '不展示---' }" v-for="item in filterList" :key="item" @click="filterChange(item, $event)">
				{{ item === '按医院' ? '按终端' : item === '按终端新增' ? '新增终端' : item === '按终端覆盖' ? '终端覆盖' : item === '按销售' ? '销售' : item === '销售(K)' ? '销售(K)' : item === '按达成%' ? '达成(%)' : item === '按同比增长%' ? '同比(%)' : item }}
			</div>
			<!-- 下载 -->
			<div v-if="downloadExcel" style="flex: 1; display: flex; justify-content: flex-end">
				<img @click="downExcel" class="filter-img" src="@/assets/img/download.png" alt="" />
			</div>
			<van-dialog v-model:show="isNotEmail" @confirm="confirm" @cancel="cancel" confirm-button-color="var(--pv-tabbar-active)" title="请输入邮箱" show-cancel-button>
				<div class="enter-email">
					<van-field v-model="enterEmail" placeholder="请输入邮箱" />
				</div>
			</van-dialog>
		</div>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
const props = defineProps(['filterList', 'currentIndex', 'title', 'downloadExcel']);
const emit = defineEmits(['filterChange', '_downExcel']);
const emial = userStore.userInfo.email;
const isNotEmail = ref(false);
const enterEmail = ref('');
const navListRef = ref(null);
const filterChange = (item, e) => {
	if (item === '不展示---') return;
	// 获取目标元素的边界
	const targetRect = e.target.getBoundingClientRect();
	// 获取外层元素的边界
	const containerRect = navListRef.value.getBoundingClientRect();
	// 计算目标元素相对于外层元素的 left 和 right
	const relativeLeft = targetRect.left - containerRect.left;
	const relativeRight = relativeLeft + targetRect.width;
	// 外层容器的可见宽度
	const containerWidth = containerRect.width;
	// 判断是否超出外层容器边界
	if (relativeRight > containerWidth || relativeLeft < 0) {
		// 计算需要滚动的距离
		const scrollAdjustment =
			relativeLeft < 0
				? relativeLeft // 左边超出，向左滚动
				: relativeRight - containerWidth; // 右边超出，向右滚动
		navListRef.value.scrollTo({
			left: navListRef.value.scrollLeft + scrollAdjustment,
			behavior: 'smooth', // 平滑滚动
		});
	}
	emit('filterChange', item);
};

// 下载报表
const downExcel = () => {
	if (emial) {
		emit('_downExcel', emial);
	} else {
		isNotEmail.value = true;
	}
};

// 确认
const confirm = () => {
	const email = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
	if (email.test(enterEmail.value)) {
		emit('_downExcel', enterEmail.value);
		cancel();
	} else {
		showToast({
			message: '请输入正确的邮箱地址',
			position: 'top',
		});
	}
};
// 取消
const cancel = () => {
	isNotEmail.value = false;
	enterEmail.value = '';
};
</script>
<style lang="scss" scoped>
.f-f {
	padding: 0 14px;
}
.filter {
	display: flex;
	align-items: center;
	position: relative;
	font-size: 12px;
	overflow-x: auto;
	border-bottom: 1px solid var(--pv-filter-bgc);
	&-item {
		flex-shrink: 0;
		padding: 6px 10px 6px;
		color: var(--pv-no-active-color);
		transition: all 0.2s;
		&-desc {
			color: var(--pv-default-color);
		}
	}
	&-img {
		width: 18px;
		height: 18px;
		margin-right: 10px;
	}
	.active {
		color: var(--pv-default-color);
		font-weight: bold;
		position: relative;
		&::after {
			content: '';
			position: absolute;
			z-index: 1;
			background-color: var(--pv-tabbar-active);
			width: 30px;
			height: 2px;
			left: 50%;
			bottom: 0px;
			transform: translateX(-50%);
			border-radius: 2px;
		}
	}
	.activefontcolor {
		color: #ffffff !important;
	}
	&::-webkit-scrollbar {
		height: 0px;
	}
}
.enter-email {
	padding: 16px 12px;
	background-color: #ffffff;
	&:deep(.van-field) {
		background-color: #f7f8fa;
	}
}

/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	.f-f {
		padding: 0 14px !important;
	}
	.filter {
		font-size: 12px !important;
		border-bottom: 1px solid var(--pv-filter-bgc) !important;
		&::after {
			height: 1px !important;
			width: calc(100% - 20px) !important;
		}
		&-item {
			padding: 6px 10px 6px !important;
		}
		&-img {
			width: 18px !important;
			height: 18px !important;
			margin-right: 10px !important;
		}
		.active {
			&::after {
				width: 30px !important;
				height: 2px !important;
				border-radius: 2px !important;
			}
		}
	}
	.enter-email {
		padding: 16px 12px !important;
	}
}
</style>
