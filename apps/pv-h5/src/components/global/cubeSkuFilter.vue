<template>
	<van-popup :lazy-render="false" teleport="body" :safe-area-inset-bottom="true" class="q_custom-popup" :close-on-click-overlay="true" position="bottom" :style="{ height: '75%' }" v-model:show="show">
		<!-- header -->
		<div class="c-header" id="ap-c-header">
			<!-- title -->
			<span class="c-header__confirm" @click="reset">重置</span>
			<!-- 确认 -->
			<span @click="confirm" class="c-header__confirm">确认</span>
		</div>

		<div class="list" id="ap-list">
			<VTreeSearch ref="vtree" v-model="checked" :data="data" checkable keyField="product_code" titleField="product_cn" :showCheckAll="false" :showCheckedButton="false" :showFooter="false" searchPlaceholder="搜索" :nodeMinHeight="(53 * clientWitdh) / 375">
				<template #search-input>
					<!-- 搜索 -->
					<div class="search-content" id="ap-search-content"><van-search @update:model-value="searchEv" @clear="resetSearch" v-model="searchValue" placeholder="搜索" /></div>
				</template>
			</VTreeSearch>
		</div>
	</van-popup>
</template>
<script setup>
import { deepClone } from '@/utils';
import { VTreeSearch } from '@wsfe/vue-tree';
const { proxy } = getCurrentInstance();
const emits = defineEmits(['_confirm']);
const props = defineProps(['listData', 'path', 'level', 'defaultCheck']);
const clientWitdh = document.documentElement.clientWidth;
const vtree = ref(null);
const checked = ref([]);
const data = ref([]);
watch(
	() => props.listData,
	(n) => {
		console.log(n);
		data.value = filterTree(deepClone(n || []), props.level || 'sku_code');
	}
);
//树形数组根据level字段过滤之后的children 数组有个字段level
const filterTree = (arr, level) => {
	if (arr.length > 0) {
		arr.forEach((item) => {
			if (item.levelNumber === level) {
				delete item.children;
			} else {
				filterTree(item.children, level);
			}
		});
	}
	return arr;
};

const show = ref(false);
const searchValue = ref('');
// 搜索事件
const searchEv = (value) => {
	proxy.$umeng('搜索', `${props.path}-产品`, value);
	vtree.value.search(value);
};

// 重置搜索
const resetSearch = (num) => {
	vtree.value.search();
};
const reset = () => {
	searchValue.value = '';
	vtree.value.search();
	checked.value = [];
	vtree.value.clearChecked();
};
const confirm = () => {
	if (checked.value.length === vtree.value.getNodesCount()) {
		emits('_confirm', { skuCode: [], skuName: [], checked: checked.value });
	} else {
		const acName = [];
		const skuCode = [];
		const flatData = vtree.value.getFlatData();
		const checkedSet = new Set(checked.value);
		flatData.forEach((item) => {
			const level = props.level || 'sku_code';
			if (checkedSet.has(item.product_code)) {
				if (item.levelNumber === level) {
					acName.push(item.product_cn);
					skuCode.push(level === 'brand' ? item.skuCode : item.product_code);
				}
			}
		});
		emits('_confirm', { skuCode, skuName: acName, checked: checked.value });
	}
	show.value = false;
};
watch(show, (n) => {
	if (n) {
		if (!props.defaultCheck) return;
		handleDefault(n);
	}
});
const handleDefault = (n) => {
	nextTick(() => {
		const checkedSet = new Set(props.defaultCheck);
		const flatData = vtree.value.getFlatData();
		flatData.forEach((item) => {
			if (checkedSet.has(item.product_code)) {
				let node = vtree.value.getNode(item.product_code);
				checked.value.push(item.product_code);
				vtree.value.setChecked(item.product_code, true);
			}
		});
		console.log(checked.value);
	});
};
const handleReset = () => {
	checked.value = [];
	vtree.value.clearChecked();
};
const getFlatData = () => {
	return vtree.value.getFlatData();
};
defineExpose({ show, confirm, handleDefault, handleReset, getFlatData });
</script>
<style lang="scss" scoped>
@import '../../style/pc/pickerFilter/skuFilter.scss';
.c-header {
	padding: 20px 20px 6px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.c-header__title {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-default-color);
	}

	.c-header__confirm {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-tabbar-active);
	}
}
.list {
	height: calc(100% - 50px);
	padding: 0 15px;
}
::v-deep(.vtree-tree-search__wrapper) {
	.vtree-tree-node__indent-wrapper {
		border-bottom: 1px solid #dfe1e6;
	}
	.vtree-tree-search__input-wrapper {
		padding-left: 0;
	}
	.vtree-tree-search__action-wrapper {
		display: none;
	}
	.vtree-tree-node__expand i {
		&:after {
			border: none;
			width: 8px;
			height: 10px;
			background-image: url('../../assets/img/expand.png');
			background-size: 100% 100%;
		}
	}
	.vtree-tree-node__title {
		font-size: 16px;
		color: #172b4d;
		margin-left: 16px;
		padding-left: 0;
	}
	.vtree-tree-node__square {
		width: 15px;
		height: 15px;
		.vtree-tree-node__checkbox {
			margin-left: 5px;
			width: 100%;
			height: 100%;
			border-radius: 2.5px;
			border: 1px solid #dfe1e6;
			background-color: initial;
		}
		.vtree-tree-node__checkbox_checked,
		.vtree-tree-node__checkbox_indeterminate {
			border-color: #0052cc;
			background-color: #0052cc;
		}
		.vtree-tree-node__checkbox_indeterminate:after {
			width: 9px;
			top: 50%;
			transform: translateY(-50%);
		}
		.vtree-tree-node__checkbox_checked:after {
			width: 5px;
			height: 10px;
		}
	}
	.vtree-tree-node__checkbox_disabled {
		border-color: #dcdee2 !important;
		background-color: #e8eaec !important;
	}
	.vtree-tree-node__title_disabled {
		color: #c5c8ce !important;
	}
}

/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	::v-deep(.vtree-tree-search__search) {
		height: 42px !important;
	}
	::v-deep(.vtree-tree-search__wrapper) {
		.vtree-tree-node__indent-wrapper {
			border-bottom: 1px solid #dfe1e6 !important;
			min-height: 53px !important;
		}
		.vtree-tree-node__expand i {
			&:after {
				width: 8px !important;
				height: 10px !important;
			}
		}
		.vtree-tree-node__title {
			font-size: 16px !important;
			margin-left: 16px !important;
			height: 26px !important;
			line-height: 26px !important;
		}
		.vtree-tree-node__square {
			width: 15px !important;
			height: 15px !important;
			.vtree-tree-node__checkbox {
				margin-left: 5px !important;
				border-radius: 2.5px !important;
				border: 1px solid #dfe1e6 !important;
			}

			.vtree-tree-node__checkbox_indeterminate:after {
				width: 9px !important;
				height: 1px !important;
				left: 2px !important;
			}
			.vtree-tree-node__checkbox_checked:after {
				width: 5px !important;
				height: 10px !important;
				top: 0px !important;
				left: 4px !important;
			}

			.vtree-tree-node__checkbox:after {
				border-width: 1px !important;
			}
		}
	}
}

.search-content {
	padding: 8px 0px 0;
	flex: 1;
	&:deep(.van-search) {
		padding: 0;
		border-radius: 3px;
		overflow: hidden;
	}
	&:deep(.van-search__field) {
		height: 36px;
	}
	&:deep(.van-field__left-icon) {
		color: var(--pv-no-active-color);
	}
	&:deep(.van-field__control::-webkit-input-placeholder) {
		color: var(--pv-no-active-color);
	}
}
</style>
