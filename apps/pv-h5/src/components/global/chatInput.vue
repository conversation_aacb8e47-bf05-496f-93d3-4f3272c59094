<template>
	<div class="chat">
		<input class="input" v-model="value" placeholder="发消息" />
		<img src="@/assets/img/yy.png" alt="" />
		<img src="@/assets/img/fj.png" alt="" />
	</div>
</template>
<script setup>
let value = ref('');
</script>
<style lang="scss" scoped>
.chat {
	height: 49px;
	border: 1px solid #dfe1e6;
	position: fixed;
	z-index: 99;
	left: 0;
	bottom: 0;
	width: 100vw;
	background-color: #fff;
	display: flex;
	align-items: center;
	.input {
		flex: 1;
		border: none;
		padding-left: 28px;
	}
	.input::placeholder,
	textarea::placeholder {
		color: #6b778c;
		font-size: 14px;
	}
	img {
		width: 20px;
		margin-right: 15px;
	}
}
</style>
