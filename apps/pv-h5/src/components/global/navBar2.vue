<template>
	<div class="nav" id="ap-nav">
		<van-icon class="icon" name="arrow-left" @click="$router.go(-1)" />
		<span ref="navTitle" class="nav-title">
			<span @animationiteration="animationiteration"> {{ reportStore.reportName }}</span>
		</span>
		<van-icon class="edit" name="edit" @click="showEdit = true" />
		<van-dialog v-model:show="showEdit" confirmButtonText="确定" confirm-button-color="#0074f9" show-cancel-button @confirm="edit">
			<div class="rp-content">
				<div class="rp-content__title">修改报告名称</div>
				<div class="rp-content__textarea">
					<van-field v-model="fileName" placeholder="请输入报告名称" />
				</div>
			</div>
		</van-dialog>
	</div>
</template>
<script setup>
import { editMyPort } from '@/api/report';
import { showToast } from 'vant';
import useUserStore from '@/store/modules/report';
const { proxy } = getCurrentInstance();

let reportStore = useUserStore();
let route = useRoute();
let showEdit = ref(false);
let fileName = reportStore.reportName;
let uname = reportStore.reportName;
const edit = async () => {
	await editMyPort({ id: route.query.reportId, name: fileName, path: route.query.label, label: route.query.tag });
	showToast('修改成功');
	reportStore.setReportName(fileName);
	proxy.$umeng('点击', `汇报-我的报告-${uname}-修改报告名称`, fileName);
};
const navTitle = ref(null);
let scrollAnimationTime = () => {
	// 获取文本元素宽度
	const textWidth = navTitle.value.scrollWidth;
	// 获取容器宽度
	const containerWidth = navTitle.value.clientWidth;
	return (textWidth / containerWidth) * 8;
};
watch(
	() => reportStore.reportName,
	(n) => {
		nextTick(() => {
			navTitle.value.children[0].style.animation = 'none';
			// 获取文本元素宽度
			const textWidth = navTitle.value.scrollWidth;
			// 获取容器宽度
			const containerWidth = navTitle.value.clientWidth;
			console.log(textWidth, containerWidth);
			// 如果文本宽度超过容器宽度，则启用滚动效果
			if (textWidth > containerWidth) {
				navTitle.value.children[0].style.animation = `scroll ${scrollAnimationTime()}s linear infinite`;
			} else {
				navTitle.value.children[0].style.animation = 'none'; // 禁用动画
			}
		});
	},
	{ immediate: true }
);
const animationiteration = () => {
	// 处理动画结束事件的逻辑
	navTitle.value.children[0].style.animation = `scroll1 ${scrollAnimationTime()}s linear infinite`;
};
onMounted(() => {
	if (route.query.title) {
		reportStore.setReportName(route.query.title);
	}
	if (route.query.name) {
		reportStore.setReportName(route.query.name);
	}
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/report/reportNav.scss';
.nav {
	// height: var(--nav-height);
	padding-top: 13px;
	padding-bottom: 13px;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	font-size: 20px;
	font-weight: bold;
	.icon {
		position: absolute;
		left: 16px;
		top: 50%;
		transform: translateY(-50%);
		font-size: 20px;
	}
	&-title {
		display: inline-block;
		width: 260px;
		overflow: hidden;
		white-space: nowrap; /* 禁止文本换行 */
		text-align: center;
		span {
			display: inline-block;
		}
	}
	.edit {
		font-size: 16px;
	}
	::v-deep(.van-dialog__content) {
		display: flex;
		justify-content: center;
		padding: 5px 20px;
		.input-inner {
			border: 1px solid var(--pv-filter-bgc);
			width: 100%;
			color: #000;
			padding: 3px;
			line-height: initial;
		}
	}
	.rp-content {
		margin-top: 10px;
		display: flex;
		flex-direction: column;
		width: 96%;
		margin-left: 2%;
		.rp-content__title {
			position: relative;
			padding-left: 5px;
			color: #283c63;
			margin-bottom: 5px;
			width: 100%;
			font-weight: 500;
			font-size: 14px;
		}
		.rp-content__title::before {
			content: '';
			position: absolute;
			top: 10%;
			left: 0;
			width: 3px;
			height: 80%;
			background-color: var(--pv-tabbar-active);
		}
		.rp-content__textarea {
			margin: 5px 0px 10px;
			border: 1px solid var(--pv-filter-bgc);
			width: 100%;
			&:deep(.van-cell) {
				padding: 5px;
			}
		}
	}
}
</style>
