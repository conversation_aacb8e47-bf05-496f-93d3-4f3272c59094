<template>
	<div class="amd">
		<!-- header -->
		<div class="amd-header">
			<span>{{ props.title || '文章详情' }}</span>
			<!-- <el-icon @click="close" size="16"><Close /></el-icon> -->
		</div>

		<!-- 文章列表 -->
		<div class="amd-content">
			<iframe :src="url"></iframe>
		</div>
	</div>
</template>
<script setup>
const props = defineProps(['sourceUrl', 'title', 'isToken']);
const emits = defineEmits(['close']);
const url = ref('');
watch(
	() => props.sourceUrl,
	(val) => {
		if (val) {
			nextTick(() => {
				init();
			});
		}
	},
	{
		immediate: true,
	}
);
const init = () => {
	url.value = props.sourceUrl;
};
const close = () => {
	emits('close');
};
</script>
<style lang="scss" scoped>
.amd {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.amd-header {
		display: flex;
		align-items: center;
		padding: 8px 12px;
		justify-content: center;
		// margin-bottom: 12px;
		span {
			font-weight: 500;
			font-size: 16px;
		}
	}

	.amd-content {
		width: 100%;
		flex: 1;
		padding: 0 12px;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		iframe {
			width: 100%;
			height: 100%;
			border: none;
		}
	}
}
</style>
