<template>
	<div>
		<van-notice-bar class="ap-NoticeBar">
			<template #left-icon>
				<!-- <svg-icon class="icon" icon-class="lb"></svg-icon> -->
			</template>
			<!-- <span v-if="props.showTime"
				>数据更新至[<span class="icon">{{ formatted }}</span
				>]，请确保在使用时遵守数据使用安全规范声明，保守公司商业秘密，保护数据隐私安全。</span
			>
			<span v-else>请确保在使用时遵守数据使用安全规范声明，保守公司商业秘密，保护数据隐私安全。</span> -->
			<span class="ap-f12">{{ $t('common.notice') }}</span>
		</van-notice-bar>
	</div>
</template>
<script setup>
import { useNow, useDateFormat } from '@vueuse/core';
const formatted = useDateFormat(useNow(), 'YYYY/MM/DD');
let props = defineProps(['showTime']);
</script>
<style lang="scss" scoped>
::v-deep(.van-notice-bar) {
	height: 22px;
	height: 22px;
	background-color: transparent;
	color: rgba(23, 43, 77, 0.6);
	padding: 0 15px;
	.van-notice-bar__content {
		font-size: 10px;
	}
	.icon {
		color: var(--pv-tabbar-active);
	}
}
</style>
