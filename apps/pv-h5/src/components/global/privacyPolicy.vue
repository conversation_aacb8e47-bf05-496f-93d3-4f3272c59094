<template>
	<van-popup round :close-on-click-overlay="false" v-model:show="show" :style="{ width: '80vw', height: '65%', overflow: 'hidden' }" @closed="closed">
		<div class="privacy-policy">
			<!-- header -->
			<div class="privacy-policy__header">
				<span class="privacy-policy__header__title">使用条款</span>
				<span class="privacy-policy__header__sub">terms of use</span>
			</div>
			<!-- content -->
			<div id="privacy-policy__content" class="privacy-policy__content">
				<!-- 标题 -->
				<span class="privacy-policy__content__title">重要提示：</span>
				<!-- 内容 -->
				<div class="privacy-policy__content__text privacy-policy__content_mt6">
					此版本的Agentbox被设计用于处理C1至C3级别的数据，Agentbox已具有端到端加密功能以确保数据安全，请注意： 除员工姓名外，请勿在Agentbox中输入任何个人信息。（个人信息是以电子或者其他方式记录的与已识别或者可识别的自然人有关的各种信息，不包括匿名化处理后的信息用）。
				</div>
				<div class="privacy-policy__content__text privacy-policy__content_mt6">您输入的信息将保存90天，便于您查询历史聊天记录。</div>

				<!-- 标题 -->
				<span class="privacy-policy__content__title privacy-policy__content_mt12">AI使用：</span>
				<!-- 内容 -->
				<div class="privacy-policy__content__text privacy-policy__content_mt6">Agentbox基于 Azure AI模型构建，仅用于处理和分析企业内部业务数据，请勿使用Agentbox工具提问非业务场景问题。</div>
				<div class="privacy-policy__content__text privacy-policy__content_mt6">Agentbox使用您的数据进行分析解读，但可能会产生不准确的信息。因此，请注意核实和检查工具反馈的所有信息的准确性。</div>

				<!-- 标题 -->
				<span class="privacy-policy__content__title privacy-policy__content_mt12">保密承诺：</span>
				<!-- 内容 -->
				<div class="privacy-policy__content__text privacy-policy__content_mt6">
					您承诺并保证严格遵守公司的保密规定，包括但不限于您与公司的相关合同项下的保密义务、公司的保密信息、信息安全相关的政策和流程要求，以及法律法规所规定的保密义务。您完全知晓并理解，本系统中的所有内容（包括用户名和密码）含有公司高度保密的专有信息，仅限内部使用。任何有权限接触此类保密信息的员工均应予以严格保密，未经公司事先批准或授权，不得擅自使用、下载、存储、分享、传输、复制、分发或以其他任何方式或形式泄露给其他人员。如发现任何违规行为，公司有权依法按照内部政策流程规定予以违纪处理，和/或采取其它法律措施。
				</div>
			</div>
			<!-- bottom -->
			<van-button :disabled="msg !== '我知道了'" color="#0052cc" @click="prAgree" type="primary">{{ msg }}</van-button>
		</div>
	</van-popup>
</template>

<script setup>
const show = ref(false);
const msg = ref('请下滑浏览全文');
const init = () => {
	// 未同意
	if (localStorage.getItem('agentMobilePrivacyPolicy') !== 'true') {
		show.value = true;
		nextTick(() => {
			const pr = document.querySelector('#privacy-policy__content');
			pr.addEventListener('scroll', () => {
				// 检验滚动条是否到底部
				if (pr.scrollHeight - pr.scrollTop <= pr.clientHeight + 10) {
					msg.value = '我知道了';
				}
			});
		});
	}
};
// 同意
const prAgree = () => {
	localStorage.setItem('agentMobilePrivacyPolicy', 'true');
	show.value = false;
};

const closed = () => {
	const pr = document.querySelector('#privacy-policy__content');
	pr.removeEventListener('scroll', () => {});
};
const showPrivacyPolicy = () => {
	init();
};
defineExpose({
	showPrivacyPolicy,
});
</script>

<style lang="scss" scoped>
.privacy-policy {
	width: 100%;
	height: 100%;
	padding: 25px 25px 20px;
	background: url('@/assets/img/pr_bg.png') no-repeat;
	background-size: cover;
	display: flex;
	flex-direction: column;

	.privacy-policy__header {
		padding-bottom: 18px;
		border-bottom: 2px solid #d7e4ff;
		display: flex;
		flex-direction: column;

		.privacy-policy__header__title {
			font-size: 18px;
			color: #0052cc;
			font-weight: 600;
		}
		.privacy-policy__header__sub {
			font-size: 10px;
			color: #0052cc;
		}
	}

	.privacy-policy__content {
		flex: 1;
		overflow-y: auto;
		overscroll-behavior: none;
		padding: 12px 0;
		color: #172b4d;
		font-size: 12px;
		display: flex;
		flex-direction: column;
		.privacy-policy__content__title {
			font-size: #172b4d;
			font-weight: 600;
			font-size: 14px;
		}

		.privacy-policy__content__text {
			color: #6b778c;
			font-size: 12px;
			text-indent: 2em;
		}

		.privacy-policy__content_mt6 {
			margin-top: 6px;
		}
		.privacy-policy__content_mt12 {
			margin-top: 12px;
		}
	}
}
</style>
