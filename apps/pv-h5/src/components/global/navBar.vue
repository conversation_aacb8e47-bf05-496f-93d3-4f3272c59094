<template>
	<div
		id="ap-nav"
		class="nav ap-nav"
		:style="{
			borderBottom: $route.path.includes('reportDetail') || $route.path.includes('myReport') || $route.path.includes('intelligentAgent') || $route.path.includes('intelligent') ? '1px solid #DFE1E6' : '',
			paddingBottom: secondListMenu.includes($route.name) ? '0.8vw' : '',
			position: secondListMenu.includes($route.name) ? '' : 'sticky',
		}"
		v-if="!$route.meta.hiddenNav"
	>
		<div class="nav-list">
			<template v-for="(item, index) in navList">
				<div
					v-if="item.navShow"
					class="nav-item ap-f16"
					:class="{
						active: $route.meta.activeMenu ? $route.meta.activeMenu === item.name : $route.name === item.name,
						reportDetailActive: item.name === reportActivePath && $route.name === 'reportDetail',
					}"
					:key="index"
					@click="to(item)"
				>
					{{ item.text }}
				</div>
			</template>
		</div>
	</div>
</template>
<script setup>
import { debounce } from '@/utils/index';
const { proxy } = getCurrentInstance();
const proprs = defineProps(['navList', 'barColor']);
let router = useRouter();
let route = useRoute();
const reportActivePath = ref(route.query.path || '');
watch(route, (n) => {
	reportActivePath.value = n.query.path || '';
});

import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();

const secondListMenu = computed(() => {
	const list = ['discovery'];
	if (perStore.activityReportList && perStore.activityReportList.length > 0) {
		for (const ele of perStore.activityReportList) {
			list.push(ele.path);
		}
	}
	return list;
});
let to = debounce(
	(item) => {
		router.push({ name: item.name, query: route.query });
		proxy.$umeng('点击', `${item.father}`, item.text);
	},
	300,
	true
);
</script>
<style lang="scss" scoped>
@import '../../style/pc/report/navBar.scss';
.nav {
	// height: var(--nav-height);
	padding-top: 7px;
	padding-bottom: 7px;
	background-color: var(--pv-nav-bgc);
	// padding: 16px 15px; //蓝色背景  下方css为白色背景
	display: flex;
	align-items: center;
	justify-content: center;
	// position: sticky;
	z-index: 11;
	top: 0;
	&-top {
		display: flex;
		justify-content: space-between;
		.logo {
			img {
				width: 121px;
				height: 23px;
			}
		}
		.user {
			border-radius: 30px;
			background-color: rgba(93, 154, 234, 0.5);
			padding: 5px 12px;
			font-size: 13px;
			color: #fff;
			span:nth-child(1)::after {
				content: '';
				display: inline-block;
				width: 1px;
				height: 14px;
				background-color: #fff;
				margin: 0 5px;
				position: relative;
				top: 2px;
			}
		}
	}
	&-list {
		display: flex;
		justify-content: center;
		// margin-top: 27px; //蓝色背景  下方css为白色背景
	}
	&-item {
		font-size: 16px;
		margin: 0 10px;
		transition: all 0.3s;
		// color: rgba(255, 255, 255, 0.5);
		color: var(--pv-no-active-color);
	}
	.active,
	.reportDetailActive {
		// color: rgba(255, 255, 255, 0.9);
		color: var(--pv-default-color);
		font-weight: bold;
		position: relative;
	}
	// .active::after {
	// 	content: '';
	// 	position: absolute;
	// 	background-color: var(--pv-nav-bar-active);
	// 	width: 100%;
	// 	height: 2px;
	// 	border-radius: 2px;
	// 	left: 50%;
	// 	bottom: -9px;
	// 	transform: translateX(-50%);
	// }
}
</style>
