<template>
	<div class="nav">
		<div class="back" @click="$router.go(-1)"></div>
		<slot name="default"> </slot>
		<div class="right">
			<slot name="right"> </slot>
		</div>
	</div>
</template>
<script setup></script>
<style lang="scss" scoped>
.nav {
	height: 46px;
	line-height: 46px;
	position: fixed;
	z-index: 99;
	top: 0;
	left: 0;
	width: 100vw;
	text-align: center;
	color: #172b4d;
	font-weight: bold;
	font-size: 18px;
	border-bottom: 1px solid #dfe1e6;
	background-color: #fff;
	.back {
		position: absolute;
		top: 50%;
		left: 18px;
		width: 10px;
		height: 10px;
		border-top: 1.5px solid #172b4d;
		border-right: 1.5px solid #172b4d;
		transform: translateY(-50%) rotate(-135deg);
	}
	.right {
		position: absolute;
		top: 50%;
		right: 15px;
		transform: translateY(-50%);
	}
}
</style>
