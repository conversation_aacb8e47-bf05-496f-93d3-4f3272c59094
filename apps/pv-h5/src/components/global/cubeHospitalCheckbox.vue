<template>
	<van-popup :lazy-render="false" teleport="body" class="q_custom-popup" :close-on-click-overlay="true" :safe-area-inset-bottom="true" position="bottom" :style="{ height: '75%' }" v-model:show="show">
		<!-- header -->
		<div class="c-header">
			<!-- title -->
			<span class="c-header__confirm" @click="reset">重置</span>
			<!-- 确认 -->
			<span @click="confirm" class="c-header__confirm">确认</span>
		</div>
		<!-- 搜索框 -->
		<div class="c-search">
			<form action="/">
				<van-search @search="searchEv" @clear="resetSearch" v-model="state.keyword" placeholder="搜索"> </van-search>
			</form>
		</div>
		<!-- <template #action>
      <div @click="searchEv">搜索</div>
    </template> -->
		<!-- 数据列表 -->
		<div class="c-data">
			<van-checkbox class="c-data--allcheck" icon-size="16" checked-color="var(--pv-tabbar-active)" shape="square" v-model="isCheckAll" :indeterminate="isIndeterminate" @change="checkAllChange"> 全选 </van-checkbox>
			<van-list v-model:loading="state.isLoading" ref="vantList" :offset="30" :immediate-check="false" :finished="state.finished" finished-text="没有更多了" @load="onLoad">
				<van-checkbox-group v-model="checkedResult" @change="checkedResultChange">
					<van-checkbox checked-color="var(--pv-tabbar-active)" icon-size="16" shape="square" v-for="item in state.dataList" :key="item.id" :name="item"> {{ item.name }} </van-checkbox>
				</van-checkbox-group>
			</van-list>
		</div>
	</van-popup>
</template>
<script setup>
import { showToast } from 'vant';
import { getcubejsData } from '@/api/sales';
import { deepClone, getNamesByIdsInArray } from '@/utils/index';
import usefilterStore from '@/store/modules/filter';
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const props = defineProps(['path', 'defaultCheck', 'defaultHosCheckedAll', 'isGobalFilter']);

const dataTotal = filterStore.hospitalTotal;
const state = reactive({
	dataList: [], //数据
	isLoading: false, // 列表的loading
	finished: false, //结束
	total: 0, //总数
	page: 0,
	size: 50,
	keyword: '',
	offset: 0,
});

const show = ref(false);
const emits = defineEmits(['_confirm', 'gobalFilterSearchChange']);

// if (filterStore.hospitalList.length !== 0) {
// 	for (const item of filterStore.hospitalList) {
// 		state.dataList.push({ id: item.hospCode, name: item.hospName });
// 	}
// }

const isCheckAll = ref(false);
const isIndeterminate = ref(false);
const checkedResult = ref([]);

const checkedResultChange = (value) => {
	console.log(value, '===============');

	const checkedCount = value.length;
	isCheckAll.value = checkedCount === state.dataList.length;
	isIndeterminate.value = checkedCount > 0 && checkedCount < state.dataList.length;
};

const checkAllChange = (val) => {
	checkedResult.value = [];
	if (val) {
		state.dataList.forEach((ele) => {
			checkedResult.value.push(ele);
		});
	}
};

// 确认
const confirm = () => {
	let ids = checkedResult.value.map((ele) => ele.id);
	if (checkedResult.value.length > 0) {
		show.value = false;
		const names = checkedResult.value.map((ele) => ele.name);
		// 返回选中的选项id
		// 初次全选默认全部医院，其他情况走正常逻辑
		if (isCheckAll.value) {
			emits('_confirm', [], '全部医院', ids, isCheckAll.value, isIndeterminate.value);
		} else {
			emits('_confirm', checkedResult.value.length === dataTotal ? [] : ids, names.join(','), ids, isCheckAll.value, isIndeterminate.value);
		}
	} else {
		emits('_confirm', [], '全部医院', ids, isCheckAll.value, isIndeterminate.value);
		show.value = false;
	}
};

const handleDefault = (n) => {
	nextTick(() => {
		console.log(props.defaultCheck);
		state.keyword = n.hospitalSearch;
		state.dataList = deepClone(n.hospitalList);
		isCheckAll.value = props.defaultHosCheckedAll;
		const checkedSet = new Set(props.defaultCheck);
		state.dataList.forEach((item) => {
			if (checkedSet.has(item.id)) {
				checkedResult.value.push(item);
			}
		});
		isIndeterminate.value = props.defaultCheck.length > 0 && props.defaultCheck.length < state.dataList.length;
		console.log(checkedResult.value);
	});
};
const handleReset = () => {
	checkedResult.value = [];
	isCheckAll.value = false;
};
// 搜索
const searchEv = (val) => {
	if (val) {
		state.page = 0;
		state.offset = 0;
		state.dataList = [];
		getHospitalList();
		if (state.keyword.length > 0) {
			proxy.$umeng('搜索', `${props.path}-医院`, state.keyword);
		}
	} else {
		showToast({
			message: '请输入关键字进行搜索',
			position: 'top',
		});
	}
};
// 重制搜索
const resetSearch = async () => {
	state.keyword = '';
	state.page = 0;
	state.dataList = [];
	checkedResult.value = [];
	await getHospitalList();
	isCheckAll.value = false;
};
const reset = async () => {
	state.keyword = '';
	state.page = 0;
	state.offset = 0;
	state.dataList = [];
	checkedResult.value = [];
	await getHospitalList();
	isCheckAll.value = false;
};
let getHospitalList = async () => {
	try {
		let res = await getcubejsData({
			query: {
				dimensions: ['pv_ads_hospital_filter.hospital_code', 'pv_ads_hospital_filter.hospital_name'],
				limit: state.size,
				offset: state.offset,
				filters: [
					{
						values: [state.keyword],
						member: 'pv_ads_hospital_filter.hospital_name',
						operator: 'contains',
					},
				],
			},
		});
		const rows = res.data;
		state.isLoading = false;
		// state.total = res.result.totalElements;
		if (rows == null || rows.length === 0) {
			state.finished = true;
			return;
		}
		isIndeterminate.value = true;
		for (const item of rows) {
			state.dataList.push({ id: item['pv_ads_hospital_filter.hospital_code'], name: item['pv_ads_hospital_filter.hospital_name'] });
		}
		state.finished = false;
		// if (state.dataList.length >= state.total) {
		// 	state.finished = true;
		// }
		if (props.isGobalFilter) {
			emits('gobalFilterSearchChange', { searchValue: state.keyword, searchList: state.dataList });
		}
		return Promise.resolve();
	} catch (error) {
		state.isLoading = false;
		state.finished = true;
		return Promise.reject(error);
	}
};

const onLoad = () => {
	if (state.finished) return;
	state.page++;
	state.offset = state.page * state.size;
	state.isLoading = true;
	getHospitalList();
};

onMounted(() => {
	if (state.dataList.length === 0) {
		getHospitalList();
	}
});

defineExpose({
	show,
	handleReset,
	handleDefault,
	confirm,
});
</script>

<style lang="scss" scoped>
.c-header {
	padding: 20px 20px 16px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.c-header__title {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-default-color);
	}

	.c-header__confirm {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-tabbar-active);
	}
}

.c-search {
	padding: 0 20px;
	&:deep(.van-search) {
		padding: 0;
		border-radius: 3px;
		overflow: hidden;
	}
	&:deep(.van-search__field) {
		height: 36px;
	}
	&:deep(.van-field__left-icon) {
		color: var(--pv-no-active-color);
	}
	&:deep(.van-field__control::-webkit-input-placeholder) {
		color: var(--pv-no-active-color);
	}
}

.c-data {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	padding: 3.5px 15px;

	&:deep(.van-checkbox) {
		overflow: initial;
		padding-left: 7px;
		padding-right: 7px;
		padding-bottom: 14px;
		padding-top: 14px;
		border-bottom: solid 1px var(--pv-filter-bgc);
	}

	&:deep(.van-checkbox__label) {
		font-size: 16px;
		color: var(--pv-default-color);
		font-weight: 400;
		margin-left: 16px;
	}
	&:deep(.van-checkbox__icon--indeterminate .van-icon) {
		border-color: var(--pv-tabbar-active);
		background-color: var(--pv-tabbar-active);
	}
}

/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	.c-header {
		padding: 20px 20px 16px !important;

		.c-header__title {
			font-size: 16px !important;
		}

		.c-header__confirm {
			font-size: 16px !important;
		}
	}

	.c-search {
		padding: 0 20px !important;
		&:deep(.van-search) {
			border-radius: 3px !important;
		}
		&:deep(.van-search__field) {
			height: 36px !important;
		}
	}

	.c-data {
		padding: 3.5px 15px !important;

		&:deep(.van-checkbox) {
			padding-left: 7px !important;
			padding-right: 7px !important;
			padding-bottom: 14px !important;
			padding-top: 14px !important;
			border-bottom: solid 1px var(--pv-filter-bgc) !important;
			.van-checkbox__icon .van-icon {
				border-width: 1px !important;
			}
		}

		&:deep(.van-checkbox__label) {
			font-size: 16px !important;
			margin-left: 16px !important;
			line-height: 1.5 !important;
		}
	}
}
</style>
