<template>
	<van-popup teleport="body" :safe-area-inset-bottom="true" class="q_custom-popup" :close-on-click-overlay="true" position="bottom" :style="{ height: '55%' }" v-model:show="show">
		<!-- header -->
		<div class="c-header">
			<!-- title -->
			<span class="c-header__title">请选择</span>
			<!-- 确认 -->
			<span @click="confirm" class="c-header__confirm">确认</span>
		</div>
		<div class="list">
			<Tree :custom-options="myCustomOptions" :nodes="treeDisplayData"></Tree>
		</div>
	</van-popup>
</template>
<script setup>
import Tree from 'vuejs-tree';
const emits = defineEmits(['_confirm']);
const props = defineProps(['listData']);

const show = ref(false);

const findNode = (nodes, nodeId) => {
	for (let node of nodes) {
		if (node.id === nodeId) {
			return node;
		}
		if (node.nodes) {
			const found = findNode(node.nodes, nodeId);
			if (found) {
				return found;
			}
		}
	}
	return null;
};

const updateParentState = (nodes, node) => {
	if (!node) return;

	const parentNode = findParentNode(nodes, node);
	if (parentNode) {
		const allChecked = parentNode.nodes.every((n) => n.state.checked);
		const indeterminate = parentNode.nodes.some((n) => n.state.indeterminate) ? true : parentNode.nodes.some((n) => n.state.checked) && parentNode.nodes.filter((n) => n.state.checked).length < parentNode.nodes.length ? true : false;

		parentNode.state.checked = allChecked;
		parentNode.state.indeterminate = indeterminate;

		updateParentState(nodes, parentNode);
	}
};

const findParentNode = (nodes, targetNode) => {
	for (let node of nodes) {
		if (node.nodes && node.nodes.includes(targetNode)) {
			return node;
		}
		if (node.nodes) {
			const found = findParentNode(node.nodes, targetNode);
			if (found) {
				return found;
			}
		}
	}
	return null;
};

const myCheckedFunction = (nodeId, checked) => {
	const node = findNode(treeDisplayData.value, nodeId);
	if (node) {
		node.state.checked = checked;
		if (!node.state.checked || node.state.checked) {
			node.state.indeterminate = false;
			if (node.nodes) {
				node.nodes.forEach((ele) => {
					ele.state.indeterminate = false;
				});
			}
		}
		updateParentState(treeDisplayData.value, node);
	}

	function addOrMoveClass(list) {
		for (let item of list) {
			if (item.state.indeterminate) {
				const checkboxWithDataId = document.querySelector(`input[type="checkbox"][data-id="${item.id}"]`);
				checkboxWithDataId?.classList.add('indeterminate');
			} else {
				const checkboxWithDataId = document.querySelector(`input[type="checkbox"][data-id="${item.id}"]`);
				checkboxWithDataId?.classList.remove('indeterminate');
			}
			// if (!item.state.checked || item.state.checked) {
			// 	item.state.indeterminate = false;
			// }
			if (item.nodes) {
				addOrMoveClass(item.nodes);
			}
		}
	}
	addOrMoveClass(treeDisplayData.value);
};
const myCustomOptions = computed(() => {
	return {
		treeEvents: {
			selected: {
				state: true,
				// fn: this.mySelectedFunction,
			},
			checked: {
				state: true,
				fn: myCheckedFunction,
			},
		},
		events: {
			expanded: {
				state: true,
			},
			selected: {
				state: false,
			},
			checked: {
				state: true,
			},
		},
		addNode: {
			state: false,
		},
		editNode: { state: false },
		deleteNode: {
			state: false,
		},
		showTags: true,
	};
});

const treeDisplayData = ref([]);

//获得选中的
let findChecked = (list) => {
	let result = [];
	function findC(list) {
		for (let item of list) {
			if (item.state.checked) {
				result.push(item);
			}
			if (item.nodes) {
				findC(item.nodes);
			}
		}
	}
	findC(list);
	return result;
};
//获得半选的
let findIndeterminate = (list) => {
	let result = [];
	function findC(list) {
		for (let item of list) {
			if (item.state.indeterminate) {
				result.push(item);
			}
			if (item.nodes) {
				findC(item.nodes);
			}
		}
	}
	findC(list);
	return result;
};

const confirm = () => {
	let result1 = findChecked(treeDisplayData.value);
	let result2 = findIndeterminate(treeDisplayData.value);
	emits('_confirm', [...result1, ...result2]);
	show.value = false;
};
watch(
	() => props.listData,
	(n) => {
		treeDisplayData.value = n;
	}
);
defineExpose({ show });
</script>
<style lang="scss" scoped>
.c-header {
	padding: 20px 20px 6px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.c-header__title {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-default-color);
	}

	.c-header__confirm {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-tabbar-active);
	}
}
.list {
	padding: 0 20px;
	::v-deep(ul) {
		padding-left: 4px;
	}
	::v-deep(.node) {
		width: 100% !important;
		word-break: break-all;
		.row_data {
			display: flex;
			align-items: center;
			.expanded_icon {
				border-width: 6px 0 6px 8px;
				border-color: transparent transparent transparent #c0c4cc;
			}
			span:nth-child(1) {
				margin-right: 5px;
			}
			input[type='checkbox'] {
				width: 15px;
				height: 15px;
				margin-right: 8px;
				position: relative;
			}
			input[type='checkbox']::after {
				position: absolute;
				top: 0;
				background-color: #fff;
				color: #000;
				width: 15px;
				height: 15px;
				display: inline-block;
				visibility: visible;
				padding-left: 0px;
				text-align: center;
				content: ' ';
				box-sizing: border-box;
				border: 1px solid #dcdfe6;
			}
			.indeterminate[type='checkbox']::after {
				content: '一';
				font-size: 11px;
				font-weight: bold;
				color: #fff;
				border: 1px solid #409eff;
				background-color: #409eff;
			}
			input[type='checkbox']:checked::after {
				content: '✓';
				font-size: 12px;
				font-weight: bold;
				color: #fff;
				border: 1px solid #409eff;
				background-color: #409eff;
			}
			.capitalize {
				font-size: 15px;
			}
		}
	}
}
</style>
