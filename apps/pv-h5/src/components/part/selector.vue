<!-- eslint-disable vue/no-mutating-props -->
<template>
	<div class="selector">
		<van-popup v-model:show="showPicker" round position="bottom" :closeable="true">
			<van-picker ref="picker" :columns="columns" :visible-option-num="4" @cancel="showPicker = false" @confirm="onConfirm">
				<template #toolbar>
					<div>
						<span class="van-picker__toolbar-title"> {{ title }}</span>
					</div>
				</template>
				<template #columns-bottom>
					<div class="bottom">
						<div @click="showPicker = false">取消</div>
						<div @click="confirmTrue">确定</div>
					</div>
				</template>
			</van-picker>
		</van-popup>
	</div>
</template>
<script setup>
import { ref } from 'vue';
const props = defineProps(['title', 'columns', 'defaultValue', 'type']);
const emit = defineEmits(['selectConfirm']);
const picker = ref(null);
const showPicker = ref(false);
const confirmTrue = () => {
	showPicker.value = false;
	picker.value.confirm();
};
const onConfirm = (value) => {
	if (props.type) {
		emit('selectConfirm', value.selectedOptions[0]);
		return;
	}
	emit('selectConfirm', value.selectedValues[0]);
};
defineExpose({ showPicker });
</script>
<style lang="scss" scoped>
::v-deep(.van-overlay) {
	background-color: rgba(0, 0, 0, 0.3);
}
::v-deep(.van-popup) {
	border-radius: 5px;
	.van-popup__close-icon {
		font-size: 19px;
	}
	.van-picker {
		.van-picker__toolbar {
			height: initial;
			color: #353639;
			font-weight: bold;
			font-size: 14px;
			padding: 20px 16px;
			.van-picker__toolbar-title {
				padding-left: 4px;
			}
		}
		.van-picker-column {
			font-size: 14px;
			// font-weight: bold;
		}
		.bottom {
			padding: 16px 16px 24px;
			display: flex;
			border: 1px solid rgba(151, 151, 151, 0.1);
			box-shadow: 0px 2px 20px 0px rgba(95, 95, 95, 0.2);
			background-color: #ffffff;
			div {
				flex: 1;
				text-align: center;
				padding: 10px;
				border-radius: 5px;
				font-size: 15px;
			}
			div:nth-child(1) {
				color: #0052cc;
				border: 1px solid #0052cc;
				margin-right: 11px;
			}
			div:nth-child(2) {
				background-color: #0052cc;
				border: 1px solid #0052cc;
				color: #ffffff;
			}
		}
	}
}
</style>
