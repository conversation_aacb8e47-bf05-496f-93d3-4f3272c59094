<template>
	<div class="amd">
		<!-- header -->
		<div class="amd-header">
			<span>执业医院</span>
			<van-icon size="16" @click="close" name="cross" />
		</div>

		<!-- 医生列表 -->
		<div class="amd-content">
			<div v-for="item in props.hosList" :key="item.hospitalId || item.doctorIdMulti" @click="hosEv(item)" class="amd-content-item">
				<img src="@/assets/img/hospital_more.png" />
				<div class="amd-content-item-desc">
					<div>
						<span class="desc-name" style="margin-right: 8px">{{ item.hospital }}</span>
					</div>
					<div>
						<span style="margin-right: 8px">{{ item.department.split('/')[1] }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
const emits = defineEmits(['close', 'activeHos']);
const props = defineProps(['hosList']);
const close = () => {
	emits('close');
};

const hosEv = (item) => {
	emits('activeHos', item);
	close();
};
</script>
<style lang="scss" scoped>
.amd {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.amd-header {
		display: flex;
		align-items: center;
		padding: 0 12px;
		justify-content: space-between;
		span {
			font-weight: 500;
			font-size: 16px;
		}
	}

	.amd-content {
		width: 100%;
		flex: 1;
		padding: 0 12px;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		.amd-content-item {
			padding: 15px 0;
			border-bottom: 1px solid var(--ac-border-color);
			display: flex;
			align-items: center;

			img {
				height: 50px;
				width: 50px;
				margin-right: 12px;
			}

			.amd-content-item-desc {
				flex: 1;
				display: flex;
				flex-direction: column;
				.desc-name {
					font-weight: 500;
					font-size: 16px;
				}
			}
		}
	}
}
</style>
