<template>
	<van-popup teleport="body" class="q_custom-popup" :close-on-click-overlay="true" :safe-area-inset-bottom="true" position="bottom" :style="{ height: '75%' }" v-model:show="show">
		<!-- header -->
		<div class="c-header" id="ap-c-header">
			<!-- title -->
			<span class="c-header__title">请选择</span>
			<!-- 确认 -->
			<span @click="confirm" class="c-header__confirm">确认</span>
		</div>
		<!-- 搜索框 -->
		<div class="c-search" id="ap-c-search">
			<van-search @update:model-value="searchEv" @clear="resetSearch" v-model="searchText" placeholder="搜索" />
		</div>
		<!-- 数据列表 -->
		<div class="c-data" id="ap-c-data">
			<van-checkbox class="c-data--allcheck" icon-size="16" checked-color="var(--pv-tabbar-active)" shape="square" v-model="isCheckAll" :indeterminate="isIndeterminate" @change="checkAllChange"> 全选 </van-checkbox>
			<van-checkbox-group v-model="checkedResult" @change="checkedResultChange">
				<van-checkbox checked-color="var(--pv-tabbar-active)" icon-size="16" shape="square" v-for="item in dataList" :key="item.id" :name="item.id"> {{ item.name }} </van-checkbox>
			</van-checkbox-group>
		</div>
	</van-popup>
</template>
<script setup>
import { showToast } from 'vant';
const { proxy } = getCurrentInstance();
const show = ref(false);
const searchText = ref('');
const emits = defineEmits(['_confirm']);
const props = defineProps(['list', 'path', 'isHas', 'activeData']);

const dataList = ref([]);

const isCheckAll = ref(false);
const isIndeterminate = ref(false);
const checkedResult = ref([]);

const checkedResultChange = (value) => {
	const checkedCount = value.length;
	isCheckAll.value = checkedCount === props.list.length;
	isIndeterminate.value = checkedCount > 0 && checkedCount < props.list.length;
};

const checkAllChange = (val) => {
	checkedResult.value = [];
	if (val) {
		dataList.value.forEach((ele) => {
			checkedResult.value.push(ele.id);
		});
	}
	isIndeterminate.value = true;
};

// 确认
const confirm = () => {
	if (checkedResult.value.length > 0) {
		show.value = false;
		// 返回选中的选项id
		emits('_confirm', checkedResult.value);
	} else {
		if (props.isHas === false) {
			show.value = false;
			emits('_confirm', []);
		} else {
			showToast({
				message: '请选择数据',
				position: 'top',
			});
		}
	}
};

// 搜索
const searchEv = (val) => {
	if (val) {
		dataList.value = dataList.value.filter((ele) => ele.name.indexOf(val) > -1);
		proxy.$umeng('搜索', props.path, val);
	} else {
		dataList.value = props.list;
	}
};
// 重制搜索
const resetSearch = () => {
	searchText.value = '';
	dataList.value = props.list;
};
const golbalInit = (v) => {
	let arr = [];
	for (const ele of v) {
		arr.push({
			name: ele.name,
			id: ele.name,
		});
	}
	dataList.value = arr;
	checkedResult.value = props.activeData || [];
	if (checkedResult.value.length > 0 && checkedResult.value.length < props.list.length) {
		isIndeterminate.value = true;
	}
};
watch(
	() => props.list,
	() => {
		nextTick(() => {
			if (dataList.value.length === 0) dataList.value = props.list;
			checkedResult.value = props.activeData || [];
			if (checkedResult.value.length > 0 && checkedResult.value.length < props.list.length) {
				isIndeterminate.value = true;
			}
		});
	},
	{
		deep: true,
	}
);

onMounted(() => {
	dataList.value = props.list;
});

defineExpose({
	show,
	golbalInit,
});
</script>

<style lang="scss" scoped>
@import '../../style/pc/pickerFilter/oneLevelCheckBox.scss';
.c-header {
	padding: 20px 20px 16px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.c-header__title {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-default-color);
	}

	.c-header__confirm {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-tabbar-active);
	}
}

.c-search {
	padding: 0 20px;
	&:deep(.van-search) {
		padding: 0;
		border-radius: 3px;
		overflow: hidden;
	}
	&:deep(.van-search__field) {
		height: 36px;
	}
	&:deep(.van-field__left-icon) {
		color: var(--pv-no-active-color);
	}
	&:deep(.van-field__control::-webkit-input-placeholder) {
		color: var(--pv-no-active-color);
	}
}

.c-data {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	padding: 3.5px 15px;

	&:deep(.van-checkbox) {
		overflow: initial;
		padding-left: 7px;
		padding-right: 7px;
		padding-bottom: 14px;
		padding-top: 14px;
		border-bottom: solid 1px var(--pv-filter-bgc);
	}

	&:deep(.van-checkbox__label) {
		font-size: 16px;
		color: var(--pv-default-color);
		font-weight: 400;
		margin-left: 16px;
	}
	&:deep(.van-checkbox__icon--indeterminate .van-icon) {
		border-color: var(--pv-tabbar-active);
		background-color: var(--pv-tabbar-active);
	}
}
</style>
