<template>
	<div class="amd">
		<!-- header -->
		<div class="amd-header">
			<span>查看医生</span>
			<van-icon size="16" @click="close" name="cross" />
		</div>
		<!-- 搜索框 -->
		<div class="amd-search">
			<form action="/">
				<van-search v-model="searchValue" left-icon="search" @search="searchEv" :clearable="false" placeholder="请输入医院/医生" />
			</form>
		</div>

		<!-- 医生列表 -->
		<div class="amd-content">
			<van-list>
				<div v-for="item in doctorList" :key="item.id" @click="docEv(item)" class="amd-content-item">
					<img v-if="item.doctorSex === '女'" src="@/assets/img/demo/gril.png" />
					<img v-else src="@/assets/img/demo/men.png" />
					<div class="amd-content-item-desc">
						<div>
							<span class="desc-name" style="margin-right: 8px">{{ item.doctorName }}</span>
							<span>{{ item.doctorTitle }}</span>
							<!-- TODO: 多点执业 -->
							<img v-if="item.multiPointPractice" class="more_job" src="@/assets/img/more_job.png" />
						</div>
						<div>
							<span style="margin-right: 8px">{{ item.hospital }}</span>
							<span>{{ item.department }}</span>
						</div>
					</div>
				</div>
			</van-list>
		</div>
	</div>
</template>
<script setup>
const props = defineProps(['doctotListFront']);
const emits = defineEmits(['close', 'activeDoc']);

const doctorList = ref([]);

const close = () => {
	emits('close');
};

watch(
	() => props.doctotListFront,
	(n) => {
		console.log(n);
		doctorList.value = JSON.parse(JSON.stringify(n));
		console.log(doctorList.value);
	},
	{ immediate: true }
);

const searchValue = ref('');
const searchEv = () => {
	console.log(searchValue);
	if (!searchValue.value) {
		doctorList.value = JSON.parse(JSON.stringify(props.doctotListFront));
	}
	const lowerKeyword = searchValue.value.toLowerCase();

	return (doctorList.value = doctorList.value.filter((item) => (item.hospital && item.hospital.toLowerCase().includes(lowerKeyword)) || (item.doctorName && item.doctorName.toLowerCase().includes(lowerKeyword))));
};

const docEv = (item) => {
	emits('activeDoc', item);
	close();
};

onMounted(() => {});
</script>
<style lang="scss" scoped>
.amd {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.amd-header {
		display: flex;
		align-items: center;
		padding: 0 12px;
		justify-content: space-between;
		span {
			font-weight: 500;
			font-size: 16px;
		}
	}
	.amd-search {
		width: 100%;
	}

	.amd-content {
		width: 100%;
		flex: 1;
		padding: 0 12px;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		.amd-content-item {
			padding: 15px 0;
			border-bottom: 1px solid var(--ac-border-color);
			display: flex;
			align-items: center;

			img {
				height: 50px;
				width: 50px;
				margin-right: 12px;
			}

			.amd-content-item-desc {
				flex: 1;
				display: flex;
				flex-direction: column;
				.desc-name {
					font-weight: 500;
					font-size: 16px;
				}

				.more_job {
					height: 8px;
					width: 38px;
					object-fit: cover;
					margin-left: 8px;
					margin-right: 0px;
				}
			}
		}
	}
}
</style>
