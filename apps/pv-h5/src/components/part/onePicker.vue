<template>
	<div class="dp">
		<van-picker :swipe-duration="500" :visible-option-num="4" :option-height="25" :show-toolbar="false" :columns="props.dataList" @change="onChange" />
	</div>
</template>
<script setup>
const emits = defineEmits(['_onChange']);
const props = defineProps(['dataList']);
const onChange = (val) => {
	emits('_onChange', val.selectedOptions[0]);
};

const columns = ref([
	{ text: '杭州', value: 'Hangzhou' },
	{ text: '宁波', value: 'Ningbo' },
	{ text: '温州', value: 'Wenzhou' },
	{ text: '绍兴', value: 'Shaoxing' },
	{ text: '湖州', value: 'Huzhou' },
]);
</script>
<style lang="scss" scoped>
.dp {
	width: 100%;
	&:deep(.van-picker) {
		background: hsla(0, 0%, 100%, 0.12);
		border-radius: 5px;
	}

	&:deep(.van-picker__mask) {
		background-image: none;
	}
	&:deep(.van-picker-column__item) {
		color: #999999;
		font-size: 10px;
	}

	&:deep(.van-picker-column__item--selected) {
		color: #ffffff;
		font-size: 12px;
	}
}
</style>
