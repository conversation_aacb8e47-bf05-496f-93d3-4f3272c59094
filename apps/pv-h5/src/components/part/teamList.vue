<template>
	<div class="amd">
		<!-- header -->
		<div class="amd-header">
			<span>请选择</span>
			<van-icon size="16" @click="close" name="cross" />
		</div>
		<!-- 搜索框 -->
		<div class="amd-search">
			<form action="/">
				<van-search v-model="search" left-icon="search" @search="searchEv" :clearable="false" placeholder="请输入同事名字" />
			</form>
		</div>

		<!-- 医生列表 -->
		<div class="amd-content">
			<template v-if="dataList.length === 0">
				<van-empty description="无数据" />
			</template>
			<template v-else>
				<VList :data="dataList" :style="{ height: '100%' }" #default="item">
					<div :key="item.id" @click="docEv(item)" class="amd-content-item">
						<img v-if="item.avatar" :src="item.avatar" />
						<img v-else src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />
						<div class="amd-content-item-desc">
							<div>
								<span class="desc-name" style="margin-right: 8px">{{ item.firstName }}</span>
							</div>
							<div>
								<span style="margin-right: 8px">{{ item.groups.length > 0 ? item.groups[0].name : '' }}</span>
							</div>
						</div>
					</div>
				</VList>
			</template>
		</div>
	</div>
</template>
<script setup>
import { VList } from 'virtua/vue';
const emits = defineEmits(['close', 'activeTask']);
const props = defineProps(['list']);
const close = () => {
	emits('close');
};
const search = ref('');
const searchEv = () => {
	if (search.value) {
		dataList.value = props.list.filter((item) => item.firstName.indexOf(search.value) > -1);
	} else {
		dataList.value = props.list;
	}
};

const docEv = ({ id }) => {
	emits('activeTask', id);
	close();
};
const dataList = ref([]);
onMounted(() => {
	dataList.value = props.list;
});
</script>
<style lang="scss" scoped>
.amd {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.amd-header {
		display: flex;
		align-items: center;
		padding: 0 12px;
		justify-content: space-between;
		span {
			font-weight: 500;
			font-size: 16px;
		}
	}
	.amd-search {
		width: 100%;
	}

	.amd-content {
		width: 100%;
		flex: 1;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		.amd-content-item {
			padding: 15px 12px;
			border-bottom: 1px solid var(--ac-border-color);
			display: flex;
			align-items: center;

			img {
				height: 50px;
				width: 50px;
				margin-right: 12px;
				border-radius: 50%;
			}

			.amd-content-item-desc {
				flex: 1;
				display: flex;
				flex-direction: column;
				.desc-name {
					font-weight: 500;
					font-size: 16px;
				}
			}
		}
	}
}
</style>
