<template>
	<div class="text" :id="props.id">
		<div class="show-think" v-if="props.think" @click="showThink = !showThink">
			点击{{ !showThink ? '查看' : '收起' }}深度思考
			<van-icon name="arrow-up" v-if="showThink" />
			<van-icon name="arrow-down" v-else />
		</div>
		<div class="think" v-if="props.think !== undefined" :class="{ show: showThink, empty: props.think === '' }" v-html="renderedThink"></div>
		<div v-if="props.think === undefined || props.msg" v-html="renderedMsg"></div>
		<div v-if="!props.msg && props.think === undefined" class="msg-placeholder">
			<span class="chatLoading"></span>
		</div>
	</div>
</template>

<script setup>
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
const props = defineProps(['msg', 'id', 'isLoading', 'think', 'name']);
const showThink = ref(true);
const md = ref(
	new MarkdownIt({
		highlight: function (str, lang) {
			if (lang && hljs.getLanguage(lang)) {
				try {
					return hljs.highlight(lang, str).value;
				} catch (e) {
					console.log(e);
				}
			}
			return ''; // 使用外部默认方式高亮
		},
	})
);

const renderedMsg = computed(() => (props.msg ? md.value.render(props.msg) : ''));
const renderedThink = computed(() => (props.think ? md.value.render(props.think) : ''));

watch(
	() => [props.msg, props.think],
	() => {
		if (props.msg) {
			nextTick(() => {
				deleteLoading();
			});
			showThink.value = false;
		}
		nextTick(() => {
			if (props.isLoading) {
				//添加loading
				addLoading();
			}
		});
	},
	{ immediate: true }
);

watch(
	() => props.isLoading,
	(val) => {
		if (!val) {
			// 删除loading
			nextTick(() => {
				deleteLoading();
			});
		}
	}
);

// 添加loading
const addLoading = () => {
	const loadingExists = document.querySelector('.chatLoading');
	if (loadingExists) return;
	const container = document.getElementById(props.id);
	if (!container) return;
	let lastInlineEl = getLastInlineTextElement(container);
	if (!lastInlineEl) {
		lastInlineEl = container.querySelector('.msg-placeholder');
	}
	if (!lastInlineEl) return;
	// 创建 loading 元素
	const loading = document.createElement('span');
	loading.className = 'chatLoading';
	loading.textContent = ''; // 可选：使用 ▋ 或空
	// 插入到 inline 元素中作为“兄弟”
	lastInlineEl.parentNode?.insertBefore(loading, lastInlineEl.nextSibling);
};

const getLastInlineTextElement = (container) => {
	const walker = document.createTreeWalker(container, NodeFilter.SHOW_TEXT, {
		acceptNode(node) {
			if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
				return NodeFilter.FILTER_ACCEPT;
			}
			return NodeFilter.FILTER_REJECT;
		},
	});
	let lastTextNode = null;
	while (walker.nextNode()) {
		lastTextNode = walker.currentNode;
	}
	return lastTextNode;
};

// 删除loading
const deleteLoading = () => {
	const loading = document.querySelector('.chatLoading');
	const nd = loading?.parentNode;
	nd?.removeChild(loading);
};
</script>

<style scoped lang="scss">
.text {
	color: #172b4d;
	.name {
		&::before {
			content: '';
			display: inline-block;
			width: 10px;
			height: 10px;
			border-radius: 50%;
			background-color: #2b5fd9;
			animation: textLoadingAnimation 0.8s infinite;
			margin-right: 6px;
			vertical-align: baseline;
		}
	}
	.show-think {
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #666;
		margin-bottom: 8px;
	}
	.think {
		color: #667085;
		padding-left: 12px;
		border-left: 4px solid #c4cbd7;
		height: 0;
		transition: all 0.5s;
		overflow: hidden;
		&:empty,
		&.empty {
			padding-left: 0;
			border-left: 0;
		}
		&.show {
			height: auto;
		}
	}
}
</style>
