<template>
	<div class="text" :id="props.id">
		<!-- <div class="name" v-if="props.isLoading && props.name">
			{{ props.name }}
		</div> -->
		<div class="show-think" v-if="props.think" @click="showThink = !showThink">
			<!-- <van-button size="small" type="default" @click="showThink = !showThink"> -->
			点击{{ !showThink ? '查看' : '收起' }}深度思考
			<van-icon name="arrow-up" v-if="showThink" />
			<van-icon name="arrow-down" v-else />
			<!-- </van-button> -->
		</div>
		<div class="think" v-if="props.think !== undefined" :class="{ show: showThink, empty: props.think === '' }" v-html="md.render(props.think)"></div>
		<div v-if="props.think === undefined || props.msg" v-html="md.render(props.msg)"></div>
	</div>
</template>

<script setup>
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
const props = defineProps(['msg', 'id', 'isLoading', 'think', 'name']);
const showThink = ref(true);
const md = ref(null);
md.value = new MarkdownIt({
	highlight: function (str, lang) {
		if (lang && hljs.getLanguage(lang)) {
			try {
				return hljs.highlight(lang, str).value;
			} catch (e) {
				console.log(e);
			}
		}
		return ''; // 使用外部默认方式高亮
	},
});
watch(
	() => [props.msg, props.think],
	() => {
		if (props.msg) {
			nextTick(() => {
				deleteLoading();
			});
			showThink.value = false;
		}
		nextTick(() => {
			if (props.isLoading) {
				//添加loading
				addLoading();
			}
		});
	},
	{ immediate: true }
);

watch(
	() => props.isLoading,
	(val) => {
		if (!val) {
			// 删除loading
			nextTick(() => {
				deleteLoading();
			});
		}
	}
);

// 添加loading
const addLoading = () => {
	const loading1 = document.querySelector('.chatLoading');
	if (loading1) {
		return;
	}
	const nd = document.getElementById(props.id);
	const loading = document.createElement('span');
	loading.className = 'chatLoading';
	const lastInnermostChild = getLastInnermostChild(nd);
	lastInnermostChild.appendChild(loading);
};

// 删除loading
const deleteLoading = () => {
	const loading = document.querySelector('.chatLoading');
	const nd = loading?.parentNode;
	nd?.removeChild(loading);
};

// 获取元素最里层的最后一个元素节点
const getLastInnermostChild = (element) => {
	const lastChild = element.lastElementChild;
	// 如果没有子元素，则返回当前元素
	if (!lastChild) {
		return element;
	}
	// 递归调用获取最里层的最后一个子元素
	return getLastInnermostChild(lastChild);
};
</script>

<style scoped lang="scss">
.text {
	color: #172b4d;
	.name {
		&::before {
			content: '';
			display: inline-block;
			width: 10px;
			height: 10px;
			border-radius: 50%;
			// transform: translate(4px, 2px) scaleY(1.3);
			background-color: #2b5fd9;
			animation: textLoadingAnimation 0.8s infinite;
			margin-right: 6px;
		}
	}
	.show-think {
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #666;
		margin-bottom: 8px;
	}
	// :deep() {
	// 	.van-button {
	// 		border-radius: 12px;
	// 		padding: 0 12px;
	// 		line-height: 62px;
	// 		margin-bottom: 12px;
	// 		.van-icon {
	// 			margin-left: 10px;
	// 		}
	// 	}
	// }
	.think {
		color: #667085;
		padding-left: 12px;
		border-left: 4px solid #c4cbd7;
		height: 0;
		transition: all 0.5s;
		overflow: hidden;
		&:empty,
		&.empty {
			padding-left: 0;
			border-left: 0;
		}
		&.show {
			height: auto;
		}
	}
}
</style>
