<template>
	<van-popup teleport="body" class="q_custom-popup" :close-on-click-overlay="true" :safe-area-inset-bottom="true" position="bottom" v-model:show="show">
		<!-- header -->
		<div class="c-header" id="ap-c-header">
			<!-- title -->
			<span class="c-header__title">请选择</span>
			<!-- 确认 -->
			<span @click="confirm" class="c-header__confirm">确认</span>
		</div>
		<!-- 日历列表 -->
		<div class="c-data" id="ap-c-data">
			<div v-for="item in timeList" :key="item.value" class="c-data-item">
				<!-- title -->
				<div class="c-data-title">{{ item.text }}</div>
				<!-- 日历 -->
				<div class="c-data-content">
					<div v-for="i in item.children" :key="i.text" class="content-item">
						<span v-for="s in i.children" :key="s.value" :class="{ 'content-item-active': s.isActive, disable: isYearEqualToCurrent(s.parent) && isMonthGreaterThanCurrent(s.value, new Date()) }" @click="setDate(s)">
							{{ s.text }}
						</span>
					</div>
				</div>
			</div>
		</div>
	</van-popup>
</template>
<script setup>
import { showToast } from 'vant';
const props = defineProps(['_startDate', '_endDate']);

const show = ref(false);

const emits = defineEmits(['_confirm']);
// 选中的开始和结束时间
const query = reactive({
	sDate: '',
	eDate: '',
});

// 数据列表
const timeList = ref([]);
onMounted(() => {
	initData();
});

// 初始化最近两年的日期数据
const initData = () => {
	const yearNum = new Date().getFullYear();
	timeList.value.push({
		text: yearNum - 1 + '',
		value: yearNum - 1 + '',
		children: [
			{
				text: '1',
				children: [
					{
						text: '1',
						value: '01',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '2',
						value: '02',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '3',
						value: '03',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '4',
						value: '04',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '5',
						value: '05',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '6',
						value: '06',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
				],
			},
			{
				text: '2',
				children: [
					{
						text: '7',
						value: '07',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '8',
						value: '08',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '9',
						value: '09',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '10',
						value: '10',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '11',
						value: '11',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '12',
						value: '12',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
				],
			},
		],
	});
	timeList.value.push({
		text: yearNum + '',
		value: yearNum + '',
		children: [
			{
				text: '1',
				children: [
					{
						text: '1',
						value: '01',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '2',
						value: '02',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '3',
						value: '03',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '4',
						value: '04',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '5',
						value: '05',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '6',
						value: '06',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
				],
			},
			{
				text: '2',
				children: [
					{
						text: '7',
						value: '07',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '8',
						value: '08',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '9',
						value: '09',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '10',
						value: '10',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '11',
						value: '11',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '12',
						value: '12',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
				],
			},
		],
	});
	if (props._startDate) {
		// 根据传入的时间范围，高亮日期
		// setActiveTimeData();
	}
};

// 确认
const confirm = () => {
	if (query.sDate && query.eDate) {
		show.value = false;
		emits('_confirm', { _startDate: query.sDate, _endDate: query.eDate });
	} else {
		showToast({
			message: '请选择日期',
			position: 'top',
		});
	}
};
function isYearEqualToCurrent(inputYear) {
	// 获取当前年份
	const currentYear = new Date().getFullYear();
	// 将传入的年份转换为整数
	const inputYearNumber = parseInt(inputYear, 10);
	// 比较传入的年份和当前年份是否相等
	return inputYearNumber === currentYear;
}
function isMonthGreaterThanCurrent(inputMonth, currentMonth) {
	// 去除传入的月份可能的前导零，并转换为整数
	const inputMonthNumber = parseInt(inputMonth.replace(/^0+/, ''), 10);
	// 获取当前月份，注意：JavaScript 中月份从 0 开始计数，所以要加 1
	const currentMonthNumber = new Date().getMonth() + 1;
	// 比较传入的月份和当前月份
	return inputMonthNumber > currentMonthNumber;
}
// 点击选中
const setDate = (item) => {
	timeList.value.forEach((ele) => {
		ele.children.forEach((el) => {
			el.children.forEach((e) => {
				e.isActive = false;
			});
		});
	});
	item.isActive = true;
	query.sDate = item.parent + '' + item.value;
	query.eDate = item.parent + '' + item.value;
};
const resetActive = () => {
	timeList.value.forEach((ele) => {
		ele.children.forEach((el) => {
			console.log(el);
			el.children.forEach((e) => {
				e.isActive = false;
			});
		});
	});
};
watch(
	() => props._startDate,
	(val) => {
		resetActive();
		if (val) {
			// 根据传入的时间范围，高亮日期
			query.sDate = props._startDate;
			query.eDate = props._endDate;
			let data = (props._startDate.slice(0, 4) + '-' + props._startDate.slice(4)).split('-');
			nextTick(() => {
				timeList.value
					.find((ele) => ele.value === data[0])
					?.children.forEach((ele) => {
						ele.children.forEach((el) => {
							if (el.value === data[1]) {
								el.isActive = true;
							}
						});
					});
			});
		}
	},
	{ immediate: true }
);
defineExpose({
	show,
	resetActive,
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/pickerFilter/datePickerSingle.scss';

.c-header {
	padding: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.c-header__title {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-default-color);
	}

	.c-header__confirm {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-tabbar-active);
	}
}

.c-data {
	display: flex;
	flex-direction: column;
	.c-data-item {
		display: flex;
		flex-direction: column;
		.c-data-title {
			height: 40px;
			background-color: var(--pv-card-bgc);
			color: var(--pv-default-color);
			font-size: 16px;
			font-weight: 500;
			line-height: 40px;
			padding-left: 25px;
		}

		.c-data-content {
			display: flex;
			flex-direction: column;
			padding: 15px 21px;
			.content-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 1px;
			}
			span {
				font-size: 16px;
				font-weight: 500;
				display: flex;
				flex: 1;
				aspect-ratio: 1;
				height: auto;
				align-items: center;
				justify-content: center;
				position: relative;
				color: var(--pv-default-color);
				span {
					position: absolute;
					color: var(--pv-bgc) !important;
					font-size: 9px;
					font-weight: bolder;
					bottom: 3px;
					font-weight: 400;
				}
			}

			.content-item-active {
				background-color: var(--pv-tabbar-active);
				color: var(--pv-bgc);
				border-radius: 5px;
			}
			.content-item-zj-active {
				background-color: var(--pv-fliter-calendar-bgc);
				color: var(--pv-tabbar-active);
			}

			.content-item-s-radius-active {
				border-top-left-radius: 5px;
				border-bottom-left-radius: 5px;
			}
			.content-item-e-radius-active {
				border-top-right-radius: 5px;
				border-bottom-right-radius: 5px;
			}
			.disable {
				pointer-events: none; /* 禁用点击 */
				opacity: 0.5; /* 设置透明度 */
			}
		}
	}
}
</style>
