<template>
	<div class="amd">
		<!-- header -->
		<div class="amd-header">
			<span>推送文章</span>
			<van-icon size="16" @click="close" name="cross" />
		</div>

		<!-- 文章列表 -->
		<div class="amd-content">
			<img src="@/assets/img/demo/pa.jpg" />
		</div>
	</div>
</template>
<script setup>
const emits = defineEmits(['close']);
const close = () => {
	emits('close');
};
</script>
<style lang="scss" scoped>
.amd {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.amd-header {
		display: flex;
		align-items: center;
		padding: 0 12px;
		justify-content: space-between;
		margin-bottom: 12px;
		span {
			font-weight: 500;
			font-size: 16px;
		}
	}

	.amd-content {
		width: 100%;
		flex: 1;
		padding: 0 12px;
		display: flex;
		flex-direction: column;
		overflow-y: auto;

		img {
			width: 96%;
			object-fit: cover;
		}
	}
}
</style>
