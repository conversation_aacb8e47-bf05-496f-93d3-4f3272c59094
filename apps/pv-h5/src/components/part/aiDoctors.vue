<template>
	<div class="amd">
		<!-- header -->
		<div class="amd-header">
			<span>为您匹配到了以下医生，请选择：</span>
			<van-icon size="16" @click="close" name="cross" />
		</div>
		<!-- 医生列表 -->
		<div class="amd-content">
			<div v-for="item in dataList" :key="item.id" @click="docEv(item)" class="amd-content-item">
				<img v-if="item.doctorSex === '女'" src="@/assets/img/demo/gril.png" />
				<img v-else src="@/assets/img/demo/men.png" />
				<div class="amd-content-item-desc">
					<div>
						<span class="desc-name" style="margin-right: 8px">{{ item.doctorName }}</span>
						<span>{{ item.doctorTitle }}</span>
						<!-- 多点执业 -->
						<img v-if="item.servingHospital && item.servingHospital.length > 0" class="more_job" src="@/assets/img/more_job.png" />
					</div>
					<div>
						<span style="margin-right: 8px">{{ item.hospital }}</span>
						<span>{{ item.department }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
const emits = defineEmits(['close', 'activeDoc']);
const props = defineProps(['doctorList']);
const dataList = ref([]);
watch(
	() => props.doctorList,
	(val) => {
		dataList.value = val;
	},
	{
		deep: true,
		immediate: true,
	}
);
const close = () => {
	emits('close');
};

const docEv = (item) => {
	emits('activeDoc', item);
	close();
};
</script>
<style lang="scss" scoped>
.amd {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.amd-header {
		display: flex;
		align-items: center;
		padding: 0 12px;
		justify-content: space-between;
		span {
			font-weight: 500;
			font-size: 16px;
		}
	}

	.amd-content {
		width: 100%;
		flex: 1;
		padding: 0 12px;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		.amd-content-item {
			padding: 15px 0;
			border-bottom: 1px solid var(--ac-border-color);
			display: flex;
			align-items: center;

			img {
				height: 50px;
				width: 50px;
				margin-right: 12px;
			}

			.amd-content-item-desc {
				flex: 1;
				display: flex;
				flex-direction: column;
				.desc-name {
					font-weight: 500;
					font-size: 16px;
				}

				.more_job {
					height: 8px;
					width: 38px;
					object-fit: cover;
					margin-left: 8px;
					margin-right: 0px;
				}
			}
		}
	}
}
</style>
