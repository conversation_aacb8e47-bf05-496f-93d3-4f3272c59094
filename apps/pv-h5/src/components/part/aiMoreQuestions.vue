<template>
	<div class="amd">
		<!-- header -->
		<div class="amd-header">
			<span>{{ props.title }}</span>
			<van-icon size="16" @click="close" name="cross" />
		</div>
		<!-- 搜索框 -->
		<div class="amd-search">
			<form action="/">
				<van-search v-model="search" left-icon="search" @update:model-value="searchEv" :clearable="false" placeholder="请输入关键字" />
			</form>
		</div>

		<!-- 医生列表 -->
		<div class="amd-content">
			<template v-if="statusLoading === 'init'">
				<div v-for="item in [1, 2, 3]" :key="item" style="width: 100%; margin-bottom: 12px">
					<van-skeleton title :row="2" />
				</div>
			</template>
			<template v-else-if="statusLoading === 'empty'">
				<van-empty description="无数据" />
			</template>
			<template v-else>
				<div v-for="(item, index) in qesList" :key="index" @click="qesEv(item)" class="amd-content-item">
					<p>{{ item }}</p>
				</div>
			</template>
		</div>
	</div>
</template>
<script setup>
import { getQesTypeList } from '@/api/user';
const emits = defineEmits(['close', 'activeQes']);
const props = defineProps(['title']);
const search = ref('');
const qesList = ref([]);
const qeReList = ref([]);
const statusLoading = ref(null);

watch(
	() => props.title,
	(val) => {
		if (val) {
			nextTick(() => {
				init();
			});
		}
	},
	{
		immediate: true,
	}
);

const close = () => {
	emits('close');
};
const getQuestionList = () => {
	const s = props.title === '全部' ? '*' : props.title;
	getQesTypeList([s])
		.then((res) => {
			qesList.value = res.result;
			qeReList.value = res.result;
			statusLoading.value = 'doing';
		})
		.catch(() => {
			statusLoading.value = 'empty';
			qesList.value = [];
		});
};
const init = () => {
	qesList.value = [];
	qeReList.value = [];
	search.value = '';
	statusLoading.value = 'init';
	getQuestionList();
};

const searchEv = () => {
	if (search.value) {
		qesList.value = qeReList.value.filter((ele) => ele.indexOf(search.value) > -1);
	} else {
		qesList.value = qeReList.value;
	}
	if (qesList.value.length === 0) {
		statusLoading.value = 'empty';
	} else {
		statusLoading.value = 'done';
	}
};

const qesEv = (item) => {
	emits('activeQes', { msg: item });
	close();
};
</script>
<style lang="scss" scoped>
.amd {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.amd-header {
		display: flex;
		align-items: center;
		padding: 0 12px;
		justify-content: space-between;
		span {
			font-weight: 500;
			font-size: 16px;
		}
	}
	.amd-search {
		width: 100%;
	}

	.amd-content {
		width: 100%;
		flex: 1;
		padding: 0 12px;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		.amd-content-item {
			padding: 12px 0;
			border-bottom: 1px solid var(--ac-border-color);
			display: flex;
			align-items: center;

			img {
				height: 50px;
				width: 50px;
				margin-right: 12px;
			}

			.amd-content-item-desc {
				flex: 1;
				display: flex;
				flex-direction: column;
				.desc-name {
					font-weight: 500;
					font-size: 16px;
				}
			}
		}

		.amd-content-loading {
			text-align: center;
			padding-top: 6px;
		}
	}
}
</style>
