<template>
	<div class="btn-content">
		<!-- 分享 -->
		<span v-if="props.isShare" @click="share">
			<img src="@/assets/img/demo/share.png" />
		</span>
		<!-- 赞 -->
		<span v-if="props.zeStatus === '0' || props.zeStatus === '1'">
			<img @click="ze('0')" v-if="props.zeStatus === '1'" src="@/assets/img/demo/z-a.png" />
			<img @click="ze('1')" v-if="props.zeStatus === '0'" src="@/assets/img/demo/z.png" />
		</span>
		<!-- 踩 -->
		<span v-if="props.zeStatus === '0' || props.zeStatus === '2'">
			<img @click="ze('0')" v-if="props.zeStatus === '2'" src="@/assets/img/demo/c-a.png" />
			<img @click="ze('2')" v-if="props.zeStatus === '0'" src="@/assets/img/demo/c.png" />
		</span>
		<!-- 重新生成 -->
		<span v-if="props.reGen" @click="reGenEv">
			<img src="@/assets/img/demo/refresh.png" />
		</span>
		<!-- 语音播报-开始 -->
		<span v-if="props.voiceStatus" @click="voiceEv('play')">
			<img src="@/assets/img/demo/sy.png" />
		</span>
		<!-- 语音播报 - 暂停 -->
		<span v-else @click="voiceEv('pasued')">
			<img src="@/assets/img/demo/sy-a.png" />
		</span>
		<!-- 复制 -->
		<span class="copy-btn" @click="copyText">
			<img src="@/assets/img/demo/copy.png" />
		</span>
	</div>
</template>

<script setup>
const props = defineProps(['id', 'voiceStatus', 'zeStatus', 'reGen', 'isShare']);
const emits = defineEmits(['copyText', 'voiceEv', '_ze', '_reGen']);

// 复制
const copyText = () => {
	emits('copyText', props.id);
};

// 语音播报
const voiceEv = (val) => {
	const json = {
		id: props.id,
		vType: val,
	};
	emits('voiceEv', json);
};

// 点赞或踩
const ze = (val) => {
	const json = {
		id: props.id,
		like: val,
	};
	emits('_ze', json);
};

// 重新生成
const reGenEv = () => {
	emits('_reGen', props.id);
};

// 分享
const share = () => {
	emits('_share', props.id);
};
</script>

<style lang="scss" scoped>
.btn-content {
	width: 100%;
	border-top: 1px solid rgba(221, 221, 221, 0.35);
	margin-top: 8px;
	display: flex;
	justify-content: flex-end;
	align-items: center;
	padding: 8px 3px 0px 0px;
	span {
		width: 25px;
		height: 25px;
		margin-left: 6px;
		display: flex;
		align-items: center;
		justify-content: center;
		img {
			width: 100%;
			height: 100%;
		}
	}
}
/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	.btn-content {
		width: 100%;
		border-top: 1px solid rgba(221, 221, 221, 0.35);
		margin-top: 8px;
		padding: 8px 3px 0px 0px;
		span {
			width: 25px;
			height: 25px;
			margin-left: 6px;
		}
	}
}
</style>
