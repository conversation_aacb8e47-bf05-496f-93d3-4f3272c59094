<template>
	<div>
		<!-- 语音 -->
		<div
			class="chat-bottom"
			:style="{
				marginLeft: '2.8570vw',
				width: '94.286vw',
			}"
			:class="{
				azsh: voiceMsg === '按住说话',
				shqx: voiceMsg === '松开发送，上滑取消',
				ssqx: voiceMsg === '松开取消',
			}"
		>
			<!-- 智能体类型 -->
			<div v-if="agentName" class="skill-type">
				<span>{{ agentName }}</span>
				<van-icon @click="delAgent" name="clear" />
			</div>
			<!-- 语音和手写 -->
			<img class="switch_new" v-show="isVoice && voiceMsg === '按住说话'" @click="switchVoice" src="@/assets/img/demo/voice2.png" />
			<img v-show="!isVoice && voiceMsg === '按住说话'" @click="switchVoice" src="@/assets/img/demo/keyword.png" />
			<!-- 左侧 按住说话 -->
			<div v-if="!isVoice" @touchstart="setVoiceSq" class="chat-bottom-middle">
				<!-- 语音 -->
				<template v-if="voiceMsg === '按住说话'">
					<div class="chat-bottom-btn">
						<span class="chat-bottom-btn-span">{{ voiceMsg }}</span>
					</div>
				</template>
				<template v-else>
					<img v-show="voiceMsg === '松开发送，上滑取消'" class="loading-img" src="@/assets/img/demo/loading-gits-blue.gif" />
					<img v-show="voiceMsg === '松开取消'" class="loading-img" src="@/assets/img/demo/loading-gits-red.gif" />
				</template>
			</div>
			<!-- 左侧 文本框 -->
			<div v-else class="chat-bottom-input">
				<!-- 文本框 -->
				<template v-if="voiceMsg === '按住说话'">
					<van-field ref="msgInput" rows="1" v-model="input" autosize type="textarea" placeholder="您现在有什么问题可以问我？" />
				</template>
			</div>
			<!-- 功能按钮列表 -->
			<img v-show="voiceMsg === '按住说话' && !input && !showMenu && ol" @click="showMenuFn" src="@/assets/img/agent-add.png" />
			<img v-show="voiceMsg === '按住说话' && !input && showMenu && ol" @click="showMenuFn" src="@/assets/img/agent-close.png" />
			<!-- 发送按钮 -->
			<img class="send_new" v-show="input && voiceMsg === '按住说话'" @click="sendMessage" src="@/assets/img/demo/send_new.png" />

			<!-- 底部功能区 -->
			<div v-if="agentFunction?.deepseek !== '' || agentFunction?.internet !== ''" class="function-type">
				<div v-if="agentFunction?.deepseek !== ''" @click="changeDeepSeek" class="mc-btns-item" :class="{ 'mc-btns-item__active': agentFunction?.deepseek }">
					<img v-if="!agentFunction?.deepseek" src="@/assets/img/deepseek.png" />
					<img v-else src="@/assets/img/deepseek_a.png" />
					深度研究
				</div>
				<div v-if="agentFunction?.internet !== ''" @click="changeInternet" class="mc-btns-item" :class="{ 'mc-btns-item__active': agentFunction?.internet }">
					<img v-if="!agentFunction?.internet" src="@/assets/img/internet.png" />
					<img v-else src="@/assets/img/internet_a.png" />
					联网搜索
				</div>
			</div>
		</div>
		<!-- 工具 -->
		<div class="chat-bottom-tool" :class="{ active: showMenu }">
			<van-swipe style="width: 100%" :loop="false" :autoplay="0" indicator-color="#172b4d">
				<van-swipe-item>
					<div v-for="(item, index) in props.toolsList.length > 8 ? props.toolsList.slice(0, 8) : props.toolsList" :key="index" @click="toolClick(item)" class="tool-item">
						<div class="tool-item-img">
							<img :src="item.img" alt="" />
						</div>
						<span class="tool-item-text">{{ item.name }}</span>
					</div>
				</van-swipe-item>
				<van-swipe-item v-if="props.toolsList.length > 8">
					<div v-for="(item, index) in props.toolsList.slice(8)" :key="index" @click="toolClick(item)" class="tool-item">
						<div class="tool-item-img">
							<img :src="item.img" alt="" />
						</div>
						<span class="tool-item-text">{{ item.name }}</span>
					</div>
				</van-swipe-item>
			</van-swipe>
		</div>
	</div>
</template>
<script setup>
import { showDialog, showToast } from 'vant';
import { isSafariBrowser } from '@/utils/index';
import Hammer, { on } from 'hammerjs';
import CryptoJS from 'crypto-js';
import RecorderManager from '@/utils/tts';
// const recorder = new RecorderManager(`http://localhost:1288/app/mobile`);
const recorder = new RecorderManager(`${location.origin}/app/mobile`);
const emits = defineEmits(['_sendMessage', '_changeEdit', '_lineVoice', '_delAgent', '_changeDeepSeek', '_changeInternet', '_scrollBottom']);
const props = defineProps(['editorText', 'ol', 'toolsList', 'agentName', 'agentFunction']);

watch(
	() => props.editorText,
	(val) => {
		isVoice.value = true;
		nextTick(() => {
			input.value = val.split('jjjjjj')[0];
			nextTick(() => {
				if (val) {
					msgInput.value?.focus();
				} else {
					msgInput.value?.blur();
				}
			});
		});
	}
);

// 是否深度研究
const changeDeepSeek = () => {
	emits('_changeDeepSeek', !props.agentFunction?.deepseek);
};
// 是否互联网
const changeInternet = () => {
	emits('_changeInternet', !props.agentFunction?.internet);
};

// 是否展示功能列表按钮
const showMenu = ref(false);
const showMenuFn = () => {
	showMenu.value = !showMenu.value;
	if (showMenu.value) {
		setTimeout(() => {
			emits('_scrollBottom');
		}, 350);
	}
};

// 删除选中的agent
const delAgent = () => {
	emits('_delAgent', '');
};

// 工具事件
const toolClick = (item) => {
	showMenu.value = false;
	setTimeout(() => {
		emits('_toolClick', item);
	}, 350);
};

// 记录开始位置
let startY = 0;

// 修改消息
const msgInput = ref(null);

// 文本框中的值
const input = ref('');

// 是否为语音 false = 语音
const isVoice = ref(true);

// 语音按钮描述
const voiceMsg = ref('按住说话');

// 语音输入的文本
const voiceText = ref('');
let isSend = false;

// hammer 实例
let hammer = '';
// 麦克风授权状态 默认 = 尚未选择是否授权麦克风
let voiceStatus = 'prompt';
const getmicrophonePermission = () => {
	// 获取用户是否允许了麦克风权限
	navigator.permissions.query({ name: 'microphone' }).then((result) => {
		// console.log('麦克风权限状态:', result.state);
		voiceStatus = result.state;
		// 已授权麦克风
		if (result.state === 'granted') {
			nextTick(() => {
				if (!isVoice.value) {
					registerHammer();
				}
			});
		}
	});
};
// 获取麦克风权限
const setVoiceSq = () => {
	// 麦克风已被禁用
	if (voiceStatus === 'denied') {
		showDialog({
			title: '提示',
			message: '麦克风已被禁用，请您在浏览器设置中开启。',
			confirmButtonColor: '#0052cc',
		});
	} else if (voiceStatus === 'prompt') {
		// 麦克风处于被询问状态
		getVoiceSq();
	}
};

// 获取麦克风的授权
const getVoiceSq = () => {
	navigator.mediaDevices
		.getUserMedia({ audio: true })
		.then((stream) => {
			voiceStatus = 'granted';
			// 立即关闭麦克风
			stream.getAudioTracks().forEach((track) => track.stop());
			stream = null; // 释放变量
			// 兼容 Safari，强制清空 audio
			if (isSafariBrowser()) {
				const audio = new Audio();
				audio.srcObject = null;
			}
			nextTick(() => {
				if (!isVoice.value) {
					registerHammer();
				}
			});
		})
		.catch((error) => {
			// console.error('❌ 用户拒绝了麦克风权限:', error);
			voiceStatus = 'denied';
			showToast({
				position: 'top',
				message: '您已拒绝授权麦克风权限！',
			});
		});
};

onMounted(() => {
	websocketUrl = getWebSocketUrl();
	// 获取用户是否允许了麦克风权限
	getmicrophonePermission();
});

// 发送消息
const sendMessage = () => {
	emits('_sendMessage', input.value);
	nextTick(() => {
		msgInput.value?.blur();
	});
	clearText();
};

// 清空文本
const clearText = () => {
	nextTick(() => {
		input.value = '';
	});
};
// 切换输入方式
const switchVoice = () => {
	if (!isVoice.value) {
		emits('_changeEdit', false);
	} else {
		voiceMsg.value = '按住说话';
	}
	isVoice.value = !isVoice.value;
	clearText();
	if (!isVoice.value) {
		nextTick(() => {
			// 释放 hammer
			hammer = '';
			// 用户已授权麦克风
			if (voiceStatus === 'granted') registerHammer();
		});
	} else {
		nextTick(() => {
			msgInput.value?.focus();
		});
	}
};

// 注册hammer事件
const registerHammer = () => {
	const s = document.querySelector('.chat-bottom-middle');
	hammer = new Hammer(s);
	hammer.get('pan').set({
		direction: Hammer.DIRECTION_ALL,
	});

	// 长按事件
	hammer.on('press', (e) => {
		// 记录初始按下的位置
		startY = e.center.y;
		// 激活讯飞在线语音api
		initVoice();
	});

	hammer.on('pressup', (e) => {
		isSend = true;
		voiceMsg.value = '按住说话';
		recorder?.stop();
	});

	hammer.on('panmove', (e) => {
		const deltaY = e.center.y - startY;

		if (deltaY < -100) {
			// 向上滑动大于60px
			voiceMsg.value = '松开取消';
		} else if (deltaY > -100 && deltaY < 20) {
			// 原地
			voiceMsg.value = '松开发送，上滑取消';
		}
	});

	// 松开取消
	hammer.on('panend', (e) => {
		if (voiceMsg.value === '松开发送，上滑取消') {
			isSend = true;
		}
		voiceMsg.value = '按住说话';
		recorder?.stop();

		// 发送最后一次语音，告诉服务端上传结束标识
		iatWS.send(
			JSON.stringify({
				data: {
					status: 2,
				},
			})
		);
	});
};

// 语音识别
let websocketUrl = '';
let iatWS = null;
let resultText = '';
let resultTextTemp = '';
const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let btnStatus = 'UNDEFINED'; // "UNDEFINED" "CONNECTING" "OPEN" "CLOSING" "CLOSED"

const initVoice = () => {
	resultText = '';
	resultTextTemp = '';
	voiceText.value = '';
	iatWS = null;
	isSend = false;
	if (btnStatus === 'UNDEFINED' || btnStatus === 'CLOSED') {
		connectWebSocket();
	}
};

// eslint-disable-next-line no-unused-vars
const connectWebSocket = () => {
	if ('WebSocket' in window) {
		iatWS = new WebSocket(websocketUrl);
	} else if ('MozWebSocket' in window) {
		iatWS = new MozWebSocket(websocketUrl);
	} else {
		showToast({
			position: 'top',
			message: '浏览器不支持WebSocket',
		});
		voiceMsg.value = '按住说话';
		voiceText.value = '';
		return;
	}
	changeBtnStatus('CONNECTING');
	iatWS.onopen = () => {
		// 开始录音
		recorder.start({
			sampleRate: 16000,
			frameSize: 1280,
		});
		var params = {
			common: {
				app_id: APPID,
			},
			business: {
				language: 'zh_cn',
				domain: 'iat',
				accent: 'mandarin',
				vad_eos: 3000,
				dwa: 'wpgs',
			},
			data: {
				status: 0,
				format: 'audio/L16;rate=16000',
				encoding: 'raw',
			},
		};
		iatWS.send(JSON.stringify(params));
		//onopen方法触发后，说明连接已经建立， 改变按钮文字，出现loading动画，并开始录音和识别
		voiceMsg.value = '松开发送，上滑取消';
	};
	iatWS.onmessage = (e) => {
		renderResult(e.data);
	};
	iatWS.onerror = (e) => {
		console.error(e);
		recorder.stop();
		changeBtnStatus('CLOSED');
	};
	iatWS.onclose = (e) => {
		console.error(e);
		recorder.stop();
		changeBtnStatus('CLOSED');
	};
};

const renderResult = (resultData) => {
	// 识别结束
	let jsonData = JSON.parse(resultData);
	if (jsonData.data && jsonData.data.result) {
		let data = jsonData.data.result;
		let str = '';
		let ws = data.ws;
		for (let i = 0; i < ws.length; i++) {
			str = str + ws[i].cw[0].w;
		}
		if (data.pgs) {
			if (data.pgs === 'apd') {
				// 将resultTextTemp同步给resultText
				resultText = resultTextTemp;
			}
			// 将结果存储在resultTextTemp中
			resultTextTemp = resultText + str;
		} else {
			resultText = resultText + str;
		}

		voiceText.value = resultTextTemp || resultText || '';
	}
	if (jsonData.code === 0 && jsonData.data.status === 2) {
		if (isSend) {
			emits('_sendMessage', voiceText.value);
			isSend = false;
		}
		iatWS.close();
	}
	if (jsonData.code !== 0) {
		iatWS.close();
	}
};

const getWebSocketUrl = () => {
	// 请求地址根据语种不同变化
	var url = 'wss://iat-api.xfyun.cn/v2/iat';
	var host = location.host;
	var apiKey = API_KEY;
	var apiSecret = API_SECRET;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/iat HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};

const changeBtnStatus = (status) => {
	btnStatus = status;
};
const toBase64 = (buffer) => {
	var binary = '';
	var bytes = new Uint8Array(buffer);
	var len = bytes.byteLength;
	for (var i = 0; i < len; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	return window.btoa(binary);
};
recorder.onStart = () => {
	changeBtnStatus('OPEN');
};
recorder.onStop = () => {
	voiceMsg.value = '按住说话';
};

recorder.onFrameRecorded = ({ isLastFrame, frameBuffer }) => {
	if (iatWS.readyState === iatWS.OPEN) {
		iatWS.send(
			JSON.stringify({
				data: {
					status: isLastFrame ? 2 : 1,
					format: 'audio/L16;rate=16000',
					encoding: 'raw',
					audio: toBase64(frameBuffer),
				},
			})
		);
		if (isLastFrame) {
			changeBtnStatus('CLOSING');
		}
	}
};
</script>
<style lang="scss" scoped>
.chat-bottom {
	z-index: 99;
	position: relative;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	border-radius: 12px;
	padding: 6px 10px 6px 0;
	border: 1px solid #dfe1e6;
	justify-content: space-between;
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.05);
	.skill-type {
		margin-left: 15px;
		width: calc(100% - 15px);
		border-bottom: solid 1px #dfe1e6;
		margin-bottom: 8px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 3px 0 4px 0;
		span {
			font-size: 12px;
			color: var(--ac-bg-active-color);
		}
	}

	.function-type {
		margin-left: 15px;
		width: calc(100% - 15px);
		display: flex;
		padding: 6px 0 2px 0;
		.mc-btns-item {
			display: flex;
			align-items: center;
			margin-right: 6px;
			border: 1px solid var(--ac-border-color);
			padding: 4px 10px;
			border-radius: 20px;
			font-size: 12px;
			color: var(--pv-no-active-color);
			img {
				width: 14px;
				height: 14px;
				margin-right: 3px;
				margin-left: 0;
			}
		}

		.mc-btns-item__active {
			background-color: #d7e4ff;
			color: var(--ac-font-color-active);
			border-color: #d7e4ff;
		}
	}
	.chat-bottom-middle {
		height: 40px;
		flex: 1;
		padding-left: 10px;
		display: flex;
		align-items: center;
		-webkit-touch-callout: none; /* Safari 和 Chrome for iOS */
		-webkit-user-select: none; /* Chrome, Safari, Opera */
		-khtml-user-select: none; /* Konqueror */
		-moz-user-select: none; /* Firefox */
		-ms-user-select: none; /* Internet Explorer/Edge */
		user-select: none;
		.chat-bottom-btn {
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #172b4d;
			span {
				padding-left: 99px;
				-webkit-touch-callout: none; /* Safari 和 Chrome for iOS */
				-webkit-user-select: none; /* Chrome, Safari, Opera */
				-khtml-user-select: none; /* Konqueror */
				-moz-user-select: none; /* Firefox */
				-ms-user-select: none; /* Internet Explorer/Edge */
				user-select: none;
			}
			.chat-bottom-btn-span {
				padding-left: 92px;
			}
		}

		.loading-img {
			width: 80%;
			margin-left: 10%;
			pointer-events: none;
			-webkit-touch-callout: none; /* Safari 和 Chrome for iOS */
			-webkit-user-select: none; /* Chrome, Safari, Opera */
			-khtml-user-select: none; /* Konqueror */
			-moz-user-select: none; /* Firefox */
			-ms-user-select: none; /* Internet Explorer/Edge */
			user-select: none;
		}
	}
	.chat-bottom-input {
		flex: 1;
		padding-left: 10px;
		&:deep(.van-field) {
			background-color: #ffffff;
			border-radius: 6px;
			padding-bottom: 8px;
			padding-top: 8px;
			min-height: 40px;
			padding-left: 3px;
			padding-right: 3px;
		}
		&:deep(.van-field__control) {
			color: var(--pv-default-color);
		}

		.chat-bottom-btn {
			height: 40px;
			display: flex;
			align-items: center;
			justify-content: center;
			user-select: none;
			color: #172b4d;
			span {
				padding-left: 88px;
			}
		}

		.loading-img {
			width: 80%;
			margin-left: 10%;
			pointer-events: none;
			user-select: none;
			-webkit-user-select: none;
		}
	}

	img {
		width: 29px;
		height: 29px;
		object-fit: contain;
		margin-left: 15px;
	}

	.send_new {
		align-self: flex-start;
		margin-top: 5.5px;
		margin-left: 12px;
	}
	.switch_new {
		align-self: flex-start;
		margin-top: 5.5px;
	}
}

.chat-bottom-tool {
	margin-left: 10px;
	display: flex;
	overflow: hidden;
	flex-wrap: wrap;
	height: 0px;
	transition: height 0.3s ease-in;
	.tool-item {
		width: 25%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 10px;
		float: left;
		.tool-item-img {
			img {
				width: 45px;
				height: 45px;
				object-fit: cover;
			}
		}

		.tool-item-text {
			font-size: 10px;
			color: #172b4d;
			margin-top: 6px;
		}
	}

	&:deep(.van-swipe__indicators) {
		bottom: 2%;
		left: 25%;

		.van-swipe__indicator {
			background-color: var(--ac-colors-myGray-500);
		}
	}
}

.chat-bottom-tool.active {
	height: 162px;
}

.azsh {
	background-color: #ffffff;
}
.shqx {
	background: linear-gradient(46deg, #ffffff 0%, #eff6ff 100%);
	border-color: #0052cc;
}
.ssqx {
	background: linear-gradient(46deg, #ffffff 0%, #fff8f8 100%);
	border-color: #ff0000;
}

// 梦层动画
.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
	opacity: 0;
}
// loading
@keyframes ball-beat {
	50% {
		opacity: 0.2;
		transform: scale(0.7);
	}

	100% {
		opacity: 1;
		transform: scale(1);
	}
}
</style>
