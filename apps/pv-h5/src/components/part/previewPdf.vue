<template>
	<van-popup :safe-area-inset-top="true" :safe-area-inset-bottom="true" :close-on-click-overlay="false" :close-on-popstate="true" style="height: 100%; width: 100%; max-width: 100%" v-model:show="pdfShow">
		<div class="pdf-preview">
			<!-- header -->
			<div class="pdf-preview__header">
				<van-icon color="var(--ac-bg-active-color)" @click="closePopup" size="20" name="clear" />
			</div>
			<!-- middle -->
			<div class="pdf-preview__middle">
				<VuePdfEmbed ref="pdfRef" :source="url" :page="query.page" @loading-failed="docFailed" @rendered="docSuccess" :width="pdfWidth"></VuePdfEmbed>
			</div>
			<!-- bottom  -->
			<div v-if="loading === 'success'" class="pdf-preview__bottom">
				<!-- 上一页 -->
				<div @click="prePage" class="bottom-item" :class="{ 'bottom-item__active': query.page <= 1 }">
					<van-icon size="14" name="arrow-left" />
				</div>
				<!-- 页数 -->
				<div class="bottom-text">{{ query.page }}/{{ query.pageCount }}</div>
				<!-- 下一页 -->
				<div @click="nextPage" class="bottom-item" :class="{ 'bottom-item__active': query.page >= query.pageCount }">
					<van-icon size="14" name="arrow" />
				</div>
			</div>
		</div>
	</van-popup>
</template>
<script setup>
import axios from 'axios';
import VuePdfEmbed from 'vue-pdf-embed';
import { showToast } from 'vant';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const props = defineProps(['apiKey', 'sourceId', 'chatId', 'appId', 'chatItemId']);
const pdfShow = ref(false);
const url = ref('');
const pdfRef = ref(null);
const pdfWidth = ref(0);
const loading = ref('');
pdfWidth.value = document.body.clientWidth;
// pdf 加载页数
const query = reactive({
	page: 1,
	pageCount: 0,
});
// 文档加载成功
const docSuccess = () => {
	loading.value = 'success';
	query.pageCount = pdfRef.value.pageCount;
};
// 文档加载失败
const docFailed = () => {
	showToast({
		position: 'top',
		message: '文档加载失败',
	});
	closePopup();
};
const getPdfUrl = () => {
	console.log(props.apiKey);
	axios({
		url: enterStore.enterInfo.agentAddress + '/api/core/dataset/collection/read',
		method: 'POST',
		data: {
			collectionId: props.sourceId || '',
			// chatId: props.chatId || '',
			// appId: props.appId || '',
			// chatItemId: props.chatItemId || '',
		},
		headers: {
			'Content-Type': 'application/json;charset=utf-8',
			Authorization: `Bearer ${props.apiKey}`,
		},
	})
		.then((res) => {
			if (res.data.data.type === 'url') {
				url.value = enterStore.enterInfo.agentAddress + res.data.data.value;
				pdfShow.value = true;
			} else {
				showToast({
					position: 'top',
					message: '文档暂不支持预览',
				});
			}
		})
		.catch(() => {
			showToast({
				position: 'top',
				message: '请求失败,请重试',
			});
		});
};

const closePopup = () => {
	url.value = '';
	loading.value = '';
	query.page = 1;
	query.pageCount = 0;
	pdfShow.value = false;
};

// 上一页
const prePage = () => {
	if (query.page > 1) {
		query.page--;
	}
};

// 下一页
const nextPage = () => {
	if (query.page < query.pageCount) {
		query.page++;
	}
};

defineExpose({
	getPdfUrl,
	closePopup,
	pdfShow,
});
</script>
