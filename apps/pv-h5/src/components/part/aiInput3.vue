<template>
	<div style="height: 100%">
		<div v-if="!isVoice" class="chat-bottom-voice">
			<div class="chat-bottom-voice__stop">
				<img src="@/assets/img/demo/playStop.png" alt="" @click="stop" />
			</div>
			<div class="chat-bottom-voice__start">
				<template v-if="isStart">
					<img src="@/assets/img/demo/playVoice.png" alt="" style="pointer-events: none" />
				</template>
				<template v-else>
					<div class="start-box" @click="switchVoice">开始</div>
				</template>
			</div>
			<div class="chat-bottom-voice__key" @click="switchVoice">
				<img src="@/assets/img/demo/playKeyboard.png" alt="" />
			</div>
		</div>
		<!-- 左侧 按住说话询问课程 -->
		<div v-else ref="target" class="chat-bottom" :class="{ azsh: voiceMsg === '按住说话询问课程', shqx: voiceMsg === '松开发送，上滑取消', ssqx: voiceMsg === '松开取消' }">
			<!-- 左侧 文本框 -->
			<div class="chat-bottom-input">
				<!-- 文本框 -->
				<template v-if="voiceMsg === '按住说话询问课程'">
					<van-field ref="msgInput" enterkeyhint="send" @keyup.enter="sendMessage" v-model="input" placeholder="发消息" />
				</template>
			</div>
			<img v-show="!input && isVoice && voiceMsg === '按住说话询问课程'" @click="switchVoice" src="@/assets/img/demo/voice2.png" />
			<!-- 语音和手写 -->
			<!-- <img v-show="!input && voiceMsg === '按住说话询问课程'" src="@/assets/img/demo/playSend.png" /> -->
			<!-- 发送 -->
			<img v-show="input && voiceMsg === '按住说话询问课程'" @click="sendMessage" src="@/assets/img/demo/send_new.png" />
		</div>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import Hammer from 'hammerjs';
import CryptoJS from 'crypto-js';
import RecorderManager from '@/utils/tts';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
// 接口地址
const domain = enterStore.enterInfo.domain;

import MonitorKeyboard from '@/utils/keyboard';
const recorder = new RecorderManager(`${domain}/app/mobile`);
const emits = defineEmits(['_sendMessage', '_changeEdit', '_changeVoice', '_stop']);
const props = defineProps(['editorText', 'iType']);
const target = ref();
const monitorkeyboard = ref();
const isStart = ref(false);
const start = () => {
	input.value = '开始';
	sendMessage();
	isStart.value = true;
};
const initKeyboard = () => {
	monitorkeyboard.value = new MonitorKeyboard();
	console.log(monitorkeyboard.value);
	monitorkeyboard.value.onShow(() => {});
	monitorkeyboard.value.onHidden(() => {
		if (isVoice.value) {
			switchVoice();
		}
		// showToast({
		// 	position: 'top',
		// 	message: '1111',
		// });
	});
};
watch(
	() => props.editorText,
	(val) => {
		isVoice.value = true;
		nextTick(() => {
			input.value = val.split('jjjjjj')[0];
			nextTick(() => {
				if (val) {
					msgInput.value?.focus();
				} else {
					msgInput.value?.blur();
				}
			});
		});
	}
);

// 获取当前可用屏幕的高度
const screenHeight = document.documentElement.clientHeight;

// 修改消息
const msgInput = ref(null);

// 文本框中的值
const input = ref('');

// 是否为语音 false = 语音
const isVoice = ref(false);

// 语音按钮描述
const voiceMsg = ref('按住说话询问课程');

// 语音输入的文本
const voiceText = ref('');
let isSend = false;

// hammer 实例
let hammer = '';

onMounted(() => {
	nextTick(() => {
		if (!isVoice.value) {
			registerHammer();
		}
		initKeyboard();
	});
});
onBeforeUnmount(() => {
	monitorkeyboard.value?.onEnd();
});
const stop = () => {
	emits('_stop');
};
// 线上语音
const onLineVoice = () => {
	showToast({
		position: 'top',
		message: '暂未开通',
	});
};

// 发送消息
const sendMessage = () => {
	console.log('发送消息', input.value);
	emits('_sendMessage', input.value);
	nextTick(() => {
		msgInput.value?.blur();
	});
	clearText();
};

// 清空文本
const clearText = () => {
	nextTick(() => {
		input.value = '';
	});
};
// 切换输入方式
const switchVoice = () => {
	isStart.value = true;
	emits('_changeEdit', isVoice.value);
	if (isVoice.value) {
		voiceMsg.value = '按住说话询问课程';
	}
	isVoice.value = !isVoice.value;
	clearText();
	if (!isVoice.value) {
		nextTick(() => {
			registerHammer();

			monitorkeyboard.value?.onEnd();
		});
	} else {
		nextTick(() => {
			msgInput.value?.focus();

			monitorkeyboard.value?.onStart();
		});
	}
};

// 注册hammer
const registerHammer = () => {
	const s = document.querySelector('.chat-bottom-voice__start');
	hammer = new Hammer(s);
	hammer.get('pan').set({
		direction: Hammer.DIRECTION_ALL,
	});

	// 长按事件
	hammer.on('press', (e) => {
		voiceMsg.value = '松开发送，上滑取消';
		emits('_changeVoice', true, '', false);
		// 激活讯飞在线语音api
		initVoice();
	});

	hammer.on('pressup', (e) => {
		isSend = true;
		voiceMsg.value = '按住说话询问课程';
		recorder?.stop();
	});

	// 向上滑动
	hammer.on('panup', (e) => {
		if (screenHeight - e.srcEvent.clientY >= 80) {
			voiceMsg.value = '松开取消';
		}
	});

	// 向下滑动
	hammer.on('pandown', (e) => {
		if (screenHeight - e.srcEvent.clientY < 80) {
			voiceMsg.value = '松开发送，上滑取消';
		}
	});

	// 松开取消
	hammer.on('panend', (e) => {
		if (voiceMsg.value === '松开发送，上滑取消') {
			isSend = true;
		} else {
			emits('_changeVoice', false);
		}
		voiceMsg.value = '按住说话询问课程';
		recorder?.stop();
	});
};

// 语音识别
let iatWS = null;
let resultText = '';
let resultTextTemp = '';
const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let btnStatus = 'UNDEFINED'; // "UNDEFINED" "CONNECTING" "OPEN" "CLOSING" "CLOSED"

const initVoice = () => {
	resultText = '';
	resultTextTemp = '';
	voiceText.value = '';
	isSend = false;
	if (btnStatus === 'UNDEFINED' || btnStatus === 'CLOSED') {
		connectWebSocket();
	}
};

// eslint-disable-next-line no-unused-vars
const connectWebSocket = () => {
	const websocketUrl = getWebSocketUrl();
	if ('WebSocket' in window) {
		iatWS = new WebSocket(websocketUrl);
	} else if ('MozWebSocket' in window) {
		iatWS = new MozWebSocket(websocketUrl);
	} else {
		showToast({
			position: 'top',
			message: '浏览器不支持WebSocket',
		});
		voiceMsg.value = '按住说话询问课程';
		voiceText.value = '';
		return;
	}
	changeBtnStatus('CONNECTING');
	let time = 0;
	iatWS.onopen = () => {
		time = new Date().getTime();
		console.log('---------- 开始录音 -------');

		// 开始录音
		recorder.start({
			sampleRate: 16000,
			frameSize: 1280,
		});

		var params = {
			common: {
				app_id: APPID,
			},
			business: {
				language: 'zh_cn',
				domain: 'iat',
				accent: 'mandarin',
				vad_eos: 3000,
				dwa: 'wpgs',
			},
			data: {
				status: 0,
				format: 'audio/L16;rate=16000',
				encoding: 'raw',
			},
		};
		iatWS.send(JSON.stringify(params));
	};

	iatWS.onmessage = (e) => {
		renderResult(e.data);
	};
	iatWS.onerror = (e) => {
		emits('_changeVoice', false);
		console.error(e);
		recorder.stop();
		changeBtnStatus('CLOSED');
	};
	iatWS.onclose = (e) => {
		console.log('close', new Date().getTime() - time);
		emits('_changeVoice', false);
		console.error(e);
		recorder.stop();
		changeBtnStatus('CLOSED');
	};
};

const renderResult = (resultData) => {
	console.log('---------- 返回文字 -------');

	// 识别结束
	let jsonData = JSON.parse(resultData);
	if (jsonData.data && jsonData.data.result) {
		let data = jsonData.data.result;
		let str = '';
		let ws = data.ws;
		for (let i = 0; i < ws.length; i++) {
			str = str + ws[i].cw[0].w;
		}
		if (data.pgs) {
			if (data.pgs === 'apd') {
				// 将resultTextTemp同步给resultText
				resultText = resultTextTemp;
			}
			// 将结果存储在resultTextTemp中
			resultTextTemp = resultText + str;
		} else {
			resultText = resultText + str;
		}

		voiceText.value = resultTextTemp || resultText || '';
		emits('_changeVoice', true, voiceText.value, true);
	}
	if (jsonData.code === 0 && jsonData.data.status === 2) {
		if (isSend) {
			emits('_sendMessage', voiceText.value);
			isSend = false;
			emits('_changeVoice', false);
		}
		iatWS.close();
	}
	if (jsonData.code !== 0) {
		iatWS.close();
		console.error(jsonData);
		emits('_changeVoice', false);
	}
};

const getWebSocketUrl = () => {
	// 请求地址根据语种不同变化
	var url = 'wss://iat-api.xfyun.cn/v2/iat';
	var host = location.host;
	var apiKey = API_KEY;
	var apiSecret = API_SECRET;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/iat HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};

const changeBtnStatus = (status) => {
	btnStatus = status;
};
const toBase64 = (buffer) => {
	var binary = '';
	var bytes = new Uint8Array(buffer);
	var len = bytes.byteLength;
	for (var i = 0; i < len; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	return window.btoa(binary);
};
recorder.onStart = () => {
	emits('_changeVoice', true, '', true);
	changeBtnStatus('OPEN');
};
recorder.onStop = () => {
	voiceMsg.value = '按住说话询问课程';
};

recorder.onFrameRecorded = ({ isLastFrame, frameBuffer }) => {
	if (iatWS.readyState === iatWS.OPEN) {
		iatWS.send(
			JSON.stringify({
				data: {
					status: isLastFrame ? 2 : 1,
					format: 'audio/L16;rate=16000',
					encoding: 'raw',
					audio: toBase64(frameBuffer),
				},
			})
		);
		if (isLastFrame) {
			changeBtnStatus('CLOSING');
		}
	}
};
</script>
<style lang="scss" scoped>
.chat-bottom-voice {
	display: flex;
	justify-content: space-around;
	align-items: center;
	&__stop {
		img {
			width: 40px;
		}
	}
	&__start {
		text-align: center;
		flex: 0 0 40%;
		img {
			width: 63px;
		}
		.start-box {
			width: 63px;
			height: 63px;
			border-radius: 50%;
			display: inline-block;
			text-align: center;
			line-height: 63px;
			background-color: #0052cc;
			color: #fff;
		}
	}
	&__key {
		img {
			width: 40px;
		}
	}
}
.chat-bottom {
	position: absolute;
	left: 0;
	// bottom: 6px;
	z-index: 99;
	display: flex;
	align-items: center;
	border-radius: 6px;
	padding: 0 10px 0 0;
	border: 1px solid #dfe1e6;
	justify-content: space-between;
	height: 50px;
	margin-left: 15px;
	width: calc(100% - 30px);
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
	.chat-bottom-middle {
		height: 100%;
		flex: 1;
		padding-left: 10px;
		display: flex;
		align-items: center;
		.chat-bottom-btn {
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			user-select: none;
			color: #172b4d;
			span {
				padding-left: 134px;
			}
		}

		.loading-img {
			width: 80%;
			margin-left: 10%;
			pointer-events: none;
			user-select: none;
			-webkit-user-select: none;
		}
	}
	.chat-bottom-input {
		flex: 1;
		padding-left: 10px;
		&:deep(.van-field) {
			background-color: #ffffff;
			border-radius: 6px;
			padding-bottom: 8px;
			padding-top: 8px;
			height: 40px;
			padding-left: 3px;
			padding-right: 3px;
		}
		&:deep(.van-field__control) {
			color: var(--pv-default-color);
		}
	}

	img {
		width: 29px;
		height: 29px;
		object-fit: contain;
		margin-left: 15px;
	}
}

.azsh {
	background-color: #ffffff;
}
.shqx {
	background-color: #0052cc;
}
.ssqx {
	background-color: #ff3e3e;
}

.bg-shadow {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	height: calc(100% - constant(safe-area-inset-bottom));
	height: calc(100% - env(safe-area-inset-bottom));
	z-index: 98;
	background: url(../../assets/img/demo/bg-shadow.png) no-repeat;
	background-size: cover;
	backdrop-filter: blur(3px);
	-webkit-backdrop-filter: blur(3px);
	display: flex;
	justify-content: flex-end;
	align-items: flex-end;
	flex-direction: column;

	.bg-shadow-content {
		background-color: #0052cc;
		border-radius: 5px 5px 0px 5px;
		padding: 12px;
		margin-right: 15px;
		margin-bottom: 114px;
		color: #ffffff;
		max-width: 250px;
		word-break: break-all;

		.q-loading,
		.q-loading > div {
			position: relative;
			box-sizing: border-box;
		}
		.q-loading {
			display: inline-block;
			font-size: 0;
			color: #fff;
		}
		.q-loading.la-dark {
			color: #ffffff;
		}
		.q-loading > div {
			display: inline-block;
			float: none;
			background-color: currentColor;
			border: 0 solid currentColor;
		}

		.q-loading > div {
			width: 6px;
			height: 6px;
			margin-right: 5px;
			border-radius: 100%;
			animation: ball-beat 1.5s -0.3s infinite linear;
		}
		.q-loading > div:last-child {
			margin-right: 0;
		}
		.q-loading > div:nth-child(2n-1) {
			animation-delay: -1s;
		}
	}

	.bg-shadow-status {
		color: #6b778c;
		font-size: 12px;
		width: 100%;
		text-align: center;
		margin-bottom: 58px;
	}
}

// 梦层动画
.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.2s ease;
}
.fade-enter-from,
.fade-leave-to {
	opacity: 0;
}
// loading
@keyframes ball-beat {
	50% {
		opacity: 0.2;
		transform: scale(0.7);
	}

	100% {
		opacity: 1;
		transform: scale(1);
	}
}
</style>
