<template>
	<div class="aq">
		<div style="display: flex; align-items: center; justify-content: space-between">
			<span>{{ props.itemData.msg }}</span>
			<slot></slot>
		</div>
		<p v-for="(item, index) in props.itemData.questionList" :key="index" @click="quesEv(item)" class="aq-item">
			<span style="font-size: 12px">{{ item }}</span>
			<van-icon name="arrow" />
		</p>
	</div>
</template>
<script setup>
const props = defineProps(['itemData']);
const emits = defineEmits(['quesEv']);
const quesEv = (item) => {
	const json = {
		doctorId: props.itemData.doctorId,
		msg: item,
	};
	emits('quesEv', json);
};
</script>
<style lang="scss" scoped>
.aq {
	width: 280px;
	display: flex;
	flex-direction: column;
	color: var(--pv-default-color);
	.aq-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: var(--ac-bg-color);
		padding: 6px;
		border-radius: 5px;
		margin-top: 6px;
	}
}
</style>
