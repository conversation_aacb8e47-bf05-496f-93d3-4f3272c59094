<template>
	<van-popup :safe-area-inset-top="true" :safe-area-inset-bottom="true" :close-on-click-overlay="false" :close-on-popstate="true" style="height: 100%; width: 100%; max-width: 100%" v-model:show="show">
		<div class="content-preview">
			<!-- header -->
			<div class="content-preview__header">
				<div class="header-left">
					<span>知识库引用({{ props.contentList.length }})条</span>
					<span class="header-left__desc">此处仅显示引用内容，不会实时更新</span>
				</div>
				<div @click="show = false">
					<van-icon color="var(--ac-bg-active-color)" size="20" name="clear" />
				</div>
			</div>
			<!-- content -->
			<div class="content-preview__content">
				<div class="content-item" v-for="item in props.contentList" :key="item.num">
					<!-- 综合排名 -->
					<van-tag color="var(--ac-bg-active-color)" size="medium">引用片段｜#{{ item.num }}</van-tag>
					<!-- 内容 -->
					<div v-html="item.q" class="item-text"></div>
					<!-- 文件名称 -->
					<div @click="previewPdf(item)" class="item-name">
						<img src="@/assets/img/demo/pdf.png" />
						<span class="ell">{{ item.sourceName }}</span>
					</div>
				</div>
			</div>
		</div>
	</van-popup>
</template>
<script setup>
const props = defineProps(['contentList']);
const show = ref(false);
const emits = defineEmits(['preview-file']);
const previewPdf = (item) => {
	emits('preview-file', item);
};
defineExpose({
	show,
});
</script>
<style lang="scss" scoped>
.content-preview {
	display: flex;
	flex-direction: column;
	height: 100%;
	padding: 12px;

	.content-preview__header {
		display: flex;
		align-items: center;
		padding-bottom: 12px;

		.header-left {
			flex: 1;
			display: flex;
			flex-direction: column;

			.header-left__desc {
				font-size: 12px;
				color: var(--ac-colors-myGray-500);
			}
		}
	}

	.content-preview__content {
		flex: 1;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		.content-item {
			border: solid 1px rgb(223, 226, 234);
			padding: 8px;
			margin-bottom: 12px;
			border-radius: 6px;

			.item-text {
				margin-top: 8px;
				font-size: 13px;
				word-wrap: break-word;
				color: #172b4d;
			}

			.item-name {
				display: flex;
				align-items: center;
				margin-top: 8px;
				margin-bottom: 4px;
				img {
					width: 14px;
					margin-right: 3px;
				}
				span {
					font-size: 13px;
					flex: 1;
					border-bottom: solid 1px #172b4d;
					font-weight: 500;
				}
			}
		}
	}
}
</style>
