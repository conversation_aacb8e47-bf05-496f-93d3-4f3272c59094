<template>
	<van-popup teleport="body" class="q_custom-popup" :close-on-click-overlay="true" :safe-area-inset-bottom="true" position="bottom" v-model:show="show">
		<!-- header -->
		<div class="c-header" id="ap-c-header">
			<!-- title -->
			<span class="c-header__title">请选择</span>
			<!-- 确认 -->
			<span @click="confirm" class="c-header__confirm">确认</span>
		</div>
		<!-- 快捷选项 -->
		<div class="c-search" id="ap-c-search">
			<!-- 当月 -->
			<div>
				<span>当月</span>
				<van-switch @click="cMonthChage" size="24" v-model="cMonth" inactive-color="var(--pv-filter-bgc)" active-color="var(--pv-tabbar-active)" />
			</div>
			<!-- 当季 -->
			<div>
				<span>当季</span>
				<van-switch @click="cJChage" size="24" v-model="cJ" inactive-color="var(--pv-filter-bgc)" active-color="var(--pv-tabbar-active)" />
			</div>
			<!-- 当年 -->
			<div>
				<span>当年</span>
				<van-switch @click="cYearChage" size="24" v-model="cYear" inactive-color="var(--pv-filter-bgc)" active-color="var(--pv-tabbar-active)" />
			</div>
		</div>
		<!-- 日历列表 -->
		<div class="c-data" id="ap-c-data">
			<div v-for="item in timeList" :key="item.value" class="c-data-item">
				<!-- title -->
				<div class="c-data-title">{{ item.text }}</div>
				<!-- 日历 -->
				<div class="c-data-content">
					<div v-for="i in item.children" :key="i.text" class="content-item">
						<span
							v-for="s in i.children"
							:key="s.value"
							:class="{ 'content-item-active': s.isActive && (s.isStart || s.isFinite), 'content-item-zj-active': s.isActive && !s.isFinite && !s.isStart, 'content-item-s-radius-active': s.isActive && s.isStart, 'content-item-e-radius-active': s.isActive && s.isFinite }"
							@click="setDate(s)"
						>
							{{ s.text }}
							<span v-if="s.isStart && !s.isFinite">开始</span>
							<span v-if="s.isFinite && !s.isStart">结束</span>
							<span v-if="s.isFinite && s.isStart">起/终</span>
						</span>
					</div>
				</div>
			</div>
		</div>
	</van-popup>
</template>
<script setup>
import { getMonthBetween, isInSameQuarter } from '@/utils';

import { showToast } from 'vant';
const props = defineProps(['_startDate', '_endDate']);

const show = ref(false);
const cMonth = ref(false);
const cJ = ref(false);
const cYear = ref(false);

const emits = defineEmits(['_confirm']);

watch(
	() => [props._startDate, props._endDate, show.value],
	([newStartDate, newEndDate, show]) => {
		if (newStartDate || newEndDate) {
			// 根据传入的时间范围，高亮日期
			setActiveTimeData();
		}
	}
);

// 选中的开始和结束时间
const query = reactive({
	sDate: '',
	eDate: '',
});

// 数据列表
const timeList = ref([]);

onMounted(() => {
	initData();
});

// 初始化最近两年的日期数据
const initData = () => {
	const yearNum = new Date().getFullYear();
	timeList.value.push({
		text: yearNum - 1 + '',
		value: yearNum - 1 + '',
		children: [
			{
				text: '1',
				children: [
					{
						text: '1',
						value: '01',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '2',
						value: '02',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '3',
						value: '03',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '4',
						value: '04',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '5',
						value: '05',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '6',
						value: '06',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
				],
			},
			{
				text: '2',
				children: [
					{
						text: '7',
						value: '07',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '8',
						value: '08',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '9',
						value: '09',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '10',
						value: '10',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '11',
						value: '11',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '12',
						value: '12',
						parent: yearNum - 1 + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
				],
			},
		],
	});
	timeList.value.push({
		text: yearNum + '',
		value: yearNum + '',
		children: [
			{
				text: '1',
				children: [
					{
						text: '1',
						value: '01',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '2',
						value: '02',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '3',
						value: '03',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '4',
						value: '04',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '5',
						value: '05',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '6',
						value: '06',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
				],
			},
			{
				text: '2',
				children: [
					{
						text: '7',
						value: '07',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '8',
						value: '08',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '9',
						value: '09',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '10',
						value: '10',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '11',
						value: '11',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
					{
						text: '12',
						value: '12',
						parent: yearNum + '',
						isActive: false,
						isStart: false,
						isFinite: false,
					},
				],
			},
		],
	});
	if (props._startDate) {
		// 根据传入的时间范围，高亮日期
		setActiveTimeData();
	}
};

const setActiveTimeData = () => {
	let durTime = getMonthBetween(props._startDate.substring(0, 4) + '-' + props._startDate.slice(-2), props._endDate.substring(0, 4) + '-' + props._endDate.slice(-2));
	if (durTime.length > 0) {
		cMonth.value = false;
		cYear.value = false;
		cJ.value = false;
		query.sDate = '';
		query.eDate = '';
		clearData();
		// 处理开始时间标记
		startTimeActive(durTime[0]);
		// 处理结束时间标记
		endTimeActive(durTime[durTime.length - 1]);
		// 高亮对应月份
		timeActive(durTime);
		// 是否需要开启便捷按钮
		isConventBtn(durTime);

		query.sDate = props._startDate;
		query.eDate = props._endDate;
	}
};

const startTimeActive = (i) => {
	let y = i.split('-')[0];
	let m = i.split('-')[1];
	if (y === timeList.value[0].value) {
		let s = timeList.value[0].children;
		for (const item of s) {
			for (const iterator of item.children) {
				if (iterator.value === m) {
					iterator.isStart = true;
					break;
				}
			}
		}
	} else {
		let s = timeList.value[1].children;
		for (const item of s) {
			for (const iterator of item.children) {
				if (iterator.value === m) {
					iterator.isStart = true;
					break;
				}
			}
		}
	}
};

const endTimeActive = (i) => {
	let y = i.split('-')[0];
	let m = i.split('-')[1];
	if (y === timeList.value[0].value) {
		let s = timeList.value[0].children;
		for (const item of s) {
			for (const iterator of item.children) {
				if (iterator.value === m) {
					iterator.isFinite = true;
					break;
				}
			}
		}
	} else {
		let s = timeList.value[1].children;
		for (const item of s) {
			for (const iterator of item.children) {
				if (iterator.value === m) {
					iterator.isFinite = true;
					break;
				}
			}
		}
	}
};

const timeActive = (l) => {
	for (const key in l) {
		let y = l[key].split('-')[0];
		let m = l[key].split('-')[1];
		if (y === timeList.value[0].value) {
			let s = timeList.value[0].children;
			for (const item of s) {
				for (const iterator of item.children) {
					if (iterator.value === m) {
						iterator.isActive = true;
						break;
					}
				}
			}
		} else {
			let s = timeList.value[1].children;
			for (const item of s) {
				for (const iterator of item.children) {
					if (iterator.value === m) {
						iterator.isActive = true;
						break;
					}
				}
			}
		}
	}
};

const isConventBtn = (l) => {
	let y = new Date().getFullYear() + '';
	let m = new Date().getMonth() + 1;
	if (m < 10) m = '0' + m;
	if (l.length === 1 && l[0] === y + '-' + m) {
		cMonth.value = true;
	} else if (l.length === 12) {
		const s = l.every((i) => i.split('-')[0] === y);
		cYear.value = s;
	} else if (l.length === 3) {
		const s = isInSameQuarter(Number(l[0].split('-')[1]), Number(l[1].split('-')[1]), Number(l[2].split('-')[1]));
		cJ.value = s;
	}
};

// 确认
const confirm = () => {
	if (query.sDate && query.eDate) {
		show.value = false;
		emits('_confirm', { _startDate: query.sDate, _endDate: query.eDate });
	} else {
		showToast({
			message: '请选择开始和结束日期',
			position: 'top',
		});
	}
};

// 当月
const cMonthChage = () => {
	clearData();
	query.sDate = '';
	query.eDate = '';
	if (cMonth.value) {
		cJ.value = false;
		cYear.value = false;
		// 让当月选中
		const cy = new Date().getFullYear() + '';
		let cm = new Date().getMonth() + 1 + '';
		const item = timeList.value.filter((item) => item.value === cy);
		let l = item[0].children[0];
		if (cm > 6) l = item[0].children[1];
		l.children.forEach((item) => {
			if (item.text === cm) {
				item.isActive = true;
				item.isStart = true;
				item.isFinite = true;
			} else {
				item.isActive = false;
				item.isStart = false;
				item.isFinite = false;
			}
		});
		cm = cm < 10 ? '0' + cm : cm + '';
		query.sDate = cy + '' + cm;
		query.eDate = cy + '' + cm;
	}
};

//当季
const cJChage = () => {
	clearData();
	query.sDate = '';
	query.eDate = '';
	if (cJ.value) {
		cMonth.value = false;
		cYear.value = false;
		// 让当季选中
		const y = new Date().getFullYear() + '';
		// 获取当前季节的三个月份
		let m1 = new Date().getMonth() + 1;
		let m2 = null;
		let m3 = null;
		if ([2, 5, 8, 11].includes(m1)) {
			m2 = m1;
			m3 = m1 + 1;
			m1 = m1 - 1;
		} else if ([1, 4, 7, 10].includes(m1)) {
			m2 = m1 + 1;
			m3 = m1 + 2;
		} else {
			m3 = m1;
			m2 = m1 - 1;
			m1 = m1 - 2;
		}
		m1 = m1 < 10 ? '0' + m1 : m1 + '';
		m2 = m2 < 10 ? '0' + m2 : m2 + '';
		m3 = m3 < 10 ? '0' + m3 : m3 + '';
		timeActive([y + '-' + m1, y + '-' + m2, y + '-' + m3]);
		startTimeActive(y + '-' + m1);
		endTimeActive(y + '-' + m3);

		query.sDate = y + '' + m1;
		query.eDate = y + '' + m3;
	}
};

// 当年
const cYearChage = () => {
	clearData();
	query.sDate = '';
	query.eDate = '';
	if (cYear.value) {
		cJ.value = false;
		cMonth.value = false;
		// 让当年选中
		const y = new Date().getFullYear() + '';
		startTimeActive(y + '-01');
		endTimeActive(y + '-12');
		timeActive([y + '-01', y + '-02', y + '-03', y + '-04', y + '-05', y + '-06', y + '-07', y + '-08', y + '-09', y + '-10', y + '-11', y + '-12']);

		query.sDate = y + '01';
		query.eDate = y + '12';
	}
};

// 全部清除
const clearData = () => {
	timeList.value.forEach((item) => {
		item.children.forEach((item) => {
			item.children.forEach((item) => {
				item.isActive = false;
				item.isStart = false;
				item.isFinite = false;
			});
		});
	});
};
// 点击选中
const setDate = (item) => {
	cJ.value = false;
	cYear.value = false;
	cMonth.value = false;
	clearData();
	if (query.sDate !== '' && query.eDate !== '') {
		query.sDate = '';
		query.eDate = item.parent + '' + item.value;
		item.isActive = true;
		item.isFinite = true;
	} else if (query.sDate === '' && query.eDate === '') {
		query.sDate = '';
		query.eDate = item.parent + '' + item.value;
		item.isActive = true;
		item.isFinite = true;
	} else {
		// 第二次选中的时间大于第一次选中的时间，需要交换一下变量
		let l = item.parent + '' + item.value;
		if (Number(l) > Number(query.eDate)) {
			query.sDate = query.eDate;
			query.eDate = l;
		} else {
			query.sDate = l;
		}
		let durTime = getMonthBetween(query.sDate.substring(0, 4) + '-' + query.sDate.slice(-2), query.eDate.substring(0, 4) + '-' + query.eDate.slice(-2));
		if (durTime.length > 0) {
			// 处理开始时间标记
			startTimeActive(durTime[0]);
			// 处理结束时间标记
			endTimeActive(durTime[durTime.length - 1]);
			// 高亮对应月份
			timeActive(durTime);
		}
	}
};
const resetActive = () => {
	timeList.value.forEach((ele) => {
		ele.children.forEach((el) => {
			console.log(el);
			el.children.forEach((e) => {
				e.isActive = false;
			});
		});
	});
};
defineExpose({
	show,
	resetActive,
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/pickerFilter/datePickerMultery.scss';
.c-header {
	padding: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.c-header__title {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-default-color);
	}

	.c-header__confirm {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-tabbar-active);
	}
}

.c-search {
	margin: 0 35px;
	padding-bottom: 12px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	div {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		span {
			font-size: 16px;
			color: var(--pv-default-color);
			padding-bottom: 8px;
		}
	}
}

.c-data {
	display: flex;
	flex-direction: column;
	.c-data-item {
		display: flex;
		flex-direction: column;
		.c-data-title {
			height: 40px;
			background-color: var(--pv-card-bgc);
			color: var(--pv-default-color);
			font-size: 16px;
			font-weight: 500;
			line-height: 40px;
			padding-left: 25px;
		}

		.c-data-content {
			display: flex;
			flex-direction: column;
			padding: 15px 21px;
			.content-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 1px;
			}
			span {
				font-size: 16px;
				font-weight: 500;
				display: flex;
				flex: 1;
				aspect-ratio: 1;
				height: auto;
				align-items: center;
				justify-content: center;
				position: relative;
				color: var(--pv-default-color);
				span {
					position: absolute;
					color: var(--pv-bgc) !important;
					font-size: 9px;
					font-weight: bolder;
					bottom: 3px;
					font-weight: 400;
				}
			}

			.content-item-active {
				background-color: var(--pv-tabbar-active);
				color: var(--pv-bgc);
			}
			.content-item-zj-active {
				background-color: var(--pv-fliter-calendar-bgc);
				color: var(--pv-tabbar-active);
			}

			.content-item-s-radius-active {
				border-top-left-radius: 5px;
				border-bottom-left-radius: 5px;
			}
			.content-item-e-radius-active {
				border-top-right-radius: 5px;
				border-bottom-right-radius: 5px;
			}
		}
	}
}
</style>
