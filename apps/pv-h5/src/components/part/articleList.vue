<template>
	<div class="amd">
		<!-- header -->
		<div class="amd-header">
			<span>请选择{{ props.title }}</span>
			<van-icon size="16" @click="close" name="cross" />
		</div>
		<van-tabs title-inactive-color="var(--pv-no-active-color)" title-active-color="var(--pv-default-color)" color="#0052cc" :line-width="25" :line-height="2" v-model:active="treeActive" @change="treeChange" shrink>
			<van-tab v-for="val in contentList" :key="val.path" :title="val.name" :name="val.path"></van-tab>
		</van-tabs>
		<!-- 搜索框 -->
		<div class="amd-search">
			<form action="/">
				<van-search v-model="query.search" left-icon="search" @search="searchEv" :clearable="false" placeholder="请输入关键字搜索" />
			</form>
		</div>

		<!-- 文章列表 -->
		<div class="amd-content">
			<template v-if="statusLoading === 'init'">
				<div v-for="item in [1, 2, 3]" :key="item" style="width: 100%; margin-bottom: 12px">
					<van-skeleton title :row="2" />
				</div>
			</template>
			<template v-else-if="statusLoading === 'empty'">
				<van-empty description="暂无内容" />
			</template>
			<template v-else>
				<van-list :immediate-check="false" v-model:loading="loading" v-model:error="error" :finished="finished" finished-text="没有更多了~~~" error-text="请求失败，点击重新加载" @load="onLoad">
					<div v-for="item in articleList" :key="item.id" class="amd-content-item">
						<!-- 左侧 checkbox -->
						<van-checkbox @click="checkEv(item)" shape="square" icon-size="16px" :disabled="item?.articleVO?.contentLabel.includes('内部资料')" checked-color="var(--ac-font-color-active)" v-model="item.checked"></van-checkbox>
						<!-- 右侧文章信息 -->
						<div class="amd-content-item-desc" @click="articleDetail(item)">
							<img v-if="item?.articleVO?.thumbMediaUrl" class="desc-img" :src="item?.articleVO?.thumbMediaUrl" />
							<div class="desc-info">
								<div class="desc-info-title ell2">{{ item?.articleVO?.title }}</div>
								<!-- 标签信息 -->
								<div v-if="item?.articleVO?.contentLabel.length > 0" class="desc-info-tag">
									<van-tag color="#0052cc" v-for="tag in item?.articleVO?.contentLabel" :key="tag" type="primary">{{ tag }}</van-tag>
								</div>
								<div class="desc-info-other">
									{{ item?.articleVO?.source }}
									{{ item?.articleVO?.publishDate.split(' ')[0] }}
								</div>
							</div>
						</div>
					</div>
				</van-list>
			</template>
		</div>
		<!-- 确定事件 -->
		<div class="amd-confirm">
			<van-button @click="confirm" style="width: 60%; background-color: #0052cc" type="primary" round>确定</van-button>
		</div>
	</div>
</template>
<script setup>
import { getArticleInterface } from '@/api/user';
import useUserStore from '@/store/modules/user';
import { showToast } from 'vant';
const emits = defineEmits(['close', '_activeArticle', '_articleDetail']);
const props = defineProps(['title']);
const userStore = useUserStore();
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
const treeActive = ref('');
const contentList = ref([]);
watch(
	() => props.title,
	(val) => {
		if (val) {
			nextTick(() => {
				init();
			});
		}
	},
	{
		immediate: true,
	}
);
const treeChange = (val) => {
	treeActive.value = val;
	query.channelId = val;
	query.page = 0;
	query.total = 0;
	loading.value = false;
	finished.value = false;
	error.value = false;
	statusLoading.value = 'init';
	articleList.value = [];
	getArticleList(query);
};
const query = reactive({
	channelId: '',
	realm: '',
	page: 0,
	size: 10,
	total: 0,
	sort: 'publishDate,desc',
	searchValue: '',
	searchKey: 'title,author,digest,product.name,field.name',
});
const articleList = ref([]);
const statusLoading = ref(null);
const loading = ref(false);
const finished = ref(false);
const error = ref(false);

const close = () => {
	emits('close');
};
const getArticleList = (data) => {
	getArticleInterface(data)
		.then((res) => {
			for (const item of res.result.content) {
				item.checked = false;
			}
			articleList.value = articleList.value.concat(res.result.content);
			query.total = res.result.totalElements;
			if (articleList.value.length >= query.total) {
				if (query.total === 0) {
					statusLoading.value = 'empty';
				} else {
					statusLoading.value = 'done';
					finished.value = true;
				}
			} else {
				statusLoading.value = 'doing';
			}
		})
		.catch(() => {
			statusLoading.value = 'empty';
			articleList.value = [];
		});
};

const onLoad = () => {
	query.page++;
	loading.value = true;
	error.value = false;
	getArticleInterface(query)
		.then((res) => {
			for (const item of res.result.content) {
				item.checked = false;
			}
			articleList.value = articleList.value.concat(res.result.content);
			query.total = res.result.totalElements;
			if (articleList.value.length >= query.total) finished.value = true;
		})
		.catch(() => {
			error.value = true;
			query.page--;
		})
		.finally(() => {
			loading.value = false;
		});
};

// 搜索事件
const searchEv = () => {
	query.page = 0;
	query.total = 0;
	loading.value = false;
	finished.value = false;
	error.value = false;
	query.searchValue = query.search;
	statusLoading.value = 'init';
	articleList.value = [];
	getArticleList(query);
};

const checkEv = (item) => {
	if (item.checked) {
		for (const i of articleList.value) {
			if (i.articleVO.id === item.articleVO.id) {
				continue;
			} else {
				i.checked = false;
			}
		}
	}
};

// 文章详情
const articleDetail = (item) => {
	emits('_articleDetail', item.articleVO.contentSourceUrl);
};

const init = () => {
	statusLoading.value = 'init';
	loading.value = false;
	finished.value = false;
	error.value = false;
	query.page = 0;
	query.searchValue = '';
	query.realm = userStore.userInfo?.enterpriseVO?.name;
	articleList.value = [];
	const list = perStore.asyncRouters.find((item) => item.path === 'content').children || [];
	contentList.value = list.filter((item) => item.type === 'TAG');
	treeActive.value = contentList.value[0].path;
	query.channelId = contentList.value[0].path;
	getArticleList(query);
};

const confirm = () => {
	const ac = articleList.value.filter((ele) => ele.checked === true);
	if (ac.length > 0) {
		emits('_activeArticle', ac[0].articleVO);
		close();
	} else {
		showToast({
			position: 'top',
			message: '请选择要推送的文章',
		});
	}
};
</script>
<style lang="scss" scoped>
.amd {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.amd-header {
		display: flex;
		align-items: center;
		padding: 0 12px;
		justify-content: space-between;
		span {
			font-weight: 500;
			font-size: 16px;
		}
	}
	.amd-search {
		width: 100%;
		&:deep(.van-field__control) {
			color: var(--ac-font-color);
		}
	}

	.amd-content {
		width: 100%;
		flex: 1;
		padding: 0 12px;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		.amd-content-item {
			padding: 12px 0;
			border-bottom: 1px solid var(--ac-border-color);
			display: flex;
			align-items: center;

			.amd-content-item-desc {
				display: flex;
				flex: 1;
				margin-left: 8px;
				align-items: center;
				.desc-img {
					width: 100px;
					height: 66px;
					border-radius: 3px;
					margin-right: 8px;
					object-fit: cover;
				}

				.desc-info {
					display: flex;
					flex-direction: column;
					flex: 1;
					.desc-info-title {
						color: var(--ac-font-color);
						width: 100%;
						font-size: 14px;
					}
					.desc-info-tag {
						span {
							margin-right: 3px;
						}
					}
					.desc-info-other {
						display: flex;
						align-items: center;
						font-size: 12px;
						margin-top: 8px;
						color: var(--ac-colors-myGray-500);
					}
				}
			}
		}
	}

	.amd-confirm {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 16px;
		font-size: 15px;
	}
}
</style>
