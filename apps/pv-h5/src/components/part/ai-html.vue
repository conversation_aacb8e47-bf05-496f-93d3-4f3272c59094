<template>
	<div v-html="msg" @click="aa"></div>
</template>
<script setup>
const props = defineProps(['msg']);
let router = useRouter();
const route = useRoute();
const emit = defineEmits(['to360']);
const aa = (e) => {
	console.log(e);
	if (e.target.className.includes('toPlan')) {
		router.push({
			name: 'keyTaskDetail',
			query: {
				isCreateTask: 'create',
				taskType: '线下拜访',
				isTabBar: route.query.isTabBar || '',
			},
		});
	}
	if (e.target.className.includes('toArticle')) {
		router.push({ name: 'visitArticle' });
	}
	if (e.target.className.includes('toMeet')) {
		router.push({ name: 'toMeet' });
	}
	if (e.target.className.includes('toReport')) {
		router.push({ name: 'toReport' });
	}
	if (e.target.getAttribute('doctorName')) {
		emit('to360', e.target.getAttribute('doctorName'));
	}
};
</script>
<style lang="scss" scoped></style>
