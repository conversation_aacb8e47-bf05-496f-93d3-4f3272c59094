<template>
	<van-popup teleport="body" :safe-area-inset-top="true" :safe-area-inset-bottom="true" v-model:show="showLeft" position="left" :style="{ width: isPc ? '375px' : '84%', height: '100%' }">
		<div class="chs">
			<!-- 搜索 -->
			<div class="chs-search">
				<form action="/">
					<van-search v-model="searchValue" left-icon="" @search="onSearch" @clear="onSearch" :placeholder="$t('common.search')" />
				</form>
				<img src="@/assets/img/search.png" />
			</div>
			<!-- 内容列表 -->
			<div v-if="dataList.length > 0" class="chs-content">
				<!--时间段 -->
				<div v-for="val in dataList" :key="val.name" class="chs-contet-time">
					<span class="time-title">{{ val.sName }}</span>
					<!-- 会话列表 -->
					<div class="chs-chat-list">
						<span v-for="s in val.children" :key="s.chatId" @click="setHistoryItem(s)" class="chat-item ell">{{ s.title || s.description }}</span>
					</div>
				</div>
			</div>
			<van-empty v-else :description="$t('history.noMore')" />
		</div>
	</van-popup>
</template>
<script setup>
const showLeft = ref(false);
const searchValue = ref('');
const props = defineProps(['historyList']);
const emits = defineEmits(['_openChat']);
const { proxy } = getCurrentInstance();

const isPc = ref(!/Android|iPhone|SymbianOS|Windows Phone|iPad|iPod/i.test(navigator.userAgent));

// 数据列表
const dataList = ref([]);
let sourceList = [];
watch(
	() => props.historyList,
	() => {
		nextTick(() => {
			sourceList = JSON.parse(JSON.stringify(props.historyList));
			if (searchValue.value) {
				filtersHistoryList(searchValue.value);
			} else {
				dataList.value = props.historyList;
			}
		});
	}
);

// 搜索事件
const onSearch = () => {
	// 根据搜索内容过滤props.historyList里的children
	if (searchValue.value) {
		proxy.$umeng('搜索', '历史会话', searchValue.value);
		filtersHistoryList(searchValue.value);
	} else {
		dataList.value = props.historyList;
	}
};

const filtersHistoryList = (val) => {
	dataList.value = [];
	for (const ite of sourceList) {
		ite.children = ite.children.filter((item) => item.title.includes(val));
		if (ite.children.length > 0) {
			dataList.value.push(ite);
		}
	}
	sourceList = JSON.parse(JSON.stringify(props.historyList));
};

// 选中事件
const setHistoryItem = (s) => {
	showLeft.value = false;
	proxy.$umeng('点击', '历史会话', s.title || s.description);
	emits('_openChat', {
		chatType: s.chatType || '',
		chatId: s.chatId || s.id,
		appName: s.doctor_id || '',
	});
};

defineExpose({
	showLeft,
});
</script>
<style lang="scss" scoped>
.chs {
	padding: 16px 0;
	display: flex;
	flex-direction: column;
	height: 100%;

	.chs-search {
		padding: 0 16px;
		margin-bottom: 20px;
		position: relative;
		&:deep(.van-search) {
			padding: 0;
		}
		&:deep(.van-field__control) {
			padding-left: 22px;
		}
		&:deep(.van-field__control::placeholder) {
			color: #6b778c;
			font-size: 14px;
		}
		img {
			width: 14px;
			height: 14px;
			position: absolute;
			top: 10px;
			left: 28px;
		}
	}

	.chs-content {
		flex: 1;
		overflow-y: scroll;

		.chs-contet-time {
			display: flex;
			flex-direction: column;

			.time-title {
				font-size: 14px;
				color: #333333;
				margin-bottom: 12px;
				padding: 0 16px;
				font-weight: 500;
			}

			.chs-chat-list {
				display: flex;
				flex-direction: column;
				width: 100%;
				margin-bottom: 12px;

				.chat-item {
					font-size: 14px;
					margin-bottom: 8px;
					margin-top: 8px;
					padding: 0px 24px;
					color: #6b778c;
				}
				.chat-item:active {
					background-color: #f4f5f7;
					border-radius: 5px;
				}
			}
		}
	}
}

/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	.chs {
		padding: 16px 0;

		.chs-search {
			padding: 0 16px;
			margin-bottom: 20px;
			&:deep(.van-search) {
				padding: 0;
			}
			&:deep(.van-field__control) {
				padding-left: 22px;
			}
			&:deep(.van-field__control::placeholder) {
				font-size: 14px;
			}
			img {
				width: 14px;
				height: 14px;
				top: 10px;
				left: 28px;
			}
		}

		.chs-content {
			.chs-contet-time {
				.time-title {
					font-size: 14px;
					margin-bottom: 12px;
					padding: 0 16px;
				}

				.chs-chat-list {
					margin-bottom: 12px;

					.chat-item {
						font-size: 14px;
						margin-bottom: 8px;
						margin-top: 8px;
						padding: 0px 24px;
					}
					.chat-item:active {
						border-radius: 5px;
					}
				}
			}
		}
	}
}
</style>
