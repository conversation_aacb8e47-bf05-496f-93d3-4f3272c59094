<template>
	<div class="amd">
		<!-- header -->
		<div class="amd-header">
			<span>医生360</span>
			<van-icon size="16" @click="close" name="cross" />
		</div>
		<!-- 我的医生和目标医生切换 -->
		<van-tabs title-inactive-color="var(--pv-no-active-color)" title-active-color="var(--pv-default-color)" color="var(--pv-tabbar-active)" :line-width="25" :line-height="2" v-model:active="treeActive" @change="treeChange" shrink>
			<van-tab title="我的医生"></van-tab>
			<van-tab title="全部医生"></van-tab>
		</van-tabs>
		<!-- 搜索框 -->
		<div class="amd-search">
			<form action="/">
				<van-search v-model="query.search" left-icon="search" @search="searchEv" :clearable="false" placeholder="请输入医院/医生" />
			</form>
		</div>

		<!-- 医生列表 -->
		<div class="amd-content">
			<template v-if="statusLoading === 'init'">
				<div v-for="item in [1, 2, 3]" :key="item" style="width: 100%; margin-bottom: 12px">
					<van-skeleton title :row="2" />
				</div>
			</template>
			<template v-else-if="statusLoading === 'empty'">
				<van-empty description="无数据" />
			</template>
			<template v-else>
				<van-list :immediate-check="false" v-model:loading="loading" v-model:error="error" :finished="finished" finished-text="没有更多了~~~" error-text="请求失败，点击重新加载" @load="onLoad">
					<div v-for="item in doctorList" :key="item.id" @click="docEv(item)" class="amd-content-item">
						<img v-if="item.doctorSex === '女'" src="@/assets/img/demo/gril.png" />
						<img v-else src="@/assets/img/demo/men.png" />
						<div class="amd-content-item-desc">
							<div>
								<span class="desc-name" style="margin-right: 8px">{{ item.doctorName }}</span>
								<span>{{ item.doctorTitle }}</span>
								<!-- 多点执业 -->
								<img v-if="item.servingHospital && item.servingHospital.length > 0" class="more_job" src="@/assets/img/more_job.png" />
							</div>
							<div>
								<span style="margin-right: 8px">{{ item.hospital }}</span>
								<span>{{ item.department }}</span>
							</div>
						</div>
					</div>
				</van-list>
			</template>
		</div>
	</div>
</template>
<script setup>
import { getTargetDoctor } from '@/api/user';
const emits = defineEmits(['close', 'activeDoc']);
const treeActive = ref('我的医生');
const query = reactive({
	search: '',
	sortBy: 'position',
	allClient: false,
	isFocus: false,
	page: 1,
	size: 10,
	total: 0,
});

const doctorList = ref([]);
const statusLoading = ref(null);
const loading = ref(false);
const finished = ref(false);
const error = ref(false);
const treeChange = (val) => {
	if (val === 0) {
		// 我的医生
		query.allClient = false;
	} else {
		// 全部医生
		query.allClient = true;
	}
	query.page = 1;
	statusLoading.value = 'init';
	loading.value = false;
	finished.value = false;
	error.value = false;
	query.total = 0;
	doctorList.value = [];
	getDoctorList(query);
};
const close = () => {
	emits('close');
};
const getDoctorList = (data) => {
	getTargetDoctor(data)
		.then((res) => {
			doctorList.value = doctorList.value.concat(res.result.doctors);
			query.total = res.result.total;
			if (doctorList.value.length >= query.total) {
				if (query.total === 0) {
					statusLoading.value = 'empty';
				} else {
					statusLoading.value = 'done';
					finished.value = true;
				}
			} else {
				statusLoading.value = 'doing';
			}
		})
		.catch(() => {
			statusLoading.value = 'empty';
			doctorList.value = [];
		});
};

const onLoad = () => {
	query.page++;
	loading.value = true;
	error.value = false;
	getTargetDoctor(query)
		.then((res) => {
			doctorList.value = doctorList.value.concat(res.result.doctors);
			query.total = res.result.total;
			if (doctorList.value.length >= query.total) finished.value = true;
		})
		.catch(() => {
			error.value = true;
			query.page--;
		})
		.finally(() => {
			loading.value = false;
		});
};

const searchEv = () => {
	query.page = 1;
	statusLoading.value = 'init';
	loading.value = false;
	finished.value = false;
	error.value = false;
	query.total = 0;
	doctorList.value = [];
	getDoctorList(query);
};

const docEv = (item) => {
	emits('activeDoc', item);
	close();
};
</script>
<style lang="scss" scoped>
.amd {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.amd-header {
		display: flex;
		align-items: center;
		padding: 0 12px;
		justify-content: space-between;
		span {
			font-weight: 500;
			font-size: 16px;
		}
	}
	.amd-search {
		width: 100%;
	}

	.amd-content {
		width: 100%;
		flex: 1;
		padding: 0 12px;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		.amd-content-item {
			padding: 15px 0;
			border-bottom: 1px solid var(--ac-border-color);
			display: flex;
			align-items: center;

			img {
				height: 50px;
				width: 50px;
				margin-right: 12px;
			}

			.amd-content-item-desc {
				flex: 1;
				display: flex;
				flex-direction: column;
				.desc-name {
					font-weight: 500;
					font-size: 16px;
				}

				.more_job {
					height: 8px;
					width: 38px;
					object-fit: cover;
					margin-left: 8px;
					margin-right: 0px;
				}
			}
		}
	}

	&:deep(.van-tabs--line .van-tabs__wrap) {
		height: 40px;
	}
}
</style>
