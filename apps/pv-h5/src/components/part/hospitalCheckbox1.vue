<template>
	<van-popup teleport="body" class="q_custom-popup" :close-on-click-overlay="true" :safe-area-inset-bottom="true" position="bottom" :style="{ height: '75%' }" v-model:show="show">
		<!-- header -->
		<div class="c-header">
			<!-- title -->
			<span class="c-header__title">请选择</span>
			<!-- 确认 -->
			<span @click="confirm" class="c-header__confirm">确认</span>
		</div>
		<!-- 搜索框 -->
		<div class="c-search">
			<form action="/">
				<van-search @search="searchEv" @clear="resetSearch" v-model="state.keyword" placeholder="搜索"> </van-search>
			</form>
		</div>
		<!-- <template #action>
      <div @click="searchEv">搜索</div>
    </template> -->
		<!-- 数据列表 -->
		<div class="c-data">
			<van-checkbox class="c-data--allcheck" icon-size="16" checked-color="var(--pv-tabbar-active)" shape="square" v-model="isCheckAll" :indeterminate="isIndeterminate" @change="checkAllChange"> 全选 </van-checkbox>
			<van-list v-model:loading="state.isLoading" ref="vantList" :offset="30" :immediate-check="false" :finished="state.finished" finished-text="没有更多了" @load="onLoad">
				<van-checkbox-group v-model="checkedResult" @change="checkedResultChange">
					<van-checkbox checked-color="var(--pv-tabbar-active)" icon-size="16" shape="square" v-for="item in state.dataList" :key="item.id" :name="item.id"> {{ item.name }} </van-checkbox>
				</van-checkbox-group>
			</van-list>
		</div>
	</van-popup>
</template>
<script setup>
import { showToast } from 'vant';
import { filterMarketHospital } from '@/api/filter';
import { getNamesByIdsInArray } from '@/utils/index';
import usefilterStore from '@/store/modules/filter';
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const props = defineProps(['path', 'province']);

const dataTotal = filterStore.hospitalTotal;
const state = reactive({
	dataList: [], //数据
	isLoading: false, // 列表的loading
	finished: false, //结束
	total: 0, //总数
	page: 0,
	size: 50,
	keyword: '',
});

const show = ref(false);
const emits = defineEmits(['_confirm']);

// if (filterStore.hospitalList.length !== 0) {
// 	for (const item of filterStore.hospitalList) {
// 		state.dataList.push({ id: item.hospCode, name: item.hospName });
// 	}
// }

const isCheckAll = ref(false);
const isIndeterminate = ref(false);
const checkedResult = ref([]);

const checkedResultChange = (value) => {
	const checkedCount = value.length;
	isCheckAll.value = checkedCount === state.dataList.length;
	isIndeterminate.value = checkedCount > 0 && checkedCount < state.dataList.length;
};

const checkAllChange = (val) => {
	checkedResult.value = [];
	if (val) {
		state.dataList.forEach((ele) => {
			checkedResult.value.push(ele.id);
		});
	}
	isIndeterminate.value = true;
};

// 确认
const confirm = () => {
	if (checkedResult.value.length > 0) {
		if (checkedResult.value.length > 50) {
			showToast({
				message: '最多支持50家终端，请重新选择',
				position: 'top',
			});
		} else {
			show.value = false;
			const names = getNamesByIdsInArray(state.dataList, checkedResult.value);
			// 返回选中的选项id
			// 初次全选默认全部医院，其他情况走正常逻辑
			if (state.page === 0 && checkedResult.value.length === state.dataList.length && state.keyword === '') {
				emits('_confirm', [], '全部医院');
			} else {
				emits('_confirm', checkedResult.value.length === dataTotal ? [] : checkedResult.value, names.join(','));
			}
		}
	} else {
		showToast({
			message: '请选择数据',
			position: 'top',
		});
	}
};

// 搜索
const searchEv = (val) => {
	if (val) {
		state.page = 0;
		state.dataList = [];
		getHospitalList();
		if (state.keyword.length > 0) {
			proxy.$umeng('搜索', `${props.path}-医院`, state.keyword);
		}
	} else {
		showToast({
			message: '请输入关键字进行搜索',
			position: 'top',
		});
	}
};
// 重制搜索
const resetSearch = () => {
	state.keyword = '';
	// checkedResult.value = [];
};

let getHospitalList = async () => {
	try {
		let res = await filterMarketHospital({
			search: state.keyword,
			page: state.page,
			size: state.size,
			province: props.province,
		});
		const rows = res.result.content;
		state.isLoading = false;
		state.total = res.result.totalElements;
		if (rows == null || rows.length === 0) {
			state.finished = true;
			return;
		}
		isIndeterminate.value = true;
		for (const item of rows) {
			state.dataList.push({ id: item.hospCode, name: item.hospName });
		}
		state.finished = false;
		if (state.dataList.length >= state.total) {
			state.finished = true;
		}
		return Promise.resolve();
	} catch (error) {
		state.isLoading = false;
		state.finished = true;
		return Promise.reject(error);
	}
};

const onLoad = () => {
	if (state.finished) return;
	state.page++;
	state.isLoading = true;
	getHospitalList();
};

onMounted(() => {
	if (state.dataList.length === 0) {
		getHospitalList();
	}
});
watch(
	() => props.province,
	() => {
		state.page = 0;
		state.dataList = [];
		checkedResult.value = [];
		getHospitalList();
	}
);
defineExpose({
	show,
});
</script>

<style lang="scss" scoped>
.c-header {
	padding: 20px 20px 16px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.c-header__title {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-default-color);
	}

	.c-header__confirm {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-tabbar-active);
	}
}

.c-search {
	padding: 0 20px;
	&:deep(.van-search) {
		padding: 0;
		border-radius: 3px;
		overflow: hidden;
	}
	&:deep(.van-search__field) {
		height: 36px;
	}
	&:deep(.van-field__left-icon) {
		color: var(--pv-no-active-color);
	}
	&:deep(.van-field__control::-webkit-input-placeholder) {
		color: var(--pv-no-active-color);
	}
}

.c-data {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	padding: 3.5px 15px;

	&:deep(.van-checkbox) {
		overflow: initial;
		padding-left: 7px;
		padding-right: 7px;
		padding-bottom: 14px;
		padding-top: 14px;
		border-bottom: solid 1px var(--pv-filter-bgc);
	}

	&:deep(.van-checkbox__label) {
		font-size: 16px;
		color: var(--pv-default-color);
		font-weight: 400;
		margin-left: 16px;
	}
	&:deep(.van-checkbox__icon--indeterminate .van-icon) {
		border-color: var(--pv-tabbar-active);
		background-color: var(--pv-tabbar-active);
	}
}

/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	.c-header {
		padding: 20px 20px 16px;

		.c-header__title {
			font-size: 16px;
		}

		.c-header__confirm {
			font-size: 16px;
		}
	}

	.c-search {
		padding: 0 20px;
		&:deep(.van-search) {
			border-radius: 3px;
		}
		&:deep(.van-search__field) {
			height: 36px;
		}
	}

	.c-data {
		padding: 3.5px 15px;

		&:deep(.van-checkbox) {
			padding-left: 7px;
			padding-right: 7px;
			padding-bottom: 14px;
			padding-top: 14px;
			border-bottom: solid 1px var(--pv-filter-bgc);
			.van-checkbox__icon .van-icon {
				border-width: 1px;
			}
		}

		&:deep(.van-checkbox__label) {
			font-size: 16px;
			margin-left: 16px;
			line-height: 1.5 !important;
		}
	}
}
</style>
