<template>
	<div></div>
</template>
<script name="setLanguage" setup>
import { useI18n } from 'vue-i18n';
import { Locale } from 'vant';
import enUS from 'vant/es/locale/lang/en-US';
import zhCN from 'vant/es/locale/lang/zh-CN';
const { locale } = useI18n();
import usefilterStore from '@/store/modules/filter';
const filterStore = usefilterStore();
function setLang(val = 'zh') {
	locale.value = val;
	val === 'zh' ? Locale.use('zh-CN', zhCN) : Locale.use('en-US', enUS);
	filterStore.SET_LANGUAGE(val);
}

defineExpose({ setLang });
</script>
