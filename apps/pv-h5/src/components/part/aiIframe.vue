<template>
	<div class="aiIfarme">
		<iframe :style="{ width: ifarmeWidth, height: ifarmeHeight }" :src="ursl"></iframe>
	</div>
</template>
<script setup>
import { isPc } from '@/utils';
const ursl = ref(null);
const props = defineProps(['url']);

watch(
	() => props.url,
	(val) => {
		ursl.value = val;
	},
	{
		deep: true,
		immediate: true,
	}
);

const ifarmeHeight = ref('inherit');
const ifarmeWidth = ref('inherit');
onMounted(() => {
	// 获取iframe宽度并通过宽度计算高度
	ifarmeWidth.value = isPc() ? '375px' : 'calc(100vw - 90px)';
	const iframe = document.querySelector('.aiIfarme');
	if (isPc()) {
		ifarmeHeight.value = 375 * 1.5 + 'px';
	} else {
		ifarmeHeight.value = iframe.clientWidth * 1.5 + 'px';
	}
});
</script>
<style lang="scss" scoped>
.aiIfarme {
	iframe {
		border: none;
	}
}
</style>
