<template>
	<van-popup teleport="body" :safe-area-inset-bottom="true" class="q_custom-popup" :close-on-click-overlay="true" position="bottom" :style="{ height: '75%' }" v-model:show="show">
		<!-- header -->
		<div class="c-header" id="ap-c-header">
			<!-- title -->
			<span class="c-header__title">请选择</span>
			<!-- 确认 -->
			<span @click="confirm" class="c-header__confirm">确认</span>
		</div>
		<!-- 搜索框 -->
		<div class="c-search" id="ap-c-search">
			<van-search @update:model-value="searchEv" @clear="resetSearch" v-model="searchText" placeholder="搜索" />
		</div>
		<!-- 数据列表 -->
		<div class="c-data" id="ap-c-data">
			<template v-if="dataList.length > 0">
				<van-radio-group checked-color="var(--pv-tabbar-active)" v-model="checked" shape="dot">
					<van-radio v-for="item in dataList" :key="item.id" :name="item.id">{{ item.name }}</van-radio>
				</van-radio-group>
			</template>
			<!-- 无数据 -->
			<van-empty v-else image-size="160" description="无数据" />
		</div>
	</van-popup>
</template>
<script setup>
import { showToast } from 'vant';
const { proxy } = getCurrentInstance();
const show = ref(false);

const searchText = ref('');
const emits = defineEmits(['_confirm']);
const props = defineProps(['list', 'defaultCheck', 'path']);

const checked = ref('');
const dataList = ref([]);

watch(
	() => props.defaultCheck,
	(val) => {
		checked.value = val || '';
		dataList.value = props.list;
	},
	{ immediate: true }
);

// 确认
const confirm = () => {
	const j = dataList.value.filter((ele) => ele.id === checked.value);
	if (j.length > 0) {
		show.value = false;
		emits('_confirm', j[0]);
	} else {
		showToast({
			message: '请选择数据',
			position: 'top',
		});
	}
};

// 搜索
const searchEv = (val) => {
	if (val) {
		dataList.value = dataList.value.filter((ele) => ele.name.indexOf(val) > -1);
		proxy.$umeng('搜索', props.path, val);
	} else {
		dataList.value = props.list;
	}
};
// 重制搜索
const resetSearch = () => {
	searchText.value = '';
	dataList.value = props.list;
};

defineExpose({
	show,
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/pickerFilter/oneLevelRadio.scss';
.c-header {
	padding: 20px 20px 16px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.c-header__title {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-default-color);
	}

	.c-header__confirm {
		font-weight: 500;
		font-size: 16px;
		color: var(--pv-tabbar-active);
	}
}

.c-search {
	padding: 0 20px;
	&:deep(.van-search) {
		padding: 0;
		border-radius: 3px;
		overflow: hidden;
	}
	&:deep(.van-search__field) {
		height: 36px;
	}
	&:deep(.van-field__left-icon) {
		color: var(--pv-no-active-color);
	}
	&:deep(.van-field__control::-webkit-input-placeholder) {
		color: var(--pv-no-active-color);
	}
}

.c-data {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	padding: 3.5px 15px;

	&:deep(.van-radio) {
		padding-left: 7px;
		padding-right: 7px;
		padding-bottom: 12px;
		padding-top: 12px;
		border-bottom: solid 1px var(--pv-filter-bgc);
	}

	&:deep(.van-radio__label) {
		font-size: 16px;
		color: var(--pv-default-color);
		font-weight: 400;
		margin-left: 16px;
	}
}
</style>
