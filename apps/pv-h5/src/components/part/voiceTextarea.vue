<template>
	<div class="voice-textarea">
		<div class="voice-textarea__title" v-if="title">{{ title }}</div>
		<div class="voice-textarea__input">
			<van-field v-model="msg" :rows="row" @change="emit('update:modelValue', msg)" :autosize="autosize" :show-word-limit="showWordLimit" type="textarea" :maxlength="maxlength" :placeholder="placeholder" :readonly="readonly" />
		</div>
		<div class="voice-textarea__footer">
			<div @click="voiceEv" class="voice-textarea__voice" v-if="showVoice">
				<img src="@/assets/img/voice.png" alt="" />
				<span>语音输入</span>
			</div>
		</div>
		<!-- 语音录入 -->
		<van-popup :close-on-popstate="true" :close-on-click-overlay="false" v-model:show="showCenter" round>
			<div class="popup__voice">
				<span class="voice-title">语音识别</span>
				<span class="voice-desc">（仅支持单条识别）</span>
				<div class="voice-gif">
					<img src="@/assets/img/visit.gif" alt="" />
				</div>
				<van-button color="#0052cc" @click="voiceDone">说完了</van-button>
			</div>
		</van-popup>
	</div>
</template>

<script setup>
import CryptoJS from 'crypto-js';
import RecorderManager from '@/utils/tts';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const domain = enterStore.enterInfo.domain;
const recorder = new RecorderManager(`${domain}/app/mobile`);
const props = defineProps({
	modelValue: {
		type: String,
		default: '',
	},
	title: {
		type: String,
		default: '',
	},
	row: {
		type: Number,
		default: 4,
	},
	autosize: {
		type: Boolean,
		default: true,
	},
	maxlength: {
		type: Number || String,
		default: null,
	},
	placeholder: {
		type: String,
		default: '请输入内容',
	},
	showVoice: {
		type: Boolean,
		default: true,
	},
	showWordLimit: {
		type: Boolean,
		default: false,
	},
	readonly: {
		type: Boolean,
		default: false,
	},
});
const emit = defineEmits(['update:modelValue']);
const msg = ref(props.modelValue);
watch(
	() => props.modelValue,
	(val) => {
		msg.value = val;
	}
);
const showCenter = ref(false);
// 语音录入
const voiceEv = () => {
	// 激活讯飞在线语音api
	initVoice();
	showCenter.value = true;
};
const voiceDone = () => {
	recorder?.stop();
	showCenter.value = false;
};
// 语音识别
let iatWS = null;
let resultText = '';
let resultTextTemp = '';
const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let btnStatus = 'UNDEFINED'; // "UNDEFINED" "CONNECTING" "OPEN" "CLOSING" "CLOSED"

const initVoice = () => {
	resultText = '';
	resultTextTemp = '';
	if (btnStatus === 'UNDEFINED' || btnStatus === 'CLOSED') {
		connectWebSocket();
	}
};

const connectWebSocket = () => {
	const websocketUrl = getWebSocketUrl();
	if ('WebSocket' in window) {
		iatWS = new WebSocket(websocketUrl);
	} else if ('MozWebSocket' in window) {
		iatWS = new MozWebSocket(websocketUrl);
	} else {
		showToast({
			position: 'top',
			message: '浏览器不支持WebSocket',
		});
		msg.value = '';
		return;
	}
	changeBtnStatus('CONNECTING');
	iatWS.onopen = () => {
		// 开始录音
		recorder.start({
			sampleRate: 16000,
			frameSize: 1280,
		});

		var params = {
			common: {
				app_id: APPID,
			},
			business: {
				language: 'zh_cn',
				domain: 'iat',
				accent: 'mandarin',
				vad_eos: 3000,
				dwa: 'wpgs',
			},
			data: {
				status: 0,
				format: 'audio/L16;rate=16000',
				encoding: 'raw',
			},
		};
		iatWS.send(JSON.stringify(params));
	};

	iatWS.onmessage = (e) => {
		renderResult(e.data);
	};
	iatWS.onerror = (e) => {
		console.error(e);
		recorder.stop();
		changeBtnStatus('CLOSED');
	};
	iatWS.onclose = (e) => {
		console.error(e);
		recorder.stop();
		changeBtnStatus('CLOSED');
	};
};

const renderResult = (resultData) => {
	// 识别结束
	let jsonData = JSON.parse(resultData);
	if (jsonData.data && jsonData.data.result) {
		let data = jsonData.data.result;
		let str = '';
		let ws = data.ws;
		for (let i = 0; i < ws.length; i++) {
			str = str + ws[i].cw[0].w;
		}
		if (data.pgs) {
			if (data.pgs === 'apd') {
				// 将resultTextTemp同步给resultText
				resultText = resultTextTemp;
			}
			// 将结果存储在resultTextTemp中
			resultTextTemp = resultText + str;
		} else {
			resultText = resultText + str;
		}
		// 第一次识别
		if (jsonData.data.status === 0 && msg.value) {
			resultTextTemp = msg.value + resultTextTemp;
			resultText = msg.value + resultText;
		}
		msg.value = resultTextTemp || resultText || '';
	}
	if (jsonData.code === 0 && jsonData.data.status === 2) {
		iatWS.close();
	}
	if (jsonData.code !== 0) {
		iatWS.close();
		console.error(jsonData);
	}
};

const getWebSocketUrl = () => {
	// 请求地址根据语种不同变化
	var url = 'wss://iat-api.xfyun.cn/v2/iat';
	var host = location.host;
	var apiKey = API_KEY;
	var apiSecret = API_SECRET;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/iat HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};

const changeBtnStatus = (status) => {
	btnStatus = status;
};
const toBase64 = (buffer) => {
	var binary = '';
	var bytes = new Uint8Array(buffer);
	var len = bytes.byteLength;
	for (var i = 0; i < len; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	return window.btoa(binary);
};
recorder.onStart = () => {
	changeBtnStatus('OPEN');
};

recorder.onFrameRecorded = ({ isLastFrame, frameBuffer }) => {
	if (iatWS.readyState === iatWS.OPEN) {
		iatWS.send(
			JSON.stringify({
				data: {
					status: isLastFrame ? 2 : 1,
					format: 'audio/L16;rate=16000',
					encoding: 'raw',
					audio: toBase64(frameBuffer),
				},
			})
		);
		if (isLastFrame) {
			changeBtnStatus('CLOSING');
		}
	}
};
</script>
<style lang="scss" scoped>
.voice-textarea {
	border-radius: 6px;
	background-color: #fff;
	padding: 15px;
	--van-cell-vertical-padding: 0;
	--van-cell-horizontal-padding: 0;
	--van-cell-background: transparent;
	&__title {
		color: #172b4d;
		font-weight: bold;
		font-size: 14px;
		margin-bottom: 8px;
	}
	&__input {
		max-height: 300px;
		overflow-y: auto;
	}
	&__voice {
		color: #0052cc;
		font-size: 14px;
		img {
			width: 16px;
			height: 16px;
			margin-right: 5px;
		}
	}
}
:deep(.popup__voice) {
	padding: 30px 0;
	width: 225px;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	align-items: center;
	.voice-title {
		color: #172b4d;
		font-weight: 500;
		font-size: 18px;
	}
	.voice-desc {
		margin: 15px 0;
		color: #6b778c;
		font-size: 12px;
	}
	.voice-gif {
		margin-top: 9px;
		margin-bottom: 28px;
		width: 150px;
		height: 59px;
		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	.van-button {
		width: 116px;
		height: 40px;
		border-radius: 20px;
	}
}
/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	.voice-textarea {
		border-radius: 6px;
		padding: 15px;
		--van-cell-vertical-padding: 0;
		--van-cell-horizontal-padding: 0;
		--van-cell-background: transparent;
		&__title {
			font-size: 14px;
			margin-bottom: 8px;
		}
		:deep(.van-field) {
			font-size: 14px;
			line-height: 1.3;
		}
		&__input {
			max-height: 300px;
		}
		&__voice {
			font-size: 14px;
			img {
				width: 16px;
				height: 16px;
				margin-right: 5px;
			}
		}
	}

	:deep(.popup__voice) {
		padding: 30px 0;
		width: 225px;
		.voice-title {
			font-size: 18px;
		}
		.voice-desc {
			margin: 15px 0;
			font-size: 12px;
		}
		.voice-gif {
			margin-top: 9px;
			margin-bottom: 28px;
			width: 150px;
			height: 59px;
		}
		.van-button {
			width: 116px;
			height: 40px;
			border-radius: 20px;
			font-size: 12px;
			padding: 0;
		}
	}
}
</style>
