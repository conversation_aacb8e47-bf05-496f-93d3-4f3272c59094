<template>
	<div>
		<!-- 语音 -->
		<div
			class="chat-bottom"
			:style="{
				height: isVoice ? 'auto' : isPc ? '50px' : '13.333vw',
				paddingTop: isVoice ? (isPc ? '4px' : '1.0667vw') : '0',
				paddingBottom: isVoice ? (isPc ? '4px' : '1.0667vw') : '0',
				marginLeft: isVoice ? (isPc ? '10.5px' : '2.8570vw') : props.sMenu === false ? (isPc ? '10.5px' : '2.8570vw') : isPc ? '50px' : '13.3333vw',
				width: props.sMenu === false ? (isPc ? '353.5px' : '94.286vw') : isVoice ? (isPc ? '310px' : '82.6667vw') : isPc ? 'calc(100% - 65px)' : 'calc(100% - 17.3333vw)',
			}"
			:class="{
				azsh: voiceMsg === '按住说话',
				shqx: voiceMsg === '松开发送，上滑取消',
				ssqx: voiceMsg === '松开取消',
			}"
		>
			<!-- 左侧 按住说话 -->
			<div v-if="!isVoice" @touchstart="setVoiceSq" class="chat-bottom-middle">
				<!-- 语音 -->
				<template v-if="voiceMsg === '按住说话'">
					<div class="chat-bottom-btn">
						<span :class="{ 'chat-bottom-btn-span': props.sMenu === false }">{{ voiceMsg }}</span>
					</div>
				</template>
				<template v-else>
					<img class="loading-img" src="@/assets/img/demo/loading-gits.gif" />
				</template>
			</div>
			<!-- 左侧 文本框 -->
			<div v-else class="chat-bottom-input">
				<!-- 文本框 -->
				<template v-if="voiceMsg === '按住说话'">
					<van-field ref="msgInput" rows="1" v-model="input" autosize type="textarea" placeholder="发消息" />
				</template>
			</div>
			<!-- 发送按钮 -->
			<img class="send_new" v-show="input && voiceMsg === '按住说话'" @click="sendMessage" src="@/assets/img/demo/send_new.png" />
			<!-- 语音和手写 -->
			<img v-show="!input && isVoice && voiceMsg === '按住说话'" @click="switchVoice" src="@/assets/img/demo/voice2.png" />
			<img v-show="!input && !isVoice && voiceMsg === '按住说话'" @click="switchVoice" src="@/assets/img/demo/keyword.png" />
			<!-- 功能按钮列表 -->
			<img v-show="!input && voiceMsg === '按住说话' && !showMenu && ol" @click="onLineVoice" src="@/assets/img/agent-add.png" />
			<img v-show="!input && voiceMsg === '按住说话' && showMenu && ol" @click="onLineVoice" src="@/assets/img/agent-close.png" />
		</div>
		<!-- 工具 -->
		<div class="chat-bottom-tool" :class="{ active: showMenu }">
			<van-swipe style="width: 100%" :loop="false" :autoplay="0" indicator-color="#172b4d">
				<van-swipe-item>
					<div v-for="(item, index) in props.toolsList.length > 8 ? props.toolsList.slice(0, 8) : props.toolsList" :key="index" @click="toolClick(item)" class="tool-item">
						<div class="tool-item-img">
							<img :src="item.img" alt="" />
						</div>
						<span class="tool-item-text">{{ item.name }}</span>
					</div>
				</van-swipe-item>
				<van-swipe-item v-if="props.toolsList.length > 8">
					<div v-for="(item, index) in props.toolsList.slice(8)" :key="index" @click="toolClick(item)" class="tool-item">
						<div class="tool-item-img">
							<img :src="item.img" alt="" />
						</div>
						<span class="tool-item-text">{{ item.name }}</span>
					</div>
				</van-swipe-item>
			</van-swipe>
		</div>
		<!-- 语音解析组件 -->
		<Transition name="fade">
			<div v-if="voiceMsg !== '按住说话'" class="bg-shadow">
				<div
					:style="{
						marginBottom: isPc ? (showMenu ? '283px' : '121px') : showMenu ? '75.2vw' : '32vw',
					}"
					class="bg-shadow-content"
				>
					<div>
						{{ voiceText }}
						<!-- loading -->
						<div class="q-loading">
							<div></div>
							<div></div>
							<div></div>
						</div>
					</div>
				</div>
				<!-- 语音状态 -->
				<div
					:style="{
						marginBottom: isPc ? (showMenu ? '227px' : '65px') : showMenu ? '60.7vw' : '17.5vw',
					}"
					class="bg-shadow-status"
				>
					{{ voiceMsg }}
				</div>
			</div>
		</Transition>
	</div>
</template>
<script setup>
import { showDialog, showToast } from 'vant';
import { isSafariBrowser } from '@/utils/index';
import Hammer, { on } from 'hammerjs';
import CryptoJS from 'crypto-js';
import RecorderManager from '@/utils/tts';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const domain = enterStore.enterInfo.domain;
const recorder = new RecorderManager(`${domain}/app/mobile`);
const emits = defineEmits(['_sendMessage', '_changeEdit', '_lineVoice']);
const props = defineProps(['editorText', 'sMenu', 'ol', 'toolsList']);
// 屏幕宽度大于600，认为在pc端
const isPc = ref(window.innerWidth >= 600 ? true : false);
// 是否展示功能列表按钮
const showMenu = ref(false);
const h = ref(0);
const onLineVoice = () => {
	showMenu.value = !showMenu.value;
	if (showMenu.value) {
		if (isPc.value) {
			h.value = 162;
		} else {
			h.value = 43.2 * (document.documentElement.clientWidth / 100);
		}
	} else {
		h.value = 0;
	}
};

watch(
	() => props.editorText,
	(val) => {
		isVoice.value = true;
		nextTick(() => {
			input.value = val.split('jjjjjj')[0];
			nextTick(() => {
				if (val) {
					msgInput.value?.focus();
				} else {
					msgInput.value?.blur();
				}
			});
		});
	}
);

// 工具事件
const toolClick = (item) => {
	showMenu.value = false;
	setTimeout(() => {
		emits('_toolClick', item);
	}, 350);
};

// 获取当前可用屏幕的高度
const screenHeight = document.documentElement.clientHeight;

// 修改消息
const msgInput = ref(null);

// 文本框中的值
const input = ref('');

// 是否为语音 false = 语音
const isVoice = ref(true);

// 语音按钮描述
const voiceMsg = ref('按住说话');

// 语音输入的文本
const voiceText = ref('');
let isSend = false;

// hammer 实例
let hammer = '';
// 麦克风授权状态 默认 = 尚未选择是否授权麦克风
let voiceStatus = 'prompt';
const getmicrophonePermission = () => {
	// 获取用户是否允许了麦克风权限
	navigator.permissions.query({ name: 'microphone' }).then((result) => {
		// console.log('麦克风权限状态:', result.state);
		voiceStatus = result.state;
		// 已授权麦克风
		if (result.state === 'granted') {
			nextTick(() => {
				if (!isVoice.value) {
					registerHammer();
				}
			});
		}
	});
};
// 获取麦克风权限
const setVoiceSq = () => {
	// 麦克风已被禁用
	if (voiceStatus === 'denied') {
		showDialog({
			title: '提示',
			message: '麦克风已被禁用，请您在浏览器设置中开启。',
			confirmButtonColor: '#0052cc',
		});
	} else if (voiceStatus === 'prompt') {
		// 麦克风处于被询问状态
		getVoiceSq();
	}
};

// 获取麦克风的授权
const getVoiceSq = () => {
	navigator.mediaDevices
		.getUserMedia({ audio: true })
		.then((stream) => {
			voiceStatus = 'granted';
			// 立即关闭麦克风
			stream.getAudioTracks().forEach((track) => track.stop());
			stream = null; // 释放变量
			// 兼容 Safari，强制清空 audio
			if (isSafariBrowser()) {
				const audio = new Audio();
				audio.srcObject = null;
			}
			nextTick(() => {
				if (!isVoice.value) {
					registerHammer();
				}
			});
		})
		.catch((error) => {
			// console.error('❌ 用户拒绝了麦克风权限:', error);
			voiceStatus = 'denied';
			showToast({
				position: 'top',
				message: '您已拒绝授权麦克风权限！',
			});
		});
};

onMounted(() => {
	websocketUrl = getWebSocketUrl();
	// 获取用户是否允许了麦克风权限
	getmicrophonePermission();
});

// 发送消息
const sendMessage = () => {
	emits('_sendMessage', input.value);
	nextTick(() => {
		msgInput.value?.blur();
	});
	clearText();
};

// 清空文本
const clearText = () => {
	nextTick(() => {
		input.value = '';
	});
};
// 切换输入方式
const switchVoice = () => {
	if (!isVoice.value) {
		emits('_changeEdit', false);
	} else {
		voiceMsg.value = '按住说话';
	}
	isVoice.value = !isVoice.value;
	clearText();
	if (!isVoice.value) {
		nextTick(() => {
			// 释放 hammer
			hammer = '';
			// 用户已授权麦克风
			if (voiceStatus === 'granted') registerHammer();
		});
	} else {
		nextTick(() => {
			msgInput.value?.focus();
		});
	}
};

// 注册hammer事件
const registerHammer = () => {
	const s = document.querySelector('.chat-bottom-middle');
	hammer = new Hammer(s);
	hammer.get('pan').set({
		direction: Hammer.DIRECTION_ALL,
	});

	// 长按事件
	hammer.on('press', (e) => {
		// 激活讯飞在线语音api
		initVoice();
	});

	hammer.on('pressup', (e) => {
		isSend = true;
		voiceMsg.value = '按住说话';
		recorder?.stop();
	});

	// 向上滑动
	hammer.on('panup', (e) => {
		if (screenHeight - e.srcEvent.clientY - h.value >= 100) {
			voiceMsg.value = '松开取消';
		}
	});

	// 向下滑动
	hammer.on('pandown', (e) => {
		if (screenHeight - e.srcEvent.clientY - h.value < 100) {
			voiceMsg.value = '松开发送，上滑取消';
		}
	});

	// 松开取消
	hammer.on('panend', (e) => {
		if (voiceMsg.value === '松开发送，上滑取消') {
			isSend = true;
		}
		voiceMsg.value = '按住说话';
		recorder?.stop();

		// 发送最后一次语音，告诉服务端上传结束标识
		iatWS.send(
			JSON.stringify({
				data: {
					status: 2,
				},
			})
		);
	});
};

// 语音识别
let websocketUrl = '';
let iatWS = null;
let resultText = '';
let resultTextTemp = '';
const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let btnStatus = 'UNDEFINED'; // "UNDEFINED" "CONNECTING" "OPEN" "CLOSING" "CLOSED"

const initVoice = () => {
	resultText = '';
	resultTextTemp = '';
	voiceText.value = '';
	iatWS = null;
	isSend = false;
	if (btnStatus === 'UNDEFINED' || btnStatus === 'CLOSED') {
		connectWebSocket();
	}
};

// eslint-disable-next-line no-unused-vars
const connectWebSocket = () => {
	if ('WebSocket' in window) {
		iatWS = new WebSocket(websocketUrl);
	} else if ('MozWebSocket' in window) {
		iatWS = new MozWebSocket(websocketUrl);
	} else {
		showToast({
			position: 'top',
			message: '浏览器不支持WebSocket',
		});
		voiceMsg.value = '按住说话';
		voiceText.value = '';
		return;
	}
	changeBtnStatus('CONNECTING');
	iatWS.onopen = () => {
		// 开始录音
		recorder.start({
			sampleRate: 16000,
			frameSize: 1280,
		});
		var params = {
			common: {
				app_id: APPID,
			},
			business: {
				language: 'zh_cn',
				domain: 'iat',
				accent: 'mandarin',
				vad_eos: 3000,
				dwa: 'wpgs',
			},
			data: {
				status: 0,
				format: 'audio/L16;rate=16000',
				encoding: 'raw',
			},
		};
		iatWS.send(JSON.stringify(params));
		//onopen方法触发后，说明连接已经建立， 改变按钮文字，出现loading动画，并开始录音和识别
		voiceMsg.value = '松开发送，上滑取消';
	};
	iatWS.onmessage = (e) => {
		renderResult(e.data);
	};
	iatWS.onerror = (e) => {
		console.error(e);
		recorder.stop();
		changeBtnStatus('CLOSED');
	};
	iatWS.onclose = (e) => {
		console.error(e);
		recorder.stop();
		changeBtnStatus('CLOSED');
	};
};

const renderResult = (resultData) => {
	// 识别结束
	let jsonData = JSON.parse(resultData);
	if (jsonData.data && jsonData.data.result) {
		let data = jsonData.data.result;
		let str = '';
		let ws = data.ws;
		for (let i = 0; i < ws.length; i++) {
			str = str + ws[i].cw[0].w;
		}
		if (data.pgs) {
			if (data.pgs === 'apd') {
				// 将resultTextTemp同步给resultText
				resultText = resultTextTemp;
			}
			// 将结果存储在resultTextTemp中
			resultTextTemp = resultText + str;
		} else {
			resultText = resultText + str;
		}

		voiceText.value = resultTextTemp || resultText || '';
	}
	if (jsonData.code === 0 && jsonData.data.status === 2) {
		if (isSend) {
			emits('_sendMessage', voiceText.value);
			isSend = false;
		}
		iatWS.close();
	}
	if (jsonData.code !== 0) {
		iatWS.close();
	}
};

const getWebSocketUrl = () => {
	// 请求地址根据语种不同变化
	var url = 'wss://iat-api.xfyun.cn/v2/iat';
	var host = location.host;
	var apiKey = API_KEY;
	var apiSecret = API_SECRET;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/iat HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};

const changeBtnStatus = (status) => {
	btnStatus = status;
};
const toBase64 = (buffer) => {
	var binary = '';
	var bytes = new Uint8Array(buffer);
	var len = bytes.byteLength;
	for (var i = 0; i < len; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	return window.btoa(binary);
};
recorder.onStart = () => {
	changeBtnStatus('OPEN');
};
recorder.onStop = () => {
	voiceMsg.value = '按住说话';
};

recorder.onFrameRecorded = ({ isLastFrame, frameBuffer }) => {
	if (iatWS.readyState === iatWS.OPEN) {
		iatWS.send(
			JSON.stringify({
				data: {
					status: isLastFrame ? 2 : 1,
					format: 'audio/L16;rate=16000',
					encoding: 'raw',
					audio: toBase64(frameBuffer),
				},
			})
		);
		if (isLastFrame) {
			changeBtnStatus('CLOSING');
		}
	}
};
</script>
<style lang="scss" scoped>
.chat-bottom {
	z-index: 99;
	position: relative;
	display: flex;
	align-items: center;
	border-radius: 6px;
	padding: 0 10px 0 0;
	border: 1px solid #dfe1e6;
	justify-content: space-between;
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
	.chat-bottom-middle {
		height: 100%;
		flex: 1;
		padding-left: 10px;
		display: flex;
		align-items: center;
		-webkit-touch-callout: none; /* Safari 和 Chrome for iOS */
		-webkit-user-select: none; /* Chrome, Safari, Opera */
		-khtml-user-select: none; /* Konqueror */
		-moz-user-select: none; /* Firefox */
		-ms-user-select: none; /* Internet Explorer/Edge */
		user-select: none;
		.chat-bottom-btn {
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #172b4d;
			span {
				padding-left: 99px;
				-webkit-touch-callout: none; /* Safari 和 Chrome for iOS */
				-webkit-user-select: none; /* Chrome, Safari, Opera */
				-khtml-user-select: none; /* Konqueror */
				-moz-user-select: none; /* Firefox */
				-ms-user-select: none; /* Internet Explorer/Edge */
				user-select: none;
			}
			.chat-bottom-btn-span {
				padding-left: 138px;
			}
		}

		.loading-img {
			width: 80%;
			margin-left: 10%;
			pointer-events: none;
			-webkit-touch-callout: none; /* Safari 和 Chrome for iOS */
			-webkit-user-select: none; /* Chrome, Safari, Opera */
			-khtml-user-select: none; /* Konqueror */
			-moz-user-select: none; /* Firefox */
			-ms-user-select: none; /* Internet Explorer/Edge */
			user-select: none;
		}
	}
	.chat-bottom-input {
		flex: 1;
		padding-left: 10px;
		&:deep(.van-field) {
			background-color: #ffffff;
			border-radius: 6px;
			padding-bottom: 8px;
			padding-top: 8px;
			min-height: 40px;
			padding-left: 3px;
			padding-right: 3px;
		}
		&:deep(.van-field__control) {
			color: var(--pv-default-color);
		}

		.chat-bottom-btn {
			height: 40px;
			display: flex;
			align-items: center;
			justify-content: center;
			user-select: none;
			color: #172b4d;
			span {
				padding-left: 88px;
			}
		}

		.loading-img {
			width: 80%;
			margin-left: 10%;
			pointer-events: none;
			user-select: none;
			-webkit-user-select: none;
		}
	}

	img {
		width: 29px;
		height: 29px;
		object-fit: contain;
		margin-left: 15px;
	}

	.send_new {
		align-self: flex-start;
		margin-top: 5.5px;
	}
}

.chat-bottom-tool {
	margin-left: 10px;
	display: flex;
	overflow: hidden;
	flex-wrap: wrap;
	height: 0px;
	transition: height 0.3s ease-in;
	.tool-item {
		width: 25%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 10px;
		float: left;
		.tool-item-img {
			img {
				width: 45px;
				height: 45px;
				object-fit: cover;
			}
		}

		.tool-item-text {
			font-size: 10px;
			color: #172b4d;
			margin-top: 6px;
		}
	}

	&:deep(.van-swipe__indicators) {
		bottom: 2%;
		left: 25%;

		.van-swipe__indicator {
			background-color: var(--ac-colors-myGray-500);
		}
	}
}

.chat-bottom-tool.active {
	height: 162px;
}

.azsh {
	background-color: #ffffff;
}
.shqx {
	background-color: #0052cc;
}
.ssqx {
	background-color: #ff3e3e;
}

.bg-shadow {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	height: calc(100% - constant(safe-area-inset-bottom));
	height: calc(100% - env(safe-area-inset-bottom));
	z-index: 98;
	background: url(../../assets/img/demo/bg-shadow.png) no-repeat;
	background-size: cover;
	backdrop-filter: blur(3px);
	-webkit-backdrop-filter: blur(3px);
	display: flex;
	justify-content: flex-end;
	align-items: flex-end;
	flex-direction: column;

	.bg-shadow-content {
		background-color: #0052cc;
		border-radius: 5px 5px 0px 5px;
		padding: 12px;
		margin-right: 15px;
		color: #ffffff;
		max-width: 250px;
		word-break: break-all;

		.q-loading,
		.q-loading > div {
			position: relative;
			box-sizing: border-box;
		}
		.q-loading {
			display: inline-block;
			font-size: 0;
			color: #fff;
		}
		.q-loading.la-dark {
			color: #ffffff;
		}
		.q-loading > div {
			display: inline-block;
			float: none;
			background-color: currentColor;
			border: 0 solid currentColor;
		}

		.q-loading > div {
			width: 6px;
			height: 6px;
			margin-right: 5px;
			border-radius: 100%;
			animation: ball-beat 1.5s -3s infinite linear;
		}
		.q-loading > div:last-child {
			margin-right: 0;
		}
		.q-loading > div:nth-child(2n-1) {
			animation-delay: -1s;
		}
	}
	.bg-shadow-status {
		color: #6b778c;
		font-size: 12px;
		width: 100%;
		text-align: center;
	}
}

// 梦层动画
.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
	opacity: 0;
}
// loading
@keyframes ball-beat {
	50% {
		opacity: 0.2;
		transform: scale(0.7);
	}

	100% {
		opacity: 1;
		transform: scale(1);
	}
}
/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	.chat-bottom {
		bottom: 6px;
		z-index: 99;
		border-radius: 6px;
		padding: 0 10px 0 0;
		border: 1px solid #dfe1e6;
		box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
		.chat-bottom-middle {
			height: 100%;
			padding-left: 10px;
			.chat-bottom-btn {
				height: 100%;
				span {
					padding-left: 99px;
				}
				.chat-bottom-btn-span {
					padding-left: 138px;
				}
			}

			.loading-img {
				width: 80%;
				margin-left: 10%;
			}
		}
		.chat-bottom-input {
			padding-left: 10px;
			&:deep(.van-field) {
				border-radius: 6px;
				padding-bottom: 8px;
				padding-top: 8px;
				min-height: 40px;
				padding-left: 3px;
				padding-right: 3px;
				line-height: 24px;
				font-size: 14px;
			}

			.chat-bottom-btn {
				height: 40px;
				span {
					padding-left: 88px;
				}
			}

			.loading-img {
				width: 80%;
				margin-left: 10%;
			}
		}

		img {
			width: 29px;
			height: 29px;
			margin-left: 15px;
		}

		.send_new {
			margin-top: 5.5px;
		}
	}

	.chat-bottom-tool {
		margin-left: 10px;
		height: 0px;
		.tool-item {
			width: 25%;
			margin-top: 10px;
			.tool-item-img {
				img {
					width: 45px;
					height: 45px;
				}
			}

			.tool-item-text {
				font-size: 11px;
				margin-top: 4px;
			}
		}
	}

	.chat-bottom-tool.active {
		height: 162px;
	}

	.bg-shadow {
		backdrop-filter: blur(3px);
		-webkit-backdrop-filter: blur(3px);

		.bg-shadow-content {
			border-radius: 5px 5px 0px 5px;
			padding: 12px;
			margin-right: 15px;
			max-width: 250px;

			.q-loading > div {
				width: 6px;
				height: 6px;
				margin-right: 5px;
			}
		}
		.bg-shadow-status {
			font-size: 12px;
		}
	}
}
</style>
