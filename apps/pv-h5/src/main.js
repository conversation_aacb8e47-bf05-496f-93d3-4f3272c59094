import { createApp } from 'vue';
import App from './App.vue';

import router from './router';
import store from './store';
import directive from './directive';

import plugins from './plugins';

// import VConsole from 'vconsole';
// new VConsole();

// 引入全局样式文件
import '@/style/reset.scss';
import 'vant/es/toast/style';
import 'vant/es/dialog/style';

import { Lazyload } from 'vant';
import '@wsfe/vue-tree/style.css';

// 终端视口宽度大于600时, 此SCSS文件生效
import '@/style/pc/common.scss';

// svg图标
import 'virtual:svg-icons-register';
import SvgIcon from '@/components/SvgIcon/icon-svg.vue';

// 全局组件文件
import reg from '@/components/global/index.js';

// 初始化多主题
import { initTheme } from './utils/theme';
initTheme();

// 国际化
import i18n from './lang';

// 拦截器
import './permission';
const app = createApp(App);
// 全局组件
app.component('svg-icon', SvgIcon);

// matomo
// import registerMatomo from '@/utils/matomo';
// registerMatomo(app, router);
// 全局指令
directive(app);
//表格
import {
	// 全局对象
	VXETable,
	// 可选组件
	Icon,
	Column,
	// 表格
	Table,
} from 'vxe-table';
import 'vxe-table/styles/cssvar.scss';
function useTable(app) {
	app
		.use(Icon)
		.use(Column)
		// 安装表格
		.use(Table);
}
app.use(i18n);
app.use(plugins);
app.use(Lazyload);
app.use(reg);
app.use(store);
app.use(router);
app.use(useTable);

import commandHandler from '@/utils/commandHandler';
app.config.globalProperties.$cHandler = commandHandler;

import DT from './utils/debugTool'; // 引入 debugTool
let dt = new DT();
// dt.init();

import { getApp } from './permission';
getApp(app);
