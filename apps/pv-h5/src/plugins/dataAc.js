// 向外暴露成一个插件对象
const dataAc = {};
// eslint-disable-next-line prettier/prettier
dataAc.install = function (Vue) {
	// 添加实例方法
	/*
	 * 友盟统计
	 * action 必传 String 动作；eg：点击(xx按钮)/修改/关注/...
	 * page 必传 String 页面；eg：首页/在医生360页面/...
	 * label 选传 String 动作对象；eg：医生名/医院名/标签名/...
	 * */
	// eslint-disable-next-line prettier/prettier
	Vue.config.globalProperties.$umeng = function (action, page, label = '') {
		window._paq = window._paq || [];
		if (action === '搜索') {
			window._paq.push(['trackSiteSearch', label.length > 50 ? label.slice(0, 50) + '...' : label, page, 1]);
		} else {
			window._paq.push(['trackEvent', page, action, label.length > 50 ? label.slice(0, 50) + '...' : label]);
		}
	};
};
export default dataAc;
