export function getMatomo() {
	return window.Piwik.getAsyncTracker();
}

export function loadScript(trackerScript, crossOrigin = undefined) {
	return new Promise((resolve, reject) => {
		const script = document.createElement('script');
		script.async = true;
		script.defer = true;
		script.src = trackerScript;

		if (crossOrigin && ['anonymous', 'use-credentials'].includes(crossOrigin)) {
			script.crossOrigin = crossOrigin;
		}

		const head = document.head || document.getElementsByTagName('head')[0];
		head.appendChild(script);

		script.onload = resolve;
		script.onerror = reject;
	});
}

export function getResolvedHref(router, path) {
	return router.resolve(path).href;
}
