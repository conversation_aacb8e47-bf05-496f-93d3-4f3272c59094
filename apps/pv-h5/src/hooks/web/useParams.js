import usePremissionStore from '@/store/modules/premission';
import usefilterStore from '@/store/modules/filter';

export function useParams(reportCd, cardsInfoList, params = {}) {
	const paramKeys = ['startTime', 'endTime', 'brandId', 'brandName', 'personId', 'personName', 'hospitalId', 'hospitalName', 'marketId', 'province', 'region', 'district', 'company', 'date', 'type', 'city', 'indicatorType', 'size', 'currentStatus', 'month', 'salesOrAmount'];
	const paramObj = paramKeys.reduce((acc, param) => {
		acc[param] = params[param] || '';
		return acc;
	}, {}); // 查找对应的卡片信息

	const cardInfo = cardsInfoList.find((ele) => ele.reportCd === reportCd); // 使用默认值
	paramObj.cardInfo = cardInfo ? cardInfo : {};
	paramObj.startDate = paramObj.startTime || cardInfo?.startTime;
	paramObj.endDate = paramObj.endTime || cardInfo?.endTime;
	paramObj.hospitalId = paramObj.hospitalId === '全部' ? '' : paramObj.hospitalId;
	console.log(paramObj);
	//todo 特殊处理默认筛选项为单选的卡片的默认值
	if (!paramObj.personId) {
		//人员特殊处理
		const usePremission = usePremissionStore();
		let ppItem = usePremission?.cardList?.find((ele) => ele.path === reportCd);
		if (ppItem) {
			let value = ppItem.attributes.find((ele) => ele.name === 'filterConfig')?.value;
			if (value) {
				const filterConfig = JSON.parse(value);
				let filterImp = filterConfig.filterList;
				// 处理人员筛选
				if (filterImp.indexOf('org') > -1) {
					const result = useOrg(filterConfig.filterInfo);
					console.log(result);
					paramObj.personId = result.personId;
					paramObj.personName = result.personName;
				}
			}
		}
	}
	if (paramObj.province && !paramObj.city) {
		const filterStore = usefilterStore();
		console.log(filterStore.provinceTree);
		paramObj.city = filterStore.provinceTree
			.find((ele) => ele.name === paramObj.province)
			?.child.map((ele) => ele.name)
			?.join(',');
		console.log(paramObj.city);
	}
	// 新增 countyName 逻辑
	const provinceArray = Array.isArray(paramObj.province) ? paramObj.province : paramObj.province?.split(',') || [];
	const cityArray = Array.isArray(paramObj.city) ? paramObj.city : paramObj.city?.split(',') || [];
	paramObj.countyName = [...provinceArray, ...cityArray].join(',') || '全部省市';
	paramObj.countyName = paramObj.countyName === ',' ? '全部省市' : paramObj.countyName;

	// 新增 month 逻辑
	if (paramObj.month) {
		paramObj.month = paramObj.month || '6';
		paramObj.monthName = '准入后' + paramObj.month + '个月';
	}

	console.log(paramObj);
	return paramObj;
}
