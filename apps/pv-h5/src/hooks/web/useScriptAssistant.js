import useEnterStore from '@/store/modules/enterprise';
import ClipboardJS from 'clipboard';
import { showToast } from 'vant';
import { uuid, findNodeByPathName, formatKNumber } from '@/utils/index';
import { getQuestion } from '@/api/filter';
import usePremissionStore from '@/store/modules/premission';
import { getAgentListApi } from '@/api/agent-tools';
import { queryList } from '@/api/sales';
import { getReports } from '@/api/report';
import { useI18n } from 'vue-i18n';
import Decimal from 'decimal.js';
import useUserStore from '@/store/modules/user';

//可以渲染卡片标识
let canRenderCard = ref(false);
//所有卡片
let cardsInfoList = ref([]);
//卡片信息
const cardInfoC = (id) => {
	return cardsInfoList.value.find((ele) => ele.reportCd === id);
};
//获取问题
let questionListAll = [];
export const useScriptAssistant = () => {
	const perStore = usePremissionStore();
	const { t } = useI18n();
	const enterStore = useEnterStore();
	const userStore = useUserStore();

	//vue-router
	const route = useRoute();
	let router = useRouter();
	// 接口地址
	const interfaceUrl = enterStore.enterInfo.interfaceAddress;
	const domain = enterStore.enterInfo.domain;
	// 屏幕宽度大于600，认为在pc端
	const isPc = ref(window.innerWidth >= 600 ? true : false);
	let agentName = ref(''); //智能体名字
	let welcomeText = 'Hi,我是你的智能分析小助手，您可以向我随时提问关于销售业绩的问题，我来帮您解答哦！'; //开场白
	const chatList = ref([]); //消息列表
	const isLoading = ref(false); //页面是否有正在回答的消息
	let chatId = ref(''); // 会话ID
	const isMultiChat = ref(true); // 默认多会话
	let isStream = ref(true);
	// 赞和踩
	const zeJson = reactive({
		id: '',
		feedback_reason: '',
	});
	//点才得理由弹框
	const caiShow = ref(false);
	//返回
	const goBack = () => {
		router.go(-1);
	};
	//生成一条用户消息
	const createUserMsg = (text) => {
		return {
			msg: text,
			dataId: '',
			type: 'user',
			id: uuid(),
			loading: false,
			zeStatus: '0',
			skillType: 'userMessage',
		};
	};
	//生成一条ai消息  添加一个参数isZeStatus，用于判断是现实赞和踩，默认显示
	const createAiMsg = (text, skillType, userMsg, isZeStatus = true, isPerformanceReport = false) => {
		let message = {
			msg: '',
			think: '',
			dataId: '',
			type: 'ai',
			id: uuid(),
			loading: true,
			isBtns: true,
			reGen: true,
			voiceStatus: true,
			zeStatus: isZeStatus ? '0' : '-3',
			isIframe: '',
			skillType,
			userMsg: userMsg || '',
			isPerformanceReport,
		};
		text ? (message.loadingText = t('common.gdprfy')) : '';
		return message;
	};
	//获取最后一条用户消息
	const getLastUser = () => {
		let ar = chatList.value.filter((ele) => ele.type === 'user');
		let msg = ar?.[ar.length - 1]?.msg;
		return msg;
	};
	//获取最后一条chat
	const getLastChat = computed(() => {
		return chatList.value[chatList.value.length - 1];
	});
	//获取最后一天user
	const getLastUserChat = computed(() => {
		let ar = chatList.value.filter((ele) => ele.type === 'user');
		let msg = ar?.[ar.length - 1];
		return msg;
	});
	//复制
	const copyText = (id) => {
		const clipboard = new ClipboardJS('.copy-btn', {
			target: function () {
				return document.getElementById(id);
			},
		});
		clipboard.on('success', () => {
			showToast({
				position: 'top',
				message: '复制成功',
			});
			clipboard.destroy();
		});
	};

	//业绩播报相关逻辑
	/**
	 * 辅助函数：根据报告代码从cardsInfoList中获取日期范围
	 * @param {string} reportCd - 报告代码
	 * @param {{startLocalDate: string, endLocalDate: string}} defaultRange - 默认日期范围
	 * @returns {{startTime: string, endTime: string}}
	 */
	const getReportDateRange = (reportCd, defaultRange) => {
		const cardInfo = cardsInfoList.value.find((item) => item.reportCd === reportCd);
		return {
			startTime: cardInfo?.startTime || defaultRange.startLocalDate,
			endTime: cardInfo?.endTime || defaultRange.endLocalDate,
		};
	};
	/**
	 * 辅助函数：重命名对象或对象数组中的键
	 * @param {object | object[]} data - 要处理的数据
	 * @param {object} keyMap - 新旧键的映射关系, e.g. { oldKey: newKey }
	 * @returns {object | object[]}
	 */
	const renameKeys = (data, keyMap) => {
		const renameObj = (obj) =>
			Object.keys(obj).reduce((acc, oldKey) => {
				const newKey = keyMap[oldKey] || oldKey;
				acc[newKey] = obj[oldKey];
				return acc;
			}, {});

		if (Array.isArray(data)) {
			return data.map(renameObj);
		}
		return renameObj(data);
	};
	/**
	 * 辅助函数：处理品牌销售数据（格式化和计算）
	 * @param {Array<object>} salesBrandItems - 来自API的品牌销售数据数组
	 * @returns {Array<object>}
	 */
	const processSalesBrandData = (salesBrandItems) => {
		return salesBrandItems.map((item) => ({
			name: item.name,
			salesV: formatKNumber(item.salesV),
			targetV: formatKNumber(item.targetV),
			achievementRate: new Decimal(item.achievementRate * 100).toDecimalPlaces(0) + '%',
			yearOnYear: new Decimal(item.yearOnYear * 100).toDecimalPlaces(0) + '%',
			yearOnYearGrowth: formatKNumber(item.yearOnYearGrowth),
			chain: new Decimal(item.chain * 100).toDecimalPlaces(0) + '%',
			moMGrowth: formatKNumber(item.moMGrowth),
			salesVLm: item.salesVLm,
			salesVLy: item.salesVLy,
		}));
	};
	/**
	 * 辅助函数：从HTML字符串中提取纯文本
	 * @param {string} htmlString
	 * @returns {string}
	 */
	const extractTextFromHtml = (htmlString) => {
		if (!htmlString) return '';
		const tempElement = document.createElement('div');
		tempElement.innerHTML = htmlString;
		return tempElement.innerText;
	};
	//按钮组
	const actions = ref([]);
	const qtList = ref([]);
	//获取所有卡片数据
	const getCardData = async () => {
		// 1. 创建所有三个独立请求的 Promise
		const queryListPromise = queryList({ reportType: 'CARD' });

		const getReportsPromise = getReports(['salesTrendsOverview', 'salesAnalysisTeamReport', 'salesTrendBrandReport', 'topHospitalSalesAnalysis', 'hospitalAnalysis']);

		const getQuestionPromise = getQuestion({ username: userStore.userInfo.username }, 0);

		// 2. 使用 Promise.all 并行执行所有三个请求
		const [queryListResult, getReportsResult, getQuestionResult] = await Promise.all([queryListPromise, getReportsPromise, getQuestionPromise]);

		// 3. 分别处理 getCardData 的逻辑
		const combinedResults = queryListResult.result;
		const combinedResults1 = getReportsResult.result.flatMap((ele) => ele.reportVOList).filter((ele) => ele.reportCd === 'hospitalAnalysis' || ele.reportCd === 'salesPerformanceProductivity');

		cardsInfoList.value = [...combinedResults, ...combinedResults1];
		// 4. 处理 getQuestionList 的逻辑
		questionListAll = getQuestionResult.result.content.map((ele) => ele.basicQuestion);
		canRenderCard.value = cardsInfoList.value.length > 0;
	};
	return {
		interfaceUrl,
		domain,
		isPc,
		agentName,
		route,
		router,
		chatList,
		welcomeText,
		goBack,
		createUserMsg,
		createAiMsg,
		getLastUser,
		getLastChat,
		chatId,
		isMultiChat,
		isStream,
		copyText,
		isLoading,
		zeJson,
		caiShow,
		cardsInfoList,
		cardInfoC,
		actions,
		qtList,
		getLastUserChat,
		getReportDateRange,
		renameKeys,
		processSalesBrandData,
		extractTextFromHtml,
		canRenderCard,
		getCardData,
		questionListAll,
	};
};
