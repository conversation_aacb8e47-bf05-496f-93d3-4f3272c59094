import hospitalAnalysis from '@/views/report/components/hospitalAnalysis';
import salesPerformanceProductivity from '@/views/report/components/salesPerformanceProductivity';
import marekShareReport from '@/views/report/components/marekShareReport';
import processManagementAndAnalysis from '@/views/report/components/processManagementAndAnalysis/processManagementAndAnalysis';
import monthlyReportOnSales from '@/views/report/components/sales/monthlyReportOnSales.vue';
import salesOverviewAnalysisReport from '@/views/report/components/sales/salesOverview/index.vue';
import trendReportOnSales from '@/views/report/components/sales/trendReportOnSales.vue';
import topHospitalSalesAnalysis from '@/views/report/components/topHospitalSalesAnalysis';
import salesAnalysisTeamReport from '@/views/report/components/salesAnalysisTeamReport';
import salesTrendProductReport from '@/views/report/components/salesTrendProductReport';
import salesTrendBrandReport from '@/views/report/components/salesTrendBrandReport';
import salesTrendsOverview from '@/views/report/components/salesTrendsOverview';
import salesTrendKEPPRA from '@/views/report/components/salesTrendKEPPRA';
import salesTrendVIMPAT from '@/views/report/components/salesTrendVIMPAT';
import salesTrendNEUPRO from '@/views/report/components/salesTrendNEUPRO';
import salesTrendZYRTEC from '@/views/report/components/salesTrendZYRTEC';
import salesTrendXYZAL from '@/views/report/components/salesTrendXYZAL';
import topHospitalSalesAnalysisKEPPRA from '@/views/report/components/topHospitalSalesAnalysisKEPPRA';
import topHospitalSalesAnalysisVIMPAT from '@/views/report/components/topHospitalSalesAnalysisVIMPAT';
import hospitalAnalysisKEPPRA from '@/views/report/components/hospitalAnalysisKEPPRA';
import hospitalAnalysisVIMPAT from '@/views/report/components/hospitalAnalysisVIMPAT';
import teamTrendAReport from '@/views/report/components/teamTrendAReport';

import firstPPT from '@/views/report/components/ppt/firstPPT.vue';
import secondPPT from '@/views/report/components/ppt/secondPPT.vue';
import lastPPT from '@/views/report/components/ppt/lastPPT.vue';

import hospitalAnalysisPPT from '@/views/report/components/ppt/hospitalAnalysisPPT.vue';
import marekShareReportPPT1 from '@/views/report/components/ppt/marekShareReportPPT1.vue';
import marekShareReportPPT2 from '@/views/report/components/ppt/marekShareReportPPT2.vue';
import processManagementAndAnalysisPPT1 from '@/views/report/components/ppt/processManagementAndAnalysisPPT1.vue';
import processManagementAndAnalysisPPT2 from '@/views/report/components/ppt/processManagementAndAnalysisPPT2.vue';
import salesPerformanceProductivityPPT from '@/views/report/components/ppt/salesPerformanceProductivityPPT.vue';
import salesOverviewAnalysisReportPPT from '@/views/report/components/ppt/salesOverview/index.vue';
import monthlyReportOnSalesPPT from '@/views/report/components/ppt/monthlyReportOnSalesPPT.vue';
import trendReportOnSalesPPT from '@/views/report/components/ppt/trendReportOnSalesPPT.vue';
import topHospitalSalesAnalysisPPT from '@/views/report/components/ppt/topHospitalSalesAnalysisPPT.vue';
import salesAnalysisTeamPPT from '@/views/report/components/ppt/salesAnalysisTeamPPT.vue';
import salesTrendProductReportPPT from '@/views/report/components/ppt/salesTrendProductReportPPT.vue';
import salesTrendBrandReportPPT from '@/views/report/components/ppt/salesTrendBrandReportPPT.vue';
import salesTrendsOverviewPPT from '@/views/report/components/ppt/salesTrendsOverviewPPT.vue';
import salesTrendsKEPPRAPPT from '@/views/report/components/ppt/salesTrendsKEPPRAPPT.vue';
import salesTrendsVIMPATPPT from '@/views/report/components/ppt/salesTrendsVIMPATPPT.vue';
import salesTrendsNEUPROPPT from '@/views/report/components/ppt/salesTrendsNEUPROPPT.vue';
import salesTrendsZYRTECPPT from '@/views/report/components/ppt/salesTrendsZYRTECPPT.vue';
import salesTrendsXYZALPPT from '@/views/report/components/ppt/salesTrendsXYZALPPT.vue';
import topHospitalSalesAnalysisPPTKEPPRA from '@/views/report/components/ppt/topHospitalSalesAnalysisPPTKEPPRA.vue';
import topHospitalSalesAnalysisPPTVIMPAT from '@/views/report/components/ppt/topHospitalSalesAnalysisPPTVIMPAT.vue';
import hospitalAnalysisPPTKEPPRA from '@/views/report/components/ppt/hospitalAnalysisPPTKEPPRA.vue';
import hospitalAnalysisPPTVIMPAT from '@/views/report/components/ppt/hospitalAnalysisPPTVIMPAT.vue';
import teamTrendAReportPPT from '@/views/report/components/ppt/teamTrendAReportPPT.vue';

export const useReport = (arr) => {
	//报告相关
	const dom = shallowReactive({
		hospitalAnalysis,
		salesPerformanceProductivity,
		marekShareReport,
		processManagementAndAnalysis,
		monthlyReportOnSales,
		salesOverviewAnalysisReport,
		trendReportOnSales,
		topHospitalSalesAnalysis,
		salesAnalysisTeamReport,
		salesTrendProductReport,
		salesTrendBrandReport,
		salesTrendsOverview,
		salesTrendKEPPRA,
		salesTrendVIMPAT,
		salesTrendNEUPRO,
		salesTrendZYRTEC,
		salesTrendXYZAL,
		topHospitalSalesAnalysisKEPPRA,
		topHospitalSalesAnalysisVIMPAT,
		hospitalAnalysisKEPPRA,
		hospitalAnalysisVIMPAT,
		teamTrendAReport,
	});
	let reportCds = ref([]);
	reportCds.value = arr.map((ele) => ele.reportCd);
	//报告ppt相关
	let obj = {
		hospitalAnalysis: 'hospitalAnalysisPPT',
		salesPerformanceProductivity: 'salesPerformanceProductivityPPT',
		marekShareReport: ['marekShareReportPPT1', 'marekShareReportPPT2'],
		processManagementAndAnalysis: ['processManagementAndAnalysisPPT1', 'processManagementAndAnalysisPPT2'],
		monthlyReportOnSales: 'monthlyReportOnSalesPPT',
		salesOverviewAnalysisReport: 'salesOverviewAnalysisReportPPT',
		trendReportOnSales: 'trendReportOnSalesPPT',
		topHospitalSalesAnalysis: 'topHospitalSalesAnalysisPPT',
		salesAnalysisTeamReport: 'salesAnalysisTeamPPT',
		salesTrendProductReport: 'salesTrendProductReportPPT',
		salesTrendBrandReport: 'salesTrendBrandReportPPT',
		salesTrendsOverview: 'salesTrendsOverviewPPT',
		salesTrendKEPPRA: 'salesTrendsKEPPRAPPT',
		salesTrendVIMPAT: 'salesTrendsVIMPATPPT',
		salesTrendNEUPRO: 'salesTrendsNEUPROPPT',
		salesTrendZYRTEC: 'salesTrendsZYRTECPPT',
		salesTrendXYZAL: 'salesTrendsXYZALPPT',
		topHospitalSalesAnalysisKEPPRA: 'topHospitalSalesAnalysisPPTKEPPRA',
		topHospitalSalesAnalysisVIMPAT: 'topHospitalSalesAnalysisPPTVIMPAT',
		hospitalAnalysisKEPPRA: 'hospitalAnalysisPPTKEPPRA',
		hospitalAnalysisVIMPAT: 'hospitalAnalysisPPTVIMPAT',
		teamTrendAReport: 'teamTrendAReportPPT',
	};
	let reportPPTs = ref(['firstPPT', 'secondPPT']);
	for (let i of reportCds.value) {
		if (Array.isArray(obj[i])) {
			reportPPTs.value.push(...obj[i]);
		} else {
			reportPPTs.value.push(obj[i]);
		}
	}
	reportPPTs.value.push('lastPPT');
	console.log(reportPPTs.value);
	const PPTDom = shallowReactive({
		firstPPT,
		secondPPT,
		lastPPT,
		hospitalAnalysisPPT,
		marekShareReportPPT1,
		marekShareReportPPT2,
		processManagementAndAnalysisPPT1,
		processManagementAndAnalysisPPT2,
		salesPerformanceProductivityPPT,
		salesOverviewAnalysisReportPPT,
		monthlyReportOnSalesPPT,
		trendReportOnSalesPPT,
		topHospitalSalesAnalysisPPT,
		salesAnalysisTeamPPT,
		salesTrendProductReportPPT,
		salesTrendBrandReportPPT,
		salesTrendsOverviewPPT,
		salesTrendsKEPPRAPPT,
		salesTrendsVIMPATPPT,
		salesTrendsNEUPROPPT,
		salesTrendsZYRTECPPT,
		salesTrendsXYZALPPT,
		topHospitalSalesAnalysisPPTKEPPRA,
		topHospitalSalesAnalysisPPTVIMPAT,
		hospitalAnalysisPPTKEPPRA,
		hospitalAnalysisPPTVIMPAT,
		teamTrendAReportPPT,
	});
	return { dom, reportCds, PPTDom, reportPPTs };
};
