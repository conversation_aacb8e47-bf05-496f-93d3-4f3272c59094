// src/hooks/useDefaultValue.js
import { reactive, ref, nextTick } from 'vue';
import { getFilterTime } from '@/utils/index';
// 定义映射关系
let indicatorTypeMap = {
	formalMedicineEntryRate: '正式准入率(%)',
	admissionRate: '准入率(%)',
	sales: '销售(K)',
	ach: '按达成%',
	growth: '按同比增长%',
	growthAmount: '同比增长值(K)',
	growthAch: '同比增量贡献(%)',
	mom: '环比(%)',
	momAmount: '环比增长值(K)',
	momAch: '环比增量贡献(%)',
};
let cubeIndicatorTypeMap = {
	shipment: '发出',
	shipment_contribution: '发出贡献',
	sales: '销售',
	sales_contribution: '销售贡献',
	inventory: '库存',
};
export async function useDefaultValue(defaultValue, options = {}, cube) {
	// 解构外部传入的引用（需由调用者提供）
	const {
		query,
		brand, // brand 组件的 ref
		orgZ, // orgZ 组件的 ref
		defaultSku,
		defaultPerson,
		defaultProvinceCity,
		defaultProvince,
		defaultHos,
		currentIndex,
		typeFilter,
		time,
		switchComRef,
	} = options;
	// 处理 defaultValue 的逻辑
	async function processDefaultValue() {
		console.log(defaultValue);

		// 1. 处理 month 和 monthMonth
		if (defaultValue.month && defaultValue.monthMonth) {
			query.month = defaultValue.month;
			query.monthName = defaultValue.monthMonth;
		}

		// 2. 处理省市
		if (defaultValue.countyName && (defaultValue.province || defaultValue.city)) {
			query.countyName = defaultValue.countyName;
			query.province = Array.isArray(defaultValue.province) ? defaultValue.province : defaultValue.province?.split(',');
			query.city = Array.isArray(defaultValue.city) ? defaultValue.city : defaultValue.city?.split(',');
			if (defaultProvinceCity) {
				defaultProvinceCity.value = {
					province: query.province,
					city: query.city,
				};
			}
			if (defaultProvince) {
				defaultProvince.value = query.province;
			}
		}

		// 3. 处理品牌
		if (defaultValue.brandName && defaultValue.brandId) {
			query.brandName = defaultValue.brandName;
			query.brandId = defaultValue.brandId;
			if (brand.value) {
				brand.value.handleReset();
				defaultSku.value = Array.isArray(defaultValue.brandId) ? defaultValue.brandId : defaultValue.brandId?.split(',');
				await nextTick();
				brand.value.handleDefault();
			}
		}

		// 4. 处理人员
		if (defaultValue.personName && defaultValue.personId) {
			query.personName = defaultValue.personName;
			query.personId = defaultValue.personId;
			if (orgZ.value) {
				orgZ.value.handleReset();
				defaultPerson.value = Array.isArray(defaultValue.personId) ? defaultValue.personId : defaultValue.personId?.split(',');
				await nextTick();
				orgZ.value.handleDefault();
			}
		}

		// 5. 处理医院
		if (defaultValue.hospitalName && defaultValue.hospitalId) {
			query.hospitalName = defaultValue.hospitalName;
			query.hospitalId = defaultValue.hospitalId;
			defaultHos.value = [query.hospitalId];
		}
		//6.处理日期
		if (defaultValue.startDate && defaultValue.endDate) {
			if (time.value) {
				time.value.resetActive();
				query.startDate = getFilterTime(defaultValue.startDate);
				query.endDate = getFilterTime(defaultValue.endDate);
			}
		}
		// 7.处理准入页签
		if (defaultValue.currentStatus) {
			currentIndex.value = defaultValue.currentStatus;
			query.currentStatus = defaultValue.currentStatus;
		}
		// 8.处理热力图页签
		if (defaultValue.indicatorType) {
			currentIndex.value = indicatorTypeMap[defaultValue.indicatorType];
			typeFilter ? (typeFilter.value = defaultValue.indicatorType) : '';
			query.indicatorType = typeFilter ? typeFilter.value : defaultValue.indicatorType;
		}
		//处理cube卡片
		if (defaultValue.indicatorType && cube) {
			currentIndex.value = cubeIndicatorTypeMap[defaultValue.indicatorType];
			query.indicatorType = defaultValue.indicatorType;
		}
		// 9.处理市场
		if (defaultValue.marketId && defaultValue.marketName) {
			if (defaultValue.brandId) {
				query.brandId = defaultValue.brandId;
			}
		}
		//10.处理销量金额切换
		if (defaultValue.salesOrAmount) {
			switchComRef.value.checked = defaultValue.salesOrAmount === '数量' ? false : true;
		}
		return Promise.resolve();
	}
	await processDefaultValue();
}
