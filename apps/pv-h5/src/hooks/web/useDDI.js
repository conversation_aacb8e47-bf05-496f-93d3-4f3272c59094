import byPersonnel from '@/views/observe/compoents/byPersonnel.vue';
import byBrand from '@/views/observe/compoents/byBrand.vue';
import byProduct from '@/views/observe/compoents/byProduct.vue';
import byDetail from '@/views/observe/compoents/byDetail.vue';
import byHospital from '@/views/observe/compoents/byHospital.vue';
import { shallowReactive } from 'vue';
export const useDDI = () => {
	const dom = shallowReactive({
		按人员: byPersonnel,
		按品牌: byBrand,
		按产品: byProduct,
		明细: byDetail,
		按医院: byHospital,
	});
	//动态组件 which render
	let com = computed(() => (item) => dom[item]);
	return { com };
};
