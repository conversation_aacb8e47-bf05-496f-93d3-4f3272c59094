import salesInsight from '@/views/manage/components/salesInsight.vue';
import visitAnalysis from '@/views/manage/components/visitAnalysis.vue';
import mySalesRanking from '@/views/manage/components/mySalesRanking.vue';

import { shallowReactive } from 'vue';
export const useMyCard = () => {
	const dom = shallowReactive({
		mySalesAchievementRank: mySalesRanking,
		mySalesInsights: salesInsight,
		myVisitAnalysis: visitAnalysis,
	});
	//动态组件 which render
	let com = computed(() => {
		return (item) => {
			return dom[item.reportCd];
		};
	});
	return { dom, com };
};
