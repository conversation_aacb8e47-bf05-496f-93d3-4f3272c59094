import LoadingComponent from '@/components/global/loading.vue';

import { shallowReactive } from 'vue';
export const useCard = () => {
	const dom = shallowReactive({
		salesAchievementStatus: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesAchievementStatus.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		branchSalesOverview: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/branchSalesOverview.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesRankingDistribution: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesRankingDistribution.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesRankingDistributionAsc: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesRankingDistributionAsc.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		saleTrend: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/saleTrend.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesShop: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesShop.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		ddi: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/ddi.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		admissionSituation: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/admissionSituation.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		admissionHospitals: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/admissionHospitals.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		listing: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/listing.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		visitSummary: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/visitSummary.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		competitiveMarketShare: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/competitiveMarketShare.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		marekShare: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/marekShare.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		visitManagementAndPerformance: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/visitManagementAndPerformance.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		visitDetails: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/visitDetails.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		channelSalesTrendAnalysis: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/channelSalesTrendAnalysis.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		channelSalesComparisonAnalysis: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/channelSalesComparisonAnalysis.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		//穆桥新增卡片
		branchSalesOverview2: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/branchSalesOverview2.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesTrendProduct: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesTrendProduct.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesAnalysisTeam: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesAnalysisTeam.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesTrendBrand: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesTrendBrand.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		recruitmentRankingOfDoctors: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/recruitmentRankingOfDoctors.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		rankingOfContractedSpeakers: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/rankingOfContractedSpeakers.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesAchievementStatusBar: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesAchievementStatusBar.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		// 按城市分析销售情况
		citySalesOverview: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/citySalesOverview.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		// 按省份分析销售情况
		provinceSalesOverview: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/provinceSalesOverview.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		admissionSituationSales: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/admissionSituationSales.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesRankingDistributionDSM: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesRankingDistributionDSM.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesRankingDistributionDSMAsc: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesRankingDistributionDSMAsc.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesRankingDistributionRSM: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesRankingDistributionRSM.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesRankingDistributionRSMAsc: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/salesRankingDistributionRSMAsc.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		admissionSituationProvince: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/admissionSituationProvince.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		admissionSituationCity: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/admissionSituationCity.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		admissionSalesHospital: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/admissionSalesHospital.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		admissionSituationTeam: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/admissionSituationTeam.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		businessSalesAnalytics: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/businessSalesAnalytics.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		keyProductsAchieved: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/keyProductsAchieved.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		admissionSituationDSM: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/admissionSituationDSM.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		admissionSituationRSM: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/admissionSituationRSM.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		admissionSituationSubordinateTeam: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/admissionSituationSubordinateTeam.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		marekShareQuarter: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/marekShareQuarter.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		marekShareQuarterRegion: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/marekShareQuarterRegion.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		marekShareRegion: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/marekShareRegion.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		competitiveMarketShareRegion: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/competitiveMarketShareRegion.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		inventorySalesPurchaseTrends: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/cube/inventorySalesPurchaseTrends.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		productAchievement: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/cube/productAchievement.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		provinceSales: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/cube/provinceSales.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		salesSituation: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/cube/salesSituation.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		sentFromChannels: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/cube/sentFromChannels.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
		teamSales: defineAsyncComponent({
			loader: () => import('@/views/observe/compoents/cube/teamSales.vue'),
			// 加载异步组件时使用的组件
			loadingComponent: LoadingComponent,
		}),
	});
	//动态组件 which render
	let com = computed(() => {
		return (item) => {
			const name = item.reportCd.indexOf('-') > -1 ? item.reportCd.split('-')[0] : item.reportCd;
			return dom[name];
		};
	});
	return { dom, com };
};
