import { getReToken, getToken, setReToken, setToken, removeReToken, removeToken } from '@/utils/auth';
import useEnterStore from '@/store/modules/enterprise';
import { refreshToken } from '@/api/login';

// --- 状态和配置  ---
let isRefreshing = ref(false);
let failedQueue = [];

// --- 队列处理器 ---
const processQueue = (error, token = null) => {
	failedQueue.forEach((prom) => {
		if (error) {
			prom.reject(error);
		} else {
			prom.resolve(token);
		}
	});
	failedQueue = [];
};

// --- 核心 fetch 封装 ---
async function fetchWithAuth(url, options = {}) {
	const enterStore = useEnterStore();
	// 1. 获取 token 并添加到请求头
	const headers = new Headers(options.headers || {});
	if (getToken()) {
		headers.set('Authorization', `Bearer ${getToken()}`);
	}
	options.headers = headers;
	// 2. 发起原始请求
	const response = await fetch(url, options);
	// 3. 检查是否是 token 过期错误 (401)
	if (response.status === 401) {
		if (isRefreshing.value) {
			// 如果已经在刷新 token，则将当前请求加入队列等待
			return new Promise((resolve, reject) => {
				failedQueue.push({ resolve, reject });
			}).then((newToken) => {
				// 等待刷新成功后，用新 token 重试
				headers.set('Authorization', `Bearer ${newToken}`);
				options.headers = headers;
				return fetch(url, options);
			});
		}
		isRefreshing.value = true;
		try {
			if (!getReToken()) {
				// 换取新token失败，跳转到登出页面
				location.href = '/app/mobile/logout';
				return Promise.reject(new Error('Session expired. No refresh token.'));
			}
			// 4. 发起刷新 token 的请求
			let res = await refreshToken(enterStore.enterInfo.authAddress, enterStore.enterInfo.id, enterStore.enterInfo.clientId);
			const { access_token, expires_in, refresh_token, refresh_expires_in } = res;
			// 5. 更新 token 并处理等待队列
			setToken(access_token, expires_in);
			setReToken(refresh_token, refresh_expires_in);
			processQueue(null, access_token);
			// 6. 用新 token 重试原始请求
			headers.set('Authorization', `Bearer ${access_token}`);
			options.headers = headers;
			return fetch(url, options);
		} catch (error) {
			// 刷新失败，登出并拒绝所有等待的请求
			processQueue(error, null);
			location.href = '/app/mobile/logout';
			return Promise.reject(error);
		} finally {
			isRefreshing.value = false;
		}
	}
	// 如果不是 401 错误，直接返回响应
	return response;
}

// --- 导出的 Composable 函数 ---

export function useApi() {
	// 返回一个可以直接使用的 fetch 实例
	return {
		apiFetch: fetchWithAuth,
	};
}
