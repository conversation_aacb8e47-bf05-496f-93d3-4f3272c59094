import { findById, hasEnabled } from '@/utils/index';
import usefilterStore from '@/store/modules/filter';
export function useOrg(data) {
	let filterStore = usefilterStore();
	let orgList = [];
	let defaultPerson = '';
	let personId = '';
	let personName = '全部人员';
	const orgData = data.find((ele) => ele.id === 'org');
	if (orgData) {
		// 设置数据范围
		if (orgData.dataRange && orgData.dataRange.length > 0) {
			const id = orgData.dataRange[orgData.dataRange.length - 1];
			orgList = [findById(filterStore.treeInfo, id)];
			if (hasEnabled(orgList)) {
				// 设置默认值
				if (orgData.dataDefault.length > 0) {
					const defaultValue = orgData.dataDefault[orgData.dataDefault.length - 1];
					const defaultData = findById(orgList, defaultValue);
					if (defaultData && defaultData.enable) {
						defaultPerson = defaultData.h;
						personId = defaultData.h;
						personName = defaultData.e;
					}
				}
			} else {
				orgList = [];
			}
		} else {
			orgList = filterStore.treeInfo;
			// 设置默认值
			if (orgData.dataDefault.length > 0) {
				const defaultValue = orgData.dataDefault[orgData.dataDefault.length - 1];
				const defaultData = findById(orgList, defaultValue);
				if (defaultData && defaultData.enable) {
					defaultPerson = defaultData.h;
					personId = defaultData.h;
					personName = defaultData.e;
				}
			}
		}
	} else {
		orgList = filterStore.treeInfo;
	}

	return {
		orgList,
		defaultPerson,
		personId,
		personName,
	};
}
