// hooks/useLocalStorage.js
import { ref } from 'vue';

// 自定义 Hook
export function useCardLibraryLocalStorage(key = 'cardLibraryInfo') {
	// 使用 ref 创建响应式数据，初始值从 localStorage 获取
	const data = ref(getStorageData());

	// 获取 localStorage 数据
	function getStorageData() {
		const storedData = localStorage.getItem(key);
		try {
			return storedData ? JSON.parse(storedData) : {};
		} catch (e) {
			console.error(`解析 ${key} 失败，初始化为空对象`, e);
			return {};
		}
	}
	// 获取指定字段的值
	function getField(fieldName) {
		return data.value[fieldName]; // 直接从响应式数据中获取
	}
	// 更新指定字段
	function updateField(fieldName, fieldValue) {
		data.value[fieldName] = fieldValue;
		localStorage.setItem(key, JSON.stringify(data.value));
		return data.value; // 返回更新后的对象
	}

	// 清空数据（可选）
	function clearStorage() {
		data.value = {};
		localStorage.removeItem(key);
	}

	// 返回 Hook 的功能
	return {
		data, // 响应式数据
		updateField, // 更新字段方法
		getField,
		clearStorage, // 清空方法（可选）
	};
}
