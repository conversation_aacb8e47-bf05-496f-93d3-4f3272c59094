/**
 * 一个用于处理聊天脚本助手中滚动和UI状态的 Vue Composable
 */
export const useScriptAssistantScroll = () => {
	// --- 响应式状态 ---
	const showScrollDown = ref(false); // 控制“滚动到底部”按钮的显示
	const isExpand = ref(false); // 控制聊天工具栏是否为展开状态

	// --- 内部变量 ---
	let intersectionObserver = null; // 用于观察滚动位置
	let mutationObserver = null; // 用于观察 class 变化

	// --- 方法 ---

	/**
	 * 平滑滚动到 #aiCon 容器的底部
	 * @param {'smooth' | 'auto'} t - 滚动行为
	 */
	const scrollBottom = (t = 'smooth') => {
		nextTick(() => {
			const aiCon = document.querySelector('#aiCon');
			if (aiCon) {
				aiCon.scrollTo({
					top: aiCon.scrollHeight,
					behavior: t,
				});
			}
		});
	};

	/**
	 * 使用 IntersectionObserver 观察一个“哨兵”元素，以确定是否滚动到底部
	 * @param {Element} sentinelElement - 位于滚动列表底部的哨兵DOM元素
	 */
	const observeVisibility = (sentinelElement) => {
		if (!sentinelElement) return;

		const observerCallback = (entries) => {
			const entry = entries[0];
			// 当哨兵元素进入视口时，表示用户在底部，应隐藏按钮
			// 当哨兵离开视口时，表示用户已向上滚动，应显示按钮
			showScrollDown.value = !entry.isIntersecting;
		};

		intersectionObserver = new IntersectionObserver(observerCallback);
		intersectionObserver.observe(sentinelElement);
	};

	/**
	 * 使用 MutationObserver 观察 .chat-bottom-tool 元素的 class 变化
	 * 以确定其是否展开 (是否含有 'active' class)
	 */
	const observeExpansion = () => {
		const toolElement = document.querySelector('.chat-bottom-tool');
		if (!toolElement) return;

		// 1. 定义当 DOM 变化时执行的回调
		const observerCallback = (mutationsList) => {
			for (const mutation of mutationsList) {
				console.log(mutation);

				// 我们只关心 'class' 属性的变化
				if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
					// 重新检查 'active' class 是否存在，并更新 isExpand
					isExpand.value = mutation.target.classList.contains('active');
					console.log(`Class changed, isExpand is now: ${isExpand.value}`);
				}
			}
		};

		// 2. 创建观察器实例
		mutationObserver = new MutationObserver(observerCallback);

		// 3. 配置并开始观察：我们只关心属性的变化
		mutationObserver.observe(toolElement, { attributes: true });

		// 4. 【重要】立即执行一次初始状态检查
		isExpand.value = toolElement.classList.contains('active');
	};

	// --- 生命周期钩子 ---

	// 在组件卸载时，断开所有观察器，防止内存泄漏
	onUnmounted(() => {
		if (intersectionObserver) {
			intersectionObserver.disconnect();
		}
		if (mutationObserver) {
			mutationObserver.disconnect();
		}
	});

	// --- 返回 ---
	return {
		// 状态
		showScrollDown,
		isExpand,
		// 方法
		scrollBottom,
		observeVisibility, // 重命名了 observeItem，更清晰
		observeExpansion,
	};
};
