import { findById1, hasEnabled } from '@/utils/index';
import usefilterStore from '@/store/modules/filter';

export function useOrg1(data) {
	let filterStore = usefilterStore();
	let orgList = [];
	let defaultPerson = '';
	let personId = '';
	let personName = '全部人员';
	const orgData = data.find((ele) => ele.id === 'org');
	if (orgData) {
		// 设置数据范围
		if (orgData.dataRange && orgData.dataRange.length > 0) {
			const id = orgData.dataRange[orgData.dataRange.length - 1];
			orgList = [findById1(filterStore.cubeOrgTreeInfo, id)];
			// if (hasEnabled(orgList)) {
			// 设置默认值
			if (orgData.dataDefault.length > 0) {
				const defaultValue = orgData.dataDefault[orgData.dataDefault.length - 1];
				const defaultData = findById1(orgList, defaultValue);
				if (defaultData && defaultData.enable) {
					defaultPerson = defaultData.territory_code;
					personId = defaultData.territory_code;
					personName = defaultData.employee_name;
				}
			}
			// } else {
			// 	orgList = [];
			// }
		} else {
			orgList = filterStore.cubeOrgTreeInfo;
			// 设置默认值
			if (orgData.dataDefault.length > 0) {
				const defaultValue = orgData.dataDefault[orgData.dataDefault.length - 1];
				const defaultData = findById1(orgList, defaultValue);
				if (defaultData && defaultData.enable) {
					defaultPerson = defaultData.territory_code;
					personId = defaultData.territory_code;
					personName = defaultData.employee_name;
				}
			}
		}
	} else {
		orgList = filterStore.cubeOrgTreeInfo;
	}

	return {
		orgList,
		defaultPerson,
		personId,
		personName,
	};
}
