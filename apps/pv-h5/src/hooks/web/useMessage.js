import { shallowReactive } from 'vue';
export const useMessage = () => {
	const dom = shallowReactive({
		userMessage: defineAsyncComponent({
			loader: () => import('@/views/intelligentAgent/myReactorAssistant/components/userMessage.vue'),
		}),
		llmMessage: defineAsyncComponent({
			loader: () => import('@/views/intelligentAgent/myReactorAssistant/components/llmMessage.vue'),
		}),
		intelligentAnalysis: defineAsyncComponent({
			loader: () => import('@/views/intelligentAgent/myReactorAssistant/components/intelligentAnalysis.vue'),
		}),
		intelligentAnalysisDeepSeekReport: defineAsyncComponent({
			loader: () => import('@/views/intelligentAgent/myReactorAssistant/components/intelligentAnalysisDeepSeekReport.vue'),
		}),
		generateSystemReport: defineAsyncComponent({
			loader: () => import('@/views/intelligentAgent/myReactorAssistant/components/generateSystemReport.vue'),
		}),
	});
	//动态组件 which render
	let com = computed(() => {
		return (item) => {
			return dom[item.skillType];
		};
	});
	return { dom, com };
};
