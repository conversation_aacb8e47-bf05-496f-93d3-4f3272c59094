import AudioPlayer from '@/utils/ap';
import { showToast } from 'vant';
import CryptoJS from 'crypto-js';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/user';
export const useScriptAssistantAudioPlayer = (chatList) => {
	// 语音相关
	const audioPlayer = new AudioPlayer();

	const APPID = '56d35e48';
	const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
	const API_KEY = '93d27369dabb708f1b9a826d03a49200';
	let ttsWS = null;
	const voiceEv = async (val) => {
		// chatList里所有的voiceStatus改位true
		resetAudioVoice();
		// 通过val.id获取chatList的相等元素
		const index = chatList.value.findIndex((item) => item.id === val.id);
		chatList.value[index].voiceStatus = val.vType === 'play' ? false : true;

		ttsWS?.close();
		audioPlayer.reset();
		if (val.vType === 'play') {
			// 开始语音播报
			const url = getWebSocketUrl(API_KEY, API_SECRET);
			if ('WebSocket' in window) {
				ttsWS = new WebSocket(url);
			} else if ('MozWebSocket' in window) {
				ttsWS = new MozWebSocket(url);
			} else {
				showToast({
					position: 'top',
					message: '浏览器不支持WebSocket',
				});
				return;
			}
			// 发送消息
			ttsWS.onopen = () => {
				audioPlayer.start({
					autoPlay: true,
					sampleRate: 16000,
					resumePlayDuration: 1000,
				});
				let text = document.getElementById(val.id).innerText;
				let tte = 'UTF8';
				let params = {
					common: {
						app_id: APPID,
					},
					business: {
						aue: 'raw',
						auf: 'audio/L16;rate=16000',
						vcn: 'x4_lingfeichen_assist', //发音人
						speed: 50, // 语速
						volume: 50, // 音量
						pitch: 50, // 音色
						bgs: 0,
						tte,
					},
					data: {
						status: 2,
						text: encodeText(text, tte),
					},
				};
				ttsWS.send(JSON.stringify(params));
			};

			ttsWS.onmessage = (e) => {
				console.log(e, '讯飞语音状态???????????????????????????????????');
				let jsonData = JSON.parse(e.data);
				// 合成失败
				if (jsonData.code !== 0) {
					showToast({
						position: 'top',
						message: '语音合成失败, 请稍后再试',
					});
					return;
				}
				audioPlayer.postMessage({
					type: 'base64',
					data: jsonData.data.audio,
					isLastData: jsonData.data.status === 2,
				});
				if (jsonData.code === 0 && jsonData.data.status === 2) {
					//0是成功2是最后一次
					ttsWS.close();
				}
			};

			ttsWS.onerror = (e) => {
				console.error(e);
			};
			ttsWS.onclose = () => {};
		}
	};

	// 关闭语音
	const closeAudioVoice = () => {
		resetAudioVoice();
		ttsWS?.close();
		audioPlayer.reset();
	};

	const resetAudioVoice = () => {
		for (const i of chatList.value) {
			if (i.type === 'user') continue;
			if (i.isBtns === false) continue;
			if (i.voiceStatus === true) continue;
			i.voiceStatus = true;
		}
	};

	// 播放停止把voiceStatus改位true
	audioPlayer.onStop = () => {
		resetAudioVoice();
	};
	const encodeText = (text, type) => {
		if (type === 'unicode') {
			let buf = new ArrayBuffer(text.length * 4);
			let bufView = new Uint16Array(buf);
			for (let i = 0, strlen = text.length; i < strlen; i++) {
				bufView[i] = text.charCodeAt(i);
			}
			let binary = '';
			let bytes = new Uint8Array(buf);
			let len = bytes.byteLength;
			for (let i = 0; i < len; i++) {
				binary += String.fromCharCode(bytes[i]);
			}
			return window.btoa(binary);
		} else {
			return Base64.encode(text);
		}
	};
	const getWebSocketUrl = (apiKey, apiSecret) => {
		var url = 'wss://tts-api.xfyun.cn/v2/tts';
		var host = location.host;
		var date = new Date().toGMTString();
		var algorithm = 'hmac-sha256';
		var headers = 'host date request-line';
		var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`;
		var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
		var signature = CryptoJS.enc.Base64.stringify(signatureSha);
		var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
		var authorization = btoa(authorizationOrigin);
		url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
		return url;
	};
	//全局语音控制
	const filterStore = usefilterStore();
	const userStore = useUserStore();
	const isGlobalVoice = computed(() => {
		return filterStore.isGlobalVoice;
	});
	const acGlobalVoice = (val) => {
		filterStore.SET_IS_GLOBAL_VOICE(val);
	};
	// 语言环境
	const language = computed(() => {
		return filterStore.language === 'zh' ? '中文' : '英文';
	});
	return {
		ttsWS,
		voiceEv,
		audioPlayer,
		resetAudioVoice,
		closeAudioVoice,
		filterStore,
		userStore,
		isGlobalVoice,
		acGlobalVoice,
		language,
	};
};
