// useFilters.js
import { filterSkuInfoTree, filterOrganizationTree, filterHospital, getProvinceTree } from '@/api/filter';

// 导出一个函数，用于处理级联选择器的变化
export function useCascader(query, refs) {
	console.log(query);

	// 从refs中解构出city、hospital、brandData
	const { city, hospital, brandData, orgList } = refs;
	// 定义一个响应式对象，用于存储加载状态
	const filterLoading = ref({
		city: false,
		hospital: false,
		brand: false,
		org: false,
	});
	// 级联方法
	async function cascaderChange(type) {
		try {
			if (type === 'org') {
				if (brandData) getBrand();
				if (city) getCity();
				if (hospital) getHospital();
			}
			if (type === 'brand') {
				if (orgList) getPerson();
				if (city) getCity();
				if (hospital) getHospital();
			}
			if (type === 'city') {
				if (brandData) getBrand();
				if (orgList) getPerson();
				if (hospital) getHospital();
			}
			if (type === 'hospital') {
				if (brandData) getBrand();
				if (city) getCity();
				if (orgList) getPerson();
			}
		} catch (error) {
			console.error('Error fetching filter data:', error);
		}
	}

	//重新获取人员筛选器
	const getPerson = async () => {
		filterLoading.value.org = true;
		// 如果query.personId存在，则将其转换为数组
		const skuCode = query.brandId ? query.brandId.split(',') : [];
		const hospCode = query.hospitalId ? query.hospitalId.split(',') : [];
		const province = query.province || [];
		const city = query.city || [];
		// 调用filterOrganizationTree函数，获取人员数据
		const res = await filterOrganizationTree({ skuCode, hospCode, province, city });
		if (res.result.isExceededAuthority) {
			orgList.value = [];
			filterLoading.value.org = false;
			return;
		}
		orgList.value = res.result;
		filterLoading.value.org = false;
	};
	//重新获取产品筛选器
	const getBrand = async () => {
		filterLoading.value.brand = true;
		// 如果query.personId存在，则将其转换为数组
		const territoryCode = query.personId ? query.personId.split(',') : [];
		const hospCode = query.hospitalId ? query.hospitalId.split(',') : [];
		const province = query.province || [];
		const city = query.city || [];
		// 调用filterSkuInfoTree函数，获取品牌数据
		let res = await filterSkuInfoTree({ territoryCode, hospCode, province, city });
		if (res.result.isExceededAuthority) {
			// 将品牌数据赋值给brandData
			brandData.value = [];
			// 将加载状态设置为false
			filterLoading.value.brand = false;
			return;
		}
		// 将品牌数据赋值给brandData
		brandData.value = res.result;
		// 将加载状态设置为false
		filterLoading.value.brand = false;
		console.log(res);
	};
	// 重新获取省份筛选器
	const getCity = async () => {
		filterLoading.value.city = true;
		// 如果query.personId存在，则将其转换为数组
		const territoryCode = query.personId ? query.personId.split(',') : [];
		const hospCode = query.hospitalId ? query.hospitalId.split(',') : [];
		const skuCode = query.brandId ? query.brandId.split(',') : [];

		// 调用getProvinceTree函数，获取省份数据
		const res = await getProvinceTree({ territoryCode, hospCode, skuCode });
		if (res.result.isExceededAuthority) {
			// 将省份数据赋值给city
			city.value.golbalInit([]);
			// 将加载状态设置为false
			filterLoading.value.city = false;
			return;
		}
		// 将省份数据赋值给city
		city.value.golbalInit(res.result);
		// 将加载状态设置为false
		filterLoading.value.city = false;
		console.log(res);
	};

	// 重新获取终端筛选器
	const getHospital = async () => {
		filterLoading.value.hospital = true;
		// 如果query.personId存在，则将其转换为数组
		const territoryCode = query.personId ? query.personId.split(',') : [];
		const skuCode = query.brandId ? query.brandId.split(',') : [];
		const province = query.province || [];
		const city = query.city || [];
		// 调用filterHospital函数，获取医院数据
		const res = await filterHospital({ territoryCode, size: 50, province, city, skuCode });
		console.log(res);
		if (res.result.isExceededAuthority) {
			hospital.value.handleDefault({
				hospitalList: [],
			});
			filterLoading.value.hospital = false;
			return;
		}
		hospital.value.handleDefault({
			hospitalList: res.result.content?.map((ele) => {
				return { id: ele.hospCode, name: ele.hospName };
			}),
		});
		filterLoading.value.hospital = false;
		// 将加载状态设置为false
	};

	// 初始化所有筛选器
	// 初始化筛选条件
	const initFilters = () => {
		let personalFlag = false;
		let brandFlag = false;
		let cityFlag = false;
		let hospitalFlag = false;
		if (query.personId && query.personName) {
			brandFlag = true;
			cityFlag = true;
			hospitalFlag = true;
		}
		if (query.brandName && query.brandId) {
			personalFlag = true;
			cityFlag = true;
			hospitalFlag = true;
		}
		if (query.province || query.city) {
			personalFlag = true;
			brandFlag = true;
			hospitalFlag = true;
		}
		if (query.hospitalName && query.hospitalId) {
			personalFlag = true;
			brandFlag = true;
			cityFlag = true;
		}
		if (personalFlag && orgList) getPerson();
		if (cityFlag && city) getCity();
		if (hospitalFlag && hospital) getHospital();
		if (brandFlag && brandData) getBrand();
	};

	return {
		cascaderChange,
		getCity,
		getHospital,
		initFilters,
		filterLoading,
	};
}
