import { initChatId, getChatDetailApi, updateChatDescApi, updateMsgApi, addMsgApi, deleteMsgApi } from '@/api/agent-tools';
import { uuid } from '@/utils/index';
const chatList = ref([]); //消息列表
let chatId = ref(''); // 会话ID
let isLoading = ref(false); //页面是否有正在回答的消息
export const useAssistant = () => {
	//init会话id
	const initChat = async () => {
		let res = await initChatId({ conversation_type: '' });
		chatId.value = res.data.conversation_id;
		const params = new URLSearchParams(window.location.search);
		params.set('chatId', chatId.value); // 添加或更新 query 参数
		const newUrl = window.location.pathname + '?' + params.toString();
		history.replaceState(null, '', newUrl);
	};
	// 直接写入回话id
	const setChatId = (id) => {
		chatId.value = id;
		const params = new URLSearchParams(window.location.search);
		params.set('chatId', chatId.value); // 添加或更新 query 参数
		const newUrl = window.location.pathname + '?' + params.toString();
		history.replaceState(null, '', newUrl);
	};
	//更新会话desc
	const updateChatDesc = async (desc = '') => {
		//更新会话描述
		if (chatList.value.filter((ele) => ele.type === 'user')?.length === 1) {
			await updateChatDescApi(chatId.value, {
				description: getLastUserMsg(),
			});
		} else if (chatList.value.length <= 0) {
			await updateChatDescApi(chatId.value, {
				description: desc,
			});
		}
	};
	//更新ai消息
	const updateAiChat = async (messageId) => {
		await updateMsgApi(messageId, { content: JSON.stringify(getLastUser.value) });
	};
	//更新user消息
	const updateUserChat = async (messageId) => {
		await updateMsgApi(messageId, { content: JSON.stringify(getLastChat.value) });
	};
	// 添加消息
	const addHistoryMessage = async (options) => {
		await addMsgApi(options);
	};
	//根据会话id获取会话详情
	const getChatList = async (id, type) => {
		let res = await getChatDetailApi(id, { limit: 1000 });
		let list = res.data.messages;
		let transformlist = [];
		if (list.length < 1) return;
		// 1. 在循环外定义一个标志位
		let skipNextAiMessage = false;

		for (let item of list) {
			// 2. 在每次循环开始时，检查是否需要跳过当前的 AI 消息
			if (skipNextAiMessage && item.role === 'assistant') {
				// 如果需要跳过，则重置标志位并进入下一次循环
				skipNextAiMessage = false;
				deleteMsgApi({ message_ids: [item.id] });
				continue; // continue 会立即结束本次循环，开始下一次循环
			}

			if (item.role === 'user') {
				let data;
				// 3. 判断用户消息的 content 是否为 JSON
				if (isJsonString(item.content)) {
					// 如果是 JSON，正常处理
					data = JSON.parse(item.content);
					data.skillType = 'userMessage';
					transformlist.push(data);
				} else {
					// 调用接口删除这条消息
					deleteMsgApi({ message_ids: [item.id] });
					skipNextAiMessage = true; // <--- 核心：设置标志，以便跳过下一个AI消息
				}
			} else {
				let data = JSON.parse(item.content);
				data.zeStatus = !item.is_disliked && !item.is_liked ? '0' : item.is_liked ? '1' : '2';
				if (data.skillType === 'intelligentAnalysis') {
					data.summaryLoading = false;
					// isLoading.value = false;
					data.loading = false;
					data.noSummary = true;
				}
				transformlist.push(data);
			}
		}
		// 检查 transformlist 是否有内容
		if (transformlist.length > 0) {
			// 获取最后一个元素
			const lastItem = transformlist[transformlist.length - 1];
			// 判断最后一个元素是否是用户消息
			if (lastItem && lastItem.type === 'user') {
				console.log('发现最后一条消息为用户消息，准备删除:', lastItem);
				try {
					// 调用接口删除这条消息
					await deleteMsgApi({ message_ids: [lastItem.dataId] });
					// 接口调用成功后，从待渲染列表中移除最后一条消息
					transformlist.pop();
					console.log('已成功从服务器和本地列表中删除最后一条用户消息。');
				} catch (deleteError) {
					console.error('调用 deleteMsgApi 删除消息失败:', deleteError);
					// 如果API调用失败，我们选择不从列表中移除它，以保持UI与服务端数据的一致性。
				}
			}
		}
		chatList.value = transformlist.concat(chatList.value);
	};
	//生成一条用户消息
	const createUserMsg = (text) => {
		return {
			msg: text,
			dataId: '',
			type: 'user',
			id: uuid(),
			loading: false,
			zeStatus: '0',
			skillType: 'userMessage',
		};
	};

	//获取最后一条用户消息文本
	const getLastUserMsg = () => {
		let ar = chatList.value.filter((ele) => ele.type === 'user');
		let msg = ar?.[ar.length - 1]?.msg;
		return msg;
	};
	//获取最后一条消息
	const getLastChat = computed(() => {
		return chatList.value[chatList.value.length - 1];
	});
	//获取最后一天用户消息对象
	const getLastUser = computed(() => {
		let ar = chatList.value.filter((ele) => ele.type === 'user');
		let obj = ar?.[ar.length - 1];
		return obj;
	});
	return { initChat, updateChatDesc, getLastUserMsg, getChatList, updateUserChat, updateAiChat, addHistoryMessage, chatList, chatId, getLastChat, getLastUser, setChatId, isLoading };
};
function isJsonString(str) {
	// 首先，必须是字符串类型
	if (typeof str !== 'string') {
		return false;
	}
	try {
		const result = JSON.parse(str);
		// 其次，解析结果必须是对象（包括数组），并且不能是 null
		// 因为 typeof null 也是 'object'，所以要额外判断 result !== null
		if (typeof result === 'object' && result !== null) {
			return true;
		}
	} catch (e) {
		return false;
	}
	return false;
}
