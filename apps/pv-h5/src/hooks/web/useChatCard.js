import LoadingComponent from '@/components/global/loading.vue';
//创建异步组件的工厂函数
const asyncC = (loader) => {
	return defineAsyncComponent({
		loader,
		loadingComponent: LoadingComponent,
	});
};
// 组件配置 - 按功能分组
const createComponentConfig = () => {
	return {
		// 销售相关组件
		salesAchievementStatus: asyncC(() => import('@/views/observe/compoents/salesAchievementStatusBar.vue')),
		salesAchievementStatusBar: asyncC(() => import('@/views/observe/compoents/salesAchievementStatusBar.vue')),
		branchSalesOverview: asyncC(() => import('@/views/observe/compoents/branchSalesOverview.vue')),
		branchSalesOverview2: asyncC(() => import('@/views/observe/compoents/branchSalesOverview2.vue')),
		citySalesOverview: asyncC(() => import('@/views/observe/compoents/citySalesOverview.vue')),
		provinceSalesOverview: asyncC(() => import('@/views/observe/compoents/provinceSalesOverview.vue')),
		salesShop: asyncC(() => import('@/views/observe/compoents/salesShop.vue')),
		salesAnalysisTeam: asyncC(() => import('@/views/observe/compoents/salesAnalysisTeam.vue')),
		businessSalesAnalytics: asyncC(() => import('@/views/observe/compoents/businessSalesAnalytics.vue')),
		// 排名分布组件
		salesRankingDistribution: asyncC(() => import('@/views/observe/compoents/salesRankingDistribution.vue')),
		salesRankingDistributionAsc: asyncC(() => import('@/views/observe/compoents/salesRankingDistributionAsc.vue')),
		salesRankingDistributionDSM: asyncC(() => import('@/views/observe/compoents/salesRankingDistributionDSM.vue')),
		salesRankingDistributionDSMAsc: asyncC(() => import('@/views/observe/compoents/salesRankingDistributionDSMAsc.vue')),
		salesRankingDistributionRSM: asyncC(() => import('@/views/observe/compoents/salesRankingDistributionRSM.vue')),
		salesRankingDistributionRSMAsc: asyncC(() => import('@/views/observe/compoents/salesRankingDistributionRSMAsc.vue')),
		// 趋势分析组件
		saleTrend: asyncC(() => import('@/views/observe/compoents/saleTrend.vue')),
		salesTrendBrand: asyncC(() => import('@/views/observe/compoents/salesTrendBrand.vue')),
		salesTrendProduct: asyncC(() => import('@/views/observe/compoents/salesTrendProduct.vue')),
		// DDI组件
		ddi: asyncC(() => import('@/views/observe/compoents/ddi.vue')),
		'ddi-zx': asyncC(() => import('@/views/observe/compoents/ddi-zx.vue')),
		// 准入情况组件
		admissionSituation: asyncC(() => import('@/views/observe/compoents/admissionSituation.vue')),
		admissionSituationSales: asyncC(() => import('@/views/observe/compoents/admissionSituationSales.vue')),
		admissionSituationProvince: asyncC(() => import('@/views/observe/compoents/admissionSituationProvince.vue')),
		admissionSituationCity: asyncC(() => import('@/views/observe/compoents/admissionSituationCity.vue')),
		admissionSalesHospital: asyncC(() => import('@/views/observe/compoents/admissionSalesHospital.vue')),
		admissionSituationTeam: asyncC(() => import('@/views/observe/compoents/admissionSituationTeam.vue')),
		admissionSituationDSM: asyncC(() => import('@/views/observe/compoents/admissionSituationDSM.vue')),
		admissionSituationRSM: asyncC(() => import('@/views/observe/compoents/admissionSituationRSM.vue')),
		admissionSituationSubordinateTeam: asyncC(() => import('@/views/observe/compoents/admissionSituationSubordinateTeam.vue')),
		admissionHospitals: asyncC(() => import('@/views/observe/compoents/admissionHospitals.vue')),
		listing: asyncC(() => import('@/views/observe/compoents/listing.vue')),
		// 市场份额组件
		competitiveMarketShare: asyncC(() => import('@/views/observe/compoents/competitiveMarketShare.vue')),
		competitiveMarketShareRegion: asyncC(() => import('@/views/observe/compoents/competitiveMarketShareRegion.vue')),
		marekShare: asyncC(() => import('@/views/observe/compoents/marekShare.vue')),
		marekShareQuarter: asyncC(() => import('@/views/observe/compoents/marekShareQuarter.vue')),
		marekShareQuarterRegion: asyncC(() => import('@/views/observe/compoents/marekShareQuarterRegion.vue')),
		marekShareRegion: asyncC(() => import('@/views/observe/compoents/marekShareRegion.vue')),
		// 访问相关组件
		visitSummary: asyncC(() => import('@/views/observe/compoents/visitSummary.vue')),
		visitManagementAndPerformance: asyncC(() => import('@/views/observe/compoents/visitManagementAndPerformance.vue')),
		visitDetails: asyncC(() => import('@/views/observe/compoents/visitDetails.vue')),
		// 报告组件
		topHospitalAnalysis: asyncC(() => import('@/views/report/components/hospitalAnalysis/index.vue')),
		salesPerformanceProductivity: asyncC(() => import('@/views/report/components/salesPerformanceProductivity/index.vue')),
		// 管理组件
		mySalesRanking: asyncC(() => import('@/views/manage/components/mySalesRanking.vue')),
		salesInsight: asyncC(() => import('@/views/manage/components/salesInsight.vue')),
		// Cube 数据分析组件
		keyProductsAchieved: asyncC(() => import('@/views/observe/compoents/keyProductsAchieved.vue')),
		inventorySalesPurchaseTrends: asyncC(() => import('@/views/observe/compoents/cube/inventorySalesPurchaseTrends.vue')),
		productAchievement: asyncC(() => import('@/views/observe/compoents/cube/productAchievement.vue')),
		provinceSales: asyncC(() => import('@/views/observe/compoents/cube/provinceSales.vue')),
		salesSituation: asyncC(() => import('@/views/observe/compoents/cube/salesSituation.vue')),
		sentFromChannels: asyncC(() => import('@/views/observe/compoents/cube/sentFromChannels.vue')),
		teamSales: asyncC(() => import('@/views/observe/compoents/cube/teamSales.vue')),
	};
};
//聊天卡片组合式函数
export const useChatCard = () => {
	const dom = shallowReactive(createComponentConfig());
	return { dom };
};
