<template>
	<div class="defaultLayout ap-defaultLayout">
		<!-- nav  -->
		<navBar v-if="!route.meta.isHiddenNavBar && !route.query.isMine" :navList="navList"></navBar>
		<navBar2 v-if="!route.meta.isHiddenNavBar && route.query.isMine"></navBar2>
		<div :class="{ homebox: route.query.isTabBar !== 'false' }">
			<!-- 主体内容 -->
			<router-view @sendMsg="sendMsg"></router-view>
		</div>
		<!-- 底部导航栏 -->
		<tabBar v-if="route.query.isTabBar !== 'false' && route.query.hiddenTabBar !== 'true'" :num="msgNum"></tabBar>

		<!-- 隐私弹窗 -->
		<privacyPolicy ref="prPolicy"></privacyPolicy>

		<!-- 我来反馈 -->
		<!-- <van-floating-bubble class="feedback-qu" v-model:offset="offset" :gap="0" axis="xy" magnetic="x" @click="iframeVis = true">
			<img style="width: 100%" src="@/assets/img/feedback.png" alt="" />
		</van-floating-bubble> -->

		<!-- 问题反馈弹窗 -->
		<!-- <van-popup v-model:show="iframeVis" position="bottom" round :style="{ height: '80%' }" :closeable="false">
			<ai-article-detail :sourceUrl="surveyUrl" :isToken="false" title="体验反馈" @close="iframeVis = false"></ai-article-detail>
		</van-popup> -->
	</div>
</template>
<script setup>
import useUserStore from '@/store/modules/user';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const userStore = useUserStore();
const userId = userStore.userInfo.username;
let route = useRoute();
let navList = ref([]); //nav栏
watch(
	route,
	(n) => {
		navList.value = n.matched[0].children.map((ele) => {
			// 是否只在demo用户显示
			if (ele.meta.isDemo === true && userId !== 'demo') {
				return { text: ele.meta.isNotI18n ? ele.meta.title : t(ele.meta.title), name: ele.name, navShow: false, father: n.matched[0].meta.title };
			} else {
				return { text: ele.meta.isNotI18n ? ele.meta.title : t(ele.meta.title), name: ele.name, navShow: !ele.meta.notNavShow, father: n.matched[0].meta.title };
			}
		});
	},
	{ immediate: true }
);

const prPolicy = ref(null);
// 显示使用须知
const showPrivacyPolicy = () => {
	setTimeout(() => {
		prPolicy.value.showPrivacyPolicy();
	}, 800);
};

// const iframeVis = ref(false);
// let surveyUrl = computed(() => {
// 	return `${import.meta.env.VITE_APP_SURVEY}?surveyParams=${encodeURIComponent(JSON.stringify({ userId: userId, userType: '代表' }))}&from=beigene&domainurl=${encodeURIComponent(`${location.protocol}//${location.host}`)}`;
// });
const offset = ref({ y: window.innerHeight * 0.7 });
let msgNum = ref(0);
const sendMsg = (num) => {
	msgNum.value = num;
};
// 显示使用须知
showPrivacyPolicy();
</script>
<style lang="scss" scoped>
.defaultLayout {
	background: var(--pv-bgc);
	color: var(--pv-default-color);

	.homebox {
		// height: calc(100vh - 75px);
		// overflow-y: scroll;
		padding-bottom: 75px;
	}
	::v-deep(.van-popup__close-icon) {
		// position: relative;
		top: 9px;
	}
}
</style>
