@import './global.scss';
@import 'filterData.scss';
@import './title.scss';

html {
	text-size-adjust: 100%;
}

body {
	height: 100%;
	margin: 0;
	font-family: 'PingFang SC', 'Microsoft YaHei', Helvetica, arial, 'Hiragino Sans GB', '\5b8b\4f53', sans-serif;
	font-size: 14px;
	line-height: 1.5;
	color: var(--ld-font-color);
	background-color: var(--ld-bg-color);
	-webkit-overflow-scrolling: touch;
	overscroll-behavior: none;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
	display: block;
}

audio,
canvas,
progress,
video {
	display: inline-block;
	vertical-align: baseline;
}

audio:not([controls]) {
	display: none;
	height: 0;
}

[hidden],
template {
	display: none;
}

svg:not(:root) {
	overflow: hidden;
}

a {
	color: #698df6;
	text-decoration: none;
	background: transparent;
	-webkit-tap-highlight-color: transparent;
}

a:active {
	color: #069;
	outline: 0;
}

abbr[title] {
	border-bottom: 1px dotted;
}

b,
strong {
	font-weight: bold;
}

dfn {
	font-style: italic;
}

mark {
	color: #000;
	background: #ff0;
}

small {
	font-size: 80%;
}

sub,
sup {
	position: relative;
	font-size: 75%;
	line-height: 0;
	vertical-align: baseline;
}

sup {
	top: -0.5em;
}

sub {
	bottom: -0.25em;
}

img {
	vertical-align: middle;
	border: 0;
}

hr {
	box-sizing: content-box;
	height: 0;
}

pre {
	overflow: auto;
	word-wrap: break-word;
	white-space: pre;
	white-space: pre-wrap;
}

code,
kbd,
pre,
samp {
	font-family: monospace;
	font-size: 1em;
}

button,
input,
optgroup,
select,
textarea {
	margin: 0;
	font: inherit;
	color: inherit;
	outline: none;
}

button {
	overflow: visible;
}

button,
select {
	text-transform: none;
}

button,
html input[type='button'],
input[type='reset'],
input[type='submit'] {
	appearance: button;
	cursor: pointer;
}

button[disabled],
html input[disabled] {
	cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
	padding: 0;
	border: 0;
}

input {
	line-height: normal;
}

input[type='checkbox'],
input[type='radio'] {
	box-sizing: border-box;
	padding: 0;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
	height: auto;
}

input[type='search'] {
	appearance: textfield;
	box-sizing: border-box;
}

input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-decoration {
	appearance: none;
}

fieldset {
	padding: 0.35em 0.625em 0.75em;
	margin: 0 2px;
	border: 1px solid #c0c0c0;
}

legend {
	padding: 0;
	border: 0;
}

textarea {
	overflow: auto;
	resize: vertical;
}

optgroup {
	font-weight: bold;
}

table {
	border-spacing: 0;
	border-collapse: collapse;
}

td,
th {
	padding: 0;
}

button,
input,
select,
textarea {
	font-family: 'Helvetica Neue', Helvetica, STHeiTi, Arial, sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
figure,
form,
blockquote {
	margin: 0;
}

ul,
ol,
li,
dl,
dd {
	padding: 0;
	margin: 0;
}

ul,
ol {
	list-style: none outside none;
}

h1,
h2,
h3 {
	font-weight: normal;
	line-height: 2;
}

input::placeholder,
textarea::placeholder {
	color: #ccc;
}

input:input-placeholder,
textarea:input-placeholder {
	color: #ccc;
}

input::input-placeholder,
textarea::input-placeholder {
	color: #ccc;
}

* {
	box-sizing: border-box;
}
