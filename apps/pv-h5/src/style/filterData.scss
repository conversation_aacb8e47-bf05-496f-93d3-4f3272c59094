.q-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap;

	.icon {
		font-size: 7px;
	}
}

.q-width-100 {
	width: 100%;
}

.q-width-49 {
	width: 49%;
}

.q-width-32 {
	width: 32%;
}

.q-fileter-item {
	border-radius: 5px;
	background: var(--pv-filter-bgc);
	display: flex;
	justify-content: center;
	align-items: center;
	height: 28px;
	margin-top: 8px;
	position: relative;

	.item-title {
		width: 70%;
		font-size: 12px;
		text-align: center;
	}

	i {
		position: absolute;
		right: 10px;
		color: #fff;
		font-size: 14px;
	}
}

.q_custom-popup {
	border-top-left-radius: 5px;
	border-top-right-radius: 5px;
	display: flex;
	flex-direction: column;
}
