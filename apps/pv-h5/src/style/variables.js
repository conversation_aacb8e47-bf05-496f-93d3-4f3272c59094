// import {tran} from '@/utils/index.js'
// 浅色主题
export const lightTheme = {
	// 'nav-height': '30.66667vw',//蓝色nav高度
	'nav-height': '11.46667vw', //白色nav高度
	'pv-bgc': '#fff', //主背景色
	'pv-card-bgc': '#F4F5F7', ////卡片背景色
	'pv-default-color': '#172B4D', //默认颜色
	'pv-no-active-color': '#6B778C',
	// 'pv-nav-bgc': '#013872',//蓝色nav
	'pv-nav-bgc': '#fff',
	'pv-nav-bar-active': '#fff',
	'pv-tabbar-active': '#0074f9',
	'pv-nav-active-color': '#000',
	'pv-filter-bgc': '#DFE1E6',
	'pv-filter-color': '#0647A6',
	'pv-fliter-calendar-bgc': 'rgba(0, 82, 204, 0.1)',
	'pv-fliter-product-sales-bgc': '#F4F5F7',
	'pv-gl': '#E95600',
	'pv-my-gl': '#5342AA',

	// 下面是demo，演示完毕可以删除
	'--ac-font-color': '#172B4D',
	'--ac-font-color-active': '#0052CC',
	'--ac-colors-myGray-500': '#7B838B',
	'--ac-active-color': '#aa9d7e',
	'--ac-bg-color': '#ffffff',
	'--ac-bg-active-color': '#0052cc',
	'--ac-bg-color--fff': '#F4F5F7',
	'--ac-bg-color--222': '#222222',
	'--ac-border-color': '#e2e2e2',
	'--ac-icon-default-color': '#999999',
};

// 深色主题
export const darkTheme = {};
