.f12 {
	font-size: 24px;
}

.f14 {
	font-size: 28px;
}

.f15 {
	font-size: 30px;
}

.f16 {
	font-size: 32px;
}

.f18 {
	font-size: 36px;
}

.f20 {
	font-size: 40px;
}

.f22 {
	font-size: 44px;
}

.f24 {
	font-size: 48px;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

.tl {
	text-align: left;
}

.tr {
	text-align: right;
}

.tc {
	text-align: center;
}

.p-x-6 {
	padding: 0 6px;
}

.p-x-12 {
	padding: 0 12px;
}

.p-y-6 {
	padding: 6px 0;
}

.p-y-12 {
	padding: 12px 0;
}

.m6 {
	margin: 6px;
}

.mt6 {
	margin-top: 6px;
}

.mb6 {
	margin-bottom: 6px;
}

.ml6 {
	margin-left: 6px;
}

.mr6 {
	margin-right: 6px;
}

.m12 {
	margin: 12px;
}

.mt12 {
	margin-top: 12px;
}

.mb12 {
	margin-bottom: 12px;
}

.ml12 {
	margin-left: 12px;
}

.mr12 {
	margin-right: 12px;
}

.clearfix {
	overflow: hidden;
	_zoom: 1;
}

.clearfix:after {
	content: '.';
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}

.circle {
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	border: 1px solid #ccc;
	overflow: hidden;
}

// 超过一行显示省略号
.ell {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

// css超出二行用点表示
.ell2 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	line-clamp: 2;
	-webkit-line-clamp: 2;
}

// css超出三行用点表示
.ell3 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	line-clamp: 3;
	-webkit-line-clamp: 3;
}

// 阴影效果
.st-shadow {
	box-shadow: -2px -2px 10px 0px rgba(246, 249, 255, 0.3), 2px 2px 15px 0px rgba(217, 217, 217, 0.5);
	border-radius: 10px;
}

#nprogress .bar {
	background: #0052cc !important; //自定义颜色
}

// echarts tooltip
.tooltip-heatmap {
	display: flex;
	padding: 5px 8px 5px 5px;
	align-items: center;
	font-size: 8px;

	.dot {
		width: 5px;
		height: 5px;
		margin-right: 5px;
		border-radius: 50%;
	}

	.date {
		color: var(--pv-no-active-color);
		margin-right: 10px;
	}

	.value {
		color: var(--pv-default-color);
	}
}

.tooltip-bar {
	padding: 5px 8px 5px 5px;
	font-size: 8px;
	line-height: 16px;

	.content {
		display: flex;
		align-items: center;
	}

	.dot {
		width: 5px;
		height: 5px;
		margin-right: 5px;
		border-radius: 50%;
	}

	.value {
		padding-left: 10px;

		span {
			color: var(--pv-default-color);
		}
	}
}

.tooltip-saletrend {
	padding: 5px 8px 5px 5px;
	font-size: 8px;

	.product {
		color: car(--pv-default-color);
		font-weight: bold;
	}

	.dot {
		display: inline-block;
		width: 5px;
		height: 5px;
		margin-right: 5px;
		border-radius: 50%;
	}

	.key {
		color: var(--pv-no-active-color);
	}

	.value {
		padding-left: 4px;
		color: var(--pv-default-color);
	}
}

.highlight {
	color: var(--pv-tabbar-active);
}

.vxe-table {
	.vxe-header--column {
		padding: 0 !important;

		.vxe-cell {
			padding: 8px 5px;
			text-align: center;
			line-height: initial;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: var(--pv-card-bgc);

			.vxe-cell--title {
				font-size: 9px;
				font-weight: bold;
			}
		}
	}

	.vxe-body--column {
		padding: 8px 0 !important;
		height: initial !important;

		.vxe-cell {
			text-align: center;
			line-height: initial;
			display: flex;
			justify-content: center;
			align-items: center;

			.vxe-cell--label {
				font-size: 9px;
			}
		}
	}

	.vxe-footer--row {
		background-color: var(--pv-tabbar-active);
	}

	.vxe-footer--column {
		padding: 0 !important;

		.vxe-cell {
			padding: 7px 5px !important;
			text-align: center !important;
			line-height: initial;

			.vxe-cell--item {
				font-size: 9px;
				color: var(--pv-bgc);
				position: relative;
				top: -2px;
			}
		}
	}

	.vxe-cell--sort {
		font-size: 8px;
	}

	.vxe-sort--desc-btn.sort--active,
	.vxe-sort--asc-btn.sort--active {
		color: var(--pv-tabbar-active) !important;
	}

	.vxe-sort--desc-btn,
	.vxe-sort--asc-btn {
		color: var(--vxe-table-column-icon-border-color) !important;
	}
}

.van-skeleton__content {
	margin-top: 20px;
}

.vxe-table--body-wrapper {
	min-height: 0 !important;
}

.demo-floating-bubble {
	width: 38px !important;
	height: 38px !important;

	i {
		font-size: 20px;
	}
}

.vxe-cell {
	color: var(--pv-default-color);
	text-align: left !important;
}

.ignore-ppt-title {
	// font-size: 33px;
	font-size: 28px;
	font-weight: bold;
	height: 46px;
	color: #000 !important;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.ignore-ppt-title-right {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		color: #000000;
		font-weight: 400;
		font-size: 10px;
		font-weight: initial;

		.ignore-ppt-title-right-text {
			width: 50px;
			display: inline-block;
		}

		.ignore-ppt-title-right-dw {
			display: inline-flex;
			justify-content: space-between;
		}

		// span {
		//   margin-bottom: 4px;
		// }
	}

	img {
		align-self: self-end;
		width: 98px !important;
		margin-bottom: 3px;
	}
}

.ignore-comment {
	margin-top: 5px;
	padding: 0 16px 70px;
	font-size: 14px;
	color: #666666;

	.comment-title {
		color: #333333;
		font-weight: bold;
		border-bottom: 2px solid #d9e2f3;
		padding-bottom: 4px;
		margin-top: 20px;
	}

	.comment-text {
		margin-top: 10px;

		.item {
			width: 112px;
			display: flex;
			justify-content: space-between;
		}
	}

	.comment-bottom {
		margin-top: 10px;
	}
}

.feedback-qu {
	height: 50px !important;
	width: 50px !important;
	// transform: none !important;
	// left: initial !important;
	// top: initial !important;
	// right: 18px !important;
	// bottom: 100px !important;
	background-color: transparent !important;
	z-index: 100 !important;

	i {
		font-size: 18px;
	}
}

@keyframes scroll {
	0% {
		transform: translateX(0);
	}

	100% {
		transform: translateX(-100%);
	}
}

@keyframes scroll1 {
	0% {
		transform: translateX(260px);
	}

	100% {
		transform: translateX(-100%);
	}
}

.vxe-loading {
	z-index: 9 !important;
}

// loading 动画
@keyframes textLoadingAnimation {
	0%,
	100% {
		opacity: 0;
	}

	50% {
		opacity: 1;
	}
}

::selection {
	background-color: transparent;
	/* 设置为透明 */
}

.chatLoading::after {
	content: '';
	display: inline-block;
	width: 3px;
	height: 14px;
	transform: translate(4px, 2px) scaleY(1.3);
	background-color: #2b5fd9;
	animation: textLoadingAnimation 0.8s infinite;
	margin-right: 6px;
  vertical-align: baseline;
}

.customer {
	color: #172b4d;
	font-size: 14px;

	.blue {
		color: #0b67e4;
	}

	&-title {
		font-weight: bold;
		font-size: 15px;
		margin-bottom: 7px;
	}

	&-title1 {
		font-weight: bold;
		font-size: 14px;
		margin-bottom: 5px;
		margin-top: 5px;
	}

	.fa {
		display: flex;
		align-items: center;
	}

	.dian {
		margin: 0 7px;

		&::after {
			content: '';
			display: block;
			border-radius: 50%;
			width: 4px;
			height: 4px;
			background-color: #172b4d;
		}
	}

	.desc {
		margin-top: 10px;

		span {
			color: #0b67e4;
		}
	}

	.mt10 {
		margin-top: 10px;
	}

	.btns {
		width: 295px;
		height: 35px;
		line-height: 35px;
		border-radius: 5px;
		background-color: #ffffff;
		color: #172b4d;
		margin-top: 10px;
		padding-left: 12px;
		padding-right: 15px;
		display: flex;
		align-items: center;

		span {
			flex: 1;
		}

		.right {
			width: 8px;
			height: 8px;
			border-top: 1px solid #6b778c;
			border-right: 1px solid #6b778c;
			transform: rotate(45deg);
		}
	}
}

// pdf 预览
.pdf-preview {
	height: 100%;
	width: 100%;
	border-radius: 6px;
	background-color: var(--pv-bgc);
	display: flex;
	flex-direction: column;

	.pdf-preview__header {
		display: flex;
		justify-content: flex-end;
		padding: 8px 12px 8px 0;
	}

	.pdf-preview__middle {
		flex: 1;
	}

	.pdf-preview__bottom {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 8px 0 12px;

		.bottom-item {
			background-color: var(--ac-bg-active-color);
			border-radius: 50%;
			width: 22px;
			height: 22px;
			display: flex;
			align-items: center;
			justify-content: center;

			i {
				color: var(--pv-bgc);
			}
		}

		.bottom-item__active {
			background-color: #cccccc;
		}

		.bottom-text {
			padding: 0 12px;
		}
	}
}

// chatType Popover
.chat-bottom-popover {
	display: flex;
	flex-direction: column;
	max-width: 320px;

	.popover-item {
		padding: 12px 8px;
		display: flex;
		align-items: center;

		img {
			width: 30px;
			height: 30px;
			margin-right: 8px;
		}

		.popover-item__desc {
			flex: 1;
			display: flex;
			flex-direction: column;

			.popover-item__desc--name {
				font-size: 16px;
				font-weight: 500;
				color: var(--ac-font-color);
			}

			.popover-item__desc--text {
				font-size: 12px;
				color: #6b778c;
			}
		}
	}

	.popover-item__active {
		background-color: #f4f5f7;
	}

	.popover-item__permission {
		display: none;
	}
}

// 关键任务
.mc-header-popover {
	.van-popover__action {
		padding: 0;
	}
}

.ignore-sale-table {
	--vxe-table-border-width: 1px;

	.vxe-table {
		.vxe-header--column {
			padding: 0 !important;

			.vxe-cell {
				padding: 8px 5px;
				text-align: center;
				line-height: initial;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: var(--pv-card-bgc);

				.vxe-cell--title {
					font-size: 9px;
					font-weight: bold;
				}
			}
		}

		.vxe-body--column {
			padding: 0px 0 !important;
			height: initial !important;

			.vxe-cell {
				text-align: center;
				line-height: initial;
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 5px;

				.vxe-cell--label {
					font-size: 9px;
				}
			}
		}

		.vxe-footer--row {
			background-color: var(--pv-tabbar-active);
		}

		.vxe-footer--column {
			padding: 0 !important;

			.vxe-cell {
				padding: 7px 5px !important;
				text-align: center;
				line-height: 1 !important;

				.vxe-cell--item {
					font-size: 9px;
					color: var(--pv-bgc);
					position: relative;
					top: -2px;
				}
			}
		}

		.vxe-cell--sort {
			font-size: 8px;
			display: none;
		}

		.vxe-sort--desc-btn.sort--active,
		.vxe-sort--asc-btn.sort--active {
			color: var(--pv-tabbar-active) !important;
		}

		.vxe-sort--desc-btn,
		.vxe-sort--asc-btn {
			color: var(--vxe-table-column-icon-border-color) !important;
		}
	}
}

.vxe-header--column.col--right {
	.vxe-cell {
		justify-content: flex-end !important;
	}
}

.vxe-body--column.col--left {
	.vxe-cell {
		justify-content: flex-start !important;
	}
}

.vxe-body--column.col--right {
	.vxe-cell {
		justify-content: flex-end !important;
	}
}

.vxe-footer--column.col--right {
	.vxe-cell {
		text-align: right !important;
	}
}
.common_disabled {
	pointer-events: none;
}
.branch-sales-overview-tip {
	line-height: 1.1;
}
