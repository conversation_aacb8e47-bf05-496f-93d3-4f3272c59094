.card-title {
	display: flex;
	justify-content: space-between;
	color: var(--pv-default-color);
	font-weight: bold;
	font-size: 14px;

	.icon-down {
		margin-right: 7px;
		width: 20px;
		margin-top: -4px;
	}

	.icon {
		width: 16px;
		height: 15px;
		position: relative;
		top: -3px;
	}

	.icon:nth-child(1) {
		margin-right: 7px;
	}
}

@media screen and (min-width: 600px) {
	.card-title {
		font-size: 14px !important;

		.icon-down {
			margin-right: 7px;
			width: 20px;
			margin-top: -4px;
		}

		.icon {
			width: 16px !important;
			height: 15px !important;
			top: -3px !important;
		}

		.icon:nth-child(1) {
			margin-right: 7px !important;
		}
	}
}
