/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	/* id 提高优先级 */
	#ap-br {
		.br-nav::after {
			height: 1px;
		}

		.ap-brNavList {
			width: 375px;
			padding: 7px 0;

			.active::after {
				width: 30px;
				height: 3px;
				border-radius: 2px;
				bottom: -7px;
			}
		}

		.br-nav-list {
			.ap-brNavListItem {
				padding: 0 16px;
			}
		}

		.ap-brList {
			width: 375px;

			.bottom-content {
				margin: 10px 15px 0;

				.content-item {
					border-radius: 5px;
					padding: 8px;
					margin-top: 12px;

					.item-title {
						font-size: 14px;
						margin-bottom: 8px;
						padding-left: 3px;
						:deep(.van-checkbox) {
							margin-right: 15px;
						}
					}

					.item-list {
						padding: 12px 15px;
						margin-bottom: 10px;
						border-radius: 5px;

						.bar {
							border-radius: 5px 0 0 5px;
							width: 4px;
						}

						.item-list__left {
							.left-name {
								font-size: 12px;

								span:nth-child(1) {
									margin-right: 3px;
								}

								.left-label {
									font-size: 7px;

									span {
										background-color: rgba(0, 82, 204, 0.1);
										border-radius: 2px;
										padding: 2px 3px;
										margin-right: 3px;
									}
								}
							}

							.desc {
								margin-top: 2px;
								font-size: 8px;
							}
						}

						.item-list__mc {
							width: 60px;
						}
					}
				}
			}
		}

		.list-btn1 {
			padding-bottom: 100px;
		}

		.list-btn2 {
			padding-bottom: 18px;
		}

		.fix-btn {
			position: fixed;
			width: 375px;
			display: flex;
			justify-content: center;
			bottom: 10%;

			&:deep(.van-button) {
				width: 280px;
				height: 42px;
				font-size: 16px;
				border-radius: 50px;
				background: linear-gradient(180deg, rgba(0, 138, 232, 1) 0%, rgba(0, 82, 204, 1) 100%);
				box-shadow: 0px 2px 10px 0px rgba(0, 82, 204, 0.1);
				font-weight: 500;
			}
		}
	}
}
