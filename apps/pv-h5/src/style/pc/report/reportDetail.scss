/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-rp {
		padding: 10px 15px;

		.bar {
			margin: 5px -15px;
		}

		.rp-header {
			gap: 8px;

			.rp-header__item {
				border-radius: 2px;
				padding: 6px 11px;

				span {
					font-size: 14px;
				}

				img {
					width: 10px;
					height: 11px;
				}
			}

			.flex-center {
				span {
					margin-right: 8px;
				}
			}
		}

		.rp-detail {
			padding-bottom: 12px;
		}

		.rp-content {
			.rp-content__title {
				padding-left: 5px;
				margin-bottom: 5px;
				font-size: 14px;
			}

			.rp-content__title::before {
				width: 3px;
			}

			.rp-content__textarea {
				border: 1px solid var(--pv-filter-bgc);

				&:deep(.van-cell) {
					padding: 5px;
				}

				&:deep(.van-field) {
					height: 80px;
				}
			}

			.rp-content__input {
				margin: 5px 0px;
				border: 1px solid var(--pv-filter-bgc);

				&:deep(.van-cell) {
					padding: 5px;
				}
			}

			.rp-content__not__editable {
				margin: 5px 0px;

				&:deep(.van-cell) {
					padding: 5px;
				}
			}

			.rp-content__list {
				margin-top: 5px;
				padding-left: 2px;

				span {
					font-size: 12px;
					padding-top: 6px;
				}
			}
		}

		::v-deep(.van-dialog__content) {
			padding: 16px 16px 8px;

			.van-cell__value {
				font-size: 14px;
				line-height: 1.5;
			}

			.item {
				font-size: 14px;

				.title {
					margin-bottom: 8px;
				}

				&:nth-child(1) {
					margin-bottom: 8px;
				}

				.van-radio-group {
					padding: 5px 0px;

					.van-radio__label {
						line-height: 1.2;
						margin-left: 8px;
					}

					.van-radio:nth-child(1) {
						margin-left: 5px;
						margin-right: 32px;
					}

					.van-icon,
					.van-radio__icon {
						width: 15px;
						height: 15px;
						font-size: 11px;
						border-width: 1px;
					}
				}
			}

			.input-inner {
				border-radius: 2px;
				padding: 3px 5px;
			}
		}

		.tip-popup {
			.wrapper {
				font-size: 14px;

				img {
					width: 90px;
					height: 82;
					top: 19px;
					right: 49px;
				}

				.tip {
					top: 116px;
					right: 39px;
				}
			}
		}

		:deep(.custo) {
			.img {
				padding-top: 30px;
				img {
					width: 35px;
				}
			}
			.tips {
				margin-top: 10px;
				font-size: 16px;
			}
			.know {
				width: 100px;
				height: 44px;
				line-height: 44px;
				border-radius: 50px 50px 50px 50px;
				border: 1px solid #ffffff;
				font-size: 16px;
				margin-top: 155px;
			}
		}
	}
}
