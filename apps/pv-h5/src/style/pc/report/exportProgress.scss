/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	/* id 提高优先级 */
	#ap-step-progress {
		padding: 16px;

		:deep(.van-popup) {
			padding: 20px;
			border-radius: 10px;
			box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
		}

		/* 步骤标题 */
		.title {
			font-size: 15px;
		}
		/* 步骤列表 */
		.step-list {
			height: 50px;
		}

		/* 每个步骤的容器 */
		.step-item {
			gap: 8px;
			padding: 2px 0;
			.step-item-pc {
				line-height: 1;
			}
			/* 步骤文本 */
			.step-title {
				font-size: 14px;
			}
		}
		/* 步骤状态样式 */
		.step-active {
			.step-title,
			.step-bullet {
				font-size: 16px !important;
			}
		}
	}
}
