/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-nav {
		padding-top: 13px;
		padding-bottom: 13px;

		font-size: 20px;

		.icon {
			left: 16px;
			font-size: 20px;
		}

		&-title {
			width: 260px;
		}

		.edit {
			font-size: 16px;
		}

		::v-deep(.van-dialog__content) {
			padding: 5px 20px;

			.input-inner {
				border: 1px solid var(--pv-filter-bgc);
				padding: 3px;
			}

			.van-cell {
				line-height: 1.2;
			}

			.van-field__value {
				font-size: 18px;
			}
		}

		.rp-content {
			margin-top: 10px;

			.rp-content__title {
				padding-left: 5px;
				margin-bottom: 5px;
				font-size: 14px;
			}

			.rp-content__title::before {
				content: '';
				position: absolute;
				width: 3px;
			}

			.rp-content__textarea {
				margin: 5px 0px 10px;
				border: 1px solid var(--pv-filter-bgc);

				&:deep(.van-cell) {
					padding: 5px;
				}
			}
		}
	}
}
