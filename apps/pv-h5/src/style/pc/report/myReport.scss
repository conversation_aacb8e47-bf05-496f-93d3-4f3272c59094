/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	/* id 提高优先级 */
	#ap-mr {
		.list {
			padding: 11px 15px;
		}

		.mr-item {
			padding: 13px 15px;
			border-radius: 4px;
			margin-bottom: 10px;

			.mr-item-header {
				.header-name {
					font-size: 16px;

					span:nth-child(2) {
						font-size: 12px;
						margin-left: 8px;
					}
				}
			}

			.card {
				padding: 14px 15px;
				border-radius: 5px;
				margin-top: 10px;

				.bar {
					width: 4px;
					border-radius: 5px 0 0px 5px;
				}
			}

			.mr-item-content {
				span {
					font-size: 14px;
					margin-bottom: 8px;
				}
			}
		}

		.icon {
			width: 44px;
			height: 44px;
		}
	}
}
