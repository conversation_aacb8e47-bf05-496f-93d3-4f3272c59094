/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-script-assistant {
		width: 375px;
		margin: 0 auto;
		.mc-header {
			width: 100%;
			height: 44px;
			padding: 0 6px;
			border-bottom: 1px solid var(--ac-border-color);

			.mc-header-icon {
				width: 32px;
				height: 32px;
				border-radius: 5px;
				img {
					width: 15px;
				}

				.history-img {
					margin-left: 12px;
					margin-right: 24px;
				}
			}

			.mc-header-name {
				font-size: 16px;
				width: 265px;
			}
		}

		.mc-content {
			.mc-content-list {
				flex: 1 0 0px;
				height: 0px;
				padding-top: 12px;
				.mc-content-list-left {
					margin-bottom: 12px;
					.mc-content-template {
						border-top-left-radius: 8px;
						border-bottom-right-radius: 8px;
						border-top-right-radius: 8px;
					}
					img {
						width: 26px;
						height: 26px;
						margin-right: 6px;
						margin-top: 6px;
					}

					.mc-content__groupList {
						padding: 8px 0 0 0;
						padding-left: 15px;
						width: 100%;
						span {
							margin-right: 8px;
							margin-bottom: 8px;
							border-radius: 5px;
							border: 1px solid #0b67e4;
							padding: 3px 6px;
						}
					}
				}

				.mc-content-list-right {
					padding-right: 15px;
					margin-bottom: 12px;
					.mc-content-template {
						border-top-right-radius: 8px;
						border-bottom-left-radius: 8px;
						border-top-left-radius: 8px;
						.mc-content-template__edit {
							border-top: solid 1px rgba(223, 225, 230, 0.3);
							margin-top: 2px;
						}
					}
				}
				.mc-content-template {
					max-width: 330px;
					min-width: 52px;
					padding: 10px;
				}
			}

			// 声明
			.mc-declare {
				margin: 6px 0px 3px 0px;
				font-size: 12px;
			}

			.mc-content-chat {
				margin: 0px auto;
				width: 100%;
				padding-inline: 0px;
				padding-top: 8px;
				padding-bottom: 6px;
				min-height: 64px;
				.chat-img {
					width: 25px;
					margin-top: 12.5px;
					margin-left: 15px;
				}
			}

			&:deep(.van-overlay) {
				width: 374px;
				left: 50%;
				margin-left: -187px;
			}
		}

		.mc-content__h5 {
			height: calc(100vh - 44px);
		}

		&:deep(.cai-action-sheet) {
			border-top-right-radius: 20px;
			border-top-left-radius: 20px;
			.van-action-sheet__header {
				font-size: 15px;
				line-height: 48px;
			}
			i {
				font-size: 16px;
			}

			.van-cell {
				margin: 0 15px 15px;
				width: calc(100% - 30px);
				border-radius: 6px;
				height: 125px;
				line-height: 24px;
				font-size: 14px;
				padding: 10px 16px;
			}

			.van-button {
				width: 238px;
				height: 40px;
				border-radius: 21px;
				font-size: 17px;
				margin-bottom: 20px;
				margin-left: 68.5px;
				padding: 0 15px;
			}
		}

		&:deep(.van-popup--bottom) {
			border-top-right-radius: 20px;
			border-top-left-radius: 20px;
			padding: 16px 0;
		}

		.btns {
			width: 100%;
			height: 35px;
			line-height: 35px;
			border-radius: 5px;
			margin-top: 10px;
			padding-left: 12px;
			padding-right: 15px;

			.right {
				width: 8px;
				height: 8px;
				border-top: 1px solid #6b778c;
				border-right: 1px solid #6b778c;
			}
		}
		.par {
			margin-left: 15px !important;
		}
		.ql {
			margin-top: 12px;
			.nk {
				margin-left: 15px;
				font-size: 12px;
			}
		}
		.ifr {
			height: 1px;
			border-radius: 5px;
			min-width: 310px;
		}
		// 华东动画
		.slide-enter-active,
		.slide-leave-active {
			transition: all 0.2s ease-in;
		}
		.slide-enter-from,
		.slide-leave-to {
			transform: translate(-50%, 25px);
			transform: translate(-50%, 25px);
			opacity: 0;
		}
		.recommend-cards {
			margin-bottom: 15px;
			&-list {
				margin-top: 8px;
			}
			&-item {
				margin-right: 9px;
				img {
					width: 96px !important;
					height: 60px !important;
					margin-right: 0 !important;
					margin-top: 0 !important;
				}
				.name {
					font-size: 10px;
					margin-top: 4px;
				}
			}
		}
		.no-found-card {
			&-item {
				margin-top: 8px;
				img {
					width: 150px !important;
					height: 94px !important;
					margin-right: 0 !important;
					margin-top: 0 !important;
				}
				span {
					font-size: 10px;
					margin-top: 8px;
				}
			}
		}

		::v-deep(.van-overlay) {
			background: rgba(0, 0, 0, 0.5);
			.wrapper {
				border-radius: 6px;
				background-color: #f4f5f7;
				// width: 90%;
				position: relative;
				top: 20%;
				left: 50%;
				transform: translateX(-50%);
				padding-bottom: 18px;
				.card-title {
					display: none;
				}
				.update-box {
					display: none;
				}
				.coms {
					max-height: 400px;
					overflow-y: auto;
				}
				.operation {
					display: flex;
					padding: 0 22px;
					div {
						width: 145px;
						flex: 1;
						line-height: 44px;
						border-radius: 6px;
						text-align: center;
						box-shadow: 0px 2px 9px 0px rgba(0, 82, 204, 0.1);
						font-size: 13px;
					}
					.cancel {
						color: #0052cc;
						background-color: rgba(0, 82, 204, 0.1);
						margin-right: 13px;
						width: 159px;
						height: 44px;
					}
					.confirm {
						background-color: #0052cc;
						color: #fff;
						width: 159px;
						height: 44px;
					}
				}
				&::before {
					content: '';
					display: block;
					width: 3%;
					height: 100%;
					position: absolute;
					left: 0;
					top: 0;
					background: rgba(0, 0, 0, 0.5);
				}
				&::after {
					content: '';
					display: block;
					width: 3%;
					height: 100%;
					position: absolute;
					right: 0;
					top: 0;
					background: rgba(0, 0, 0, 0.5);
				}
			}
		}
		.q-content {
			.q-title {
				display: flex;
				justify-content: space-between;
				margin-bottom: 4px;
				span:nth-child(1) {
					font-weight: bold;
					font-size: 14px;
					color: #172b4d;
				}
				span:nth-child(2) {
					color: #0052cc;
				}
			}
			.q-list {
				// background: #ffffff;
				border-radius: 5px;
				margin-bottom: 8px;
				padding: 9px 12px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-right: 35px;
				color: #0b67e4;
				border: 1px solid #0b67e4;
				&::after {
					content: '';
					width: 7px;
					height: 7px;
					border-top: 1px solid #0b67e4;
					border-right: 1px solid #0b67e4;
					transform: rotate(45deg);
				}
			}
		}
	}
	:deep(.van-popup) {
		.van-popover__arrow {
			left: 16px;
		}
	}
	#question-list {
		:deep(.van-popup) {
			background: #fff !important;
			padding: 28px 16px 20px !important;
			height: 80%;
			.top {
				display: flex;
				align-items: center;
				font-weight: bold;
				font-size: 16px;
				.back {
					width: 10px;
					height: 10px;
					border-top: 1px solid #000;
					border-right: 1px solid #000;
					transform: rotate(-135deg);
					margin-right: 13px;
				}
				img {
					margin-left: auto;
					width: 20px;
				}
			}
			.van-search {
				padding: 0;
				margin-top: 22px;
				.van-search__content {
					padding-left: 0;
					.van-search__field {
						height: 30px;
						padding: 0px 16px;
						.van-icon-search,
						.van-field__control::placeholder {
							color: #6b778c;
						}
					}
				}
			}
			.van-list {
				height: calc(100% - 58px - 20px);
				overflow-y: auto;
			}
			.list {
				&-item {
					padding: 16px 0;
					font-size: 15px;
					color: #172b4d;
					border-bottom: 1px solid rgba(151, 151, 151, 0.3);
				}
			}
		}
	}
}
