/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-sales-new-recommend {
		width: 375px;
		margin: 0 auto;

		.mc-header {
			width: 100%;
			height: 44px;
			padding: 0 6px;
			border-bottom: 1px solid var(--ac-border-color);
			.mc-header-icon {
				width: 32px;
				height: 32px;
				border-radius: 5px;
				img {
					width: 15px;
				}
			}

			.mc-header-name {
				font-size: 16px;
			}
		}

		.mc-content {
			.mc-content-cotainer {
				padding: 15px;
				margin-bottom: 20px;
			}
		}
		.item {
			border-radius: 6px;
			border: 1px solid #dfe1e6;
			padding: 15px;
			margin-bottom: 15px;
			:deep(.van-tag) {
				border-radius: 5px;
				font-size: 10px;
			}
			&__tag {
				margin-bottom: 8px;
			}
			&__title {
				font-size: 13px;
				margin-bottom: 8px;
			}
			&__info {
				font-size: 10px;
			}
			&__btn {
				:deep(.van-button) {
					--van-button-default-height: 24px;
					font-size: 12px;
					& + .van-button {
						margin-left: 10px;
					}
				}
			}
		}
		.btn-group {
			gap: 7px;
			margin: 12px auto 0;
			padding: 10px 15px;
			width: 375px;
			&:deep(.van-button) {
				border-radius: 5px;
				padding: 0;
				font-size: 14px;
				height: 40px;
			}
		}
		.mc-content__h5 {
			height: calc(100% - 44px);
		}
	}
}
