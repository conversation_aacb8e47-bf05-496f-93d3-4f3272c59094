/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-sales-recommend-list {
		width: 375px;
		margin: 0 auto;
		.mc-header {
			width: 100%;
			height: 44px;
			padding: 0 6px;
			border-bottom: 1px solid var(--ac-border-color);
			.mc-header-icon {
				width: 32px;
				height: 32px;
				border-radius: 5px;
				img {
					width: 15px;
				}
			}

			.mc-header-name {
				font-size: 16px;
			}
		}

		.mc-content {
			.mc-content-cotainer {
				padding: 0 15px;
				margin-bottom: 20px;
				flex: 1;
			}
		}
		.list {
			height: 100%;
			margin-top: 15px;
			.item {
				border-radius: 6px;
				margin-bottom: 15px;
				padding: 15px;
				font-size: 14px;
				&__name {
					border-bottom: 1px solid #dfe1e6;
					padding-bottom: 15px;
					padding-left: 10px;
					margin-bottom: 10px;
					&::before {
						width: 4px;
						height: 12px;
						top: 6px;
					}
				}
				&__body {
					font-size: 12px;
					padding: 0 10px;
				}
				&__question {
					margin-bottom: 10px;
				}
				&__time {
					margin-bottom: 10px;
				}
			}
		}
		.mc-content__h5 {
			height: calc(100% - 44px);
		}
	}
}
