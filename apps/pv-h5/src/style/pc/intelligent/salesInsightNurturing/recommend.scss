/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-sales-recommend {
		width: 375px;
		margin: 0 auto;
		.mc-header {
			width: 100%;
			height: 44px;
			padding: 0 6px;
			border-bottom: 1px solid var(--ac-border-color);
			.mc-header-icon {
				width: 32px;
				height: 32px;
				border-radius: 5px;
				img {
					width: 15px;
				}
			}

			.mc-header-name {
				font-size: 16px;
			}
		}

		.mc-content {
			.mc-content-cotainer {
				padding: 0 15px;
				margin-bottom: 20px;
			}
		}
		.empty {
			font-size: 10px;
			margin-top: 150px;
			img {
				width: 170px;
			}
		}
		.list {
			.item {
				border-bottom: 1px solid #dfe1e6;
				margin: 15px 0;
				padding-bottom: 15px;
				&__thumb {
					width: 165px;
					height: 106px;
					margin-right: 15px;
				}
				&__title {
					font-size: 13px;
					margin-bottom: 10px;
				}
				&__desc {
					font-size: 10px;
				}
			}
		}
		.mc-content__h5 {
			height: calc(100% - 44px);
		}
	}
}
