/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-sales-list {
		width: 375px;
		margin: 0 auto;
		.mc-header {
			width: 100%;
			height: 44px;
			padding: 0 6px;
			border-bottom: 1px solid var(--ac-border-color);
			.mc-header-icon {
				width: 32px;
				height: 32px;
				border-radius: 5px;
				img {
					width: 15px;
				}
			}

			.mc-header-name {
				font-size: 16px;
			}
		}

		.mc-content {
			.mc-content-cotainer {
				padding: 15px;
				margin-bottom: 20px;
			}
		}
		--van-search-background: #f4f5f7;
		--van-search-padding: 0;
		--van-search-content-background: #fff;
		.list {
			gap: 2px 15px;
			margin: 15px 0;
			.item {
				flex: 0 0 calc(50% - 7.5px);
				&__content {
					margin-bottom: 5px;
					img {
						height: 106px;
					}
				}
				&__title {
					font-size: 13px;
				}
			}
		}
		.mc-content__h5 {
			height: calc(100% - 44px);
		}
	}
}
