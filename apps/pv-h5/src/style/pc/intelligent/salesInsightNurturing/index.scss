/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-sales-insight-nurturing {
		width: 375px;
		margin: 0 auto;
		.mc-header {
			height: 44px;
			width: 100%;
			padding: 0 6px;
			.mc-header-icon {
				width: 32px;
				height: 32px;
				border-radius: 5px;
				img {
					width: 15px;
				}
			}

			.mc-header-name {
				font-size: 16px;
			}
		}

		.mc-content {
			.mc-content-cotainer {
				padding: 0 15px;
				margin-bottom: 20px;
			}
		}
		.header {
			margin-bottom: 13px;
			&__logo {
				width: 50px;
				height: 50px;
				box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.05);
				line-height: 50px;
				margin-right: 15px;
				img {
					max-width: 50px;
					max-height: 50px;
				}
			}
			&__name {
				font-size: 15px;
			}
			&__desc {
				font-size: 10px;
			}
			&__icon {
				img {
					width: 14px;
				}
			}
		}
		.info {
			border-radius: 16px;
			padding: 15px;
			margin-bottom: 15px;
			&:before {
				border-left: 7px solid transparent;
				border-right: 7px solid transparent;
				border-bottom: 7px solid rgba(255, 255, 255, 0.8);
				top: -7px;
				left: 20px;
			}
		}
		.count {
			border-radius: 6px;
			padding: 15px;
			margin-bottom: 15px;
			.item {
				&__info {
					font-size: 12px;
				}
				&__num {
					font-size: 20px;
				}
				&__title {
					font-size: 12px;
					img {
						width: 12px;
						vertical-align: -1px;
						margin-left: 2px;
					}
				}
				&:after {
					width: 1px;
					height: 30px;
				}
			}
		}
		.list {
			gap: 15px;
			.item {
				flex: 0 0 calc(50% - 7.5px);
				border-radius: 10px;
				padding: 15px;
				&__info {
					font-size: 12px;
				}
				&__num {
					font-size: 20px;
				}
				&__title {
					font-size: 12px;
					img {
						width: 5px;
					}
				}
			}
		}
		.mc-content__h5 {
			height: calc(100% - 44px);
		}
	}
}
