/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-sales-detail {
		width: 375px;
		margin: 0 auto;
		.mc-header {
			width: 100%;
			height: 44px;
			padding: 0 6px;
			border-bottom: 1px solid var(--ac-border-color);
			.mc-header-icon {
				width: 32px;
				height: 32px;
				border-radius: 5px;
				img {
					width: 15px;
				}
			}

			.mc-header-name {
				font-size: 16px;
			}
		}

		.mc-content {
			.mc-content-cotainer {
				padding: 0 15px;
				margin-bottom: 20px;
			}
		}
		:deep(.van-cell) {
			border-radius: 6px;
			--van-field-label-color: #172b4d;
			padding: 10px 16px !important;
			font-size: 14px !important;
			line-height: 24px !important;
		}
		.m-15 {
			margin: 15px 0;
		}
		.question-list {
			border-radius: 6px;
			padding: 15px;
			&--last {
				margin-bottom: 40px;
			}
			--van-cell-vertical-padding: 0;
			--van-cell-horizontal-padding: 0;
			&__title {
				font-size: 14px;
				margin-bottom: 8px;
			}
			&__add {
				font-size: 14px;
				img {
					width: 16px;
					margin-right: 3px;
				}
			}
			.question-item {
				margin-bottom: 10px;
				&__content {
					border-radius: 6px;
					padding: 10px;
					margin-right: 15px;
					--van-cell-background: transparent;
				}
				img {
					width: 16px;
				}
			}
		}
		.btn-group {
			gap: 7px;
			margin: 12px auto 0;
			width: 375px;
			padding: 10px 15px;
			&:deep(.van-button) {
				border-radius: 5px;
				height: 40px;
				width: 169px;
				padding: 0;
				font-size: 12px;
			}
		}
		.mc-content__h5 {
			height: calc(100% - 44px);
		}
	}
}
