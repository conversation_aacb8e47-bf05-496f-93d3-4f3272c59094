/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-sales-q-list {
		width: 375px;
		margin: 0 auto;

		.mc-header {
			width: 100%;
			height: 44px;
			padding: 0 6px;
			border-bottom: 1px solid var(--ac-border-color);
			.mc-header-icon {
				width: 32px;
				height: 32px;
				border-radius: 5px;
				img {
					width: 15px;
				}
			}

			.mc-header-name {
				font-size: 16px;
			}
		}

		.mc-content {
			.mc-content-cotainer {
				padding: 0 15px;
				margin-bottom: 20px;
			}
		}
		:deep(.van-search) {
			--van-search-padding: 15px 0;
			--van-radius-sm: 6px;
			.van-search__content {
				border: 1px solid #dfe1e6;
			}
			.van-search__field {
				height: auto;
			}
		}
		.list {
			.item {
				border-radius: 6px;
				border: 1px solid #dfe1e6;
				padding: 15px;
				margin-bottom: 15px;
				:deep(.van-tag) {
					border-radius: 5px;
					font-size: 10px;
					line-height: 1.5;
					border-width: 1px;
				}
				:deep(.van-tag--plain:before) {
					border-width: 1px;
				}
				&__tag {
					margin-bottom: 8px;
					.type {
						font-size: 12px;
					}
				}
				&__title {
					font-size: 13px;
					margin-bottom: 8px;
				}
				&__info {
					font-size: 10px;
				}
				&__btn {
					:deep(.van-button) {
						--van-button-default-height: 24px;
						font-size: 12px;
						width: 55px;
						padding: 0;
						& + .van-button {
							margin-left: 10px;
						}
					}
				}
			}
		}
		.mc-content__h5 {
			height: calc(100% - 44px);
		}
	}
}
