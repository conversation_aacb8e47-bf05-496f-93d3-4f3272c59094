/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-c-header {
		padding: 20px;

		.c-header__title {
			font-size: 16px;
		}

		.c-header__confirm {
			font-size: 16px;
		}
	}

	#ap-c-search {
		margin: 0 35px;
		padding-bottom: 12px;

		div {
			span {
				font-size: 16px;
				padding-bottom: 8px;
			}
		}
	}

	#ap-c-data {
		.c-data-item {
			.c-data-title {
				height: 40px;
				font-size: 16px;
				line-height: 40px;
				padding-left: 25px;
			}

			.c-data-content {
				padding: 15px 21px;

				.content-item {
					margin-bottom: 1px;
				}

				span {
					font-size: 16px;

					span {
						font-size: 9px;
						bottom: 3px;
					}
				}

				.content-item-s-radius-active {
					border-top-left-radius: 5px;
					border-bottom-left-radius: 5px;
				}

				.content-item-e-radius-active {
					border-top-right-radius: 5px;
					border-bottom-right-radius: 5px;
				}
			}
		}
	}
}
