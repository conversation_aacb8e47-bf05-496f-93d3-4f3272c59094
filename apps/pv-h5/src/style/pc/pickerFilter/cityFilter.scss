/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-c-header {
		padding: 20px 20px 6px;

		.c-header__title {
			font-size: 16px;
		}

		.c-header__confirm {
			font-size: 16px;
		}
	}

	#ap-list {
		height: calc(100% - 50px);
		padding: 0 15px;

		.search-content {
			padding: 8px 0px 0;

			&:deep(.van-search) {
				border-radius: 3px;
			}

			&:deep(.van-search__field) {
				height: 36px;
			}
		}

		:deep(.van-tabs) {
			.van-tabs__wrap {
				border-bottom: 1px solid var(--pv-filter-bgc);
				height: 44px;

				.van-tabs__nav--line {
					padding-bottom: 15.5px;
				}

				.van-tabs__nav--line.van-tabs__nav--shrink,
				.van-tabs__nav--line.van-tabs__nav--complete {
					padding-left: 8.5px;
					padding-right: 8.5px;
				}

				.van-tab {
					font-size: 14px;
					line-height: 20px;
				}

				.van-tab--shrink {
					padding: 0 8.5px;

					.van-tab__text {
						font-size: 14px;
						line-height: 20px;
					}
				}

				.van-tabs__line {
					bottom: 15px;
					width: 40px;
					height: 3px;
					border-radius: 3px;
				}
			}

			.van-tabs__content {
				height: calc(100% - 44px);
			}
		}

		.data {
			height: calc(100% - 60px);

			&:deep(.van-cell) {
				padding: 10px 16px;
				font-size: 14px;
				line-height: 24px;

				.van-cell__value {
					font-size: 14px;

					.van-checkbox__label {
						margin-left: 8.5px;
						line-height: 20px;
					}

					.van-checkbox__icon .van-icon {
						border: 1px solid var(--van-checkbox-border-color);
					}
				}

				.van-cell__right-icon {
					margin-left: 4px;
					height: 24px;
					line-height: 24px;
					font-size: 16px;
				}
			}

			.van-cell:after {
				border-bottom: 1px solid var(--van-cell-border-color);
				left: 16px;
				right: 16px;
			}
		}
	}
}
