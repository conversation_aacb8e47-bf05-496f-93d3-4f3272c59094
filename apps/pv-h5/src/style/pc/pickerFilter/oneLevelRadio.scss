/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-c-header {
		padding: 20px 20px 16px;

		.c-header__title {
			font-size: 16px;
		}

		.c-header__confirm {
			font-size: 16px;
		}
	}

	#ap-c-search {
		padding: 0 20px;

		&:deep(.van-search) {
			border-radius: 3px;
		}

		&:deep(.van-search__field) {
			height: 36px;
		}
	}

	#ap-c-data {
		padding: 3.5px 15px;

		&:deep(.van-radio) {
			padding-left: 7px;
			padding-right: 7px;
			padding-bottom: 12px;
			padding-top: 12px;
			border-bottom: solid 1px var(--pv-filter-bgc);
		}

		&:deep(.van-radio__label) {
			font-size: 16px;
			margin-left: 16px;
		}
	}
}
