.ap-NoticeBar {
	padding: 0 15px !important;

	.van-notice-bar__content {
		font-size: 12px !important;
		line-height: 1 !important;
	}
}

.item-list__right {
	.van-checkbox .van-checkbox__icon {
		width: 16px !important;
		height: 16px !important;

		.van-icon {
			width: 16px !important;
			height: 16px !important;
			font-size: 15px !important;
			border: 1px solid var(--van-checkbox-border-color);
			border-radius: 5px !important;
		}
	}
}
.item-title {
	.van-checkbox .van-checkbox__icon {
		width: 16px !important;
		height: 16px !important;

		.van-icon {
			width: 16px !important;
			height: 16px !important;
			font-size: 15px !important;
			border: 1px solid var(--van-checkbox-border-color);
			border-radius: 5px !important;
		}
	}
}

// 重置 dialog 样式
.van-dialog {
	width: 320px;
	border-radius: 20px;

	.van-dialog__header {
		padding-top: 26px;
		font-size: 16px;
		line-height: 1.5;
	}

	.van-dialog__message {
		padding-bottom: 12px;
		padding-top: 12px;
		font-size: 16px;
		line-height: 1.5;
	}

	.van-button--large {
		height: 48px;
		font-size: 16px;
	}
}

// 重置 toast
.van-toast {
	padding: 12px;
	min-width: 120px;
	border-radius: 5px;

	.van-toast__text {
		font-size: 14px;
		line-height: 1.2;
	}
}

.van-toast--loading {
	min-width: 120px !important;
	min-height: 120px !important;
	width: 120px !important;
	height: 120px !important;
	padding: 16px !important;

	.van-toast__loading {
		padding: 4px !important;
		line-height: 1.2 !important;

		.van-loading__spinner {
			width: 30px !important;
			height: 30px !important;
		}
	}

	.van-toast__text {
		margin-top: 6px !important;
	}
}

// 重置 toast
.ppt-pc-loading {
	border-radius: 5px !important;
	padding: 0 !important;

	.van-toast__text {
		font-size: 14px !important;
		line-height: 1.2 !important;
	}
}

.ppt-success-loading {
	border-radius: 5px !important;
	padding: 0 !important;
	width: 125px !important;
	height: 125px !important;
	min-height: 120px !important;

	.van-icon {
		font-size: 38px !important;
	}

	.van-toast__text {
		font-size: 14px !important;
		line-height: 1.2 !important;
		margin-top: 8px !important;
	}
}

// 重置 popup
.van-popup--center.van-popup--round {
	border-radius: 16px !important;
}
.van-popup--bottom {
	width: 375px;
	left: 50%;
	margin-left: -187.5px;
}

// radio
.van-radio {
	.van-radio__icon--dot {
		width: 20px;
		height: 20px;
		border-width: 2px;

		.van-radio__icon--dot__icon {
			height: calc(100% - 7px);
			width: calc(100% - 7px);
		}
	}

	.van-radio__label {
		line-height: 1.5;
	}
}

// 搜索
.van-search {
	border-radius: 3px !important;

	.van-search__content {
		padding-left: 12px !important;
		border-radius: 3px !important;

		.van-search__field {
			padding-right: 12px !important;
			height: 34px !important;
			.van-field__left-icon {
				line-height: 36px !important;
				margin-right: 8px;
			}

			.van-field__clear {
				line-height: 36px !important;
			}

			.van-field__control {
				font-size: 14px !important;
				line-height: 36px !important;
			}

			.van-icon {
				font-size: 18px !important;
			}
		}
	}
}

// 空提示
.van-empty {
	padding: 20px 0 !important;
	.van-empty__image {
		width: 160px !important;
		height: 160px !important;
	}
	.van-empty__description {
		font-size: 14px !important;
		padding: 0 !important;
		margin: 0 !important;
	}
}

.loading {
	height: 190px !important;
	border-radius: 5px !important;
	margin-top: 8px !important;
	.loader {
		font-size: 26px !important;
	}
}

.van-list__loading {
	font-size: 14px !important;
	line-height: 40px !important;

	.van-loading__spinner {
		width: 20px !important;
		height: 20px !important;
	}

	.van-loading__text {
		margin-left: 8px !important;
		font-size: 14px !important;
	}
}

.van-list__finished-text {
	font-size: 14px !important;
	line-height: 30px !important;
}

.van-skeleton {
	padding: 0 15px !important;

	.van-skeleton-title {
		height: 20px !important;
	}

	.van-skeleton-paragraph {
		height: 20px !important;
		margin-top: 15px !important;
	}
}

// 重置 van-tabs
.van-tabs {
	.van-tabs__wrap {
		height: 44px !important;
		.van-tabs__nav--complete {
			padding-right: 8px !important;
			padding-left: 8px !important;
		}
		.van-tabs__nav--line {
			padding-bottom: 15px !important;
		}
		.van-tab--shrink {
			padding: 0 8px !important;
		}
		.van-tab {
			font-size: 14px !important;
			line-height: 20px !important;
		}
		.van-tabs__line {
			bottom: 15px !important;
			width: 40px !important;
			height: 3px !important;
			border-radius: 3px !important;
		}

		.van-tab--grow {
			padding: 0 12px;
		}
	}
}
