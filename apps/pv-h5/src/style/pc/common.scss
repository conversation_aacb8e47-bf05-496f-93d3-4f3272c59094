/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	@import './navList.scss';
	@import './coverVant.scss';
	html {
		height: 100%;
	}
	#app {
		height: 100%;
	}
	body {
		font-size: 14px;
		background-color: #ffffff;

		.scroll-down {
			img {
				width: 29px !important;
				height: 29px !important;
			}
		}

		.intell-agent-popover {
			.van-popover__arrow {
				display: none !important;
			}

			.van-popover__content {
				border-radius: 8px !important;
			}
		}
	}

	#nprogress .bar {
		height: 1px !important;
	}

	.ap-f12 {
		font-size: 12px !important;
	}

	.ap-f14 {
		font-size: 14px !important;
	}

	.ap-f15 {
		font-size: 15px !important;
	}

	.ap-f16 {
		font-size: 16px !important;
	}

	.ap-f18 {
		font-size: 18px !important;
	}

	.ap-f20 {
		font-size: 20px !important;
	}

	.ap-f22 {
		font-size: 22px !important;
	}

	.ap-f24 {
		font-size: 24px !important;
	}

	// 阴影效果
	.st-shadow {
		box-shadow: -2px -2px 10px 0px rgba(246, 249, 255, 0.3), 2px 2px 15px 0px rgba(217, 217, 217, 0.5) !important;
		border-radius: 10px !important;
	}

	// echarts tooltip
	.tooltip-heatmap {
		padding: 5px 8px 5px 5px !important;
		font-size: 8px !important;

		.dot {
			width: 5px !important;
			height: 5px !important;
			margin-right: 5px !important;
		}

		.date {
			margin-right: 10px !important;
		}
	}

	.tooltip-bar {
		padding: 5px 8px 5px 5px !important;
		font-size: 8px !important;
		line-height: 16px !important;

		.dot {
			width: 5px !important;
			height: 5px !important;
			margin-right: 5px !important;
		}

		.value {
			padding-left: 10px !important;
		}
	}

	.ap-defaultLayout {
		width: 375px !important;
		margin: 0 auto !important;
	}

	.q-box {
		.icon {
			font-size: 7px !important;
		}
	}

	.q-fileter-item {
		border-radius: 5px !important;
		height: 28px !important;
		margin-top: 8px !important;

		.item-title {
			font-size: 12px !important;
		}

		i {
			right: 10px !important;
			font-size: 14px !important;
		}
		.load {
			width: 10px !important;
		}
	}

	.q_custom-popup {
		border-top-left-radius: 5px !important;
		border-top-right-radius: 5px !important;
	}

	.custom-header {
		.left-label {
			bottom: -10px !important;
			left: -25px !important;
			font-size: 9px !important;
		}
		.right-label {
			top: -10px !important;
			right: -25px !important;
			font-size: 9px !important;
		}
	}
	.vxe-table {
		.vxe-header--column {
			.vxe-cell {
				padding: 8px 5px !important;

				.vxe-cell--title {
					font-size: 9px !important;
				}
			}
		}

		.vxe-body--column {
			padding: 8px 0 !important;

			.vxe-cell {
				padding: 5px !important;

				.vxe-cell--label {
					font-size: 9px !important;
				}
			}
		}

		.vxe-footer--column {
			.vxe-cell {
				padding: 7px 5px !important;

				.vxe-cell--item {
					font-size: 9px !important;
					top: -2px !important;
				}
			}
		}

		.vxe-cell--sort {
			font-size: 8px !important;
		}

		.vxe-table--empty-content {
			font-size: 14px !important;
		}
	}

	.vxe-table--render-default {
		font-size: 12px !important;
	}

	.vxe-icon-indicator {
		font-size: 16px !important;
	}

	.vxe-loading--text {
		font-size: 14px !important;
	}

	// 重置switch
	.van-switch {
		width: 47px !important;
		height: 28px !important;

		.van-switch__node {
			width: 24px !important;
			height: 24px !important;
			top: 2px !important;
			left: 2px !important;
		}
	}
}

.van-skeleton__content {
	margin-top: 20px !important;
}

.tooltip-saletrend {
	padding: 5px 8px 5px 5px !important;
	font-size: 8px !important;

	.dot {
		width: 5px !important;
		height: 5px !important;
		margin-right: 5px !important;
	}

	.value {
		padding-left: 4px !important;
	}
}

.demo-floating-bubble {
	width: 38px !important;
	height: 38px !important;

	i {
		font-size: 20px !important;
	}
}

.chatLoading::after {
	width: 3px !important;
	height: 14px !important;
	transform: translate(4px, 2px) scaleY(1.3) !important;
	margin-right: 6px !important;
}

.customer {
	font-size: 14px !important;

	&-title {
		font-size: 15px !important;
		margin-bottom: 7px !important;
	}

	&-title1 {
		font-size: 14px !important;
		margin-bottom: 5px !important;
		margin-top: 5px !important;
	}

	.dian {
		margin: 0 7px !important;

		&::after {
			width: 4px !important;
			height: 4px !important;
		}
	}

	.desc {
		margin-top: 10px !important;
	}

	.mt10 {
		margin-top: 10px !important;
	}

	.btns {
		width: 295px !important;
		height: 35px !important;
		line-height: 35px !important;
		border-radius: 5px !important;
		margin-top: 10px !important;
		padding-left: 12px !important;
		padding-right: 15px !important;

		.right {
			width: 8px !important;
			height: 8px !important;
			border-top-width: 1px !important;
			border-right-width: 1px !important;
		}
	}
}

// pdf 预览
.pdf-preview {
	border-radius: 6px !important;

	.pdf-preview__header {
		padding: 8px 12px 8px 0 !important;
	}

	.pdf-preview__bottom {
		padding: 8px 0 12px !important;

		.bottom-item {
			width: 22px !important;
			height: 22px !important;
		}

		.bottom-text {
			padding: 0 12px !important;
		}
	}
}

// chatType Popover
.chat-bottom-popover {
	max-width: 320px !important;

	.popover-item {
		padding: 12px 8px !important;

		img {
			width: 30px !important;
			height: 30px !important;
			margin-right: 8px !important;
		}

		.popover-item__desc {
			.popover-item__desc--name {
				font-size: 16px !important;
			}

			.popover-item__desc--text {
				font-size: 12px !important;
			}
		}
	}
}
