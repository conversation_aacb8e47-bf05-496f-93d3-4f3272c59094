/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-admission-situation {
		margin: 10px 15px;
		border-radius: 5px;
		padding: 8px 8px;
		.top {
			font-size: 15px;
		}
		.admission-situation-card {
			margin-top: 8px;
			border-radius: 5px;
			padding-bottom: 15px;
			min-height: 140px;
			max-height: 175px;
			/* 设置纵向滚动条样式 */
			&::-webkit-scrollbar {
				width: 4px; /* 设置纵向滚动条宽度 */
			}

			&::-webkit-scrollbar-thumb {
				border-radius: 3px;
			}

			&::-webkit-scrollbar-track {
				border-radius: 5px;
			}
		}
		.admission-table {
			margin-top: 8px;
			&-title {
				font-size: 12px;
				padding: 6px;
				.icon {
					font-size: 7px;
					right: 14px;
				}
			}
			&-detail {
				margin: 0 7px;
			}
			&-box {
				max-height: 180px;
				margin-top: 5px;
				font-size: 9px;
				border: 1px solid #dfe1e6;
				&-title {
					border-bottom: 1px solid #dfe1e6;
					&-desc {
						padding: 8px 9px;
						border-right: 1px solid #dfe1e6;
					}
				}
				&-list {
					&-item {
						padding: 8px 9px;
						border-right: 1px solid #dfe1e6;
						border-bottom: 1px solid #dfe1e6;
					}
				}
				:deep(.van-list__finished-text) {
					font-size: 9px;
				}
				:deep(.van-loading__text) {
					font-size: 9px;
				}
			}
		}
	}
}
