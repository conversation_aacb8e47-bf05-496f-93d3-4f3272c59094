/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-sale-brand {
		margin: 10px 15px;
		border-radius: 5px;
		padding: 8px 8px;

		.top {
			font-size: 15px;
		}

		.sale-brand-filter {
			margin-top: 8px;
			border-radius: 5px;
			background-color: var(--pv-bgc);

			padding-bottom: 15px;

			&-echarts {
				height: 220px;
			}
		}

		.shop-table {
			margin-top: 8px;

			&-title {
				font-size: 12px;
				padding: 6px;

				.icon {
					font-size: 7px;
					right: 14px;
				}
			}

			&-detail {
				font-size: 9px;
				border: 1px solid #dfe1e6;
				margin: 0 7px;

				&-title {
					border-bottom: 1px solid #dfe1e6;
					padding: 8px 9px;

					span:nth-child(1) {
						margin-right: 4px;
					}
				}

				&-list {
					height: 214px;

					&::-webkit-scrollbar {
						width: 4px;
						/* 设置纵向滚动条宽度 */
					}

					&::-webkit-scrollbar-thumb {
						/* 设置纵向滚动条拖动部分的颜色 */
						border-radius: 3px;
					}

					&::-webkit-scrollbar-track {
						/* 设置纵向滚动条轨道的颜色 */
						border-radius: 5px;
						// margin: 50px 0px;
					}

					&-item {
						padding: 8px 9px;
						border-bottom: 1px solid #dfe1e6;
					}
				}
			}
		}
	}
}
