/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-admission-situation-hs {
		margin: 10px 15px;
		border-radius: 5px;
		padding: 8px 8px;
		.top {
			font-size: 15px;
		}
		.admission-situation-card {
			margin-top: 8px;
			border-radius: 5px;
			padding-bottom: 15px;
			min-height: 140px;
			max-height: 175px;
			/* 设置纵向滚动条样式 */
			&::-webkit-scrollbar {
				width: 4px; /* 设置纵向滚动条宽度 */
			}

			&::-webkit-scrollbar-thumb {
				border-radius: 3px;
			}

			&::-webkit-scrollbar-track {
				border-radius: 5px;
			}
			.zr {
				position: absolute;
				top: 42px;
				right: 20px;
				font-size: 7px;
				color: #6b778c;
			}
			.zr-item {
				position: absolute;
				top: 58px;
				right: 20px;
				font-size: 9px;
				color: #172b4d;
				height: calc(100% - 15px - 50px - 20px);
				display: flex;
				flex-direction: column;
				justify-content: space-between;
			}
		}
		.admission-table {
			margin-top: 8px;
			&-title {
				font-size: 12px;
				padding: 6px;
				.icon {
					font-size: 7px;
					right: 14px;
				}
			}
			&-detail {
				margin: 0 7px;
			}
		}
	}
}
