/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-discovery {
		.discovery-nav {
			&-list {
				width: 375px;
				// border-bottom: 1px solid #dfe1e6;
				padding: 7px 0;

				// position: relative;
				&::-webkit-scrollbar {
					height: 1px;
					/* 宽度 */
				}

				&-item {
					font-size: 14px;
					padding: 0 16px;
				}

				.active::after {
					width: 30px;
					height: 3px;
					bottom: -7px;
					border-radius: 2px;
				}
			}

			&::after {
				height: 1px;
			}
		}

		.list {
			width: 375px;
		}
	}
}
