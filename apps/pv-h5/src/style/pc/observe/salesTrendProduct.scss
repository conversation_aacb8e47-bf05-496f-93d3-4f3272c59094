/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-marek-share {
		margin: 10px 15px;
		border-radius: 5px;
		padding: 8px 8px;

		.content {
			margin-top: 8px;
			border-radius: 5px;
		}

		.marek-share-legend {
			padding: 4px 8px;
			max-height: 65px;

			/* 设置纵向滚动条样式 */
			&::-webkit-scrollbar {
				width: 4px;
				/* 设置纵向滚动条宽度 */
			}

			&::-webkit-scrollbar-thumb {
				/* 设置纵向滚动条拖动部分的颜色 */
				border-radius: 3px;
			}

			&::-webkit-scrollbar-track {
				/* 设置纵向滚动条轨道的颜色 */
				border-radius: 5px;
			}

			&-item {
				font-size: 11px;
				margin-right: 20px;
				margin-top: 4px;

				.bar {
					height: 1.1px;
					width: 16px;
					margin-right: 8px;

					&::before {
						width: 4px;
						height: 4px;
						border-width: 1px;
					}
				}
			}
		}

		.marek-share-echarts {
			height: 215px;
		}

		.tags {
			font-size: 9px;
			padding: 0 10px 8px;

			&-item {
				border-radius: 2px;
				margin-bottom: 5px;
				margin-right: 5px;
				padding: 3px;
			}
		}

		.sale-table {
			margin-top: 8px;

			&-title {
				font-size: 12px;
				padding: 6px;

				.icon {
					font-size: 7px;
					right: 14px;
				}
			}

			&-content {
				padding: 0 8px 0px;
			}
		}
	}
}
