/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-sales-ranking-recruitment {
		margin: 10px 15px;
		border-radius: 5px;
		padding: 8px 8px;

		.top {
			font-size: 15px;

			.card-title {
				.card-title-sub {
					margin-right: 12px;
					margin-left: 6px;
					font-size: 10px;
				}
			}
		}

		.sales-ranking-distribution-tip {
			padding: 5px 5px 0;
			font-size: 12px;
		}

		.sales-ranking-distribution-card {
			margin-top: 8px;
			border-radius: 5px;

			&-content {
				height: 250px;

				/* 设置纵向滚动条样式 */
				&::-webkit-scrollbar {
					width: 4px;
					/* 设置纵向滚动条宽度 */
				}

				&::-webkit-scrollbar-thumb {
					/* 设置纵向滚动条拖动部分的颜色 */
					border-radius: 3px;
				}

				&::-webkit-scrollbar-track {
					/* 设置纵向滚动条轨道的颜色 */
					border-radius: 5px;
				}

				&-title {
					font-size: 9px;
					padding: 8px 5px 0px 10px;
					top: -1px;

					span:nth-child(1) {
						span:nth-child(1) {
							margin-right: 13px;
						}
					}
				}
			}
		}

		.sales-ranking-distribution-list {
			font-size: 9px;
			padding: 8px 10px 0px 10px;

			&-item {
				margin-bottom: 8px;

				span:nth-child(1),
				span:nth-child(2) {
					width: 30px;
					margin-right: 4px;
				}

				span:nth-child(1) {
					width: 20px;

					img {
						width: 20px;
						height: 13px;
					}
				}

				.bar {
					width: 225px !important;
					margin-right: 4px;
					height: 6px;

					span {
						height: 6px;
					}
				}
			}

			.isUserPart {
				box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
				padding: 7px 0;

				&::before {
					width: 2px;
					border-radius: 8px 0 0 8px;
					left: -2px;
				}
			}

			&-average {
				&::after {
					top: 14px;
					width: 1px;
					height: calc(100% - 20px);
					border-left: 1px dashed #dfe1e6;
				}
			}
		}

		.q-box-mt {
			margin-top: 10px;
		}

		.sale-table {
			margin-top: 8px;

			&-title {
				font-size: 12px;
				padding: 6px;

				.icon {
					font-size: 7px;
					right: 14px;
				}
			}

			&-content {
				padding: 0 8px 0px;
			}
		}
	}
}
