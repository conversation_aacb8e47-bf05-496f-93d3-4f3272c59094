/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-marek-share {
		margin: 10px 15px;
		border-radius: 5px;
		padding: 8px 8px;
		.content {
			margin-top: 8px;
			border-radius: 5px;
		}
		.marek-share-legend {
			padding: 4px 8px;
			max-height: 65px;
			/* 设置纵向滚动条样式 */
			&::-webkit-scrollbar {
				width: 4px; /* 设置纵向滚动条宽度 */
			}

			&::-webkit-scrollbar-thumb {
				border-radius: 3px;
			}

			&::-webkit-scrollbar-track {
				border-radius: 5px;
			}
			&-item {
				cursor: pointer;
				font-size: 11px;
				margin-right: 20px;
				margin-top: 4px;
				.bar {
					height: 1.1px;
					width: 16px;
					margin-right: 8px;
					&::before {
						width: 4px;
						height: 4px;
						border: 1px solid var(--bar-color);
					}
				}
			}
		}
		.marek-share-echarts {
			height: 215px;
		}
		.tags {
			font-size: 9px;
			padding: 0 10px 8px;
			&-item {
				border-radius: 2px;
				margin-bottom: 5px;
				margin-right: 5px;
				padding: 3px;
			}
		}
	}
}
