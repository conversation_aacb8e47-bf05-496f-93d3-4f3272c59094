/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-branch-sales-overview {
		margin: 10px 15px;
		border-radius: 5px;
		padding: 8px 8px;

		.top {
			font-size: 15px;
		}

		.branch-sales-overview-tip {
			padding: 5px 5px 0;
			font-size: 12px;
		}

		.branch-sales-overview-card {
			margin-top: 8px;
			border-radius: 5px;
			padding-right: 3px;

			&-content {
				height: 174px;
				padding-top: 8px;

				/* 设置纵向滚动条样式 */
				&::-webkit-scrollbar {
					width: 4px;
					/* 设置纵向滚动条宽度 */
					height: 4px;
				}

				&::-webkit-scrollbar-thumb {
					/* 设置纵向滚动条拖动部分的颜色 */
					border-radius: 3px;
				}

				&::-webkit-scrollbar-track {
					/* 设置纵向滚动条轨道的颜色 */
					border-radius: 5px;
					// margin: 50px 0px;
				}
			}
		}
	}
}
