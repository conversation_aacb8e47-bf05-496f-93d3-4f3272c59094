/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-sales-achievement-status {
		margin: 10px 15px;
		border-radius: 5px;
		padding: 8px 8px;

		.sales-achievement-status-tip {
			padding: 5px 5px 0;
			font-size: 12px;
		}

		.card-echart {
			margin-top: 8px;
			border-radius: 5px;
			padding-top: 7px;

			.ym-month {
				top: 10px;
				font-size: 12px;
			}

			.ym-quarter {
				top: 10px;
				font-size: 12px;
			}

			.info1 {
				left: 45px;
				top: 43px;
				line-height: 18px;
				width: 70px;

				div:nth-child(1) {
					font-size: 10px;
				}

				div:nth-child(2) {
					font-size: 13px;
				}
			}

			.info2 {
				left: 215px;
				top: 43px;
				line-height: 18px;
				width: 70px;

				div:nth-child(1) {
					font-size: 10px;
				}

				div:nth-child(2) {
					font-size: 13px;
				}
			}

			.value1 {
				font-size: 10px;
				width: 140px;
				left: 20px;
			}

			.value2 {
				font-size: 10px;
				width: 137px;
				left: 189px;
			}

			&-info {
				font-size: 12px;

				span:nth-child(1) {
					left: 23px;
				}

				span:nth-child(2) {
					left: 50px;
				}

				span:nth-child(3) {
					left: 268px;
					width: 55px;
				}

				.month {
					top: -75px;

					div:nth-child(1) {
						font-size: 30px;
					}

					div:nth-child(2) {
						font-size: 12px;
						top: -30px;
					}
				}

				.season {
					font-size: 12px;
					top: -140px;

					&.left {
						left: 20px;
					}

					&.right {
						right: 20px;
					}
				}
			}

			&-detail {
				margin: 18px 11px 8px;
				gap: 8px;

				&-item {
					flex: 1;
					border: 1px dashed #dfe1e6;
					padding: 3px 6px;
					border-radius: 5px;

					.other {
						margin-top: 5px;
						font-size: 12px;

						.icon {
							font-size: 15px;
						}
					}
				}
			}
		}

		.sales-achievement-status-echarts {
			height: 90px;
		}
	}
}
