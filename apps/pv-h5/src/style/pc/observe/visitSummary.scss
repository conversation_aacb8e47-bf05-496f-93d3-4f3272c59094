/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-visit-summary {
		margin: 10px 15px;
		border-radius: 5px;
		padding: 8px 8px;
		.top {
			font-size: 15px;
		}

		.visit-summary-echarts {
			&-card {
				margin-top: 8px;
				border-radius: 5px;
				padding: 0 15px 15px;
				.item {
					padding-top: 23px;
					padding-bottom: 8px;
					border-bottom: 1px dashed #dfe1e6;
					&-name {
						font-size: 15px;
					}
					&-fg {
						margin-top: 10px;
						font-size: 12px;
						&-right {
							font-size: 9px;
						}
					}
					&-detail {
						gap: 4px;
						margin-top: 6px;
						span {
							border-radius: 2px;
							padding: 3px 0px;
							font-size: 9px;
						}
					}
					&-tip {
						margin-top: 8px;
						font-size: 12px;
					}
					&-echarts {
						top: 15px;
						width: 70px;
						height: 35px;
						&::after {
							content: '覆盖率';
							font-size: 9px;
							top: 12px;
							left: -28px;
						}
					}
				}
			}
		}
	}
}
