/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-ddi {
		margin: 10px 15px;
		border-radius: 5px;
		padding: 8px 8px;

		.scroll {
			height: 240px;
			min-width: 100%;

			&::-webkit-scrollbar {
				height: 8px;
				/* 设置纵向滚动条宽度 */
			}

			&::-webkit-scrollbar-thumb {
				/* 设置纵向滚动条拖动部分的颜色 */
				border-radius: 3px;
				&:hover {
					background-color: rgb(154, 153, 153); /* 鼠标悬浮时滑块颜色 */
				}
			}

			&::-webkit-scrollbar-track {
				/* 设置纵向滚动条轨道的颜色 */
				border-radius: 5px;
			}
		}

		.ddi-echarts {
			height: 100%;

			&-card {
				margin-top: 8px;
				border-radius: 5px;
			}
		}

		.table {
			padding: 10px;
			min-height: 200px;
		}
	}
}
