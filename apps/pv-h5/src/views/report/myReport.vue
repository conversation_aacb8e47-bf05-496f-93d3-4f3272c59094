<template>
	<div class="mr" id="ap-mr">
		<notice-bar></notice-bar>
		<div class="list">
			<div class="mr-item" @click="reportDetail(item)" v-for="item in myList" :key="item.id">
				<div class="mr-item-header">
					<span class="header-name">
						<span>{{ item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name }}</span>
						<span>{{ '创建时间：' + item.lastModifiedDate.split('T')[0].replace(/-/g, '/') + ' ' + item.lastModifiedDate.split('T')[1].slice(0, 5) }}</span>
					</span>
				</div>
				<div class="card">
					<div class="mr-item-content" v-for="iten in item.reportVOList" :key="iten.reportCd">
						<span> {{ iten.name }}</span>
					</div>
					<div class="bar"></div>
				</div>
				<img class="icon" src="@/assets/img/delete.png" alt="" @click.stop="deleteItem(item)" />
			</div>
		</div>
	</div>
</template>
<script setup>
import { getNameByPath } from '@/utils/index';
import { getMyReport, deleteReport } from '@/api/report';
import { showConfirmDialog, showToast } from 'vant';
import { queryList } from '@/api/sales';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
const router = useRouter();
const myList = ref([]);
const deleteItem = (item) => {
	showConfirmDialog({
		title: '提示',
		message: '确认删除？',
	}).then(async () => {
		let res = await deleteReport(item.id);
		if (res.code === 200) {
			myList.value.splice(myList.value.indexOf(item), 1);
			showToast({ message: '删除成功' });
		} else {
			showToast({ message: '删除失败' });
		}
		proxy.$umeng('点击', `${page}-删除`, item.name);
	});
};

const reportDetail = (item) => {
	let arr = item.reportVOList.map((ele) => {
		return {
			reportCd: ele.reportCd,
			name: ele.name,
			startTime: ele.startTime,
			endTime: ele.endTime,
			filterConfig: ele.filterConfig,
		};
	});
	reportStore.setReportName(item.name);
	localStorage.setItem('selected-report-list', JSON.stringify(arr));
	router.push({
		name: 'reportDetail',
		query: {
			isMine: true,
			name: item.name,
			reportId: item.id,
			path: 'myReport',
			label: item.label,
			tag: item.tag,
			isTabBar: route.query.isTabBar || '',
		},
	});
	proxy.$umeng('点击', `${page}`, item.name);
};

const getReports = async () => {
	let res = await getMyReport({ page: 0, size: 100, sort: 'createdDate,DESC' });
	for (const item of res.result.content) {
		// 需要吧报告可筛选器信息关联在一起
		let arrList = perStore.reportList.filter((ele) => ele.path === item.path)[0].children;
		arrList = arrList.filter((ele) => ele.path === item.label)[0].children;
		for (const ite of item.reportVOList) {
			const attributes = arrList.find((ele) => ele.path === ite.reportCd)?.attributes || [];
			ite.filterConfig = attributes.find((ele) => ele.name === 'filterConfig')?.value || '';
		}
		myList.value.push({
			id: item.id,
			name: item.name,
			label: item.path,
			tag: item.label,
			reportVOList: item.reportVOList,
			lastModifiedDate: item.lastModifiedDate,
		});
	}
};
//获取市场的两个日期

let getDate = async () => {
	let res = await queryList({ reportType: 'CARD', menuId: 'Market Share' });
	reportStore.setTime(res.result);
};
//获取拜访两个日期
let getDate1 = async () => {
	let res = await queryList({ reportType: 'CARD', menuId: 'Engagement' });
	reportStore.setTime(res.result);
};
onMounted(() => {
	getReports();
	getDate();
	getDate1();
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/report/myReport.scss';
.mr {
	.list {
		padding: 11px 15px;
	}
	.mr-item {
		background-color: var(--pv-card-bgc);
		color: #ffffff;
		padding: 13px 15px;
		border-radius: 4px;
		margin-bottom: 10px;
		position: relative;
		.mr-item-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			.header-name {
				font-size: 16px;
				color: var(--pv-default-color);
				display: flex;
				align-items: center;
				span:nth-child(2) {
					color: var(--pv-no-active-color);
					font-size: 12px;
					margin-left: 8px;
				}
			}
		}
		.card {
			background-color: #fff;
			padding: 14px 15px;
			border-radius: 5px;
			margin-top: 10px;

			position: relative;
			.bar {
				position: absolute;
				width: 4px;
				height: 100%;
				left: 0px;
				top: 0;
				border-radius: 5px 0 0px 5px;
				background-color: var(--pv-tabbar-active);
			}
		}
		.mr-item-content {
			display: flex;
			flex-direction: column;
			color: var(--pv-default-color);
			overflow: hidden;

			span {
				font-size: 14px;
				margin-bottom: 8px;
			}
			&:nth-last-child(2) {
				span {
					margin-bottom: 0;
				}
			}
		}
		.icon {
			position: absolute;
			bottom: 0;
			right: 0;
			width: 44px;
			height: 44px;
		}
	}
}
</style>
