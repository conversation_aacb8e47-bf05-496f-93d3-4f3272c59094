<template>
	<div class="br" id="ap-br">
		<notice-bar></notice-bar>
		<div class="br-nav">
			<div class="br-nav-list ap-brNavList" ref="navListRef">
				<div class="ap-f14 ap-brNavListItem br-nav-list-item" :class="{ active: currentIndex === item.name }" v-for="item in navList" :key="item" @click="navChange(item.name, $event)">
					{{ item.name === '全部' ? $t('report.all') : item.text }}
				</div>
			</div>
		</div>
		<div class="list ap-brList" :class="isBtn ? 'list-btn1' : 'list-btn2'">
			<!-- bottom-content -->
			<div class="bottom-content">
				<div v-for="(item, index) in reportList" :key="index" class="content-item">
					<!-- title -->
					<p class="item-title">
						<span>{{ item.groupType }}</span>
						<van-checkbox v-model="item.isChecked" :indeterminate="item.indeterminate" checked-color="#283c63" shape="square" @click="(e) => fatherChange(item)"></van-checkbox>
					</p>

					<div v-for="iten in item.reportVOList" :key="iten.reportCd" @click="reportEv(iten)" class="item-list" :class="{ gray: !iten.enable }">
						<div class="bar" :style="{ backgroundColor: colors[0] }"></div>
						<div class="item-list__left">
							<span class="left-name">
								<span>{{ iten.name }}</span>
							</span>
							<span class="desc">{{ iten.description || '' }}</span>
						</div>
						<div class="item-list__right">
							<van-checkbox v-model="iten.isChecked" checked-color="#283c63" shape="square"></van-checkbox>
						</div>
						<!-- 遮盖复选框 -->
						<div class="item-list__mc"></div>
					</div>
				</div>
			</div>
		</div>

		<!-- 生成标准报告按钮 -->
		<div v-if="isBtn" class="fix-btn">
			<van-button @click="genReport" type="primary">生成标准报告</van-button>
		</div>
	</div>
</template>
<script setup>
import { getLabels, getReports } from '@/api/report';
import { queryList, queryReportById } from '@/api/sales';
import useReport from '@/store/modules/report';
import { getNameByPath, findChildrenById } from '@/utils/index';
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
const router = useRouter();
const navList = ref([]);
const reportList = ref([]);
const reportCards = ref([]);
let currentIndex = ref('全部');

const navListRef = ref(null);
//页签切换
const navChange = (item, e) => {
	// loading.value = true;
	currentIndex.value = item;
	const rect = e.target.getBoundingClientRect();
	if (rect.left + rect.width > (window.innerWidth || document.documentElement.clientWidth) || rect.left < 0) {
		navListRef.value.scrollTo({
			left: navListRef.value.scrollLeft + (rect.left < 0 ? rect.left : rect.left + rect.width - window.innerWidth),
			behavior: 'smooth', // 可选，以平滑动画过渡
		});
	}
	reportList.value = [];
	getReportData();
	proxy.$umeng('点击', `${page}顶部页签`, item);
};

const isBtn = computed(() => {
	for (const ele of reportList.value) {
		if (ele.reportVOList.some((i) => i.isChecked)) return true;
		continue;
	}
});

const reportEv = (i) => {
	i.isChecked = !i.isChecked;
	let father = reportList.value.find((ele) => ele.groupType === i.groupType);
	let fatherChildrenCheckedAll = father.reportVOList.every((ele) => ele.isChecked);
	let fatherChildrenCheckedOne = father.reportVOList.some((ele) => ele.isChecked);
	// 更新父节点的 isChecked 和 indeterminate
	father.isChecked = fatherChildrenCheckedAll;
	father.indeterminate = !fatherChildrenCheckedAll && fatherChildrenCheckedOne;
};
const fatherChange = (item) => {
	item.reportVOList.forEach((ele) => (ele.isChecked = item.isChecked));
	if (item.reportVOList.every((ele) => ele.isChecked)) item.indeterminate = false;
};
const genReport = () => {
	reportCards.value = reportList.value.flatMap((group) => group.reportVOList.filter((report) => report.isChecked));
	let arr = reportCards.value.map((ele) => {
		return {
			reportCd: ele.reportCd,
			name: ele.name,
			startTime: ele.startTime,
			endTime: ele.endTime,
			filterConfig: ele.filterConfig,
		};
	});
	localStorage.setItem('selected-report-list', JSON.stringify(arr));
	router.push({
		name: 'reportDetail',
		query: {
			isTabBar: route.query.isTabBar || '',
			path: route.name,
			label: currentIndex.value === '全部' ? 'ADMIN' : currentIndex.value,
		},
	});
	let uname = '';
	for (const item of arr) {
		uname = uname.concat('-', item.name);
	}
	proxy.$umeng('点击', `${page}生成标准报告`, uname);
};
//获取市场的两个日期
let useReportStore = useReport();
let getDate = async () => {
	let res = await queryList({ reportType: 'CARD', menuId: 'Market Share' });
	useReportStore.setTime(res.result);
};
//获取拜访两个日期
let getDate1 = async () => {
	let res = await queryList({ reportType: 'CARD', menuId: 'Engagement' });
	useReportStore.setTime(res.result);
};
watch(
	() => route.path,
	() => {
		if (!['myReport', 'reportDetail'].includes(route.name) && route.fullPath.indexOf('report') > -1) {
			page = getNameByPath(route.fullPath);
			init();
		}
	}
);
onMounted(() => {
	init();
});
const init = () => {
	reportList.value = [];
	getLabelData();
	getReportData();
	getDate();
	getDate1();
};
const getLabelData = async () => {
	let arrList = perStore.reportList.filter((ele) => ele.path === route.name)[0].children;
	navList.value = arrList.map((ele) => (ele.path === 'ADMIN' ? { name: '全部', text: '全部' } : { name: ele.path, text: ele.name }));
	currentIndex.value = navList.value[0].name;
};

const getReportData = async () => {
	let arrList = perStore.reportList.filter((ele) => ele.path === route.name)[0].children;
	const list = findChildrenById(arrList, currentIndex.value === '全部' ? 'ADMIN' : currentIndex.value);
	const listArr = [];
	if (list.length > 0) {
		for (const ele of list) {
			if (ele.type === 'REPORT') {
				listArr.push(ele.path);
			}
		}
	}
	if (listArr.length === 0) return;
	let res = await queryReportById(listArr);
	if (res.result.length > 0) {
		// 需要把 筛选器的相关信息和报告信息关联在一起
		let arrList = perStore.reportList.filter((ele) => ele.path === route.name)[0].children;
		arrList = arrList.filter((ele) => ele.path === (currentIndex.value === '全部' ? 'ADMIN' : currentIndex.value))[0].children;
		for (const ele of res.result) {
			if (ele.reportVOList.length > 0) {
				for (const item of ele.reportVOList) {
					const attributes = arrList.find((ele) => ele.path === item.reportCd)?.attributes || [];
					item.filterConfig = attributes.find((ele) => ele.name === 'filterConfig')?.value || '';
				}
			}
		}
	}
	reportList.value = res.result;
};

let colors = ['#0052CC', '#01A2BF', '#FF991F', '#E45D3B'];
</script>
<style lang="scss" scoped>
@import '../../style/pc/report/report.scss';
.br {
	&-nav {
		// height: 36px;
		position: sticky;
		top: 0;
		z-index: 3;
		&-list {
			display: flex;
			align-items: center;
			height: 100%;
			overflow-x: scroll;
			scrollbar-width: none;
			width: 100vw;
			// border-bottom: 1px solid #dfe1e6;
			background-color: var(--pv-bgc);
			padding: 7px 0;
			position: relative;
			&::-webkit-scrollbar {
				height: 1px; /* 宽度 */
			}
			&-item {
				flex-shrink: 0;
				font-size: 14px;
				color: var(--pv-no-active-color);
				padding: 0 16px;
				text-align: center;
			}
			.active {
				font-weight: 400;
				color: var(--pv-default-color);
				position: relative;
				font-weight: bold;
			}
			.active::after {
				content: '';
				position: absolute;
				z-index: 1;
				background-color: var(--pv-tabbar-active);
				width: 30px;
				height: 3px;
				left: 50%;
				bottom: -7px;
				transform: translateX(-50%);
				border-radius: 2px;
			}
		}
		&::after {
			content: '';
			position: absolute;
			height: 1px;
			width: 100%;
			background-color: #dfe1e6;
			bottom: 0;
		}
	}
	.list {
		// height: calc(100vh - 35px - var(--nav-height) - 75px);
		width: 100vw;
		// overflow-y: scroll;
	}
	.list-btn1 {
		padding-bottom: 26.66667vw;
	}
	.list-btn2 {
		padding-bottom: 5vw;
	}
	.bottom-content {
		margin: 10px 15px 0;
		.content-item {
			background: hsla(0, 0%, 100%, 0.12);
			box-shadow: none;
			border-radius: 5px;
			width: 100%;
			padding: 8px 8px;
			margin-top: 12px;
			background-color: var(--pv-card-bgc);
			.item-title {
				font-size: 14px;
				font-weight: bolder;
				margin-bottom: 8px;
				padding-left: 3px;
				display: flex;
				justify-content: space-between;
				::v-deep(.van-checkbox) {
					margin-right: 15px;
					.van-checkbox__icon {
						height: 15px;
					}
					.van-icon {
						width: 15px;
						height: 15px;
						border-radius: 2.5px;
						line-height: initial;
					}
					.van-checkbox__icon--checked .van-icon {
						border: none;
						background-color: var(--pv-tabbar-active) !important;
					}
				}
			}

			.item-list {
				width: 100%;
				padding: 12px 15px;
				background: #fff;
				display: flex;
				align-items: center;
				margin-bottom: 10px;
				border-radius: 5px;
				justify-content: space-between;
				position: relative;
				.bar {
					position: absolute;
					width: 4px;
					height: 100%;
					left: 0px;
					border-radius: 5px 0 0px 5px;
				}
				.item-list__left {
					display: flex;
					flex-direction: column;
					.left-name {
						color: var(--pv-default-color);
						font-weight: bold;
						font-size: 12px;
						display: flex;
						align-items: center;
						span:nth-child(1) {
							margin-right: 3px;
						}
					}
					.left-label {
						color: #0052cc;
						font-size: 7px;
						span {
							background-color: rgba(0, 82, 204, 0.1);
							border-radius: 2px;
							padding: 2px 3px;
							margin-right: 3px;
						}
					}
					.desc {
						font-size: 8px;
						color: #0052cc;
						margin-top: 2px;
					}
				}

				.item-list__right {
					&:deep(.van-checkbox__label) {
						display: none;
					}
					::v-deep(.van-checkbox) {
						.van-checkbox__icon {
							height: 15px;
						}
						.van-icon {
							width: 15px;
							height: 15px;
							border-radius: 2.5px;
							line-height: initial;
						}
						.van-checkbox__icon--checked .van-icon {
							border: none;
							background-color: var(--pv-tabbar-active) !important;
						}
					}
				}

				.item-list__mc {
					height: 100%;
					width: 60px;
					position: absolute;
					z-index: 6;
					right: 0;
					bottom: 0;
				}
				&:nth-last-child(1) {
					margin-bottom: 0;
				}
			}
			.gray {
				filter: grayscale(100%);
				opacity: 0.5;
				pointer-events: none;
			}
		}
	}

	.fix-btn {
		position: fixed;
		width: 100%;
		display: flex;
		justify-content: center;
		bottom: 14%;
		&:deep(.van-button) {
			width: 280px;
			border-radius: 50px;
			background: linear-gradient(180deg, rgba(0, 138, 232, 1) 0%, rgba(0, 82, 204, 1) 100%);
			box-shadow: 0px 2px 10px 0px rgba(0, 82, 204, 0.1);
			font-size: 16px;
			font-weight: 500;
		}
	}
}
</style>
