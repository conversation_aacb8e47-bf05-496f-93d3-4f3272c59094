<template>
	<div class="ignore-sale">
		<div class="card-title">
			<span>{{ title }}</span>
		</div>
		<div class="content">
			<div class="nox">
				<div class="marek-share-echarts" :id="`marek-share-echarts-${echartsId}`"></div>
			</div>
			<div class="marek-share-table">
				<tableCom :tableData="tableData" :columns="columns" :isPPT="true"></tableCom>
			</div>
		</div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { generateRandomNumber, formatNumber, isPCTrue } from '@/utils/index';
import tableCom from './table.vue';

let props = defineProps(['info', 'title']);
let myEcharts = echarts;
let chart;
let echartsId = generateRandomNumber();

const tableData = ref([]);
const columns = ref([]);
const tableList = ref([]);
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#marek-share-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let x = tableList.value.map((ele) => ele.date);
	let xs = tableList.value.map((ele) => ele.sales);
	let zb = tableList.value.map((ele) => ele.target);
	let dc = tableList.value.map((ele) => ele.ach);
	let tbzz = tableList.value.map((ele) => ele.growth);
	let tq = tableList.value.map((ele) => ele.salesLy);
	let option = {
		grid: {
			top: 60,
			left: 58,
			right: 35,
			bottom: 30,
		},
		legend: {
			data: ['指标额', '销售额', '同期', '达成', '同比增长'],
			left: 'center',
			top: 15,
			itemGap: 10,
			itemWidth: 15,
			itemHeight: 4,
			textStyle: {
				color: '#6B778C',
				fontSize: 12,
			},
		},
		tooltip: {
			show: false,
		},
		xAxis: [
			{
				type: 'category',
				data: x,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: 9,
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: 9,
					formatter: (value) => {
						return formatNumber(value);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: 9,
					formatter: (value) => {
						return formatNumber(value) + '%';
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
			},
		],
		color: ['#E1EBFF', '#0052CC', '#ec97a0', '#E95600', '#01A2BF'],
		series: [
			{
				name: '指标额',
				type: 'bar',
				data: zb,
				barWidth: 12,
				z: 2,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '销售额',
				type: 'bar',
				data: xs,
				barWidth: 12,
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '同期',
				type: 'bar',
				data: tq,
				barWidth: 12,
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '达成',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: 1,
				},
				symbolSize: 5,
				data: dc,
				z: 4,
			},
			{
				name: '同比增长',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: 1,
				},
				symbolSize: 5,
				data: tbzz,
				z: 4,
			},
		],
	};
	chart.setOption(option);
};

watch(
	() => props.info,
	(res) => {
		console.log(res);
		tableList.value = res.tableList;
		columns.value = res.columns;
		tableData.value = res.tableData;
		initChart();
	},
	{ deep: true }
);
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.ignore-sale {
	font-size: 10px;
	width: 100%;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 4px 4px;
	.card-title {
		width: 100%;
		border-radius: 4px 4px 0 0;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: #283b5c;
		height: 23px;
		span {
			font-size: 10px;
			color: #fff;
		}
	}
	.content {
		height: calc(100% - 23px);
		padding: 0 10px;
		.marek-share-legend {
			display: flex;
			justify-content: center;
			flex-wrap: wrap;
			padding: 4px 8px;
			max-height: 65px;
			overflow-y: auto;
			&-item {
				font-size: 9px;
				color: var(--pv-no-active-color);
				margin-right: 20px;
				margin-top: 4px;
				display: flex;
				align-items: center;
				.bar {
					height: 1.1px;
					width: 16px;
					background-color: var(--bar-color);
					margin-right: 8px;
					position: relative;
					&::before {
						position: absolute;
						content: '';
						width: 4px;
						height: 4px;
						border-radius: 50%;
						border: 1px solid var(--bar-color);
						background-color: #fff;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
					}
				}
			}
		}
		.nox {
			height: 50%;
			.marek-share-echarts {
				height: 100%;
				width: 100%;
			}
		}

		.marek-share-table {
			height: 50%;
			width: 100%;
		}
	}
}
</style>
