<template>
	<div class="sales-overview" id="ap-sales-overview">
		<div class="top">
			<div class="card-title">
				<span>{{ '销售趋势分析' }}</span>
			</div>
		</div>
		<!-- 通用筛选器 -->
		<globalFilter
			:filters="[
				{ name: 'time', size: '100', queryName: 'dateRange', single: true },
				{ name: 'org', size: '32', queryName: 'personName' },
				{ name: 'city', size: '32', queryName: 'countyName' },
				{ name: 'hospital', size: '32', queryName: 'hospitalName' },
			]"
			:query="query"
			:loading="filterLoading"
			@openTime="openTime"
			@openOrg="openOrg"
			@openCity="openCity"
			@openHospital="openHospital"
		></globalFilter>
		<div class="list">
			<card :info="props.dinfo" :tableList="tableList[0]" :comStatus="loadingList[0]"></card>
			<div class="top">
				<div class="card-title mt8">
					<span>各产品销售趋势分析</span>
				</div>
			</div>
			<tableCom :tableList="detailList" :comStatus="loadingList[3]" :info="dinfo"></tableCom>
			<div class="top">
				<div class="card-title mt8">
					<span>{{ 'KEPPRA' }}销售趋势分析</span>
				</div>
			</div>
			<card :info="props.dinfo" :tableList="tableList[1]" :comStatus="loadingList[1]"></card>
			<div class="top">
				<div class="card-title mt8">
					<span>{{ 'VIMPAT' }}销售趋势分析</span>
				</div>
			</div>
			<card :info="props.dinfo" :tableList="tableList[2]" :comStatus="loadingList[2]"></card>
		</div>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :defaultHosCheckedAll="defaultHosCheckedAll" :personalId="query.personId" :province="query.province" :city="query.city" :path="`${page}${props.info?.nameCn}`" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import { salesTrends } from '@/api/sales';
import Decimal from 'decimal.js';
import { formatNumber, formatNumberTH, formatNumberT, formatPrecentOne, getNameByPath, getFirstMonthStartDate, getFilterTime } from '@/utils/index';
import card from './card.vue';
import tableCom from './table.vue';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
let filterStore = usefilterStore();
const route = useRoute();
const props = defineProps(['dinfo', 'pathAddress']);

const { proxy } = getCurrentInstance();

let page = getNameByPath(route.fullPath);

// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.dinfo?.endTime),
	endDate: getFilterTime(props.dinfo?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
});
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.dinfo.filterConfig) {
		const filterConfig = JSON.parse(props.dinfo.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	getAllData();
	proxy.$umeng('筛选', `${page}${props.dinfo.name}-日期`, `${_startDate}-${_endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.endDate;
		query.endDate = n.endDate;
		getAllData();
		proxy.$umeng('筛选', `${page}${props.dinfo?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);

watch(
	() => reportStore.globalFilterInfo.area,
	async (n) => {
		console.log(n);
		city.value.golbalInit(n.provinceData);
		query.countyName = n.countyName;
		query.city = n.city;
		query.province = n.province;
		getAllData();
		proxy.$umeng('筛选', `${page}${props.dinfo.nameCn}-城市`, query.countyName);
	}
);
// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city, hospital, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	getAllData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
// 城市

const openCity = () => {
	city.value.show = true;
};
const cityConfirm = async (params) => {
	console.log(params);
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	getAllData();

	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
watch(
	() => reportStore.globalFilterInfo.people,
	async (n) => {
		orgZ.value.handleReset();
		defaultPerson.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		orgZ.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		orgZ.value.confirm();
	}
);
// 医院筛选

const openHospital = () => {
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	getAllData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};
const defaultHos = ref('');
const defaultHosCheckedAll = ref('');

watch(
	() => reportStore.globalFilterInfo.hospital,
	async (n) => {
		console.log(n);

		hospital.value.handleReset();
		defaultHos.value = n.checked; //默认选中
		defaultHosCheckedAll.value = n.isCheckAll;
		await nextTick(); // 确保 DOM 更新完成后再执行
		hospital.value.handleDefault(n);
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		hospital.value.confirm();
	}
);
const tableList = ref([]);
const detailList = ref([]);
const loadingList = ref(['loading', 'loading', 'loading', 'loading']);
let getData = async (brandId, index) => {
	loadingList.value[3] = 'loading';
	loadingList.value[index] = 'loading';
	let startTime = getFirstMonthStartDate(`${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`);
	let res = await salesTrends({
		skuCode: brandId || [],
		territoryCode: query.personId ? query.personId.split(',') : [],
		hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
		startLocalDate: startTime,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		province: query.province || [],
		city: query.city || [],
		pathAddress: props.pathAddress || '',
	});
	tableList.value[index] = res.result.data;
	loadingList.value[index] = 'normal';
	detailList.value = [];
	// for (const item of res.result) {
	// 	detailList.value.push({
	// 		date: item.date,
	// 		sales: formatNumber(item.sales, props.dinfo.unit, props.dinfo.unitValue),
	// 		target: formatNumber(item.target, props.dinfo.unit, props.dinfo.unitValue),
	// 		ach: `${new Decimal(item.ach).toDecimalPlaces(0) + '%'}`,
	// 		growth: `${new Decimal(item.growth).toDecimalPlaces(0) + '%'}`,
	// 	});
	// }
};
const emit = defineEmits(['filterChange']);
let getAllData = async () => {
	await Promise.all([getData(void 0, 0), getData(['065006', '065007', '065034'], 1), getData(['065050', '065047', '065046', '065054'], 2)]);
	let arr = tableList.value[0].map((item) => {
		const date = item.date;
		// 找到对应日期的数据
		const data0 = tableList.value[1].find((value) => value.date === date);
		const data1 = tableList.value[2].find((value) => value.date === date);
		return {
			date,
			list: [
				[data0.sales, data1.sales],
				[data0.ach, data1.ach],
				[data0.growth, data1.growth],
			],
		};
	});

	// 格式化数据
	arr.forEach((item) => {
		item.list[0] = item.list[0].map((ele) => formatNumberTH(ele, props.dinfo.unit, props.dinfo.unitValue));
		item.list[1] = item.list[1].map((ele) => `${formatPrecentOne(ele)}%`);
		item.list[2] = item.list[2].map((ele) => `${formatPrecentOne(ele)}%`);
	});
	detailList.value = arr;
	loadingList.value[3] = 'normal';
	console.log(arr);
	let monthText = query.endDate.slice(2, 4) + '年' + query.endDate.slice(4, 6) + '月';

	// 提取公共变量，避免重复查找
	const sales1 = new Decimal(tableList.value[1].find((ele) => ele.date === query.endDate).sales);
	const sales2 = new Decimal(tableList.value[2].find((ele) => ele.date === query.endDate).sales);
	// 计算总和
	const sum = sales1.plus(sales2);
	// 确定哪个产品的销售额更大
	const isProduct0 = sales1.greaterThan(sales2);
	const product = isProduct0 ? 'KEPPRA' : 'VIMPAT';
	// 提取对应的产品信息
	const productData = isProduct0 ? tableList.value[1].find((ele) => ele.date === query.endDate) : tableList.value[2].find((ele) => ele.date === query.endDate);
	const c = formatPrecentOne(productData.ach);
	const d = formatPrecentOne(productData.growth);
	let time = query.endDate.slice(0, 4) + '年' + query.endDate.slice(4, 6) + '月';
	// 拼接summary
	let message = `
<div class="comment-title">销售分析:</div>
<div class="comment-text ">${time}KEPPRA和VIMPAT的销售额为<span class="highlight ">${formatNumberT(sum, props.dinfo.unit, props.dinfo.unitValue)}</span>，其中<span class="highlight ">${product}</span>的达成率更高，为<span class="highlight ">${
		c + '%'
	}</span>，去年同期相比增长了<span class="highlight ">${d + '%'}</span></div>
`;

	emit('filterChange', {
		name: 'salesOverviewAnalysisReportPPT',
		info: {
			monthText,
			people: query.personName,
			product: query.brandName,
			hospital: query.hospitalName,
			province: query.countyName,
			name: `${query.endDate.slice(0, 4)}年${query.endDate.slice(4, 6)}月重点产品销售总览`,
			data: {
				tableList: tableList.value,
				detailList: detailList.value,
				unit: props.dinfo.unit,
				unitValue: props.dinfo.unitValue,
				message,
			},
		},
	});
};
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//初始化级联
	initFilters();
	getAllData();
});
</script>
<style lang="scss" scoped>
@import './pc-index.scss';
.sales-overview {
	background-color: var(--pv-card-bgc);
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
		.mt8 {
			margin-top: 8px;
		}
	}
}
</style>
