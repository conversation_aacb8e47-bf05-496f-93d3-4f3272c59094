/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#ap-sales-table {
		border-radius: 5px;
		margin-top: 8px;
		padding: 15px 9px;

		.sales-table {
			font-size: 8px;
			border: 1px solid #dfe1e6;

			&-header {
				&-left {
					flex: 1;
					border-right: 1px solid #dfe1e6;
					border-bottom: 1px solid #dfe1e6;

					& span:nth-child(1) {
						bottom: 6px;
						left: 6px;
					}

					& span:nth-child(2) {
						top: 6px;
						right: 6px;
					}
				}

				&-right {
					&-row {
						&-item {
							.first {
								padding: 6px 0;
							}

							&-son {
								padding: 6px 0;
							}
						}
					}

					.row-first {
						.sales-table-header-right-row-item {
							border-right-width: 1px;
						}
					}

					.row-second {
						.sales-table-header-right-row-item {
							border-right-width: 1px;
						}
					}
				}
			}

			&-body {
				&-left {
					border-right-width: 1px;
					padding: 6px 0;
				}

				&-right {
					&-row {
						&-item {
							&-son {
								padding: 6px 0;
								border-top-width: 1px;
								border-right-width: 1px;
							}
						}
					}
				}
			}
		}
	}
}
