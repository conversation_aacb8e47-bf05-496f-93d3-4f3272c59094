<template>
	<div class="sale-trend" id="ap-sale-trend">
		<loading v-show="comStatus === 'loading'"></loading>
		<div v-show="comStatus === 'normal'" class="sale-trend-echarts-card">
			<div class="sale-trend-echarts" :id="`sale-trend-echarts-${echartsId}`"></div>
		</div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { formatNumber, generateRandomNumber, translateFont, saleTrendFilter, spaces, isPCTrue } from '@/utils/index';
import Decimal from 'decimal.js';
let props = defineProps(['tableList', 'info', 'comStatus']);
let myEcharts = echarts;
let chart;
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#sale-trend-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let x = props.tableList.map((ele) => ele.date);
	let xs = props.tableList.map((ele) => ele.sales);
	let zb = props.tableList.map((ele) => ele.target);
	let dc = props.tableList.map((ele) => ele.ach);
	let tbzz = props.tableList.map((ele) => ele.growth);
	let option = {
		grid: {
			top: translateFont(60),
			left: translateFont(58),
			right: translateFont(35),
			bottom: translateFont(30),
		},
		legend: {
			data: ['指标额', '销售额', '达成', '同比增长'],
			left: translateFont(10),
			top: translateFont(15),
			itemGap: translateFont(15),
			itemWidth: translateFont(15),
			itemHeight: translateFont(4),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = '';
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${saleTrendFilter(item, props.info.unit, props.info.unitValue)}</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			axisPointer: {
				type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
				},
			},
			extraCssText: 'z-index:9',
		},
		xAxis: [
			{
				type: 'category',
				data: x,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: spaces(x.length), // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumber(value, props.info.unit, props.info.unitValue);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return new Decimal(value).toDecimalPlaces(0) + '%';
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
			},
		],
		color: ['#E1EBFF', '#0052CC', '#E95600', '#01A2BF'],
		series: [
			{
				name: '指标额',
				type: 'bar',
				data: zb,
				barWidth: translateFont(12),
				z: 2,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '销售额',
				type: 'bar',
				data: xs,
				barWidth: translateFont(6),
				barGap: '-74%',
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '达成',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: dc,
				z: 4,
			},
			{
				name: '同比增长',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: tbzz,
				z: 4,
			},
		],
	};
	chart.setOption(option);
};

let echartsId = generateRandomNumber();

watch(
	() => props.tableList,
	() => {
		nextTick(() => {
			initChart();
		});
	}
);
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
@import './pc-card.scss';
.sale-trend {
	// background-color: var(--pv-card-bgc);
	// margin: 10px 15px;
	// border-radius: 5px;
	// padding: 8px 8px;
	// .top {
	// 	font-size: 15px;
	// }
	&-echarts {
		height: 220px;
		&-card {
			background-color: var(--pv-bgc);
			margin-top: 8px;
			border-radius: 5px;
		}
	}
}
</style>
