<template>
	<div class="top" id="ap-sales-table">
		<loading v-show="comStatus === 'loading'"></loading>
		<div v-show="comStatus === 'normal'" class="sales-table">
			<div class="sales-table-header">
				<div class="sales-table-header-left">
					<span>时间</span>
					<span>指标</span>
				</div>
				<div class="sales-table-header-right">
					<div class="sales-table-header-right-row row-first">
						<div class="sales-table-header-right-row-item">
							<div class="first">销售额{{ info.unit || 'K' }}</div>
						</div>
						<div class="sales-table-header-right-row-item">
							<div class="first">达成率%</div>
						</div>
						<div class="sales-table-header-right-row-item">
							<div class="first">同比增长率%</div>
						</div>
					</div>
					<div class="sales-table-header-right-row row-second">
						<div class="sales-table-header-right-row-item">
							<div class="sales-table-header-right-row-item-son">KEPPRA</div>
							<div class="sales-table-header-right-row-item-son">VIMPAT</div>
						</div>
						<div class="sales-table-header-right-row-item">
							<div class="sales-table-header-right-row-item-son">KEPPRA</div>
							<div class="sales-table-header-right-row-item-son">VIMPAT</div>
						</div>
						<div class="sales-table-header-right-row-item">
							<div class="sales-table-header-right-row-item-son">KEPPRA</div>
							<div class="sales-table-header-right-row-item-son">VIMPAT</div>
						</div>
					</div>
				</div>
			</div>
			<div class="sales-table-body">
				<div class="sales-table-body-tr" v-for="item in tableList" :key="item.date">
					<div class="sales-table-body-left">{{ item.date }}</div>
					<div class="sales-table-body-right">
						<div class="sales-table-body-right-row">
							<div class="sales-table-body-right-row-item" v-for="value in item.list" :key="value">
								<div class="sales-table-body-right-row-item-son" v-for="i in value" :key="i">{{ i }}</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
const props = defineProps(['tableList', 'comStatus', 'info']);
</script>
<style lang="scss" scoped>
@import './pc-table.scss';
.top {
	border-radius: 5px;
	background-color: #fff;
	margin-top: 8px;
	padding: 15px 9px;
}
.sales-table {
	color: #6b778c;
	font-size: 8px;
	border: 1px solid #dfe1e6;
	text-align: center;
	&-header {
		display: flex;
		&-left {
			flex: 1;
			border-right: 1px solid #dfe1e6;
			border-bottom: 1px solid #dfe1e6;
			position: relative;
			background: linear-gradient(45deg, transparent 49.5%, #dfe1e6 50%, transparent 50.5%);
			& span:nth-child(1) {
				position: absolute;
				bottom: 6px;
				left: 6px;
			}
			& span:nth-child(2) {
				position: absolute;
				top: 6px;
				right: 6px;
			}
		}
		&-right {
			flex: 6;
			&-row {
				display: flex;
				&-item {
					flex: 1;
					display: flex;
					.first {
						padding: 6px 0;
						background-color: #e1e3e8;
						flex: 1;
					}
					&-son {
						flex: 1;
						padding: 6px 0;
						background-color: #f4f5f7;
					}
				}
			}
			.row-first {
				.sales-table-header-right-row-item {
					border-right: 1px solid #fff;
				}
				& > div:nth-last-child(1) {
					border: none;
				}
			}
			.row-second {
				.sales-table-header-right-row-item {
					border-right: 1px solid #dfe1e6;
				}
				& > div:nth-last-child(1) {
					border: none;
				}
			}
		}
	}
	&-body {
		&-tr {
			display: flex;
		}
		&-left {
			flex: 1;
			border-right: 1px solid #dfe1e6;
			padding: 6px 0;
			background-color: #e1e3e8;
		}
		&-right {
			flex: 6;
			&-row {
				display: flex;
				&-item {
					flex: 1;
					display: flex;
					&-son {
						flex: 1;
						padding: 6px 0;
						border-top: 1px solid #dfe1e6;
						border-right: 1px solid #dfe1e6;
					}
				}
				& > div:nth-last-child(1) {
					.sales-table-body-right-row-item-son:nth-last-child(1) {
						border-right: none;
					}
				}
			}
		}
	}
}
</style>
