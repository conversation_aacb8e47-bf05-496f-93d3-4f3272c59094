<template>
	<div class="ignore-sale">
		<div class="card-title">
			<span>{{ title }}</span>
		</div>
		<div class="content">
			<!-- <div class="sale-trend-echarts" :id="`sale-trend-echarts-${echartsId}`"></div> -->
			<div class="sale-trend-table">
				<salesAnalysisTeamTable :tableData="detailList" :lastResult="lastResult" :isPPT="true"></salesAnalysisTeamTable>
			</div>
		</div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { generateRandomNumber, formatNumbeW, isPCTrue } from '@/utils/index';
import Decimal from 'decimal.js';
import salesAnalysisTeamTable from '@/views/observe/compoents/salesAnalysisTeamTable.vue';

let props = defineProps(['info', 'title']);
let myEcharts = echarts;
let chart;
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#sale-trend-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let x = tableList.value.map((ele) => ele.territoryCodeCn);
	let xs = tableList.value.map((ele) => ele.sales);
	let zb = tableList.value.map((ele) => ele.target);
	// let zhanbi = tableList.value.map((ele) => ele.achTotal);
	let tb = tableList.value.map((ele) => ele.growth);
	let dc = tableList.value.map((ele) => ele.ach);
	let option = {
		grid: {
			top: 60,
			left: 58,
			right: 40,
			bottom: 30,
		},
		// { name: '占比', icon: 'rect' },
		legend: {
			data: ['指标', '销售', '同比', '达成率'],
			left: 'center',
			top: 15,
			itemGap: 15,
			itemWidth: 15,
			itemHeight: 4,
			textStyle: {
				color: '#6B778C',
				fontSize: 12,
			},
		},
		xAxis: [
			{
				type: 'category',
				data: x,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					// interval: spaces(x.length), // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: 9,
					formatter: function (value) {
						// 使用正则表达式每 6 个字符插入一个换行符
						return value.replace(/(.{7})/g, '$1\n');
					},
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: 9,
					formatter: (value) => {
						return formatNumbeW(value);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: 9,
					formatter: (value) => {
						return formatNumbeW(value) + '%';
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
			},
		],
		// '#E7FAFC',
		color: ['#E1EBFF', '#0052CC', '#F08C52', '#62C5D7'],
		series: [
			{
				name: '指标',
				type: 'bar',
				data: zb,
				barWidth: 12,
				z: 2,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '销售',
				type: 'bar',
				data: xs,
				barWidth: 6,
				barGap: '-74%',
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			// {
			// 	name: '占比',
			// 	type: 'line',
			// 	yAxisIndex: 1,
			// 	lineStyle: {
			// 		width: 1,
			// 	},
			// 	symbolSize: 5,
			// 	data: zhanbi,
			// 	z: 4,
			// 	areaStyle: {
			// 		color: 'rgba(135, 206, 250, 0.5)', // 区域背景颜色
			// 	},
			// },
			{
				name: '同比',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: 1,
				},
				symbolSize: 5,
				data: tb,
				z: 4,
			},
			{
				name: '达成率',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: 1,
				},
				symbolSize: 5,
				data: dc,
				z: 4,
			},
		],
	};
	chart.setOption(option);
};
let tableList = ref({});
const detailList = ref([]);
const lastResult = ref({});
let echartsId = generateRandomNumber();
watch(
	() => props.info,
	(res) => {
		console.log(res, '???????');
		tableList.value = res.tableList;
		detailList.value = res.detailList;
		lastResult.value = res.lastResult;
		// initChart();
	},
	{ deep: true }
);
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.ignore-sale {
	font-size: 10px;
	width: 100%;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 4px 4px;
	.card-title {
		width: 100%;
		border-radius: 4px 4px 0 0;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: #283b5c;
		height: 23px;
		span {
			font-size: 10px;
			color: #fff;
		}
	}
	.content {
		height: calc(100% - 23px);
		padding: 0 10px;
	}
}
.sale-trend-echarts {
	height: 50%;
	width: 100%;
}
.sale-trend-table {
	height: 50%;
	width: 100%;
}
</style>
