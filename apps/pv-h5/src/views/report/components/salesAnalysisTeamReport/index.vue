<template>
	<div>
		<salesAnalysisTeam v-if="info" :info="info" :pathAddress="props.pathAddress" :isPPT="true"></salesAnalysisTeam>
	</div>
</template>
<script setup>
import salesAnalysisTeam from '@/views/observe/compoents/salesAnalysisTeam.vue';
import { queryList } from '@/api/sales';
const props = defineProps(['dinfo', 'pathAddress']);
let info = ref(null);
onMounted(async () => {
	let res = await queryList({ reportType: 'CARD' });
	let json = res.result?.filter((ele) => ele.reportCd === 'salesAnalysisTeam')?.[0];
	json.filterConfig = props.dinfo.filterConfig;
	info.value = json;
});
</script>

<style lang="scss" scoped>
::v-deep(.sale-trend) {
	margin: 10px 0 0 0;
	.card-title {
		& > span:last-child {
			display: none;
		}
	}
}
</style>
