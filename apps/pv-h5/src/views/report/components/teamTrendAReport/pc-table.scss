/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	#t1 {
		&::-webkit-scrollbar {
			height: 4px; /* 设置纵向滚动条宽度 */
		}

		&::-webkit-scrollbar-thumb {
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			border-radius: 5px;
		}
		table {
			border: 1px solid #dfe1e6;
			tr {
				th,
				td {
					font-size: 9px;
					padding: 4px 0;
					border-right: 1px solid #fff; /* 为所有单元格添加 1px 黑色实线边框 */
				}
				td {
					border: 1px solid #dfe1e6;
					padding: 5px 0;
				}
				.th-1 {
					width: 60px;
				}
				.th-2 {
					width: 120px;
				}
				.th-3 {
					width: 70px;
				}
				.zz {
					span:nth-child(1) {
						margin-right: 2px;
					}
				}
				.green-s {
					border-width: 0 4px 6px 4px;
				}
				.gray-s {
					border-width: 6px 4px 0 4px;
				}
			}
		}
		.info {
			font-size: 9px;
			padding: 8px;
			.yuan {
				width: 6px;
				height: 6px;
				margin-right: 3px;
			}
			.yuan.green {
				margin-left: 15px;
			}
		}
	}
}
