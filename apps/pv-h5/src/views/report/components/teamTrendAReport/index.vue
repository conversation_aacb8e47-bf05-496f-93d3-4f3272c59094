<template>
	<div class="sales-overview" id="tA">
		<div class="top">
			<div class="card-title">
				<span>
					{{ dinfo?.name || pageTitle }}
				</span>
			</div>
		</div>
		<!-- 通用筛选器 -->
		<globalFilter
			:filters="[
				{ name: 'time', size: '49', queryName: 'dateRange', single: true },
				{ name: 'org', size: '49', queryName: 'personName' },
				{ name: 'brand', size: '32', queryName: 'brandName' },
				{ name: 'city', size: '32', queryName: 'countyName' },
				{ name: 'hospital', size: '32', queryName: 'hospitalName' },
			]"
			:query="query"
			:loading="filterLoading"
			@openTime="openTime"
			@openOrg="openOrg"
			@openBrand="openBrand"
			@openCity="openCity"
			@openHospital="openHospital"
		></globalFilter>
		<div class="list">
			<div class="top">
				<div class="card-title mt8">
					<span>团队达成趋势分析</span>
				</div>
			</div>
			<div class="br5">
				<tableCom :info="props.dinfo" :tableList="tableList[0]" :comStatus="loading"></tableCom>
			</div>

			<div class="top">
				<div class="card-title mt8">
					<span>团队增长趋势分析</span>
				</div>
			</div>
			<div class="br5">
				<tableCom1 :info="props.dinfo" :tableList="tableList[1]" :comStatus="loading"></tableCom1>
			</div>
		</div>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :defaultHosCheckedAll="defaultHosCheckedAll" :personalId="query.personId" :province="query.province" :city="query.city" :skuCode="query.brandId" :path="`${page}${props.info?.nameCn}`" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import { teamSalesTrendAnalysis } from '@/api/report';
import { formatNumbeWT, formatPrecentOne, getNameByPath, getFirstMonthStartDate, getFilterTime } from '@/utils/index';
import tableCom from './table.vue';
import tableCom1 from './table1.vue';

import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
let filterStore = usefilterStore();
const route = useRoute();
const props = defineProps(['dinfo', 'pathAddress']);

const { proxy } = getCurrentInstance();

let page = getNameByPath(route.fullPath);

// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.dinfo?.endTime || '2025-03-05'),
	endDate: getFilterTime(props.dinfo?.endTime || '2025-03-05'),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
});
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.dinfo.filterConfig) {
		const filterConfig = JSON.parse(props.dinfo.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	getAllData();
	proxy.$umeng('筛选', `${page}${props.dinfo.name}-日期`, `${_startDate}-${_endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.endDate;
		query.endDate = n.endDate;
		getAllData();
		proxy.$umeng('筛选', `${page}${props.dinfo?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);
// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	brand.value.show = true;
};

const defaultSku = ref('');
watch(
	() => reportStore.globalFilterInfo.brand,
	async (n) => {
		brand.value.handleReset();
		brandData.value = filterStore.skuInfoTree;
		defaultSku.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		brand.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		brand.value.confirm();
	}
);

watch(
	() => reportStore.globalFilterInfo.area,
	async (n) => {
		console.log(n);
		city.value.golbalInit(n.provinceData);
		query.countyName = n.countyName;
		query.city = n.city;
		query.province = n.province;
		getAllData();
		proxy.$umeng('筛选', `${page}${props.dinfo.nameCn}-城市`, query.countyName);
	}
);
// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city, hospital, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	getAllData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	getAllData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 城市

const openCity = () => {
	city.value.show = true;
};
const cityConfirm = async (params) => {
	console.log(params);
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	getAllData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
watch(
	() => reportStore.globalFilterInfo.people,
	async (n) => {
		orgZ.value.handleReset();
		defaultPerson.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		orgZ.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		orgZ.value.confirm();
	}
);
// 医院筛选

const openHospital = () => {
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	getAllData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};
const defaultHos = ref('');
const defaultHosCheckedAll = ref('');

watch(
	() => reportStore.globalFilterInfo.hospital,
	async (n) => {
		console.log(n);

		hospital.value.handleReset();
		defaultHos.value = n.checked; //默认选中
		defaultHosCheckedAll.value = n.isCheckAll;
		await nextTick(); // 确保 DOM 更新完成后再执行
		hospital.value.handleDefault(n);
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		hospital.value.confirm();
	}
);
const tableList = ref([]);
const loading = ref('loading');
let getData = async (sort, index) => {
	loading.value = 'loading';

	let startTime = getFirstMonthStartDate(`${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`);
	let res = await teamSalesTrendAnalysis({
		skuCode: query.brandId ? query.brandId.split(',') : [],
		territoryCode: query.personId ? query.personId.split(',') : [],
		hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
		startLocalDate: startTime,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		province: query.province || [],
		city: query.city || [],
		pathAddress: props.pathAddress || '',
		sort,
	});
	const formattedResult = {
		months: res.result.axisMonth,
		message: res.result.message,
		tableData: res.result.tableData.map((ele) => {
			// 检查必要属性是否存在
			if (!ele || typeof ele !== 'object') {
				console.warn('无效数据项:', ele);
				return {
					ytdRate: 'N/A',
					ytdValue: 'N/A',
					monthlyRates: {},
					monthlyValue: {},
				};
			}
			const formattedEle = {
				name: ele.name,
				ytdRate: ele.ytdRate != null ? formatPrecentOne(ele.ytdRate) : 'N/A',
				ytdValue: ele.ytdValue != null ? formatNumbeWT(ele.ytdValue).replace(/,/g, '') : 'N/A',
				monthlyRates: {},
				monthlyValue: {},
			};

			// 格式化 monthlyRates
			if (ele.monthlyRates && typeof ele.monthlyRates === 'object') {
				for (let i in ele.monthlyRates) {
					formattedEle.monthlyRates[i] = ele.monthlyRates[i] != null ? formatPrecentOne(ele.monthlyRates[i]).replace(/,/g, '') : 'N/A';
				}
			}

			// 格式化 monthlyValue
			if (ele.monthlyValue && typeof ele.monthlyValue === 'object') {
				for (let i in ele.monthlyValue) {
					formattedEle.monthlyValue[i] = ele.monthlyValue[i] != null ? formatNumbeWT(ele.monthlyValue[i]).replace(/,/g, '') : 'N/A';
				}
			}

			return formattedEle;
		}),
	};
	tableList.value[index] = formattedResult;
	console.log(tableList.value);
	loading.value = 'normal';
};
let getAllData = async () => {
	await Promise.all([getData('ach,DESC', 0), getData('growth,DESC', 1)]);

	reportStore.setReportInfo({
		name: '团队销售趋势分析',
		info: {
			monthText: `${query.startDate.slice(0, 4)}${query.startDate.slice(4, 6)}`,
			name: `${query.startDate.slice(0, 4)}年${query.startDate.slice(4, 6)}团队销售趋势分析`,
			people: query.personName,
			hospital: query.hospitalName,
			product: query.brandName,
			data: {
				tableList: tableList.value,
				message: '<div class="comment-title">团队达成趋势摘要:</div>' + tableList.value[0].message + '<div class="comment-title">团队增长趋势摘要:</div>' + tableList.value[1].message,
			},
		},
	});
};
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//初始化级联
	initFilters();
	getAllData();
});
</script>
<style lang="scss" scoped>
@import './pc-index.scss';
.sales-overview {
	background-color: var(--pv-card-bgc);
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
		.mt8 {
			margin-top: 8px;
			margin-bottom: 8px;
		}
	}
	.br5 {
		border-radius: 5px;
		overflow: hidden;
	}
}
</style>
