<template>
	<div class="ignore-sale">
		<div>
			<div class="card-title">
				<span>团队达成趋势分析</span>
			</div>
			<div class="content">
				<div class="marek-share-table">
					<tablePPT :tableList="tableList[0]"></tablePPT>
				</div>
			</div>
		</div>
		<div>
			<div class="card-title">
				<span>团队增长趋势分析</span>
			</div>
			<div class="content">
				<div class="marek-share-table">
					<tablePPT1 :tableList="tableList[1]"></tablePPT1>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import tablePPT from './tablePPT.vue';
import tablePPT1 from './tablePPT1.vue';

let tableList = ref([]);
let props = defineProps(['info', 'title']);
watch(
	() => props.info,
	(res) => {
		console.log(res);
		tableList.value = res.tableList;
	},
	{ deep: true }
);
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.ignore-sale {
	font-size: 10px;
	width: 100%;
	// border-top: 1px solid rgba(0, 0, 0, 0.1);
	// border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 4px 4px;
	display: flex;
	gap: 8px;
	& > div {
		flex: 1;
	}
	.card-title {
		width: 100%;
		border-radius: 4px 4px 0 0;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: #283b5c;
		height: 23px;
		span {
			font-size: 10px;
			color: #fff;
		}
	}
	.content {
		height: calc(100% - 23px);
		// padding: 8px;
		border-right: 1px solid rgba(0, 0, 0, 0.1);
		border-left: 1px solid rgba(0, 0, 0, 0.1);
		border-bottom: 1px solid rgba(0, 0, 0, 0.1);
		border-radius: 0 0 4px 4px;
		.marek-share-table {
			// height: 50%;
			width: 100%;
			height: 100%;
		}
	}
}
</style>
