<template>
	<div class="ignore-table-container">
		<table>
			<thead>
				<tr>
					<th class="th-1 sticky-column">人员</th>
					<th class="th-2">YTD达成率</th>
					<th class="th-3">YTD实际</th>
					<th class="th-4" v-for="item in props.tableList?.months" :key="item">{{ item }}</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="(item, index) in props.tableList?.tableData" :key="item">
					<td class="sticky-column">{{ item.name }}</td>
					<td v-if="index === 0" class="rs" :rowspan="props.tableList?.tableData.length">
						<div class="echarts" :id="`echarts-${echartsId}`"></div>
					</td>
					<td>{{ item.ytdValue }}</td>
					<!-- 动态渲染每月数据 -->
					<td v-for="month in props.tableList?.months" :key="month">
						<div>{{ item.monthlyValue[month] }}</div>
						<div :class="{ 'green-text': item.monthlyRates[month] >= 100 }">{{ item.monthlyRates[month] }}%</div>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="info">
			<div>
				<span>实际：</span>
				<span class="yuan"></span>
			</div>
			<div>
				<span>达成率：</span>
				<span class="yuan"></span>
				<span>小于100</span>
				<span class="yuan green"></span>
				<span class="green">大于等于100</span>
			</div>
		</div>
	</div>
</template>
<script setup>
const props = defineProps(['tableList']);

import * as echarts from 'echarts';
let myEcharts = echarts;
import { generateRandomNumber, isPCTrue } from '@/utils/index';
let chart;
let echartsId = generateRandomNumber();
let initChart = () => {
	const userAgent = navigator.userAgent;
	const isIOS = /iPad|iPhone|iPod/.test(userAgent);
	const isAndroid = /Android/.test(userAgent);
	const renderer = isIOS ? 'svg' : isAndroid ? 'canvas' : 'auto';
	chart = myEcharts.init(document.querySelector(`#echarts-${echartsId}`), null, {
		renderer,
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
const setOption = () => {
	let yD = props.tableList.tableData.map((ele) => ele.name)?.reverse();
	let xD = props.tableList.tableData.map((ele) => ele.ytdRate)?.reverse();

	let option = {
		tooltip: {
			show: false,
		},
		grid: {
			top: 0,
			bottom: 0,
			left: 0,
			right: 0,
		},
		xAxis: {
			type: 'value',
			position: 'top',
			splitLine: {
				show: false,
			},
			axisLabel: { show: false },
		},
		yAxis: {
			type: 'category',
			axisLine: { show: false },
			axisLabel: { show: false },
			axisTick: { show: false },
			splitLine: { show: false },
			data: yD,
		},
		series: [
			{
				type: 'bar',
				label: {
					show: true,
					position: 'left',
					align: 'left',
					offset: [10, 0],
					color: '#333333',
					fontSize: 8,
					formatter: function (v) {
						return v.value + '%';
					},
				},
				itemStyle: {
					color: function (n) {
						let color = n.value >= 100 ? '#E5FAE8' : '#FAE5E5';
						return color;
					},
				},
				data: xD,
			},
		],
	};
	chart.setOption(option);
};
watch(
	() => props.tableList,
	() => {
		nextTick(() => {
			console.log(props.tableList);
			initChart();
		});
	}
);
</script>
<style lang="scss" scoped>
// @import './pc-table.scss';
.ignore-table-container {
	background-color: #fff;
	overflow-x: auto;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	table {
		border-collapse: collapse; /* 合并边框，防止双边框 */
		table-layout: fixed; /* 固定列宽 */
		width: 100%;
		border-top: 1px solid #dfe1e6;
		border-left: 1px solid #dfe1e6;
		height: fit-content;
		tr {
			th,
			td {
				font-weight: normal;
				font-size: 9px;
				color: #6b778c;
			}
			th {
				background: #e1e3e8;
				padding: 4px 0;
				background-image: linear-gradient(#fff, #fff), linear-gradient(#fff, #fff);
				background-repeat: no-repeat;
				background-size: 1px 100%, 100% 1px;
				background-position: 100% 0, 100% 100%;
			}
			td {
				text-align: center;
				background-image: linear-gradient(#dfe1e6, #dfe1e6), linear-gradient(#dfe1e6, #dfe1e6);
				background-repeat: no-repeat;
				background-size: 1px 100%, 100% 1px;
				background-position: 100% 0, 100% 100%;
				padding: 3px 0;
				font-size: 8px;
			}
			.th-1 {
				width: 40px;
			}
			.th-2 {
				width: 60px;
			}
			.th-3 {
				width: 38px;
			}
			.th-4 {
				width: 38px;
			}
			.rs {
				padding: 0;
				.echarts {
					height: 100%;
				}
			}
			.green-text {
				color: #09b041;
			}
		}
	}
	.info {
		font-size: 9px;
		color: #6b778c;
		padding: 8px 8px 0;
		.yuan {
			display: inline-block;
			width: 6px;
			height: 6px;
			border-radius: 50%;
			background-color: #6b778c;
			margin-right: 3px;
		}
		.yuan.green {
			background-color: #09b041;
			margin-left: 15px;
		}
		.green {
			color: #09b041;
		}
	}
}
</style>
