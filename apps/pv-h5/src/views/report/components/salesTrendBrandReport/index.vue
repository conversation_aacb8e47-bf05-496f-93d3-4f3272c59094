<template>
	<div>
		<salesTrendBrand v-if="info" :info="info" :pathAddress="props.pathAddress" :isPPT="true"></salesTrendBrand>
	</div>
</template>
<script setup>
import salesTrendBrand from '@/views/observe/compoents/salesTrendBrand.vue';
import { queryList } from '@/api/sales';
const props = defineProps(['dinfo', 'pathAddress']);
let info = ref(null);
onMounted(async () => {
	let res = await queryList({ reportType: 'CARD' });
	let json = res.result?.filter((ele) => ele.reportCd === 'salesTrendBrand')?.[0];
	json.filterConfig = props.dinfo.filterConfig;
	info.value = json;
});
</script>

<style lang="scss" scoped>
::v-deep(.marek-share) {
	margin: 10px 0 10px 0;
	.card-title {
		& > span:last-child {
			display: none;
		}
	}
}
</style>
