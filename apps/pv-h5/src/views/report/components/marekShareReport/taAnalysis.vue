<template>
	<div :class="{ 'ignore-tal': isPPT, tal: !isPPT }">
		<div :class="{ 'ignore-psa-card__title': isPPT, 'psa-card__title': !isPPT }">
			<span>by TA 纯销分析</span>
		</div>
		<div style="padding-bottom: 30px" :class="{ 'ignore-psa-card__content': isPPT, 'psa-card__content': !isPPT }">
			<!-- title -->
			<div :class="{ 'ignore-bar__title': isPPT, bar__title: !isPPT }">
				<span :class="{ 'ignore-lenged-item-k': isPPT, 'lenged-item-k': !isPPT, 'ignore-lenged-item-k1': isPPT, 'lenged-item-k1': !isPPT }"></span>
				<span :class="{ 'ignore-lenged-item-text': isPPT, 'lenged-item-text': !isPPT }">直销渠道</span>
				<span :class="{ 'ignore-lenged-item-k': isPPT, 'lenged-item-k': !isPPT, 'ignore-lenged-item-k2': isPPT, 'lenged-item-k2': !isPPT }"></span>
				<span :class="{ 'ignore-lenged-item-text': isPPT, 'lenged-item-text': !isPPT }">经销商渠道</span>
			</div>
			<!-- table -title -->
			<div :class="{ 'ignore-bar-table__title': isPPT, 'bar-table__title': !isPPT }">
				<div :class="{ 'ignore-bar-table__item1': isPPT, 'bar-table__item1': !isPPT }"></div>
				<div :class="{ 'ignore-bar-table__item2': isPPT, 'bar-table__item2': !isPPT }">
					<span :class="{ 'ignore-bar-table-item-span1': isPPT, 'bar-table-item-span1': !isPPT }">销售金额(直销/经销商)</span>
				</div>
				<div :class="{ 'ignore-bar-table__item3': isPPT, 'bar-table__item3': !isPPT }">
					<span :class="{ 'ignore-bar-table-item-span2': isPPT, 'bar-table-item-span2': !isPPT }">达成率</span>
				</div>
				<div :class="{ 'ignore-bar-table__item4': isPPT, 'bar-table__item4': !isPPT }">
					<span :class="{ 'ignore-bar-table-item-span2': isPPT, 'bar-table-item-span2': !isPPT }">增长率</span>
				</div>
			</div>
			<!-- table -content -->
			<div :class="{ 'ignore-bar-table__title': isPPT, 'bar-table__title': !isPPT }" v-for="(item, index) in props.taData" :key="index">
				<div :class="{ 'ignore-bar-table__item1': isPPT, 'bar-table__item1': !isPPT }">
					<span :class="{ 'ignore-bar-table-item-span3': isPPT, 'bar-table-item-span3': !isPPT }" class="ell">{{ item.skuCn }}</span>
				</div>
				<div :class="{ 'ignore-bar-table__item2': isPPT, 'bar-table__item2': !isPPT }">
					<!-- 底部div -->
					<div :class="{ 'ignore-item2-content': isPPT, 'item2-content': !isPPT }">
						<!-- 直销占比 -->
						<div :class="{ 'ignore-item2-content__one': isPPT, 'item2-content__one': !isPPT }"></div>
						<!-- 经销商占比 -->
						<div :class="{ 'ignore-item2-content__two': isPPT, 'item2-content__two': !isPPT }"></div>
						<!-- 文字描述 -->
						<span :class="{ 'ignore-item2-content__text': isPPT, 'item2-content__text': !isPPT }">{{ item.salesThis + '(' + item.dstPercentage + '%' + '/' + item.dlrPercentage + '%' + ')' }}</span>
					</div>
				</div>
				<div :class="{ 'ignore-bar-table__item3': isPPT, 'bar-table__item3': !isPPT }">
					<!-- 底部div -->
					<div :class="{ 'ignore-item3-content': isPPT, 'item3-content': !isPPT }">
						<!-- 比例 -->
						<div :class="{ 'ignore-item3-content__one': isPPT, 'item3-content__one': !isPPT }"></div>
						<!-- 文字描述 -->
						<span :class="{ 'ignore-item3-content__text': isPPT, 'item3-content__text': !isPPT }">{{ item.achPercentage + '%' }}</span>
					</div>
				</div>
				<div :class="{ 'ignore-bar-table__item4': isPPT, 'bar-table__item4': !isPPT }">
					<!-- 底部div -->
					<div :class="{ 'ignore-item4-content': isPPT, 'item4-content': !isPPT }">
						<!-- 比例 -->
						<div :class="{ 'ignore-item4-content__one': isPPT, 'item4-content__one': !isPPT }"></div>
						<!-- 文字描述 -->
						<span :class="{ 'ignore-item4-content__text': isPPT, 'item4-content__text': !isPPT }">{{ item.growthPercentage + '%' }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
const props = defineProps(['taData', 'isPPT']);
</script>
<style lang="scss" scoped>
.tal,
.ignore-tal {
	width: 100%;
	.psa-card__title,
	.ignore-psa-card__title {
		width: 100%;
		margin-bottom: 3px;
		border-radius: 3px;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: linear-gradient(90deg, #2a3f67, #4b6aa0);
		span {
			font-size: 14px;
		}
	}

	.psa-card__content,
	.ignore-psa-card__content {
		width: 100%;
		background-color: #ffffff;
		padding: 8px;
		border-radius: 3px;
	}

	.bar__title,
	.ignore-bar__title {
		border-bottom: solid 1px #e2e2e2;
		display: flex;
		align-items: center;
		padding: 3px 0 6px;
		.lenged-item-text,
		.ignore-lenged-item-text {
			font-size: 12px;
			color: #333333;
			margin-left: 3px;
		}
		.lenged-item-k,
		.ignore-lenged-item-k {
			width: 10px;
			height: 10px;
			border-radius: 4px;
		}
		.lenged-item-k1,
		.ignore-lenged-item-k1 {
			background-color: #8598bb;
		}

		.lenged-item-k2,
		.ignore-lenged-item-k2 {
			background-color: #c1c6d1;
			margin-left: 6px;
		}
	}

	.bar-table__title,
	.ignore-bar-table__title {
		border-bottom: solid 1px #e2e2e2;
		display: flex;
		align-items: center;
		padding: 8px 0;
		width: 100%;
		.bar-table__item1,
		.ignore-bar-table__item1 {
			width: 17%;
			display: flex;
			.bar-table-item-span3,
			.ignore-bar-table-item-span3 {
				font-size: 10px;
				width: 100%;
				color: #333333;
				padding-right: 5px;
			}
		}
		.bar-table__item2,
		.ignore-bar-table__item2 {
			width: 43%;
			display: flex;
			.item2-content,
			.ignore-item2-content {
				width: 95%;
				height: 16px;
				position: relative;
				display: flex;
				align-items: center;

				.item2-content__one,
				.ignore-item2-content__one {
					background-color: #8597ba;
					width: 50%;
					height: 100%;
				}
				.item2-content__two,
				.ignore-item2-content__two {
					background-color: #c1c6d1;
					width: 50%;
					height: 100%;
				}
				//

				.item2-content__text,
				.ignore-item2-content__text {
					position: absolute;
					left: 5px;
					width: 100%;
					font-size: 10px;
					color: #333333;
				}
			}
		}
		.bar-table__item3,
		.ignore-bar-table__item3 {
			width: 20%;
			display: flex;
			.item3-content,
			.ignore-item3-content {
				width: 95%;
				height: 16px;
				position: relative;
				display: flex;
				align-items: center;

				.item3-content__one,
				.ignore-tem3-content__one {
					background-color: #d47969;
					width: 50%;
					height: 100%;
				}

				.item3-content__text,
				.ignore-item3-content__text {
					position: absolute;
					left: 5px;
					width: 100%;
					font-size: 10px;
					color: #333333;
				}
			}
		}
		.bar-table__item4,
		.ignore-bar-table__item4 {
			width: 20%;
			display: flex;
			.item4-content,
			.ignore-item4-content {
				width: 100%;
				height: 16px;
				position: relative;
				display: flex;
				align-items: center;

				.item4-content__one,
				.ignore-item4-content__one {
					background-color: #80986e;
					width: 70%;
					height: 100%;
				}

				.item4-content__text,
				.ignore-item4-content__text {
					position: absolute;
					left: 5px;
					width: 100%;
					font-size: 10px;
					color: #333333;
				}
			}
		}

		.bar-table-item-span1,
		.ignore-bar-table-item-span1 {
			color: #000;
			font-size: 12px;
		}
		.bar-table-item-span2,
		.ignore-bar-table-item-span2 {
			color: #ccc;
			font-size: 12px;
		}
	}
}
</style>
