<template>
	<div class="market">
		<competitiveMarketShare :hiddenStar="true" v-bind="$attrs"></competitiveMarketShare>
		<marekShare :hiddenStar="true" v-bind="$attrs"></marekShare>
	</div>
</template>
<script setup>
import competitiveMarketShare from '@/views/observe/compoents/competitiveMarketShare.vue';
import marekShare from '@/views/observe/compoents/marekShare.vue';
</script>
<style lang="scss" scoped>
.market {
	.cm,
	.marek-share {
		margin: 10px 0 0;
		background-color: #fff;
	}
}
</style>
