<template>
	<div :class="{ 'ignore-da': isPPT, da: !isPPT }">
		<div :class="{ 'ignore-psa-card__title': isPPT, 'psa-card__title': !isPPT }">
			<span>vs全国直销渠道/经销商渠道占比</span>
		</div>
		<div :class="{ 'ignore-psa-card__content': isPPT, 'psa-card__content': !isPPT, 'ignore-pie-chat': isPPT, 'pie-chat': !isPPT }">
			<div :class="{ 'ignore-pie-chat__item': isPPT, 'pie-chat__item': !isPPT }" :id="'pie-one' + echartsId"></div>
			<div :class="{ 'ignore-pie-chat__item': isPPT, 'pie-chat__item': !isPPT }" :id="'pie-two' + echartsId"></div>

			<!-- 图例 -->
			<div :class="{ 'ignore-pie-chat__lenged': isPPT, 'pie-chat__lenged': !isPPT }">
				<!-- 直销渠道 -->
				<div :class="{ 'ignore-lenged-item': isPPT, 'lenged-item': !isPPT }">
					<span :class="{ 'ignore-lenged-item-k': isPPT, 'lenged-item-k': !isPPT, 'ignore-lenged-item-k1': isPPT, 'lenged-item-k1': !isPPT }"></span>
					<span :class="{ 'ignore-lenged-item-text': isPPT, 'lenged-item-text': !isPPT }">直销渠道</span>
				</div>
				<!-- 经销商渠道 -->
				<div :class="{ 'ignore-lenged-item': isPPT, 'lenged-item': !isPPT }" class="lenged-item">
					<span :class="{ 'ignore-lenged-item-k': isPPT, 'lenged-item-k': !isPPT, 'ignore-lenged-item-k2': isPPT, 'lenged-item-k2': !isPPT }"></span>
					<span :class="{ 'ignore-lenged-item-text': isPPT, 'lenged-item-text': !isPPT }">经销商渠道</span>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { generateRandomNumber, isPCTrue } from '@/utils/index';
import { nextTick } from 'vue';
const props = defineProps(['dealerData', 'isPPT']);

watch(props.dealerData, (val) => {
	if (val) {
		setOptionOne();
		setOptionTwo();
	}
});

const init = () => {
	// 初始化渠道报表
	initPieChat();
};

const initPieChat = () => {
	nextTick(() => {
		pieOne();
		pieTwo();
	});
};

let chat1 = null;
let chat2 = null;

const pieOne = () => {
	const chartDom = document.getElementById('pie-one' + echartsId);
	chat1 = echarts.init(chartDom, { devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio });
};
const setOptionOne = () => {
	const option = {
		title: {
			text: props.dealerData.province,
			top: '10',
			left: 'center',
			textStyle: {
				fontSize: 12,
			},
		},
		color: ['#8598bb', '#c1c6d1'],
		series: [
			{
				name: '北京',
				type: 'pie',
				radius: ['30%', '45%'],
				label: {
					show: true,
					position: 'outside',
					formatter: '{c}%',
					fontSize: 10,
					color: '#8598bb',
				},
				labelLine: {
					show: false,
					length: 0,
					length2: 5,
				},
				data: [
					{ value: props.dealerData.dstPercentage, name: '直销渠道' },
					{ value: props.dealerData.dlrPercentage, name: '经销商渠道' },
				],
			},
		],
	};
	chat1.setOption(option);
};

const pieTwo = () => {
	const chartDom = document.getElementById('pie-two' + echartsId);
	chat2 = echarts.init(chartDom, { devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio });
};
const setOptionTwo = () => {
	const option = {
		title: {
			text: '全国',
			top: '10',
			left: 'center',
			textStyle: {
				fontSize: 12,
			},
		},
		color: ['#8598bb', '#c1c6d1'],
		series: [
			{
				name: '全国',
				type: 'pie',
				radius: ['30%', '45%'],
				label: {
					show: true,
					position: 'outside',
					formatter: '{c}%',
					fontSize: 10,
					color: '#8598bb',
				},
				labelLine: {
					show: false,
					length: 0,
					length2: 5,
				},
				data: [
					{ value: props.dealerData.dstAllPercentage, name: '直销渠道' },
					{ value: props.dealerData.dlrAllPercentage, name: '经销商渠道' },
				],
			},
		],
	};
	chat2.setOption(option);
};
let echartsId = generateRandomNumber();
onMounted(() => {
	init();
});

onUnmounted(() => {
	chat1 && chat1.dispose();
	chat2 && chat2.dispose();
});
</script>
<style lang="scss" scoped>
.da,
.ignore-da {
	width: 100%;

	.psa-card__title,
	.ignore-psa-card__title {
		width: 100%;
		margin-bottom: 3px;
		border-radius: 3px;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: linear-gradient(90deg, #2a3f67, #4b6aa0);
		span {
			font-size: 14px;
		}
	}

	.psa-card__content,
	.ignore-psa-card__content {
		width: 100%;
		background-color: #ffffff;
		padding: 8px;
		border-radius: 3px;
	}

	.pie-chat,
	.ignore-pie-chat {
		width: 100%;
		height: 200px;
		display: flex;
		position: relative;
		.pie-chat__item,
		.ignore-pie-chat__item {
			width: 50%;
			height: 100%;
		}

		.pie-chat__lenged,
		.ignore-pie-chat__lenged {
			position: absolute;
			bottom: 10px;
			left: 0;
			display: flex;
			justify-content: center;
			width: 100%;
			.lenged-item,
			.ignore-lenged-item {
				display: flex;
				align-items: center;
				.lenged-item-text,
				.ignore-lenged-item-text {
					font-size: 12px;
					color: #333333;
					margin-left: 3px;
				}
				.lenged-item-k,
				.ignore-lenged-item-k {
					width: 12px;
					height: 12px;
					border-radius: 4px;
				}
				.lenged-item-k1,
				.ignore-lenged-item-k1 {
					background-color: #8598bb;
				}

				.lenged-item-k2,
				.ignore-lenged-item-k2 {
					background-color: #c1c6d1;
				}
			}

			& > .lenged-item:nth-child(1),
			& > .ignore-lenged-item:nth-child(1) {
				margin-right: 12px;
			}
		}
	}
}
</style>
