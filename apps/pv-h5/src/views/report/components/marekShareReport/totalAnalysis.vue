<template>
	<div :class="{ 'ignore-ta': isPPT, ta: !isPPT }">
		<div :class="{ 'ignore-psa-card__title': isPPT, 'psa-card__title': !isPPT }">
			<span>Total 纯销分析</span>
		</div>
		<div :class="{ 'ignore-psa-card__content': isPPT, 'psa-card__content': !isPPT }">
			<!-- header -->
			<div :class="{ 'ignore-tot-header': isPPT, 'tot-header': !isPPT }">
				<p>单位（Mn RMB）</p>
				<div :class="{ 'ignore-t-header__content': isPPT, 't-header__content': !isPPT }">Total 纯销 {{ props.totalData.dlrAllSales + props.totalData.dstAllSales }}</div>
			</div>
			<!-- middle -->
			<div :class="{ 'ignore-tot-middle': isPPT, 'tot-middle': !isPPT }">
				<!-- left -->
				<div :class="{ 'ignore-tot-middle__left': isPPT, 'tot-middle__left': !isPPT }">
					<span>Ach%</span>
					<div :class="{ 'ignore-tot-middle__left-bottom': isPPT, 'tot-middle__left-bottom': !isPPT }">
						<div :class="{ 'ignore-tot-middle__left-bottom-div': isPPT, 'tot-middle__left-bottom-div': !isPPT }">
							<!-- 第一部分 -->
							<div :class="{ 'ignore-tot-middle': isPPT, 'tot-middle': !isPPT }" class="bottom-div-item1">
								<!-- 黑色线分界点 -->
								<span :class="{ 'ignore-bottom-div-item1__span': isPPT, 'bottom-div-item1__span': !isPPT }"></span>
							</div>
							<!-- 第二部分 -->
							<div :class="{ 'ignore-bottom-div-item2': isPPT, 'bottom-div-item2': !isPPT }"></div>

							<!-- 占比部分 -->
							<div :class="{ 'ignore-tot-middle__left-bottom-zb': isPPT, 'tot-middle__left-bottom-zb': !isPPT }"></div>
						</div>
						<span :class="{ 'ignore-tot-middle__left-bottom-text': isPPT, 'tot-middle__left-bottom-text': !isPPT }">{{ props.totalData.achPercentage + '%' }}</span>
					</div>
				</div>
				<!-- right -->
				<div :class="{ 'ignore-tot-middle__right': isPPT, 'tot-middle__right': !isPPT }">
					<span>Gr%</span>
					<div :class="{ 'ignore-tot-middle__right-precent': isPPT, 'tot-middle__right-precent': !isPPT }">
						<img src="@/assets/img/s-top.png" />
						<span>{{ props.totalData.growthPercentage + '%' }}</span>
					</div>
				</div>
			</div>
			<!-- bottom -->
			<div :class="{ 'ignore-tot-bottom': isPPT, 'tot-bottom': !isPPT }">
				<!-- 销售贡献 -->
				<div :class="{ 'ignore-tot-bottom__item': isPPT, 'tot-bottom__item': !isPPT }">
					<img src="@/assets/img/total-ana.png" />
					<div :class="{ 'ignore-tot-bottom__item-right': isPPT, 'tot-bottom__item-right': !isPPT }">
						<span>销售贡献</span>
						<span>{{ props.totalData.salesContribution * 100 + '%' }}</span>
					</div>
				</div>
				<!-- 增长贡献 -->
				<div :class="{ 'ignore-tot-bottom__item': isPPT, 'tot-bottom__item': !isPPT }">
					<img src="@/assets/img/total-ana.png" />
					<div :class="{ 'ignore-tot-bottom__item-right': isPPT, 'tot-bottom__item-right': !isPPT }">
						<span>增长贡献</span>
						<span>{{ props.totalData.growthContribution * 100 + '%' }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
const props = defineProps(['totalData', 'isPPT']);
</script>
<style lang="scss" scoped>
.ta,
.ignore-ta {
	width: 100%;

	.psa-card__title,
	.ignore-psa-card__title {
		width: 100%;
		margin-bottom: 3px;
		border-radius: 3px;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: linear-gradient(90deg, #2a3f67, #4b6aa0);
		span {
			font-size: 14px;
		}
	}

	.psa-card__content,
	.ignore-psa-card__content {
		width: 100%;
		background-color: #ffffff;
		padding: 8px;
		border-radius: 3px;

		.tot-header,
		.ignore-tot-header {
			p {
				color: #000;
				font-size: 12px;
			}
			.t-header__content,
			.ignore-t-header__content {
				width: 100%;
				background-color: #4b6ba1;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #ffffff;
				font-size: 12px;
				padding: 6px 0;
			}
		}

		.tot-middle,
		.ignore-tot-middle {
			border-bottom: solid 1px #e2e2e2;
			padding: 12px 0;
			display: flex;
			align-items: center;
			height: 60px;
			.tot-middle__left,
			.ignore-tot-middle__left {
				flex: 2;
				border-right: solid 1px #e2e2e2;
				display: flex;
				flex-direction: column;
				padding-right: 12px;
				span {
					color: #000000;
					font-size: 12px;
				}

				.tot-middle__left-bottom,
				.ignore-tot-middle__left-bottom {
					display: flex;
					align-items: center;
					.tot-middle__left-bottom-div,
					.ignore-tot-middle__left-bottom-div {
						flex: 1;
						display: flex;
						height: 12px;
						position: relative;
						overflow: hidden;
						.bottom-div-item1,
						.ignore-bottom-div-item1 {
							height: 100%;
							background: rgba(219, 173, 84, 0.2);
							flex: 4;
							position: relative;

							.bottom-div-item1__span,
							.ignore-bottom-div-item1__span {
								height: 12px;
								width: 2px;
								background-color: #2a3f67;
								position: absolute;
								top: 0;
								right: 0;
								z-index: 8;
							}
						}
						.bottom-div-item2,
						.ignore-bottom-div-item2 {
							height: 100%;
							background: rgba(219, 173, 84, 0.2);
							flex: 1;
						}

						.tot-middle__left-bottom-zb,
						.ignore-tot-middle__left-bottom-zb {
							position: absolute;
							height: 8px;
							width: 60%;
							margin-top: 2px;
							background-color: rgb(219, 173, 84);
							z-index: 6;
						}
					}
					.tot-middle__left-bottom-text,
					.ignore-tot-middle__left-bottom-text {
						color: rgb(219, 173, 84);
						font-size: 12px;
						margin-left: 6px;
					}
				}
			}
			.tot-middle__right,
			.ignore-tot-middle__right {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
				span {
					color: #000000;
					font-size: 12px;
				}
				.tot-middle__right-precent,
				.ignore-tot-middle__right-precent {
					display: flex;
					align-items: center;
					img {
						margin-right: 3px;
					}
				}
			}
		}

		.tot-bottom,
		.ignore-tot-bottom {
			width: 100%;
			padding-top: 8px;
			display: flex;
			align-items: center;
			justify-content: space-around;
			.tot-bottom__item,
			.ignore-tot-bottom__item {
				display: flex;
				img {
					width: 43px;
					margin-right: 8px;
				}
				.tot-bottom__item-right,
				.ignore-tot-bottom__item-right {
					display: flex;
					flex-direction: column;
					font-size: 12px;
					color: #000000;
					justify-content: center;
				}
			}
		}
	}
}
</style>
