<template>
	<div>
		<echarts v-if="info" :info="info" :pathAddress="props.pathAddress" :isPPT="true"></echarts>
	</div>
</template>
<script setup>
import echarts from './echarts';
import { queryList } from '@/api/sales';
const props = defineProps(['dinfo', 'pathAddress']);
let info = ref(null);
onMounted(async () => {
	let res = await queryList({ reportType: 'CARD' });
	let json = res.result?.filter((ele) => ele.reportCd === 'branchSalesOverview2')?.[0];
	json.startTime = props.dinfo.startTime;
	json.endTime = props.dinfo.endTime;
	json.filterConfig = props.dinfo.filterConfig;
	json.nameCn = props.dinfo.name;
	info.value = json;
});
</script>

<style lang="scss" scoped>
::v-deep(.branch-sales-overview) {
	margin: 10px 0 0 0;
	.card-title {
		& > span:last-child {
			display: none;
		}
	}
}
</style>
