<template>
	<div class="ignore-sale">
		<div class="card-title">
			<span>{{ title }}</span>
		</div>
		<div class="content">
			<div class="branch-sales-overview-card-echarts" :id="`branch-sales-overview-card-echarts-${echartsId}`"></div>
		</div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { generateRandomNumber, isPCTrue } from '@/utils/index';
import Decimal from 'decimal.js';
let props = defineProps(['info', 'title']);
let myEcharts = echarts;
let chart;
let initChart = () => {
	const userAgent = navigator.userAgent;
	const isIOS = /iPad|iPhone|iPod/.test(userAgent);
	const isAndroid = /Android/.test(userAgent);
	const renderer = isIOS ? 'svg' : isAndroid ? 'canvas' : 'auto';
	chart = myEcharts.init(document.querySelector(`#branch-sales-overview-card-echarts-${echartsId}`), null, { renderer, devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio });
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};

let setOption = () => {
	let hours = chartData.value.axisMonth;
	const days = chartData.value.nameArray;
	const data = chartData.value.dateArray;
	let option = {
		grid: {
			top: 18,
			bottom: 0,
			left: 110,
			right: 10,
		},
		xAxis: {
			type: 'category',
			data: hours,
			axisLabel: {
				fontSize: 8,
				color: '#6B778C',
			},
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			splitArea: {
				show: true,
				interval: 0,
			},
			position: 'top',
		},
		yAxis: {
			type: 'category',
			data: days,
			axisLabel: {
				//是否显示
				inside: false, //坐标轴刻度文字的风格        (可选楷体  宋体  华文行楷等等)
				formatter: (value) => {
					return value.length > 12 ? `{a|${value.slice(0, 12)}...}` : `{a|${value}}`;
				},
				interval: 0,
				rich: {
					a: {
						color: '#6B778C',
						fontSize: 8,
						align: 'left',
						width: 140,
						backgroundColor: 'none',
						padding: [0, -43, 0, 0],
					},
				},
			},
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			splitArea: {
				show: true,
				interval: 0,
			},
			name: 'TOP20医院',
			nameTextStyle: {
				color: '#172B4D',
				fontSize: 9,
				fontWeight: 'bold',
				padding: [0, 120, -8, 50],
			},
		},
		visualMap: {
			show: false,
			min: 0,
			max: chartData.value.max,
			color: ['#01A2BF', '#ffffff'],
		},
		series: [
			{
				// name: 'Punch Card',
				type: 'heatmap',
				data: data,
				progressive: 200, // 每次渲染的数据点数量
				progressiveThreshold: 200, // 触发渐进渲染的最小数据量
				label: {
					show: true,
					fontSize: 7,
					color: '#172B4D',
					formatter: ({ value }) => {
						return value[2];
					},
				},
				heatmapSize: [10, 30],
			},
		],
	};
	chart.setOption(option);
};
let chartData = ref({});

let echartsId = generateRandomNumber();
watch(
	() => props.info,
	(res) => {
		chartData.value = res.result;
		initChart();
	},
	{ deep: true }
);
onUnmounted(() => {
	chart && chart.dispose();
});
// defineExpose({ query });
</script>
<style lang="scss" scoped>
.ignore-sale {
	font-size: 10px;
	width: 100%;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 4px 4px;
	.card-title {
		width: 100%;
		border-radius: 4px 4px 0 0;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: #283b5c;
		height: 23px;
		span {
			font-size: 10px;
			color: #fff;
		}
	}
	.content {
		height: calc(100% - 23px);
		display: flex;
		align-items: center;
		padding: 10px 0;
	}
}
.branch-sales-overview-card-echarts {
	height: 100%;
	width: 100%;
}
</style>
