<template>
	<div class="pay" id="ap-pay">
		<div class="pay-title">{{ pageTitle }}</div>
		<div class="pay-desc">MR月度生产力=RSM对应销售额/MR岗位数</div>
		<div class="pay-lengend">
			<div class="pay-lengend-item">
				<img src="@/assets/img/up.png" />
				<span>高于BU全国平均生产力</span>
			</div>
			<div class="pay-lengend-item">
				<img src="@/assets/img/down.png" />
				<span>低于BU全国平均生产力</span>
			</div>
		</div>
		<div class="q-box">
			<div @click="openTime" class="q-width-100 q-fileter-item">
				<span class="item-title ell">{{ query.startDate }}</span>
				<svg-icon class="icon" icon-class="down2"></svg-icon>
			</div>
		</div>
		<div class="pay-content">
			<div class="pay-content__item" :class="index % 2 === 0 ? 'item-left' : 'item-right'" v-for="(item, index) in peopleList" :key="index">
				<div class="pay-content__item-header" :class="[`pay-content__item-header__d1`]">
					<div class="pay-content__item-header__title" :class="[`pay-content__item-header__title--d1`]">
						{{ item.rsdName }}
					</div>
					<div class="pay-content__item-bottom" :class="[`pay-content__item-bottom--d1`]">
						<div class="pay-content__item--rows">
							<div class="pay-content__item--rows--item">
								<div class="pay-content__item--rows--label">RSM岗位数{{ item.rsmNum }}</div>
								<div class="pay-content__item--rows--value">
									<span class="lz">{{ item.rsm }}</span>
									{{ '(' + item.tbaRsmNum + '空岗' + ((item.tbaRsmNum / item.rsmNum) * 100).toFixed(2) + '%' + ')' }}
								</div>
							</div>
							<div class="pay-content__item--rows--item">
								<div class="pay-content__item--rows--label">DSM岗位数{{ item.dsmNum }}</div>
								<div class="pay-content__item--rows--value">
									<span class="lz">{{ item.dsm }}</span>
									{{ '(' + item.tbaDsmNum + '空岗' + ((item.tbaDsmNum / item.dsmNum) * 100).toFixed(2) + '%' + ')' }}
								</div>
							</div>
							<div class="pay-content__item--rows--item">
								<div class="pay-content__item--rows--label">MR岗位数{{ item.mrNum }}</div>
								<div class="pay-content__item--rows--value">
									<span class="lz">{{ item.dsm }}</span>
									{{ '(' + item.tbaMrNum + '空岗' + ((item.tbaMrNum / item.mrNum) * 100).toFixed(2) + '%' + ')' }}
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="pay-content__item-main">
					<div class="pay-content__item-main--list">
						<div :class="[`pay-content__item-main--for-1`, `pay-content__item-header__d1`]">
							<div class="pay-content__item-main--role">RSM</div>
							<div class="pay-content__item-main--empName">{{ item.rsmName }}</div>
						</div>
						<div :class="[`pay-content__item-main--for-1`, `pay-content__item-header__d1`]">
							<div class="pay-content__item-main--role">{{ item.dsmNum + ' DSM' }}</div>
							<div class="pay-content__item-main--empName">
								{{ item.tbaDsmNum + ' 空岗' }}
							</div>
						</div>
						<div :class="[`pay-content__item-main--for-1`, `pay-content__item-header__d1`]">
							<div class="pay-content__item-main--role">{{ item.mrNum + ' MR' }}</div>
							<div class="pay-content__item-main--empName">
								{{ item.tbaMrNum + ' 空岗' }}
							</div>
						</div>
						<div :class="[`pay-content__item-main--for-1`]">
							<div class="pay-content__item-main--role mr-month">MR月度生产力</div>
							<div class="pay-content__item-main--empName">
								<div>{{ (item.avgAmountRsm / 1000).toFixed(2) + 'K' }}</div>
								<div class="pay-content__item-main--empName__img_up" v-if="item.avgAmountRsm > item.avgAmountBud"></div>
								<div class="pay-content__item-main--empName__img" v-else></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
	</div>
</template>
<script setup>
import { salesPerformance } from '@/api/report';
import { getFilterTime, getNameByPath } from '@/utils/index';
const { proxy } = getCurrentInstance();
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let props = defineProps(['dinfo', 'pathAddress', 'summary', 'inChat']);
let pageTitle = props.dinfo?.name || 'XPRT销售生产力报告';
// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.dinfo?.startTime),
	endDate: getFilterTime(props.dinfo?.endTime),
});
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	getData();
	proxy.$umeng('筛选', `${page}${pageTitle}-日期`, `${_startDate}-${_endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.endDate;
		query.endDate = n.endDate;
		getData();
		proxy.$umeng('筛选', `${page}${props.dinfo?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);
const peopleList = ref([]);
const message = ref('');
onMounted(async () => {
	await getData();
});
onUnmounted(() => {});

const emit = defineEmits(['filterChange', 'summaryIng', 'openfilter']);
let getData = async () => {
	let res = await salesPerformance({
		territoryCode: 'CN',
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		pathAddress: props.pathAddress || '',
	});
	peopleList.value = res.result.list;
	console.log(query.startDate.slice(0, 4) + '年' + query.startDate.slice(4, 6) + '月');
	emit('filterChange', {
		name: 'salesPerformanceProductivityPPT',
		info: {
			date: {
				startLocalDate: query.startDate.slice(0, 4) + '年' + query.startDate.slice(4, 6) + '月',
			},
			name: pageTitle,
			data: { peopleList: peopleList.value, message: res.result.message },
		},
	});
	if (props.summary) {
		emit('summaryIng', { data: { ...res.result }, params: query });
	}
};
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import './pc-peoplesAnalysis.scss';
$d1--color: #01a2bf;
$d1--background--color: rgba(94, 103, 104, 0.1);
$d1--background--color2: rgba(1, 162, 191, 0.8);

.pay {
	width: 100%;
	padding: 8px 8px;
	.pay-title {
		margin-left: 5px;
		color: var(--pv-default-color);
		font-weight: bold;
		font-size: 14px;
	}
	.pay-desc {
		margin: 5px;
		color: var(--pv-no-active-color);
		font-size: 9px;
	}
	.pay-lengend {
		margin-left: 5px;
		align-items: center;
		display: flex;
		.pay-lengend-item {
			align-items: center;
			color: var(--pv-no-active-color);
			display: flex;
			font-size: 9px;
			margin-right: 8px;
			img {
				position: relative;
				margin-right: 3px;
				width: 10px;
			}
		}
	}
	.pay-content {
		margin-top: 10px;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		.item-left {
			margin-right: 10px;
		}
		.item-right {
			margin-right: 0px;
		}

		.pay-content__item {
			width: calc((100% - 10px) / 2);
			.pay-content__item-header {
				margin-bottom: 14px;
				position: relative;
				.pay-content__item-header__title {
					background-color: #b5b5b5;
					color: #ffffff;
					font-size: 9px;
					line-height: 20px;
					text-align: center;
				}
				.pay-content__item-bottom {
					padding: 10px;

					.pay-content__item--rows {
						margin: 0 auto;
						width: fit-content;

						.pay-content__item--rows--item {
							align-items: center;
							display: flex;
							margin-bottom: 3px;

							.pay-content__item--rows--label {
								color: var(--pv-default-color);
								font-size: 9px;
							}
							.pay-content__item--rows--bar {
								height: 4px;
								div {
									height: 100%;
								}
							}

							.pay-content__item--rows--value {
								color: var(--pv-default-color);
								font-size: 9px;
							}

							.pay-content__item--rows--icon {
								background-position: 50%;
								background-size: 100% 100%;
								height: 8px;
								margin-right: 3px;
								width: 10px;
							}
						}
					}
				}
			}
			.pay-content__item-header::after {
				content: ' ';
				height: 14px;
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				width: 17px;
				background-position: 50%;
				background-size: 100% 100%;
			}

			.pay-content__item-main {
				align-items: center;
				display: flex;
				justify-content: space-around;
				width: 100%;
				gap: 4px;
				.pay-content__item-main--list {
					flex: 1 1;
					.pay-content__item-main--for-1 {
						align-items: center;
						border-radius: 4px;
						display: flex;
						flex-direction: column;
						height: 36px;
						justify-content: center;
						margin: 3px 0px 13.5px;
						position: relative;
						border: 2px solid $d1--background--color2;

						.pay-content__item-main--role {
							color: var(--pv-default-color);
							font-weight: bold;
							display: inline-block;
							font-size: 12px;
							line-height: 1;
							margin-bottom: 3px;
						}
						.mr-month {
							color: var(--pv-no-active-color);
							font-size: 9px;
							font-weight: 400;
						}

						.pay-content__item-main--empName {
							color: var(--pv-no-active-color);
							font-size: 9px;
							line-height: 1;
							display: flex;
						}
						&:nth-last-child(1) {
							margin-bottom: 8px;
						}
					}

					.pay-content__item-main--for-1::after {
						bottom: -15.8px;
						content: ' ';
						height: 14px;
						left: 50%;
						position: absolute;
						transform: translateX(-50%);
						width: 17px;
						background-position: 50%;
						background-size: 100% 100%;
					}
				}
			}
		}

		.pay-content__item-header__title--d1 {
			background-color: $d1--color;
		}

		.pay-content__item-bottom--d1 {
			background-color: $d1--background--color;
		}

		.pay-content__item--rows--bar-d1 {
			background: $d1--background--color2;
		}
		.pay-content__item-header__d1::after {
			background: url('../../../../assets/img/d1-icon.png') no-repeat;
		}
		.pay-content__item-main--empName__img {
			height: 10px;
			margin-left: 3px;
			width: 10px;
			background: url('../../../../assets/img/down.png') no-repeat;
			background-position: 50%;
			background-size: 100% 100%;
			position: relative;
			// top: 2px;
			// transform: rotate(180deg);
		}
		.pay-content__item-main--empName__img_up {
			height: 10px;
			margin-left: 3px;
			width: 10px;
			background: url('../../../../assets/img/up.png') no-repeat;
			background-position: 50%;
			background-size: 100% 100%;
			position: relative;
			top: -1px;
			// transform: rotate(180deg);
		}
	}
	.lz {
		font-weight: bold;
		margin-right: 2px;
	}
}
</style>
