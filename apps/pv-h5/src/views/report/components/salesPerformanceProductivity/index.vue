<template>
	<div class="spp" id="ap-spp">
		<!-- title -->
		<!-- <div class="psa-title">
			<span class="psa-title__text">销售生产力报告</span>
		</div> -->

		<!-- card - 人员及生产力分析 -->
		<div class="psa-card">
			<peoples-analysis ref="peoplesAnalysisRef" v-bind="$attrs"></peoples-analysis>
		</div>
	</div>
</template>
<script setup>
import peoplesAnalysis from './peoplesAnalysis';
let peoplesAnalysisRef = ref(null);
let query = ref(null);
onMounted(() => {
	nextTick(() => {
		query.value = peoplesAnalysisRef.value.query;
	});
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import './pc-index.scss';
.spp {
	width: 100%;
	display: flex;
	flex-direction: column;
	// margin-top: 10px;
	background-color: #fff;
	border-radius: 5px;
	padding: 0px 8px 6px;
	.psa-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 7px;
		.psa-title__text {
			font-size: 16px;
			font-weight: bold;
			color: var(--pv-default-color);
		}
	}

	.psa-card {
		display: flex;
		flex-direction: column;
	}
}
</style>
