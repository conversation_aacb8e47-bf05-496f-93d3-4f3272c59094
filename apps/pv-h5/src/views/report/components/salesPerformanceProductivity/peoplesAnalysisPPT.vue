<template>
	<div class="ignore-pay">
		<div class="card-title">
			<span>人员及生产力分析</span>
		</div>
		<div class="pay-content">
			<div class="pay-content__item item-left" v-for="(item, index) in peopleList" :key="index">
				<div class="pay-content__item-header" :class="[`pay-content__item-header__d1`]">
					<div class="pay-content__item-header__title" :class="[`pay-content__item-header__title--d1`]">{{ item.rsdName }}</div>
					<div class="pay-content__item-bottom" :class="[`pay-content__item-bottom--d1`]">
						<div class="pay-content__item--rows">
							<div class="pay-content__item--rows--item">
								<div class="pay-content__item--rows--label">RSM岗位数{{ item.rsmNum }}</div>
								<div class="pay-content__item--rows--value">
									<span class="lz">{{ item.rsm }}</span>
									{{ '(' + item.tbaRsmNum + '空岗' + ((item.tbaRsmNum / item.rsmNum) * 100).toFixed(2) + '%' + ')' }}
								</div>
							</div>
							<div class="pay-content__item--rows--item">
								<div class="pay-content__item--rows--label">DSM岗位数{{ item.dsmNum }}</div>
								<div class="pay-content__item--rows--value">
									<span class="lz">{{ item.dsm }}</span>
									{{ '(' + item.tbaDsmNum + '空岗' + ((item.tbaDsmNum / item.dsmNum) * 100).toFixed(2) + '%' + ')' }}
								</div>
							</div>
							<div class="pay-content__item--rows--item">
								<div class="pay-content__item--rows--label">MR岗位数{{ item.mrNum }}</div>
								<div class="pay-content__item--rows--value">
									<span class="lz">{{ item.dsm }}</span>
									{{ '(' + item.tbaMrNum + '空岗' + ((item.tbaMrNum / item.mrNum) * 100).toFixed(2) + '%' + ')' }}
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="pay-content__item-main">
					<div class="pay-content__item-main--list">
						<div :class="[`pay-content__item-main--for-1`, `pay-content__item-header__d1`]">
							<div class="pay-content__item-main--role">RSM</div>
							<div class="pay-content__item-main--empName">{{ item.rsmName }}</div>
						</div>
						<div :class="[`pay-content__item-main--for-1`, `pay-content__item-header__d1`]">
							<div class="pay-content__item-main--role">{{ item.dsmNum + ' DSM' }}</div>
							<div class="pay-content__item-main--empName">{{ item.tbaDsmNum + ' 空岗' }}</div>
						</div>
						<div :class="[`pay-content__item-main--for-1`, `pay-content__item-header__d1`]">
							<div class="pay-content__item-main--role">{{ item.mrNum + ' MR' }}</div>
							<div class="pay-content__item-main--empName">{{ item.tbaMrNum + ' 空岗' }}</div>
						</div>
						<div :class="[`pay-content__item-main--for-1`]">
							<div class="pay-content__item-main--role mr-month">MR月度生产力</div>
							<div class="pay-content__item-main--empName">
								<div style="color: #172b4d; font-weight: bold; font-size: 24px">{{ (item.avgAmountRsm / 1000).toFixed(2) }}</div>
								<div class="pay-content__item-main--empName__img_up" v-if="item.avgAmountRsm > item.avgAmountBud"></div>
								<div class="pay-content__item-main--empName__img" v-else></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="info">
			<div>
				<div class="item">
					<div class="pay-content__item-main--empName__img_up"></div>
					高于BU平均MR生产力
				</div>
				<div class="item">
					<div class="pay-content__item-main--empName__img"></div>
					低于BU平均MR生产力
				</div>
			</div>

			<div>MR月度生产力=RSM对应销售额/MR岗位数</div>
		</div>
	</div>
</template>
<script setup>
import { salesPerformance } from '@/api/report';
const props = defineProps(['info']);
const peopleList = ref([]);
onMounted(async () => {
	// await getData();
});
onUnmounted(() => {});

// TODO:调整接口默认参数
let getData = async () => {
	let res = await salesPerformance({ territoryCode: 'XPRT', skuCode: 'BJSZ006', province: '北京' });
	peopleList.value = res.result;
	console.log(peopleList.value);
};

watch(
	() => props.info,
	(n) => {
		if (n.info.data) {
			peopleList.value = n.info.data.peopleList;
		}
	}
);
</script>
<style lang="scss" scoped>
$d1--color: #01a2bf;
$d1--background--color: rgba(94, 103, 104, 0.1);
$d1--background--color2: rgba(1, 162, 191, 0.8);

.ignore-pay {
	transform: scale(0.5);
	font-size: 20px;
	width: 200%;
	border: 2px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 8px 8px;
	height: 200%;
	position: absolute;
	left: -395px;
	top: -203px;
	.card-title {
		width: 100%;
		border-radius: 8px 8px 0 0;
		display: flex;
		align-items: center;
		padding: 12px 24px;
		background: #283b5c;
		height: 46px;
		span {
			font-size: 20px;
			color: #fff;
		}
	}
	.pay-desc {
		margin: 10px;
		color: var(--pv-no-active-color);
		font-size: 18px;
	}
	.pay-lengend {
		margin-left: 10px;
		align-items: center;
		display: flex;
		.pay-lengend-item {
			align-items: center;
			color: var(--pv-no-active-color);
			display: flex;
			font-size: 18px;
			margin-right: 16px;
			img {
				position: relative;
				margin-right: 6px;
				width: 20px;
			}
		}
	}
	.pay-content {
		display: flex;
		justify-content: space-between;
		flex-wrap: nowrap;
		padding: 30px 30px 0;
		.item-left {
			margin-right: 20px;
		}
		.item-left:nth-last-child(1) {
			margin-right: 0;
		}
		.item-right {
			margin-right: 0px;
		}

		.pay-content__item {
			flex: 1;
			.pay-content__item-header {
				margin-bottom: 28px;
				position: relative;
				.pay-content__item-header__title {
					background-color: #b5b5b5;
					color: #ffffff;
					font-size: 18px;
					line-height: 40px;
					text-align: center;
				}
				.pay-content__item-bottom {
					margin-top: 10px;
					padding: 10px 20px 0;
					height: 200px;
					.pay-content__item--rows {
						margin: 0 auto;
						width: fit-content;
						.pay-content__item--rows--item {
							line-height: 26px;
							margin-bottom: 10px;
							.pay-content__item--rows--label {
								color: var(--pv-default-color);
								font-size: 18px;
							}
							.pay-content__item--rows--bar {
								height: 8px;
								div {
									height: 200%;
								}
							}

							.pay-content__item--rows--value {
								color: var(--pv-default-color);
								font-size: 18px;
							}

							.pay-content__item--rows--icon {
								background-position: 50%;
								background-size: 100% 100%;
								height: 16px;
								margin-right: 6px;
								width: 20px;
							}
						}
					}
				}
			}
			.pay-content__item-header::after {
				content: ' ';
				height: 28px;
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				width: 34px;
				background-position: 50%;
				background-size: 100% 100%;
			}

			.pay-content__item-main {
				align-items: center;
				display: flex;
				justify-content: space-around;
				width: 100%;
				gap: 8px;
				.pay-content__item-main--list {
					flex: 1 1;
					.pay-content__item-main--for-1 {
						align-items: center;
						border-radius: 8px;
						display: flex;
						flex-direction: column;
						height: 72px;
						justify-content: center;
						margin: 0px 0px 27px;
						position: relative;
						border: 2px solid $d1--background--color2;

						.pay-content__item-main--role {
							color: var(--pv-default-color);
							font-weight: bold;
							display: inline-block;
							font-size: 24px;
							line-height: 1;
							margin-bottom: 6px;
						}
						.mr-month {
							color: var(--pv-no-active-color);
							font-size: 18px;
							font-weight: 400;
						}

						.pay-content__item-main--empName {
							color: var(--pv-no-active-color);
							font-size: 18px;
							line-height: 1;
							display: flex;
						}
						&:nth-last-child(1) {
							margin-bottom: 16px;
						}
					}

					.pay-content__item-main--for-1::after {
						bottom: -29.4px;
						content: ' ';
						height: 28px;
						left: 50%;
						position: absolute;
						transform: translateX(-50%);
						width: 34px;
						background-position: 50%;
						background-size: 100% 100%;
					}
					.pay-content__item-main--for-1:nth-last-child(1) {
						margin-bottom: 0;
					}
				}
			}
		}

		.pay-content__item-header__title--d1 {
			background-color: $d1--color;
		}

		.pay-content__item-bottom--d1 {
			background-color: $d1--background--color;
		}

		.pay-content__item--rows--bar-d1 {
			background: $d1--background--color2;
		}
		.pay-content__item-header__d1::after {
			background: url('../../../../assets/img/d1-icon.png') no-repeat;
		}
	}
	.pay-content__item-main--empName__img {
		height: 20px;
		margin-left: 6px;
		width: 20px;
		background: url('../../../../assets/img/down.png') no-repeat;
		background-position: 50%;
		background-size: 100% 100%;
		position: relative;
		// top: 2px;
		// transform: rotate(180deg);
	}
	.pay-content__item-main--empName__img_up {
		height: 20px;
		margin-left: 6px;
		width: 20px;
		background: url('../../../../assets/img/up.png') no-repeat;
		background-position: 50%;
		background-size: 100% 100%;
		position: relative;
		top: -2px;
		// transform: rotate(180deg);
	}
	.lz {
		font-weight: bold;
		margin-right: 4px;
	}
	.info {
		display: flex;
		margin: 30px 30px 0;
		color: #6b778c;
		justify-content: space-between;
		& > div {
			display: flex;
		}
		.item {
			display: flex;
			margin-right: 24px;
			div:nth-child(1) {
				margin-right: 6px;
			}
		}
	}
}
</style>
