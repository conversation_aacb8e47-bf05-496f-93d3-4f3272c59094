<template>
	<div class="psa" id="ap-hia">
		<!-- title -->
		<!-- card - Total 纯销分析 -->
		<div class="psa-card">
			<hospital-internal-sales ref="hospitalInternalSalesRef" v-bind="$attrs"></hospital-internal-sales>
		</div>
	</div>
</template>
<script setup>
import hospitalInternalSales from './hospitalInternalSales.vue';
const hospitalInternalSalesRef = ref(null);
let query = ref(null);
onMounted(() => {
	nextTick(() => {
		query.value = hospitalInternalSalesRef.value.query;
	});
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import './pc-index.scss';
.psa {
	width: 100%;
	display: flex;
	flex-direction: column;
	margin-top: 10px;
	background-color: #fff;
	border-radius: 5px;
	.psa-card {
		display: flex;
		flex-direction: column;
	}
}
</style>
