<template>
	<div class="his" id="ap-his">
		<div class="his-title">{{ pageTitle }}</div>
		<!-- 通用筛选器 -->
		<globalFilter
			:filters="[
				{ name: 'time', size: '49', queryName: 'dateRange' },
				{ name: 'org', size: '49', queryName: 'personName' },
				{ name: 'brand', size: '49', queryName: 'brandName', disabled: true },
				{ name: 'city', size: '49', queryName: 'countyName' },
			]"
			:query="query"
			:loading="filterLoading"
			@openTime="openTime"
			@openOrg="openOrg"
			@openBrand="openBrand"
			@openCity="openCity"
		></globalFilter>
		<div class="his-table ignore-sale-table">
			<vxe-table show-footer max-height="400" :data="rankList" :scroll-y="{ enabled: false }" @scroll="onScroll" :sort-config="{ trigger: 'cell' }" :footer-method="footerMethod" :footer-span-method="footerColspanMethod">
				<vxe-column field="ranking" align="center" title="排名" width="40px"></vxe-column>
				<vxe-column field="hospName" align="left" title="医院名称" sortable width="140px"></vxe-column>
				<vxe-column field="salesV" align="right" title="销售金额K" sortable width="70px"></vxe-column>
				<vxe-column field="salesPercentage" align="right" title="销售贡献" sortable width="70px"></vxe-column>
				<vxe-column field="ach" align="right" title="达成率" sortable width="70px"></vxe-column>
				<vxe-column field="growth" align="right" title="同比增长" sortable width="70px"></vxe-column>
				<vxe-column field="ms" align="right" title="市场份额" sortable width="70px"></vxe-column>
				<vxe-column field="ei" align="right" title="EI" sortable width="70px"></vxe-column>
				<vxe-column field="admissionStatus" align="right" title="进院状态" sortable width="70px"></vxe-column>
			</vxe-table>
		</div>
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
		<!-- 品牌 -->
		<!-- <OneLevelRadio ref="brand" :list="brandData" :path="`${page}${pageTitle}-产品`" :defaultCheck="query.brandId" @_confirm="brandConfirm"></OneLevelRadio> -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${pageTitle}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.dinfo?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import { topHospital } from '@/api/report';
import { formatKNumberT, formatPrecentOne, getFilterTime, getNameByPath } from '@/utils/index';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
let props = defineProps(['isPPT', 'dinfo', 'pathAddress']);

let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let pageTitle = props.dinfo?.name || 'TOP医院分析报告';
const brandData = ref([]);

// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.dinfo?.startTime),
	endDate: getFilterTime(props.dinfo?.endTime),
	brandId: '',
	brandName: '全部产品',
	countyName: '全部省市',
	personId: '',
	personName: '全部人员',
});
//初始化品牌
function findBrand(arr, brand) {
	for (let item of arr) {
		// 检查当前节点是否匹配
		if (item.code === brand) {
			return item;
		}
		// 如果有子节点，递归查找
		if (item.children && item.children.length > 0) {
			const result = findBrand(item.children, brand);
			if (result) {
				return result; // 找到后立即返回
			}
		}
	}
	// 如果未找到，返回 undefined
	return undefined;
}
let a = findBrand(filterStore.skuInfoTree, 'KEPPRA');
console.log(a);
query.brandName = a.code;
query.brandId = a.skuCode;
// 默认选中人员
const defaultPerson = ref('');

// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.dinfo.filterConfig) {
		const filterConfig = JSON.parse(props.dinfo.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgZ.value.show = true;
};
const city = ref(null);
const { cascaderChange, initFilters, filterLoading } = useCascader(query, { city, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	await getData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
watch(
	() => reportStore.globalFilterInfo.people,
	async (n) => {
		orgZ.value.handleReset();
		defaultPerson.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		orgZ.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		orgZ.value.confirm();
	}
);
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	getData();
	proxy.$umeng('筛选', `${page}${pageTitle}-日期`, `${_startDate}-${_endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.startDate;
		query.endDate = n.endDate;
		getData();
		proxy.$umeng('筛选', `${page}${props.dinfo?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);
// 城市
const openCity = () => {
	city.value.show = true;
};
const cityConfirm = async (params) => {
	console.log(params);
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
watch(
	() => reportStore.globalFilterInfo.area,
	async (n) => {
		console.log(n);
		city.value.golbalInit(n.provinceData);
		query.countyName = n.countyName;
		query.city = n.city;
		query.province = n.province;
		await getData();
		proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
	}
);
// 品牌筛选
const brand = ref(null);
const openBrand = () => {
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
	brand.value.show = true;
};

let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	await getData();
	proxy.$umeng('筛选', `${page}${pageTitle}-产品`, name);
};
const defaultSku = ref('');
watch(
	() => reportStore.globalFilterInfo.brand,
	async (n) => {
		brand.value.handleReset();
		brandData.value = filterStore.skuInfoTree;
		defaultSku.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		brand.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		brand.value.confirm();
	}
);
const top20Total = ref({
	sales: '',
	ach: '',
	salesContribution: '',
	growth: '',
	ms: '',
});
const total = ref({
	salesV: '',
	ach: '',
	salesContribution: '',
	growth: '',
	ms: '',
});
const rankList = ref([]);
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//初始化级联
	initFilters();
	await getData();
});
onUnmounted(() => {});

const emit = defineEmits(['filterChange']);
let getData = async () => {
	let startLocalDate = `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`;
	let endLocalDate = `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`;
	let res = await topHospital({
		sort: 'salesV,desc',
		territoryCode: query.personId ? query.personId : '',
		skuCode: query.brandId,
		startLocalDate: startLocalDate,
		endLocalDate: endLocalDate,
		province: query.province ? query.province.join(',') : '',
		city: query.city ? query.city.join(',') : '',
		pathAddress: props.pathAddress || '',
	});
	rankList.value = [];
	footerData.value = [];

	for (const item of res.result.list) {
		rankList.value.push({
			ranking: item.ranking,
			hospName: item.hospName,
			salesV: formatKNumberT(item.salesV),
			salesPercentage: `${formatPrecentOne(item.salesPercentage) + '%'}`,
			ach: `${formatPrecentOne(item.ach) + '%'}`,
			growth: `${formatPrecentOne(item.growth) + '%'}`,
			ms: `${formatPrecentOne(item.ms) + '%'}`,
			ei: item.ei,
			admissionStatus: item.admissionStatus,
			isThereSales: item.isThereSales === true ? '是' : '否',
		});
	}

	top20Total.value = res.result.top20Total;
	total.value = res.result.total;
	let reportName = `${query.startDate.slice(0, 4)}年${query.startDate.slice(4, 6)}月 - ${query.endDate.slice(0, 4)}年${query.endDate.slice(4, 6)}月开浦兰TOP20终端贡献分析`;
	if (query.startDate.slice(4, 6) + '' === query.endDate.slice(4, 6) + '') {
		reportName = `${query.startDate.slice(0, 4)}年${query.startDate.slice(4, 6)}月开浦兰TOP20终端贡献分析`;
	}
	emit('filterChange', {
		name: 'hospitalAnalysisPPTKEPPRA',
		info: {
			date: {
				startLocalDate: startLocalDate.slice(0, 7).replace('-', '年') + '月',
				endLocalDate: endLocalDate.slice(0, 7).replace('-', '年') + '月',
			},
			name: `${reportName}`,
			product: query.brandName,
			data: {
				rankList: rankList.value,
				top20Total: top20Total.value,
				total: total.value,
				message: res.result.message,
			},
			people: query.personName,
		},
	});
};
let onScroll = (e) => {
	if (e.isY && e.scrollHeight - e.scrollTop === e.bodyHeight) {
		console.log('触底了');
	}
};

watch(
	() => top20Total.value,
	(val) => {
		if (val) {
			let temp = [' Top20 医院合计 ', formatKNumberT(val.sales), `${formatPrecentOne(val.salesContribution) + '%'}`, `${formatPrecentOne(val.ach) + '%'}`, `${formatPrecentOne(val.growth) + '%'}`, `${formatPrecentOne(val.ms) + '%'}`, '-', '-', '-', '-'];
			footerData.value.push(temp);
		}
	},
	{ flush: 'post' }
);
watch(
	() => total.value,
	(val) => {
		if (val) {
			let temp = [' Total 总计 ', formatKNumberT(val.salesV), `${formatPrecentOne(val.salesContribution) + '%'}`, `${formatPrecentOne(val.ach) + '%'}`, `${formatPrecentOne(val.growth) + '%'}`, `${formatPrecentOne(val.ms) + '%'}`, '-', '-', '-', '-'];
			footerData.value.push(temp);
		}
	},
	{ flush: 'post' }
);

const footerData = ref([]);
const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};
const footerColspanMethod = (value) => {
	if (value.columnIndex === 0) {
		return {
			rowspan: 1,
			colspan: 2,
		};
	}
};
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import './pc-hospitalInternalSales.scss';
.his {
	width: 100%;
	padding: 8px 8px;
	background-color: var(--pv-card-bgc);
	border-radius: 5px;
	.his-title {
		margin-left: 5px;
		color: var(--pv-default-color);
		font-weight: bold;
		font-size: 14px;
	}

	.his-table {
		background-color: #ffffff;
		&:deep(.vxe-footer--column.col--left) {
			.vxe-cell {
				text-align: right !important;
			}
		}

		&:deep(.vxe-header--column.col--center) {
			.vxe-cell {
				justify-content: center !important;
			}
		}
		&:deep(.vxe-header--column.col--left) {
			.vxe-cell {
				justify-content: left !important;
			}
		}
	}
	::v-deep(.vxe-table--footer-wrapper) {
		.col--last {
			display: none;
		}
	}
	.q-fileter-item {
		margin: 5px 0;
	}
}
</style>
