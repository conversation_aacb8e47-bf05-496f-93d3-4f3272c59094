<template>
	<div class="marek-share" id="ap-marek-share">
		<div class="top">
			<div class="card-title">
				<span>
					{{ dinfo?.name || pageTitle }}
				</span>
			</div>
		</div>
		<!-- 通用筛选器 -->
		<globalFilter
			:filters="[
				{ name: 'time', size: '49', queryName: 'dateRange', single: true },
				{ name: 'org', size: '49', queryName: 'personName' },
				{ name: 'brand', size: '32', queryName: 'brandName', disabled: true },
				{ name: 'city', size: '32', queryName: 'countyName' },
				{ name: 'hospital', size: '32', queryName: 'hospitalName' },
			]"
			:query="query"
			:loading="filterLoading"
			@openTime="openTime"
			@openOrg="openOrg"
			@openBrand="openBrand"
			@openCity="openCity"
			@openHospital="openHospital"
		></globalFilter>
		<div class="content">
			<div>
				<loading v-show="comStatus === 'loading'"></loading>
				<div v-show="comStatus === 'normal'" class="marek-share-echarts" :id="`marek-share-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
		</div>
		<div class="sale-table">
			<div class="sale-table-title" @click="zk = !zk">
				<span>点击查看明细</span>
				<svg-icon class="icon" :class="{ down: zk }" icon-class="down2"></svg-icon>
			</div>
			<!-- <transition @beforeEnter="handleBeforeEnter" @enter="handleEnter" @leave="handleLeave"> -->
			<div v-show="zk" class="sale-table-content">
				<tableCom :tableData="tableData" :columns="columns"></tableCom>
			</div>
			<!-- </transition> -->
		</div>
		<!-- 数据更新至 -->
		<!-- <dataUpdateBy :updateTime="props.dinfo?.dataSyncTime"></dataUpdateBy> -->
		<!-- 品牌 -->
		<skuFilter level="brand" ref="brand" :listData="brandData" :path="`${page}${props.dinfo?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.dinfo?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :defaultHosCheckedAll="defaultHosCheckedAll" :path="`${page}${props.dinfo?.nameCn}`" :personalId="query.personId" :province="query.province" :city="query.city" :skuCode="query.brandId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<!-- <DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery> -->
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import { showToast, showLoadingToast } from 'vant';
import { salesTrends, salesInfoDownload } from '@/api/sales';
import { generateRandomNumber, getFilterTime, translateFont, deepClone, getNameByPath, formatNumbeW, formatNumbeWT, spaces, formatNumber, saleTrendFilter, isPCTrue } from '@/utils/index';
import * as echarts from 'echarts';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
let filterStore = usefilterStore();
import tableCom from './table';
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let showUpdateTime = ref(true);
let pageTitle = '销售趋势总览';
if (route.fullPath.includes('/report')) {
	showUpdateTime.value = false;
}
const props = defineProps(['dinfo', 'pathAddress']);

const query = reactive({
	startDate: getFilterTime(props.dinfo?.startTime),
	endDate: getFilterTime(props.dinfo?.endTime),
	brandId: '',
	brandName: '全部品牌',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
});
const defaultPerson = ref('');

// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.dinfo.filterConfig) {
		const filterConfig = JSON.parse(props.dinfo.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
};
//初始化品牌
function findBrand(arr, brand) {
	for (let item of arr) {
		// 检查当前节点是否匹配
		if (item.code === brand) {
			return item;
		}
		// 如果有子节点，递归查找
		if (item.children && item.children.length > 0) {
			const result = findBrand(item.children, brand);
			if (result) {
				return result; // 找到后立即返回
			}
		}
	}
	// 如果未找到，返回 undefined
	return undefined;
}
let a = findBrand(filterStore.skuInfoTree, 'XYZAL');
console.log(a);
query.brandName = a.code;
query.brandId = a.skuCode;

// 日期筛选
// const showPicker = ref(false);
// const pickerValue = ref([]);
// const timerColumns = ref([
// 	{ text: '2024', value: '2024' },
// 	{ text: '2025', value: '2025' },
// ]);
const time = ref(null);

const openTime = () => {
	// showPicker.value = true;
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	// showPicker.value = false;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.dinfo?.nameCn || pageTitle}-日期`, `${_startDate}-${_endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.endDate;
		query.endDate = n.endDate;
		// showPicker.value = false;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.dinfo?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);

watch(
	() => reportStore.globalFilterInfo.area,
	async (n) => {
		console.log(n);
		city.value.golbalInit(n.provinceData);
		query.countyName = n.countyName;
		query.city = n.city;
		query.province = n.province;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.dinfo.nameCn}-城市`, query.countyName);
	}
);
const lastResult = ref({});

// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
// 传入树形数组只保留传入的层数

const filterTree = (arr, level, max) => {
	level = level || 0;
	max = max || 2;
	return arr.filter((item) => {
		if (item.children) {
			item.children = filterTree(item.children, level + 1, max);
		}
		return level <= max;
	});
};
const openBrand = () => {
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
	brand.value.show = true;
};

let brandConfirm = async (params) => {
	console.log(params);
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部品牌';
	}
	query.brandId = params.skuCode.join(',');

	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.dinfo.nameCn}-产品`, query.brandName);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city, hospital, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.dinfo.nameCn}-人员`, query.personName);
};
// 城市
const openCity = () => {
	city.value.show = true;
};
const cityConfirm = async (params) => {
	console.log(params);
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.dinfo.nameCn}-城市`, query.countyName);
};
watch(
	() => reportStore.globalFilterInfo.people,
	async (n) => {
		orgZ.value.handleReset();
		defaultPerson.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		orgZ.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		orgZ.value.confirm();
	}
);
// 医院筛选
const openHospital = () => {
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.dinfo.nameCn}-医院`, query.hospitalName);
};
const defaultHos = ref('');
const defaultHosCheckedAll = ref('');

watch(
	() => reportStore.globalFilterInfo.hospital,
	async (n) => {
		console.log(n);

		hospital.value.handleReset();
		defaultHos.value = n.checked; //默认选中
		defaultHosCheckedAll.value = n.isCheckAll;
		await nextTick(); // 确保 DOM 更新完成后再执行
		hospital.value.handleDefault(n);
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		hospital.value.confirm();
	}
);
let myEcharts = echarts;
let chart;
const tableList = ref([]);

let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#marek-share-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let x = tableList.value.map((ele) => ele.date);
	let xs = tableList.value.map((ele) => ele.sales);
	let zb = tableList.value.map((ele) => ele.target);
	let dc = tableList.value.map((ele) => ele.ach);
	let tbzz = tableList.value.map((ele) => ele.growth);
	let tq = tableList.value.map((ele) => ele.salesLy);
	let option = {
		grid: {
			top: translateFont(60),
			left: translateFont(58),
			right: translateFont(35),
			bottom: translateFont(30),
		},
		legend: {
			data: ['同期', '指标额', '销售额', '达成', '同比增长'],
			left: translateFont(10),
			top: translateFont(15),
			itemGap: translateFont(10),
			itemWidth: translateFont(15),
			itemHeight: translateFont(4),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = '';
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${saleTrendFilter(item)}</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			axisPointer: {
				type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
				},
			},
			extraCssText: 'z-index:9',
		},
		xAxis: [
			{
				type: 'category',
				data: x,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumber(value);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumber(value) + '%';
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
			},
		],
		color: ['rgba(211,211,211,.5)', '#E1EBFF', '#0052CC', '#E95600', '#01A2BF'],
		series: [
			{
				name: '同期',
				type: 'bar',
				data: tq,
				barWidth: translateFont(12),
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '指标额',
				type: 'bar',
				data: zb,
				barWidth: translateFont(12),
				z: 2,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '销售额',
				type: 'bar',
				data: xs,
				barWidth: translateFont(12),
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '达成',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: dc,
				z: 4,
				label: {
					show: true,
					position: 'top',
					formatter: (v) => {
						return v.value > 0 ? v.value + '%' : '';
					},
				},
			},
			{
				name: '同比增长',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: tbzz,
				z: 4,
			},
		],
	};
	chart.setOption(option);
};
let comStatus = ref('loading');
let zk = ref(false);
const tableData = ref([]);
const columns = ref([]);
let getData = async () => {
	comStatus.value = 'loading';

	let res = await salesTrends({
		skuCode: query.brandId ? query.brandId.split(',') : [],
		territoryCode: query.personId ? query.personId.split(',') : [],
		hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		province: query.province || [],
		city: query.city || [],
		type: 'salesTrendsOverview',
		pathAddress: props.pathAddress || '',
	});
	tableList.value = res.result.data;
	res.result.tableDynamicColumns.forEach((ele) => {
		if (ele.title === 'Metric') return;
		ele.title = ele.title + '  ';
	});
	tableData.value = res.result.tableData?.map((ele) => {
		if (ele.metric.includes('销售额') || ele.metric.includes('指标')) {
			for (let i in ele) {
				if (i !== 'metric') {
					ele[i] = formatNumbeWT(ele[i]) ? formatNumbeWT(ele[i]) : '';
				}
			}
			return ele;
		} else {
			for (let i in ele) {
				if (i !== 'metric') {
					ele[i] = ele[i] ? ele[i] + '%' : '';
				}
			}
			return ele;
		}
	});
	console.log(tableData.value);

	columns.value = res.result.tableDynamicColumns;
	comStatus.value = res.result.data.length < 1 ? 'empty' : 'normal';
	let itensDateLength = res.result.data.length;
	let clientWidth = document.body.clientWidth || document.documentElement.clientWidth;
	if (clientWidth > 600) {
		document.querySelector(`#marek-share-echarts-${echartsId}`).style.width = 15 * 3.75 * itensDateLength + 26 * 3.75 + 'px';
	} else {
		document.querySelector(`#marek-share-echarts-${echartsId}`).style.width = 15 * itensDateLength + 26 + 'vw';
	}
	reportStore.setReportInfo({
		name: '优泽销售趋势分析',
		info: {
			monthText: `${query.startDate.slice(0, 4)}${query.startDate.slice(4, 6)}`,
			name: `${query.startDate.slice(0, 4)}年${query.startDate.slice(4, 6)}月优泽销售趋势分析`,
			people: query.personName,
			hospital: query.hospitalName,
			product: query.brandName,
			data: {
				tableList: tableList.value,
				tableData: tableData.value,
				columns: columns.value,
				message: res.result.message,
			},
		},
	});
};
let echartsId = generateRandomNumber();
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//初始化级联
	initFilters();
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../../style/pc/observe/salesTrendProduct.scss';
.marek-share {
	background-color: var(--pv-card-bgc);
	// margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.content {
		overflow-x: auto;
		overflow-y: hidden;
		background-color: var(--pv-nav-bar-active);
		margin-top: 8px;
		border-radius: 5px;
		&::-webkit-scrollbar {
			height: 4px; /* 设置纵向滚动条宽度 */
		}

		&::-webkit-scrollbar-thumb {
			background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
			border-radius: 5px;
		}
	}
	&-echarts {
		width: 100%;
		height: 215px;
	}
	.tags {
		font-size: 9px;
		padding: 0 10px 8px;
		display: flex;
		&-item {
			background: #f2f5f8;
			border-radius: 0.533vw;
			color: #a9b2c2;
			margin-bottom: 1.333vw;
			margin-right: 1.333vw;
			text-align: center;
			padding: 3px;
		}
	}
}
.sale-table {
	background-color: #fff;
	margin-top: 8px;
	overflow: hidden;
	&-title {
		font-size: 12px;
		color: var(--pv-default-color);
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 6px;
		position: relative;
		.icon {
			font-size: 7px;
			position: absolute;
			right: 14px;
			transition: all 0.5s;
		}
		.down {
			transform: rotate(180deg);
		}
	}
	&-content {
		padding: 0 8px 0px;
	}
}
::v-deep(.van-picker__confirm) {
	color: #0052cc;
}
:deep(.vxe-cell--title) {
	white-space: pre-wrap;
}
</style>
