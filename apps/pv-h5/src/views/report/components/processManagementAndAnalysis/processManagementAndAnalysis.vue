<template>
	<div class="processManagementAndAnalysis">
		<visitSummary :hiddenStar="true" v-bind="$attrs"></visitSummary>
		<visitManagementAndPerformance :hiddenStar="true" v-bind="$attrs"></visitManagementAndPerformance>
		<!-- <img src="@/assets/img/report5.png" alt="" /> -->
	</div>
</template>
<script setup>
import visitSummary from '@/views/observe/compoents/visitSummary.vue';
import visitManagementAndPerformance from '@/views/observe/compoents/visitManagementAndPerformance.vue';
</script>
<style lang="scss" scoped>
.processManagementAndAnalysis {
	.visit-summary,
	.visit-management {
		margin: 10px 0 0;
		background-color: #fff;
	}
	img {
		width: 100%;
	}
}
</style>
