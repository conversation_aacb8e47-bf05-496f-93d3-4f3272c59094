<template>
	<div class="cityConferenceAttendanceRate">
		<div class="cityConferenceAttendanceRate-title">DSM 城市会参会率</div>
		<div class="cityConferenceAttendanceRate-card">
			<div class="cityConferenceAttendanceRate-card-legend">
				<div class="cityConferenceAttendanceRate-card-legend-item" v-for="(item, index) in type" :key="index">
					<div class="dot" :style="{ '--customer-color': color[index] }"></div>
					<div>{{ item }}</div>
				</div>
			</div>
			<div class="cityConferenceAttendanceRate-card-total"><span>nameAA团队</span>&emsp; <span>线上 2</span>&emsp; <span>线下 0</span>&emsp;</div>
			<div class="cityConferenceAttendanceRate-card-bar" v-for="(item, index) in data" :key="index">
				<div class="cityConferenceAttendanceRate-card-bar-name">{{ item.name }}</div>
				<div class="barr">
					<div class="cityConferenceAttendanceRate-card-bar-offline" :style="{ flex: item.Offline }">
						<div class="num">{{ item.Offline }}</div>
					</div>
					<div class="cityConferenceAttendanceRate-card-bar-online" :style="{ flex: item.online }">
						<div class="num">{{ item.online }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
let color = ref(['rgb(196, 118, 58)', 'rgb(225, 186, 156)']);
let type = ref(['线下', '线上']);
let data = ref([
	{
		name: 'nameAAAA',
		online: 2,
		Offline: 0,
	},
	{
		name: 'nameAAAA',
		online: 2,
		Offline: 0,
	},
]);
</script>
<style lang="scss" scoped>
.cityConferenceAttendanceRate {
	&-title {
		background: linear-gradient(90deg, #2a3f67, #4b6aa0);
		border-radius: 3px;
		font-size: 12px;
		margin-bottom: 4px;
		padding: 2px 10px;
	}
	&-card {
		background-color: #fff;
		border-radius: 3px;
		padding: 0 15px 12px;
		margin-bottom: 4px;
		&-legend {
			display: flex;
			color: #000;
			font-size: 12px;
			padding: 8px 0px;
			&-item {
				display: flex;
				align-items: center;
				margin-right: 8px;
				.dot {
					width: 8px;
					height: 8px;
					background-color: var(--customer-color);
					margin-right: 5px;
					border-radius: 2px;
				}
			}
		}
		&-total {
			background: #c4763a;
			color: #fff;
			display: flex;
			justify-content: center;
			text-align: center;
			font-size: 13px;
			padding: 2px;
			margin-bottom: 20px;
		}

		&-bar {
			display: flex;
			align-items: center;
			width: 100%;
			color: #000;
			font-size: 11px;
			border-bottom: 1px solid #eee;
			padding: 30px 0;
			text-align: center;
			&:nth-of-type(3) {
				border-top: 1px solid #eee;
			}
			&-name {
				margin-right: 5px;
			}
			.barr {
				flex: 1;
				background-color: red;
				height: 15px;
				display: flex;
			}
			&-offline {
				background-color: rgb(196, 118, 58);
				position: relative;
			}
			&-online {
				background-color: rgb(225, 186, 156);
				position: relative;
			}
			.num {
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				z-index: 1;
			}
		}
	}
}
</style>
