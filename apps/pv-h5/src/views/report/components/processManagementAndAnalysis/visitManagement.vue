<template>
	<div class="visitManagement">
		<div class="visitManagement-title">拜访管理及表现</div>
		<div class="visitManagement-card">
			<div class="visitManagement-card-legend">
				<div class="visitManagement-card-legend-item" v-for="(item, index) in type" :key="index">
					<div class="dot" :style="{ '--customer-color': color[index] }"></div>
					<div>{{ item.text }}</div>
				</div>
			</div>
			<div class="visitManagement-card-total">
				<div class="visitManagement-card-total-item" v-for="(item, index) in type.filter((ele) => ele.count)" :key="index" :style="{ backgroundColor: color[index] }">
					<div>{{ item.text }}</div>
					<div>56%</div>
				</div>
			</div>
			<div class="visitManagement-card-echarts"></div>
		</div>
	</div>
</template>
<script setup>
import { visitManagement } from '@/api/report';
import { isPCTrue } from '@/utils/index';

import * as echarts from 'echarts';
let myEcharts = echarts;
let chart;
let color = ref(['rgb(85, 101, 133)', 'rgb(75, 107, 161)', 'rgb(118, 177, 216)', 'rgb(74, 50, 118)', 'rgb(219, 174, 84)']);
let type = ref([{ text: 'A客户拜访频次', count: 3.6 }, { text: 'B客户拜访频次', count: 2.6 }, { text: 'C客户拜访频次' }, { text: 'AB客户覆盖率', count: 56 }, { text: '拜访计划执行率' }]);

let initChart = () => {
	chart = myEcharts.init(document.querySelector('.visitManagement-card-echarts'), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let option = {
		// color: color.value,
		grid: [
			{ top: '8%', left: '5%', right: '5%', bottom: '70%', show: true, borderColor: '#eee' },
			{ top: '35%', left: '5%', right: '5%', bottom: '8%' },
		],
		xAxis: [
			{
				type: 'category',
				data: ['A', 'B', 'C', 'D', 'E', 'a'],
				gridIndex: 0,
				axisLabel: { show: false },
				axisTick: { show: false },
				splitLine: {
					show: true,
					interval: 1,
					lineStyle: { color: '#eee' },
				},
				axisLine: { lineStyle: { color: '#eee' } },
			},
			{ type: 'category', data: ['F', 'G', 'H', 'I', 'J', 'a'], gridIndex: 1, axisLabel: { show: true, color: '#000' }, axisTick: { show: false }, splitLine: { show: true, lineStyle: { color: '#eee' }, interval: 1 }, axisLine: { lineStyle: { color: '#eee' } } },
		],
		yAxis: [
			{
				type: 'value',
				gridIndex: 0,
				axisLabel: { show: false },
				axisTick: { show: false },
				splitLine: {
					show: false,
				},
			},
			{ type: 'value', gridIndex: 1, axisLabel: { show: false }, axisTick: { show: false }, splitLine: { show: false } },
		],
		series: [
			{
				type: 'line',
				lineStyle: {
					width: 1,
				},
				data: [10, 20, 30, 40, 50, 1],
				xAxisIndex: 0,
				yAxisIndex: 0,
				label: { show: true, distance: -1 },
			},
			{
				type: 'line',
				lineStyle: {
					width: 1,
				},
				data: [30, 21, 0, 40, 0, 1],
				xAxisIndex: 0,
				yAxisIndex: 0,
				label: { show: true, distance: -1 },
			},
			{
				type: 'bar',
				data: [15, 25, 35, 45, 55, 1],
				xAxisIndex: 1,
				yAxisIndex: 1,
			},
			{
				type: 'bar',
				data: [15, 25, 35, 45, 55, 1],
				xAxisIndex: 1,
				yAxisIndex: 1,
			},
			{
				type: 'bar',
				data: [15, 25, 35, 45, 55, 1],
				xAxisIndex: 1,
				yAxisIndex: 1,
			},
		],
	};
	chart.setOption(option);
};

let getData = async () => {
	let res = await visitManagement({ territoryCode: 'ONMR631' });
	console.log(res.result);
};

onMounted(async () => {
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.visitManagement {
	&-title {
		background: linear-gradient(90deg, #2a3f67, #4b6aa0);
		border-radius: 3px;
		font-size: 12px;
		margin-bottom: 4px;
		padding: 2px 10px;
	}
	&-card {
		background-color: #fff;
		border-radius: 3px;
		padding: 0 15px 12px;
		margin-bottom: 4px;
		&-legend {
			display: flex;
			flex-wrap: wrap;
			color: #000;
			font-size: 12px;
			padding: 8px 0px;
			&-item {
				display: flex;
				align-items: center;
				margin-right: 8px;
				margin-bottom: 4px;
				.dot {
					width: 8px;
					height: 8px;
					background-color: var(--customer-color);
					margin-right: 5px;
					border-radius: 2px;
				}
			}
		}
		&-total {
			font-size: 11px;
			display: flex;
			&-item {
				padding: 2px 8px;
				flex: 1;
				margin-right: 6px;
				div:nth-child(2) {
					text-align: center;
				}
			}
		}
		&-echarts {
			width: 342px;
			height: 235px;
		}
	}
}
</style>
