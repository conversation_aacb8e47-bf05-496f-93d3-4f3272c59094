<template>
	<div>
		<branchSalesOverview2 v-if="info" :info="info" :pathAddress="props.pathAddress" :isPPT="true"></branchSalesOverview2>
	</div>
</template>
<script setup>
import branchSalesOverview2 from '@/views/observe/compoents/branchSalesOverview2.vue';
import { queryList } from '@/api/sales';
const props = defineProps(['dinfo', 'pathAddress']);
let info = ref(null);
onMounted(async () => {
	let res = await queryList({ reportType: 'CARD' });
	let json = res.result?.filter((ele) => ele.reportCd === 'branchSalesOverview2')?.[0];
	json.startTime = props.dinfo.startTime;
	json.endTime = props.dinfo.endTime;
	json.filterConfig = props.dinfo.filterConfig;
	info.value = json;
});
</script>

<style lang="scss" scoped>
::v-deep(.branch-sales-overview) {
	margin: 10px 0 0 0;
	.card-title {
		& > span:last-child {
			display: none;
		}
	}
}
</style>
