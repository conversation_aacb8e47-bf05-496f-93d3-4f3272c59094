<template>
	<div :class="{ 'ignore-sale-table': isPPT }">
		<vxe-table v-if="show" max-height="400" :data="props.tableData" :sort-config="{ trigger: 'cell' }" :scroll-y="{ enabled: true, gt: 0, oSize: 100 }">
			<vxe-column
				v-for="(item, index) in props.columns"
				:key="index"
				:field="item.field"
				:width="isPPT ? (item.title === 'Metric' ? '60px' : item.title === '全年' ? '63px' : '') : item.title === 'Metric' ? '110px' : item.title === '全年' ? '160' : 90"
				:title="item.title"
				:min-width="isPPT ? '' : item.width"
				:align="item.field === 'metric' ? 'left' : 'right'"
				:fixed="item.field === 'metric' ? 'left' : ''"
			>
				<!-- 自定义列头 -->
				<template #header="{ column }">
					<div v-if="column.field === 'metric'" class="custom-header">
						<span class="left-label">指标</span>
						<span class="right-label">日期</span>
					</div>
					<div v-else>
						<span>{{ column.title }}</span>
					</div>
				</template>
			</vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
const props = defineProps(['tableData', 'columns', 'isPPT']);
let show = ref(false);
watch(
	() => props.columns,
	(n) => {
		if (n) {
			show.value = true;
		}
	}
);
</script>
<style lang="scss" scoped>
.ignore-sale-table {
	::v-deep(.vxe-table) {
		.vxe-body--column {
			.vxe-cell {
				padding-top: 5px !important;
				padding-bottom: 5px !important;
			}
		}
		.vxe-footer--column {
			.vxe-cell {
				padding: 5px !important;
			}
		}
		.vxe-cell {
			.vxe-cell--item {
				font-size: 7px !important;
			}
			.vxe-cell--label {
				font-size: 7px !important;
			}
		}
	}
	.custom-header::before {
		content: '';
		position: absolute;
		top: 0;
		left: -45px;
		width: 90px;
		height: 1px;
		background: #e8eaec;
		transform: rotate(30deg);
	}
	.custom-header .left-label {
		position: absolute;
		bottom: -13px;
		left: -18px;
		font-size: 8px;
	}

	.custom-header .right-label {
		position: absolute;
		top: -13px;
		right: -18px;
		font-size: 8px;
	}
}
.custom-header {
	position: relative;
	width: 100%;
	height: 100%;
}
.custom-header::before {
	content: '';
	position: absolute;
	top: 0;
	left: -54px;
	width: 100px;
	height: 1px;
	background: #e8eaec;
	transform: rotate(13deg);
}
.custom-header .left-label {
	position: absolute;
	bottom: -10px;
	left: -30px;
	font-size: 9px;
}

.custom-header .right-label {
	position: absolute;
	top: -10px;
	right: -30px;
	font-size: 9px;
}
</style>
