<template>
	<div class="ignore-sale">
		<div class="card-title">
			<span>{{ title }}</span>
		</div>
		<div class="content">
			<div class="nox">
				<div class="marek-share-legend">
					<div class="marek-share-legend-item" v-for="(item, index) in legend" :key="item" :style="{ '--bar-color': color[index % color.length] }">
						<span class="bar"></span>
						<span>{{ item.name }}</span>
					</div>
				</div>

				<div class="marek-share-echarts" :id="`marek-share-echarts-${echartsId}`"></div>
			</div>

			<div class="marek-share-table">
				<salesTrendProductTable :tableData="tableData" :lastResult="lastResult" :columns="columns" :isPPT="true"></salesTrendProductTable>
			</div>
		</div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { generateRandomNumber, formatNumbeW, isPCTrue } from '@/utils/index';
import salesTrendProductTable from '@/views/observe/compoents/salesTrendProductTable.vue';

let props = defineProps(['info', 'title']);
let myEcharts = echarts;
let chart;
let echartsId = generateRandomNumber();

const tableData = ref([]);
const columns = ref([]);
let color = ['#FFD400', '#9EF278', '#757575', '#FF5757', '#A1DCFF'];

const legend = ref([]);
const result = ref(null);
const lineList = ref([]);
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#marek-share-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let option = {
		legend: {
			show: false,
		},
		color,
		grid: {
			left: 10,
			right: 20,
			bottom: 10,
			top: 15,
			containLabel: true,
		},
		xAxis: {
			type: 'category',
			show: true,
			data: result.value.axisMonth,
			axisLabel: {
				show: true, // 是否显示坐标轴刻度标签。
				// interval: 2, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
				inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
				color: ' #6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
				fontSize: 10,
			},
			axisLine: {
				show: true, // 是否显示坐标轴轴线
				lineStyle: {
					color: '#fff',
					width: 1,
					type: 'solid',
				},
			},
			axisTick: {
				show: true,
			},
		},
		yAxis: {
			type: 'value',
			axisLabel: {
				show: true, // 是否显示坐标轴刻度标签。
				interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
				inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
				color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
				fontSize: 10,
				formatter: function (params) {
					return `${formatNumbeW(params)}`;
				},
			},
			splitLine: {
				show: true, // 隐藏 Y 轴横线
				lineStyle: {
					color: 'rgba(223,225,230,.5)',
				},
			},
			splitNumber: 4,
		},
		series: lineList.value,
	};
	chart.setOption(option, true);
};
const lastResult = ref({});

watch(
	() => props.info,
	(res) => {
		console.log(res);
		result.value = res.result;
		lineList.value = res.lineList.slice(0, 4);
		columns.value = res.columns;
		legend.value = res.legend.slice(0, 4);
		tableData.value = res.tableData.slice(0, 4);
		lastResult.value = res.lastResult;
		initChart();
	},
	{ deep: true }
);
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.ignore-sale {
	font-size: 10px;
	width: 100%;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 4px 4px;
	.card-title {
		width: 100%;
		border-radius: 4px 4px 0 0;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: #283b5c;
		height: 23px;
		span {
			font-size: 10px;
			color: #fff;
		}
	}
	.content {
		height: calc(100% - 23px);
		padding: 0 10px;
		.marek-share-legend {
			display: flex;
			justify-content: center;
			flex-wrap: wrap;
			padding: 4px 8px;
			max-height: 65px;
			overflow-y: auto;
			&-item {
				font-size: 9px;
				color: var(--pv-no-active-color);
				margin-right: 20px;
				margin-top: 4px;
				display: flex;
				align-items: center;
				.bar {
					height: 1.1px;
					width: 16px;
					background-color: var(--bar-color);
					margin-right: 8px;
					position: relative;
					&::before {
						position: absolute;
						content: '';
						width: 4px;
						height: 4px;
						border-radius: 50%;
						border: 1px solid var(--bar-color);
						background-color: #fff;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
					}
				}
			}
		}
		.nox {
			height: 50%;
			.marek-share-echarts {
				height: calc(100% - 29px);
				width: 100%;
			}
		}

		.marek-share-table {
			height: 50%;
			width: 100%;
		}
	}
}
</style>
