<template>
	<div>
		<salesTrendProduct v-if="info" :info="info" :pathAddress="props.pathAddress" :isPPT="true"></salesTrendProduct>
	</div>
</template>
<script setup>
import salesTrendProduct from '@/views/observe/compoents/salesTrendProduct.vue';
import { queryList } from '@/api/sales';
const props = defineProps(['pathAddress']);
let info = ref(null);
onMounted(async () => {
	let res = await queryList({ reportType: 'CARD' });
	info.value = res.result?.filter((ele) => ele.reportCd === 'salesTrendProduct')?.[0];
});
</script>

<style lang="scss" scoped>
::v-deep(.marek-share) {
	margin: 10px 0 10px 0;
	.card-title {
		& > span:last-child {
			display: none;
		}
	}
}
</style>
