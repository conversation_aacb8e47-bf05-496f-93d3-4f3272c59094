<template>
	<div class="ignore-his">
		<!-- 内容表格 -->
		<div :class="{ 'ignore-his-table': isPPT, 'his-table': !isPPT }">
			<!-- 表格 header -->
			<div class="his-table__header">
				<div class="his-table__th width_7">
					<div>排名</div>
				</div>
				<div class="his-table__th width_24">医院名称</div>
				<div class="his-table__th width_10">
					<div>销售金额</div>
				</div>
				<div class="his-table__th width_10">销售贡献</div>
				<div class="his-table__th width_10">达成率</div>
				<div class="his-table__th width_10">
					<div>同比增长</div>
				</div>
				<div class="his-table__th width_10">
					<div>市场份额</div>
				</div>
				<div class="his-table__th width_10">
					<div>进院状态</div>
				</div>
				<div class="his-table__th width_10">
					<div>EI</div>
				</div>
			</div>
			<div class="his-table__tbody">
				<div class="his-table__tr" v-for="item in rankList" :key="item.ranking">
					<div class="his-table__td width_7">
						{{ item.ranking }}
					</div>
					<div class="his-table__td width_24 ovflow">{{ item.hospName }}</div>
					<div class="his-table__td width_10 ovflow">{{ item.salesV }}</div>
					<div class="his-table__td width_10">{{ item.salesPercentage }}</div>
					<div class="his-table__td width_10" :class="{ active: item.achActive }">{{ item.ach }}</div>
					<div class="his-table__td width_10" :class="{ active: item.growthActive }">{{ item.growth }}</div>
					<div class="his-table__td width_10">{{ item.ms }}</div>
					<div class="his-table__td width_10 ovflow">{{ item.admissionStatus }}</div>
					<div class="his-table__td width_10">{{ item.ei }}</div>
				</div>
			</div>
			<div class="his-table__footer">
				<div class="his-table__td width_31 ovflow">Top20 HCO Sub Total</div>
				<div class="his-table__td width_10 ovflow">{{ formatNumberTH(top20Total.sales) }}</div>
				<div class="his-table__td width_10 ovflow">{{ new Decimal(top20Total.salesContribution).toDecimalPlaces(1) + '%' }}</div>
				<div class="his-table__td width_10 ovflow">{{ new Decimal(top20Total.ach).toDecimalPlaces(1) + '%' }}</div>
				<div class="his-table__td width_10 ovflow">{{ new Decimal(top20Total.growth).toDecimalPlaces(1) + '%' }}</div>
				<div class="his-table__td width_10 ovflow">{{ new Decimal(top20Total.ms).toDecimalPlaces(1) + '%' }}</div>
				<div class="his-table__td width_20 ovflow"></div>
			</div>
			<div class="his-table__footer">
				<div class="his-table__td width_31 ovflow">HCO Total</div>
				<div class="his-table__td width_10 ovflow">{{ formatNumberTH(total.salesV) }}</div>
				<div class="his-table__td width_10 ovflow">{{ new Decimal(total.salesContribution).toDecimalPlaces(1) + '%' }}</div>
				<div class="his-table__td width_10 ovflow">{{ new Decimal(total.ach).toDecimalPlaces(1) + '%' }}</div>
				<div class="his-table__td width_10 ovflow">{{ new Decimal(total.growth).toDecimalPlaces(1) + '%' }}</div>
				<div class="his-table__td width_10 ovflow">{{ new Decimal(total.ms).toDecimalPlaces(1) + '%' }}</div>
				<div class="his-table__td width_20 ovflow"></div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { formatNumberTH, deepClone } from '@/utils/index';
import Decimal from 'decimal.js';

const top20Total = ref({
	sales: 0,
	ach: 0,
	salesContribution: 0,
	growth: 0,
	ms: 0,
});
const total = ref({
	salesV: 0,
	ach: 0,
	salesContribution: 0,
	growth: 0,
	ms: 0,
});
const rankList = ref([]);
let props = defineProps(['isPPT', 'info']);
onUnmounted(() => {});
watch(
	() => props.info,
	(n) => {
		if (n.info.data) {
			top20Total.value = n.info.data.top20Total;
			total.value = n.info.data.total;
			rankList.value = n.info.data.rankList;
			getTopThreeAchValues(rankList.value);
		}
	}
);
function getTopThreeAchValues(array) {
	let arr = deepClone(array);
	// 首先对数组进行排序，按照 ach 值从大到小排序
	arr.sort((a, b) => Number(b.ach.slice(0, -1)) - Number(a.ach.slice(0, -1)));
	let achMax = arr.map((ele) => ele.ach).slice(0, 3);
	arr.sort((a, b) => Number(b.growth.slice(0, -1)) - Number(a.growth.slice(0, -1)));
	let growthMax = arr.map((ele) => ele.growth).slice(0, 3);
	array.forEach((ele) => {
		if (achMax.includes(ele.ach)) {
			ele.achActive = true;
		}
		if (growthMax.includes(ele.growth)) {
			ele.growthActive = true;
		}
	});
}
</script>
<style lang="scss" scoped>
.ignore-his {
	transform: scale(0.5);
	transform-origin: center top;
	width: 200%;
	position: absolute;
	left: -395px;
	top: 0px;
	.his-table,
	.ignore-his-table {
		width: 100%;
		background-color: #ffffff;
		padding: 0px 0px;
		border-radius: 4px;
		display: flex;
		flex-direction: column;

		.width_7 {
			width: 7%;
		}

		.width_10 {
			width: 10%;
		}

		.width_20 {
			width: 20%;
		}

		.width_24 {
			width: 24%;
		}

		.width_31 {
			width: 31%;
		}

		.width_29 {
			width: 30%;
		}

		.ovflow {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.text-left {
			text-align: left;
		}

		.his-table__title {
			font-size: 20px;
			color: #333333;
			padding-bottom: 6px;
		}

		.his-table__header {
			height: 46px;
			border-radius: 8px 8px 0px 0px;
			background-color: #283b5c;
			font-size: 20px;
			display: flex;
			.his-table__th {
				color: #fff;
				font-weight: bolder;
				padding: 14px 0;
				word-wrap: break-word;
				position: relative;
				text-align: center;
				word-break: break-all;
				line-height: 1.2;
			}
		}

		.his-table__tbody {
			.his-table__tr {
				align-items: center;
				border-bottom: 2px solid #e0e0e0;
				display: flex;
				padding: 0;
				& .his-table__td:first-child {
					border-left: 0;
				}

				.his-table__td {
					border-left: 2px solid #e0e0e0;
					color: #000000;
					font-size: 18px;
					height: 33.2px;
					line-height: 33.2px;
					font-style: normal;
					text-align: center;
					word-wrap: break-word;
					position: relative;
					text-align: center;
					word-break: break-all;
					img {
						width: 20px;
					}
				}
			}
		}

		.his-table__footer {
			align-items: center;
			background: #b7c4d9;
			border-bottom: 2px solid #e0e0e0;
			display: flex;
			padding: 0;
			& .his-table__td:first-child {
				border-left: 0;
			}
			.his-table__td {
				border-left: 1px solid #e0e0e0;
				color: #ffffff;
				background-color: #718097;
				font-size: 18px;
				height: 34px;
				line-height: 34px;
				font-style: normal;
				text-align: center;
				word-wrap: break-word;
				position: relative;
				text-align: center;
				word-break: break-all;
			}
		}
	}
	.active {
		background-color: rgb(122, 159, 102);
		color: #fff !important;
	}
}
</style>
