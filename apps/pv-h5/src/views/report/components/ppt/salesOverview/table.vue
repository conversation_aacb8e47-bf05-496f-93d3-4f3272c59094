<template>
	<div class="ignore-top">
		<div class="card-title">
			<span>{{ title }}</span>
		</div>
		<div class="content">
			<div class="sales-table">
				<div class="sales-table-header">
					<div class="sales-table-header-left">
						<span>时间</span>
						<span>指标</span>
					</div>
					<div class="sales-table-header-right">
						<div class="sales-table-header-right-row row-first">
							<div class="sales-table-header-right-row-item">
								<div v-if="defaultInfo" class="first">销售额{{ defaultInfo.unit }}</div>
							</div>
							<div class="sales-table-header-right-row-item">
								<div class="first">达成率%</div>
							</div>
							<div class="sales-table-header-right-row-item">
								<div class="first">同比增长率%</div>
							</div>
						</div>
						<div class="sales-table-header-right-row row-second">
							<div class="sales-table-header-right-row-item">
								<div class="sales-table-header-right-row-item-son">KEPPRA</div>
								<div class="sales-table-header-right-row-item-son">VIMPAT</div>
							</div>
							<div class="sales-table-header-right-row-item">
								<div class="sales-table-header-right-row-item-son">KEPPRA</div>
								<div class="sales-table-header-right-row-item-son">VIMPAT</div>
							</div>
							<div class="sales-table-header-right-row-item">
								<div class="sales-table-header-right-row-item-son">KEPPRA</div>
								<div class="sales-table-header-right-row-item-son">VIMPAT</div>
							</div>
						</div>
					</div>
				</div>
				<div class="sales-table-body" v-if="info">
					<div class="sales-table-body-tr" v-for="item in info" :key="item.date">
						<div class="sales-table-body-left">{{ item.date }}</div>
						<div class="sales-table-body-right">
							<div class="sales-table-body-right-row">
								<div class="sales-table-body-right-row-item" v-for="value in item.list" :key="value">
									<div class="sales-table-body-right-row-item-son" v-for="i in value" :key="i">{{ i }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
const props = defineProps(['info', 'defaultInfo', 'title']);
console.log(props, '///////');
</script>
<style lang="scss" scoped>
.ignore-top {
	font-size: 10px;
	width: 100%;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 4px 4px;
	.card-title {
		width: 100%;
		border-radius: 4px 4px 0 0;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: #283b5c;
		height: 23px;
		span {
			font-size: 10px;
			color: #fff;
		}
	}
	.content {
		height: calc(100% - 46px);
		padding: 12px 16px;
	}
	.sales-table {
		color: #6b778c;
		font-size: 8px;
		border: 1px solid #dfe1e6;
		text-align: center;
		&-header {
			display: flex;
			&-left {
				flex: 1;
				border-right: 1px solid #dfe1e6;
				border-bottom: 1px solid #dfe1e6;
				position: relative;
				background: linear-gradient(to top right, transparent 49.5%, #dfe1e6 50%, transparent 50.5%);
				& span:nth-child(1) {
					position: absolute;
					bottom: 6px;
					left: 6px;
				}
				& span:nth-child(2) {
					position: absolute;
					top: 6px;
					right: 6px;
				}
			}
			&-right {
				flex: 6;
				&-row {
					display: flex;
					&-item {
						flex: 1;
						display: flex;
						.first {
							padding: 4px 0;
							background-color: #e1e3e8;
							flex: 1;
						}
						&-son {
							flex: 1;
							padding: 4px 0;
							background-color: #f4f5f7;
						}
					}
				}
				.row-first {
					.sales-table-header-right-row-item {
						border-right: 1px solid #fff;
					}
					& > div:nth-last-child(1) {
						border: none;
					}
				}
				.row-second {
					.sales-table-header-right-row-item {
						border-right: 1px solid #dfe1e6;
					}
					& > div:nth-last-child(1) {
						border: none;
					}
				}
			}
		}
		&-body {
			&-tr {
				display: flex;
			}
			&-left {
				flex: 1;
				border-right: 1px solid #dfe1e6;
				padding: 4px 0;
				background-color: #e1e3e8;
			}
			&-right {
				flex: 6;
				&-row {
					display: flex;
					&-item {
						flex: 1;
						display: flex;
						&-son {
							flex: 1;
							padding: 4px 0;
							border-top: 1px solid #dfe1e6;
							border-right: 1px solid #dfe1e6;
						}
					}
					& > div:nth-last-child(1) {
						.sales-table-body-right-row-item-son:nth-last-child(1) {
							border-right: none;
						}
					}
				}
			}
		}
	}
}
</style>
