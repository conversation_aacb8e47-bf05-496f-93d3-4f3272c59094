<template>
	<div class="ignore-sale">
		<div class="card-title">
			<span>{{ title }}</span>
		</div>
		<div class="content">
			<div class="sale-trend-echarts" :id="`sale-trend-echarts-${echartsId}`"></div>
		</div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { formatNumber, generateRandomNumber, translateFont, saleTrendFilter, spaces, isPCTrue } from '@/utils/index';
import Decimal from 'decimal.js';
let props = defineProps(['info', 'defaultInfo', 'title']);
let myEcharts = echarts;
let chart;
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#sale-trend-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let x = props.info.map((ele) => ele.date);
	let xs = props.info.map((ele) => ele.sales);
	let zb = props.info.map((ele) => ele.target);
	let dc = props.info.map((ele) => ele.ach);
	let tbzz = props.info.map((ele) => ele.growth);
	let option = {
		grid: {
			top: 40,
			left: 58,
			right: 35,
			bottom: 30,
		},
		legend: {
			data: ['指标额', '销售额', '达成', '同比增长'],
			left: 'center',
			top: 5,
			itemGap: 15,
			itemWidth: 15,
			itemHeight: 4,
			textStyle: {
				color: '#6B778C',
				fontSize: 12,
			},
		},
		xAxis: [
			{
				type: 'category',
				data: x,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: spaces(x.length), // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: 9,
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: 9,
					formatter: (value) => {
						return formatNumber(value, props.defaultInfo.unit, props.defaultInfo.unitValue);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: 9,
					formatter: (value) => {
						return new Decimal(value).toDecimalPlaces(0) + '%';
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
			},
		],
		color: ['#E1EBFF', '#0052CC', '#E95600', '#01A2BF'],
		series: [
			{
				name: '指标额',
				type: 'bar',
				data: zb,
				barWidth: 12,
				z: 2,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '销售额',
				type: 'bar',
				data: xs,
				barWidth: 6,
				barGap: '-74%',
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '达成',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: 1,
				},
				label: {
					show: true,
					position: [-5, -10],
					fontSize: 9,
					color: '#E95600',
					formatter: (v) => {
						return v.value > 0 ? v.value + '%' : '0';
					},
				},
				symbolSize: 4,
				data: dc,
				z: 4,
			},
			{
				name: '同比增长',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: 1,
				},
				symbolSize: 4,
				data: tbzz,
				z: 4,
			},
		],
	};
	chart.setOption(option);
};

let echartsId = generateRandomNumber();

watch(
	() => props.info,
	(n) => {
		nextTick(() => {
			initChart();
		});
	}
);
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.ignore-sale {
	font-size: 10px;
	width: 100%;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 4px 4px;
	.card-title {
		width: 100%;
		border-radius: 4px 4px 0 0;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: #283b5c;
		height: 23px;
		span {
			font-size: 10px;
			color: #fff;
		}
	}
	.content {
		height: calc(100% - 23px);
		display: flex;
		align-items: center;
	}
}
.sale-trend-echarts {
	height: 100%;
	width: 100%;
}
</style>
