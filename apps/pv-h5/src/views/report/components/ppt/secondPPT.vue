<template>
	<div class="ignore-img" ref="ppt">
		<img v-if="realm === 'muqiao'" src="@/assets/img/secondPPT1.png" alt="" />
		<img v-else src="@/assets/img/secondPPT.png" alt="" />
	</div>
</template>
<script setup>
let ppt = ref(null);
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const realm = enterStore.enterInfo.id;
defineExpose({ ppt });
</script>
<style lang="scss" scoped>
.ignore-img {
	width: 960px;
	height: 540px;
	position: relative;
	img {
		width: 100%;
	}
}
</style>
