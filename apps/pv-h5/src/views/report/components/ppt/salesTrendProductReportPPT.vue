<template>
	<div ref="ppt" class="ignore-ppt">
		<div class="ignore-ppt-title">
			<span>{{ filterInfo?.info?.name }}</span>
			<span>
				<img v-if="realm === 'muqiao'" src="@/assets/img/pptlogo1.png" alt="" />
				<img v-else src="@/assets/img/pptlogo.png" alt="" />
				<span>文档编号：{{ pptInfo.no1 }}</span>
				<span></span>
			</span>
		</div>
		<div class="ppt-kuang">
			<img v-if="realm === 'muqiao'" src="@/assets/img/kuang1.png" alt="" />
			<img v-else src="@/assets/img/kuang.png" alt="" />
		</div>
		<div class="ppt-content">
			<reportFilter :filterInfo="filterInfo?.info"></reportFilter>
			<div class="ppt-content-box">
				<div class="ppt-content-right">
					<cardPPT :info="filterInfo?.info?.data" title="各产品销售趋势分析报告"></cardPPT>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import cardPPT from '../salesTrendProductReport/cardPPT.vue';
import reportFilter from './reportFilter.vue';
import useUserStore from '@/store/modules/report';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const realm = enterStore.enterInfo.id;
let reportStore = useUserStore();
let filterInfo = ref(null);
watch(
	() => reportStore.reportInfo,
	(n) => {
		filterInfo.value = reportStore.reportInfo?.find((ele) => ele.name === '各产品销售趋势分析报告');
		console.log(filterInfo.value);
	},
	{ deep: true }
);

defineProps({
	pptInfo: {
		default: function () {
			return { time: '', name: '', no: '' };
		},
	},
});

let ppt = ref(null);
defineExpose({ ppt });
</script>
<style lang="scss" scoped>
.ignore-ppt {
	width: 960px;
	height: 540px;
	padding: 13px 20px;
	.ppt-kuang {
		line-height: 0px;
		margin-bottom: 12px;
		margin-top: 8px;
		img {
			width: 100%;
			height: 100%;
		}
	}
	.ppt-content {
		position: relative;
		height: calc(540px - 46px - 36px - 16px - 16px - 20px);
		&-box {
			position: absolute;
			top: 0;
			left: 130px;
			width: 790px;
			height: 100%;
		}
		&-right {
			flex: 1;
			display: flex;
			height: 100%;
		}
	}
	img {
		width: 100%;
	}
}
</style>
