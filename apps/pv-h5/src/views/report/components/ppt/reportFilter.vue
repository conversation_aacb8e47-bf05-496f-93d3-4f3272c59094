<template>
	<div>
		<div class="ignore-ppt-content-left">
			<div class="ignore-ppt-content-left-filter">
				<div class="ignore-ppt-content-left-filter-title">报告筛选器</div>
				<div class="ignore-ppt-content-left-filter-content">
					<div v-if="filterInfo && filterInfo.date">
						<div class="title">年月：</div>
						<div>
							{{ filterInfo.date.startLocalDate.slice(2) }}<span v-if="filterInfo.date.endLocalDate">-{{ filterInfo.date.endLocalDate.slice(2) }}</span>
						</div>
					</div>
					<div v-if="filterInfo && filterInfo.monthText">
						<div class="title">年月：</div>
						<div>{{ filterInfo.monthText }}</div>
					</div>
					<div v-if="filterInfo && filterInfo.people">
						<div class="title">人员：</div>
						<div class="ell2">{{ filterInfo.people }}</div>
					</div>
					<div v-if="filterInfo && filterInfo.hospital">
						<div class="title">终端：</div>
						<div class="ell2">{{ filterInfo.hospital === '全部医院' ? '全部终端' : filterInfo.hospital }}</div>
					</div>
					<!-- <div>
						<div>价格类型：</div>
						<div>RMB</div>
					</div> -->
					<div v-if="filterInfo && filterInfo.marketName">
						<div class="title">市场：</div>
						<div>{{ filterInfo.marketName }}</div>
					</div>
					<div v-if="filterInfo && filterInfo.product">
						<div class="title">产品：</div>
						<div>{{ filterInfo.product }}</div>
					</div>
					<div v-if="filterInfo && filterInfo.province">
						<div class="title">省份：</div>
						<div>{{ filterInfo.province }}</div>
					</div>
				</div>
			</div>
			<div class="ignore-comment">
				<img v-if="realm !== 'muqiao'" style="width: 15px" src="@/assets/img/logo1.png" alt="" />
				<div v-if="filterInfo?.data?.message" v-html="filterInfo.data.message"></div>
				<!-- <div class="comment-title">TOP医院总体销售金额:</div>
				<div class="comment-text">TOP医院总体销售额为<span class="highlight">5045K</span>，占医院贡献<span class="highlight">86%</span>；达成<span class="highlight">91%</span>，同比增长<span class="highlight">66.6%</span></div>
				<div class="comment-title">销售贡献排名前三的医院:</div>
				<div class="comment-text">
					<div>1、中国人民解放军： 销售金额<span class="highlight">930K</span>，达成<span class="highlight">0%</span>，同比增长<span class="highlight">-44%</span>。市场份额<span class="highlight">12%</span></div>
					<div>2、中国人民解放军： 销售金额<span class="highlight">930K</span>，达成<span class="highlight">0%</span>，同比增长<span class="highlight">-44%</span>。市场份额<span class="highlight">12%</span></div>
					<div>3、中国人民解放军： 销售金额<span class="highlight">930K</span>，达成<span class="highlight">0%</span>，同比增长<span class="highlight">-44%</span>。市场份额<span class="highlight">12%</span></div>
				</div>
				<div class="comment-bottom">
					<div>未达成指标的医院：<span class="highlight">7</span>家</div>
					<div>同比负增长的医院：<span class="highlight">7</span>家</div>
					<div>EI&lt;100的医院：<span class="highlight">7</span>家</div>
				</div> -->

				<!-- 饼图 -->
				<!-- <div class="comment-title">市场份额占比:</div>
				<div class="comment-text">
					目前XX市场在XX领域共有<span class="highlight">XX</span>个竞争产品，目前排名第一的产品是<span class="highlight">XX</span>，市场份额是<span class="highlight">XX</span>。<span class="highlight">XX</span>产品排名第<span class="highlight">XX</span>，市场份额是<span class="highlight">XX</span>。
				</div> -->

				<!-- 折线图 -->
				<!-- <div class="comment-title">市场份额趋势:</div>
				<div class="comment-text">安加维份额占比排名第一，份额为<span class="highlight">20%</span>，对比上月份额增长<span class="highlight">20%</span>；百汇泽份额占比排名第二，份额为<span class="highlight">20%</span>，对比上月降低<span class="highlight">20%</span>。</div> -->

				<!-- 人员 -->
				<!-- <div class="comment-title">目前架构在岗人数:</div>
				<div class="comment-text">
					<div class="item">
						<span>RSM</span>
						<span class="highlight">15位</span>
					</div>
					<div class="item">
						<span>DSM</span>
						<span class="highlight">15位</span>
					</div>
					<div class="item">
						<span>MR</span>
						<span class="highlight">15位</span>
					</div>
				</div>
				<div class="comment-title">空岗人数:</div>
				<div class="comment-text"><span class="highlight">15位</span>，其中<span class="highlight">西二区、华南区</span>空岗率最高，建议加速招聘。</div>
				<div class="comment-title">XPRT MR月均生产力:</div>
				<div class="comment-text">
					940,236K</span>，其中<span style="color: #72c35d">西一区</span>、<span style="color: #72c35d">东一区</span>空高于BU MR月均生产力；<span style="color: #fc0000">西二区</span>、<span style="color: #fc0000">华南区</span>低于BU MR月均生产力。
				</div> -->
				<!-- 拜访汇总 -->
				<!-- <div class="comment-title">拜访汇总情况</div>
				<div class="comment-text">A级客户拜访<span class="highlight">52</span>人，覆盖率<span class="highlight">52%</span>，平均每位医生拜访<span class="highlight">1.8</span>次。</div>
				<div class="comment-text">B级客户拜访<span class="highlight">52</span>人，覆盖率<span class="highlight">52%</span>，平均每位医生拜访<span class="highlight">1.8</span>次。</div>
				<div class="comment-text">C级客户拜访<span class="highlight">52</span>人，覆盖率<span class="highlight">52%</span>，平均每位医生拜访<span class="highlight">1.8</span>次。</div>
				<div class="comment-text">AB医生覆盖率<span class="highlight">66%</span>；</div> -->
				<!-- 拜访管理及表现报告 -->
				<!-- <div class="comment-title">拜访管理及表现：</div>
				<div class="comment-text">AB客户覆盖率与上月相比增长/降低了<span class="highlight">20%</span>，拜访执行率比上月有所提升/下降，达到<span class="highlight">85%</span>；</div>
				<div class="comment-text">截止2024年3月25日（本月拜访数据的max日期），本月时间进度<span class="highlight">82%</span>，AB覆盖率<span class="highlight">32%</span>，拜访执行率<span class="highlight">65%</span>；</div> -->
			</div>
			<div class="ignore-ppt-content-left-footer">
				<div>备注：{{ sourceTeam }}</div>
				<!-- <div>更新时间:Invalid date</div> -->
			</div>
		</div>
	</div>
</template>
<script setup>
import useEnterStore from '@/store/modules/enterprise';
import usePremissionStore from '@/store/modules/premission';
const enterStore = useEnterStore();
const perStore = usePremissionStore();
const realm = enterStore.enterInfo.id;
const route = useRoute();
let props = defineProps(['filterInfo']);
const sourceTeam = ref('');
console.log(route.query.label);
if (route.query.path === 'myReport') {
	for (const ele of perStore.reportList) {
		if (ele.path === route.query.label) {
			sourceTeam.value = ele.name.indexOf('团队') > -1 ? ele.name + '报告' : ele.name + '团队报告';
			break;
		}
	}
} else {
	for (const ele of perStore.reportList) {
		if (ele.path === route.query.path) {
			sourceTeam.value = ele.name.indexOf('团队') > -1 ? ele.name + '报告' : ele.name + '团队报告';
			break;
		}
	}
}
</script>
<style lang="scss" scoped>
.ignore-ppt-content-left {
	transform: scale(0.5) translateX(-50%) translateY(-50%);
	width: 244px;
	height: 812px;
	margin-right: 16px;
	border-radius: 0px 0px 8px 8px;
	background-color: #ffffff;
	border: 2px solid rgba(0, 0, 0, 0.1);
	position: relative;
	display: flex;
	flex-direction: column;
	&-filter {
		font-size: 16px;
		color: #000;
		background-color: #d8e2f2;
		&-title {
			background-color: #283c63;
			color: #fff;
			padding: 6px 16px 4px;
			border-radius: 8px 8px 0 0;
		}
		&-content {
			padding: 10px 16px;
			min-height: 170px;
			& > div {
				margin-bottom: 1px;
				& div:nth-of-type(1) {
					font-size: 16px;
					color: #333333;
				}
				& div:nth-of-type(2) {
					font-size: 16px;
					color: #172b4d;
					font-weight: bold;
					margin-bottom: 10px;
				}
			}
		}
	}
	&-footer {
		position: absolute;
		padding: 18px 16px 8px 0;
		font-size: 14px;
		text-align: center;
		width: 100%;
		color: #999;
		bottom: 0;
		right: 0;
	}
}
</style>
