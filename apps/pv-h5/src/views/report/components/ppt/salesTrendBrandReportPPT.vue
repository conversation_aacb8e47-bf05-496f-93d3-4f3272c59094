<template>
	<div ref="ppt" class="ignore-ppt">
		<div class="ignore-ppt-title">
			<span>{{ filterInfo?.info?.name }}</span>
			<span class="ignore-ppt-title-right">
				<img v-if="realm === 'muqiao'" src="@/assets/img/pptlogo1.png" alt="" />
				<img v-else src="@/assets/img/pptlogo.png" alt="" />
				<span>
					<span class="ignore-ppt-title-right-text ignore-ppt-title-right-dw">
						<span>创建</span>
						<span>日期：</span>
					</span>
					{{ pptInfo.time }}
				</span>
				<span>
					<span class="ignore-ppt-title-right-text ignore-ppt-title-right-dw">
						<span>单</span>
						<span>位：</span>
					</span>
					RMB K
				</span>
				<span>
					<span class="ignore-ppt-title-right-text ignore-ppt-title-right-dw">
						<span>文档</span>
						<span>编号：</span>
					</span>
					{{ pptInfo.no1 }}
				</span>
			</span>
		</div>
		<div class="ppt-kuang">
			<img v-if="realm === 'muqiao'" src="@/assets/img/kuang1.png" alt="" />
			<img v-else src="@/assets/img/kuang.png" alt="" />
		</div>
		<div class="ppt-content">
			<reportFilter :filterInfo="filterInfo?.info"></reportFilter>
			<div class="ppt-content-box">
				<div class="ppt-content-right">
					<cardPPT :info="filterInfo?.info?.data" title="各品牌销售趋势分析报告"></cardPPT>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import cardPPT from '../salesTrendBrandReport/cardPPT.vue';
import reportFilter from './reportFilter.vue';
import useUserStore from '@/store/modules/report';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const realm = enterStore.enterInfo.id;
let reportStore = useUserStore();
let filterInfo = ref(null);
watch(
	() => reportStore.reportInfo,
	(n) => {
		filterInfo.value = reportStore.reportInfo?.find((ele) => ele.name === '各品牌销售趋势分析报告');
		console.log(filterInfo.value);
	},
	{ deep: true }
);

defineProps({
	pptInfo: {
		default: function () {
			return { time: '', name: '', no: '' };
		},
	},
});

let ppt = ref(null);
defineExpose({ ppt });
</script>
<style lang="scss" scoped>
.ignore-ppt {
	width: 960px;
	height: 540px;
	padding: 13px 20px;
	.ppt-kuang {
		line-height: 0px;
		margin-bottom: 12px;
		margin-top: 8px;
		img {
			width: 100%;
			height: 100%;
		}
	}
	.ppt-content {
		position: relative;
		height: calc(540px - 46px - 36px - 16px - 16px - 20px);
		&-box {
			position: absolute;
			top: 0;
			left: 130px;
			width: 790px;
			height: 406px;
		}
		&-right {
			flex: 1;
			display: flex;
			height: 100%;
		}
	}
	img {
		width: 100%;
	}
}
</style>
