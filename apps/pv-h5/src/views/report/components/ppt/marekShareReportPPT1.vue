<template>
	<div ref="ppt" class="ignore-ppt">
		<div class="ignore-ppt-title">
			<span>{{ filterInfo.info.name }}</span>
			<span>
				<img v-if="realm === 'muqiao'" src="@/assets/img/pptlogo1.png" alt="" />
				<img v-else src="@/assets/img/pptlogo.png" alt="" />
				<span>文档编号：{{ pptInfo.no1 }}</span>
				<span></span>
			</span>
		</div>
		<div class="ppt-kuang">
			<img v-if="realm === 'muqiao'" src="@/assets/img/kuang1.png" alt="" />
			<img v-else src="@/assets/img/kuang.png" alt="" />
		</div>
		<div class="ppt-content">
			<reportFilter :filterInfo="filterInfo.info"></reportFilter>
			<div class="ppt-content-right">
				<competitiveMarketSharePPT :info="filterInfo"></competitiveMarketSharePPT>
			</div>
		</div>
	</div>
</template>
<script setup>
import competitiveMarketSharePPT from '@/views/observe/compoents/competitiveMarketSharePPT.vue';
import reportFilter from './reportFilter.vue';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const realm = enterStore.enterInfo.id;
defineProps({
	pptInfo: {
		default: function () {
			return { time: '', name: '', no: '' };
		},
	},
	filterInfo: {
		default: function () {
			return { name: '', info: {} };
		},
	},
});

let ppt = ref(null);
defineExpose({ ppt });
</script>
<style lang="scss" scoped>
.ignore-ppt {
	width: 960px;
	height: 540px;
	padding: 13px 20px;
	.ppt-kuang {
		line-height: 0px;
		margin-bottom: 12px;
		margin-top: 8px;
		img {
			width: 100%;
			height: 100%;
		}
	}
	.ppt-content {
		display: flex;
		height: calc(540px - 46px - 36px - 16px - 16px - 20px);
		&-right {
			flex: 1;
			display: flex;
		}
	}
}
</style>
