<template>
	<div class="ignore-img" ref="ppt">
		<span :class="realm === 'muqiao' ? 'aa' : 'bb'">
			<div>{{ pptInfo.fileName }}</div>
			<div>
				<span>{{ pptInfo.user }}</span
				>&nbsp;
				<span> {{ formatDate(new Date().getTime()).slice(0, 10) }}</span>
			</div>
		</span>
		<img v-if="realm === 'muqiao'" src="@/assets/img/firstPPT1.png" alt="" />
		<img v-else src="@/assets/img/firstPPT.png" alt="" />
	</div>
</template>
<script setup>
import { formatDate } from '@/utils/index';
let props = defineProps(['pptInfo']);
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const realm = enterStore.enterInfo.id;
watch(
	() => props.pptInfo,
	(n) => {
		console.log(n);
	}
);
console.log(props.pptInfo);
let ppt = ref(null);
defineExpose({ ppt });
</script>
<style lang="scss" scoped>
.ignore-img {
	width: 960px;
	height: 540px;
	position: relative;
	img {
		width: 100%;
	}
	.aa {
		position: absolute;
		bottom: 45px;
		left: 40px;
		color: #000c23;
		font-weight: bold;
		font-size: 15px;
		line-height: 1;
		div:nth-child(1) {
			font-weight: bold;
			font-size: 40px;
		}
		div:nth-child(2) {
			font-weight: normal;
			margin-top: 16px;
		}
	}
	.bb {
		position: absolute;
		top: 250px;
		left: 84px;
		color: #ffffff;
		font-weight: bold;
		font-size: 15px;
		line-height: 1;
		div:nth-child(1) {
			font-weight: bold;
			font-size: 40px;
		}
		div:nth-child(2) {
			font-weight: normal;
			margin-top: 16px;
		}
	}
}
</style>
