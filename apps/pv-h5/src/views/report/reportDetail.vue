<template>
	<div
		class="rp"
		id="ap-rp"
		:style="{
			backgroundColor: reportCards.includes('salesPerformanceProductivity') ? '#f1f2f4' : '#fff',
		}"
	>
		<!-- 顶部功能按钮 -->
		<div class="rp-header">
			<!-- 添加成我的报告 -->
			<div v-if="!route.query.isMine" @click="showReport = true" class="rp-header__item flex-between">
				<span>添加到我的报告</span>
				<img src="@/assets/img/myReport.png" />
			</div>
			<!-- 导出ppt -->
			<div
				class="rp-header__item"
				:class="{
					'flex-between': !route.query.isMine,
					'flex-center': route.query.isMine,
				}"
				@click="showExport = true"
			>
				<span>生成报告</span>
				<img src="@/assets/img/export.png" />
			</div>
		</div>
		<notice-bar class="bar"></notice-bar>
		<!-- 全局筛选 -->
		<globalFilter v-if="currentLabelGolbalFilter?.value === 'true'" :filterConfig="currentLabelGolbalFilterConfig"></globalFilter>
		<!-- 报告详情 -->
		<div class="rp-detail">
			<div v-for="(item, index) in reportCds" :key="index">
				<component :is="dom[item]" @filterChange="filterChange" :pathAddress="comFilterInfo(item)" :dinfo="reportCards[index]"></component>
			</div>
			<!-- 返回顶部 -->
			<!-- <van-back-top right="85vw" bottom="100px" :immediate="true" target=".rp-detail" /> -->
		</div>

		<!-- 添加我的报告弹框 -->
		<van-dialog v-model:show="showReport" @confirm="createMyReport" confirmButtonText="确定" confirm-button-color="#0074f9" show-cancel-button>
			<div class="rp-content">
				<!-- 报告名称 -->
				<div class="rp-content__title">报告名称</div>
				<div class="rp-content__textarea">
					<van-field v-model="groupName" placeholder="请在此输入报告名称" />
				</div>
				<!-- 分组详情 -->
				<div class="rp-content__list">
					<span> 图表数目 * {{ reportCards.length }} </span>
					<span v-for="(item, index) in reportCards" :key="item.reportCd"> {{ index + 1 + '.' + item.name }}</span>
				</div>
			</div>
		</van-dialog>
		<!-- 导出ppt弹窗 -->
		<van-dialog v-model:show="showExport" confirmButtonText="确定" confirm-button-color="#0074f9" show-cancel-button @confirm="exportPPT">
			<div class="item rp-content">
				<div class="rp-content__title">报告名称</div>
				<div class="rp-content__input" v-if="!$route.query.isMine">
					<van-field v-model="fileName" placeholder="请输入报告名称" />
				</div>
				<div class="rp-content__not__editable" v-else>
					<div>{{ reportStore.reportName }}</div>
				</div>
			</div>
			<div class="item">
				<van-radio-group v-model="saveType" shape="square">
					<van-radio name="1">下载查看</van-radio>
					<van-radio v-if="enterStore.enterInfo.id !== 'roche'" name="2">发送到邮箱</van-radio>
				</van-radio-group>
			</div>
		</van-dialog>
		<!-- ppt-content -->
		<div v-for="(item, index) in reportPPTs" :key="index">
			<component :is="PPTDom[item]" :ref="(el) => pptRefHandler(el, index)" :pptInfo="pptInfo" :filterInfo="handleFilterInfo(item)" class="ppt-postion"></component>
		</div>
		<!-- 安卓微信环境下提示引导 -->
		<van-overlay :show="tipShow" z-index="101" class-name="tip-popup">
			<div class="wrapper">
				<img src="@/assets/img/popuptip.png" alt="" />
				<div class="tip">请点击右上角“…”在浏览器打开</div>
			</div>
		</van-overlay>
		<!-- <img v-for="item in imgs" :key="item" :src="item" alt="" /> -->
		<exportProgress ref="progressRef" :topThreeStepsTimes="topThreeStepsTimes" :exportPPTComplate="exportPPTComplate"></exportProgress>
		<van-popup class="custo" v-model:show="GuideLayerShow" :close-on-click-overlay="false">
			<div class="img">
				<img src="@/assets/img/guide.png" alt="" />
			</div>
			<div class="tips">导出报告前，您可在此根据需要修改筛选条件</div>
			<div class="know" @click="know">我知道了</div>
		</van-popup>
	</div>
</template>
<script setup>
// ppt
import html2canvas from 'html2canvas';
import exportChartsToPPT from '@/utils/ExportToPPT';
import { formatDate, isWeChat, getNameByPath, isPCTrue } from '@/utils/index';
import { createReport, sendEmail } from '@/api/report';
import { showToast, showLoadingToast, showSuccessToast } from 'vant';
import { wechatCrop, uploadMinio } from '@/api/login';
import wxApi from '@/api/wxApi.js';
import useReportStore from '@/store/modules/report';
import useUserStore from '@/store/modules/user.js';
import useEnterStore from '@/store/modules/enterprise';
import useWxMenu from '@/store/modules/wxOptionMenu';
import { getToken } from '@/utils/auth';
import globalFilter from './globalFilter.vue';
import { updateUserAttributes } from '@/api/user';
// 是否是移动端
const isMobile = window.navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser)/i);
let reportStore = useReportStore();
let userStore = useUserStore();
let enterStore = useEnterStore();
let wxMenuStore = useWxMenu();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
const groupName = ref('');
const reportCards = ref([]);
const showReport = ref(false);

const createMyReport = async () => {
	let cds = ref([]);
	for (const i of reportCards.value) {
		cds.value.push(i.reportCd);
	}
	let res = await createReport({
		name: groupName.value,
		path: route.query.path || '',
		label: route.query.label || '',
		reportCds: cds.value,
	});
	if (res.code === 200) {
		showReport.value = false;
		showToast({ message: '创建成功' });
	} else {
		showReport.value = true;
		showToast({ message: '创建失败' });
	}
	proxy.$umeng('点击', '添加到我的报告-确定', groupName.value);
};

//同步筛选器
let comFilterInfos = ref([]);
const filterChange = (item) => {
	if (comFilterInfos.value.find((ele) => ele.name === item.name)) {
		comFilterInfos.value.splice(
			comFilterInfos.value.findIndex((ele) => ele.name === item.name),
			1,
			item
		);
	} else {
		comFilterInfos.value.push(item);
	}
	console.log(comFilterInfos.value);
};
//动态传入筛选器信息
const handleFilterInfo = computed(() => {
	return (item) => {
		return comFilterInfos.value.find((ele) => ele.name === item);
	};
});
//ppt导出
let showExport = ref(false);
let fileName = ref('');
let pptRefs = ref([]);
let imgs = ref([]);
let pptInfo = ref({});
let saveType = ref('1');
//动态ref
let pptRefHandler = (el, index) => {
	pptRefs.value[index] = el;
};
//引导提示
const tipShow = ref(false);
const progressRef = ref(null);
const topThreeStepsTimes = ref(3000);
const exportPPTComplate = ref(false);
let exportPPT = async () => {
	route.query.isMine ? (fileName.value = reportStore.reportName) : '';
	if (!fileName.value) return showToast('请填写文件名');

	let no = `${userStore.userInfo.nickname}-${new Date().getTime()}-${fileName.value}`;
	let no1 = Math.random().toString().slice(2, 14);
	// let no1 = `${userStore.userInfo.nickname}-${fileName.value}-${formatDate(new Date()).replace(/-/g, '').replace(' ', '').replace(/:/g, '')}`;
	pptInfo.value = {
		time: formatDate(new Date().getTime()).slice(0, 10),
		// time: formatDate(new Date().getTime()),
		user: userStore.userInfo.nickname,
		no1,
		fileName: fileName.value,
	};

	await nextTick();
	// 创建一个异步函数，用于处理html2canvas
	// showLoadingToast({
	// 	message: '生成中...',
	// 	forbidClick: true,
	// 	duration: 0,
	// 	className: 'ppt-pc-loading',
	// });
	exportPPTComplate.value = false;
	progressRef.value.progressShow = true;
	const processHtml2Canvas = async (item) => {
		const canvas = await html2canvas(item.ppt, {
			scale: isPCTrue() ? window.devicePixelRatio * 4 : window.devicePixelRatio,
			ignoreElements: (e) => {
				if (
					e.contains(item.ppt) ||
					item.ppt.contains(e) ||
					e.tagName === 'STYLE' ||
					e.tagName === 'LINK' ||
					e.getAttribute('data-html2canvas') != null // header里面的样式不能筛掉
				) {
					return false;
				}
				// console.log(e.tagName);
				return true;
			},
		});
		return canvas.toDataURL('image/jpeg');
	};
	//处理图片异步执行
	setTimeout(async () => {
		// const promises = pptRefs.value.map(processHtml2Canvas);
		// const dataURLs = await Promise.all(promises);
		const startTime = performance.now(); // 记录开始时间
		const batchSize = 3; // 每批处理的元素数量
		for (let i = 0; i < pptRefs.value.length; i += batchSize) {
			let batch = pptRefs.value.slice(i, i + batchSize);
			for (let item of batch) {
				let res = await processHtml2Canvas(item);
				imgs.value.push(res);
			}
			// 在处理完一批元素后，暂停一段时间，以释放设备资源
			console.log('第一批处理完了');
			await new Promise((resolve) => setTimeout(resolve, 2000)); // 1秒钟
		}

		// for (let item of pptRefs.value) {
		// 	let res = await processHtml2Canvas(item);
		// 	imgs.value.push(res);
		// }
		const endTime = performance.now(); // 记录结束时间
		const elapsedTime = ((endTime - startTime) / 1000).toFixed(2); // 计算并格式化耗时（秒）
		console.log(`总耗时: ${elapsedTime} 秒`);
		console.log(imgs.value, '截完图了');
		// imgs.value.push(...dataURLs);
		let { pptBlob, filename, pptBlobSize, downloadPPT } = await exportChartsToPPT(imgs.value, no);
		if (saveType.value === '1') {
			//在线预览

			if (isWeChat() === '企业微信' && isMobile) {
				//走企微认证
				// 1.调接口获取认证信息
				let res = await wechatCrop({ url: window.location.href.split('#')[0] });
				//2.调用企微config认证
				await wxApi.wxRegister(res.result);
				// 3.上传文件minio
				const uploadData = new FormData();
				uploadData.append('file', pptBlob);
				let { result } = await uploadMinio(uploadData, 'beigene');
				console.log(result);
				exportPPTComplate.value = true;
				// 4.调用企微预览文件接口
				wxApi.previewFile({ url: result, name: filename, size: pptBlobSize });
			} else if (isWeChat() === '企业微信' && !isMobile) {
				// pc端企业微信环境
				//正常下载
				exportPPTComplate.value = true;
				downloadPPT();
			} else if (navigator.userAgent.toLowerCase().includes('miniprogram')) {
				//如果是小程序端
				return showToast('暂不支持小程序端下载查看，请使用手机或电脑网页端打开');
			} else {
				if (route.query.isTabBar === 'false') {
					function blobToBase64(blob) {
						return new Promise((resolve, reject) => {
							const reader = new FileReader();
							reader.onloadend = () => resolve(reader.result);
							reader.onerror = reject;
							reader.readAsDataURL(blob);
						});
					}
					let resBase64 = await blobToBase64(pptBlob);
					//2.调用app方法
					proxy.$cHandler.openAppView({
						url: resBase64,
						module: 'file',
						title: filename,
					});
					imgs.value = [];
					fileName.value = '';
					// showSuccessToast('已完成');
					return false;
				}
				//正常下载
				downloadPPT();
				exportPPTComplate.value = true;
			}
		} else {
			//发送到邮箱
			// 创建 FormData 对象并添加 PPT 文件
			const formData = new FormData();
			formData.append('files', pptBlob, filename);
			let obj = {
				from: enterStore.enterInfo.contactEmail || 'support',
				subject: `智体视界报告《${filename}》`,
				text: `<!DOCTYPE html>
<html lang="zh" xmlns="http://www.w3.org/1999/html"><head><meta charset="UTF-8"/><title>智体视界报告</title><style>.signature-line {width: 300px;border: 0;border-top: 1px solid #000;margin: 10px;}</style></head><body><div><p>Dear <span>${
					userStore.userInfo.username.split('@')[0]
				}</span>,</p ><p>附件是来自智体视界汇报模块的PPT报告文件《<span>${filename}</span>》，请查收。</p ><p>请确保在使用时遵守数据安全规范，保护数据隐私安全</p ><hr class="signature-line"><p>&nbsp;&nbsp;Best regards,</p ><p>&nbsp;&nbsp;<span>${
					userStore.userInfo.email
				}</span></p><p>&nbsp;&nbsp;智体视界</p></div></body></html>`,
				to: userStore.userInfo.email,
			};
			sendEmail(formData, obj);
		}
		imgs.value = [];
		fileName.value = '';
		// showSuccessToast({
		// 	message: '已完成',
		// 	className: 'ppt-success-loading',
		// });
		// showSuccessToast('已完成');
		exportPPTComplate.value = true;
	}, 0);

	if (route.query.isMine) {
		proxy.$umeng('点击', `我的报告-生成报告-${saveType.value === '1' ? '在线预览' : '发送到邮箱'}-确定`, fileName.value);
	} else {
		proxy.$umeng('点击', `标准报告-生成报告-${saveType.value === '1' ? '在线预览' : '发送到邮箱'}-确定`, fileName.value);
	}
};
// 根据URL上的path,label, tag生成筛选对象
const comFilterInfo = computed(() => {
	return (item) => {
		return route.query.path !== 'myReport' ? `report->${route.query.path}->${route.query.label}->${item}` : `report->${route.query.label}->${route.query.tag}->${item}`;
	};
});

//根据reportCds动态组件渲染
reportCards.value = JSON.parse(localStorage.getItem('selected-report-list'));
let { dom, reportCds, PPTDom, reportPPTs } = useReport(reportCards.value);

//判断是否是安卓微信环境 true 弹出引导层
function isAndroid() {
	const userAgent = navigator.userAgent.toLowerCase();
	console.log(userAgent);
	const isAndroid = userAgent.includes('android');
	return isAndroid;
}
// 在当前url中拼接参数
function pushStateParams(key, value) {
	// 获取当前页面的 URL
	const currentUrl = window.location.href;
	// 检查 URL 中是否已经有查询参数
	const separator = currentUrl.includes('?') ? '&' : '?';
	// 构建新的 URL
	const newUrl = currentUrl + separator + encodeURIComponent(key) + '=' + encodeURIComponent(value);
	// 更新浏览器地址栏中的 URL
	window.history.replaceState({}, '', newUrl);
}
function replaceStateParams(key, value) {
	// 获取当前页面的 URL
	const currentUrl = new URL(window.location.href);
	// 添加或更新查询参数
	currentUrl.searchParams.set(key, value);
	// 更新浏览器地址栏中的 URL而不添加新的历史记录条目
	window.history.replaceState({}, '', currentUrl);
}
const GuideLayerShow = ref(false);
const setFilterHighlight = () => {
	let dom = document.querySelector('.fa');
	dom.querySelectorAll('.q-fileter-item').forEach((item) => {
		item.style.backgroundColor = '#ffffff';
	});
	let distanceToPageTop = dom.getBoundingClientRect().top;
	let aa = document.querySelector('.custo');
	aa.style.top = distanceToPageTop + dom.offsetHeight + 'px';
	dom.style.pointerEvents = 'none';
	dom.style.position = 'relative';
	dom.style.zIndex = '9999';
	dom.style.background = '#e1ebf9';
	dom.style.borderRadius = '5px 5px 5px 5px';
};
const resetFilterHighlight = () => {
	let dom = document.querySelector('.fa');
	if (!dom) return;
	// 移除 fa 的内联样式
	dom.removeAttribute('style');
	// 移除子元素 .q-fileter-item 的背景色
	dom.querySelectorAll('.q-fileter-item').forEach((item) => {
		item.removeAttribute('style');
	});
};
const know = async () => {
	GuideLayerShow.value = false;
	resetFilterHighlight();
	await updateUserAttributes({
		attributes: { isKnow: 'true' },
		businessId: userStore.userInfo.id,
	});
	userStore.GET_USERINFO();
};
//获取权限树
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
let arrList = perStore.reportList;
function flattenArray(arr) {
	let result = [];
	// 递归函数
	function flatten(item) {
		// 如果有子节点，则递归处理
		if (item.children && item.children.length > 0) {
			item.children.forEach((child) => flatten(child));
		}
		result.push(item); // 将当前节点推入结果数组
	}
	arr.forEach((item) => flatten(item));
	return result;
}
arrList = flattenArray(arrList);
let currentLabelGolbalFilter = arrList.find((ele) => ele.path === route.query.label)?.attributes?.find((ele) => ele.name === 'globalFilter');
console.log(currentLabelGolbalFilter, '==========================');
let currentLabelGolbalFilterConfig = arrList.find((ele) => ele.path === route.query.label)?.attributes?.find((ele) => ele.name === 'filterConfig')?.value;
onMounted(async () => {
	//todo调接口查询当前用户是否展示引导层
	const isKnowAttribute = userStore.userInfo.attributes?.find((ele) => ele.name === 'isKnow')?.value === 'true';
	if (!isKnowAttribute && currentLabelGolbalFilter.value === 'true') {
		GuideLayerShow.value = true;
		nextTick(() => {
			setFilterHighlight();
		});
	}

	if (isWeChat() === '微信' && isAndroid()) {
		pushStateParams('token', getToken());
		replaceStateParams('hiddenTabBar', 'true');
		tipShow.value = true;
		return;
	}
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/report/reportDetail.scss';
.rp {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	position: relative;
	background-color: #fff;
	padding: 10px 15px;
	.bar {
		margin: 5px -15px;
	}
	.rp-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 8px;
		.rp-header__item {
			flex: 1;
			background: var(--pv-filter-bgc);
			display: flex;
			align-items: center;

			border-radius: 2px;
			padding: 6px 11px;
			span {
				font-size: 14px;
				color: #3c4e71;
			}
			img {
				width: 10px;
				height: 11px;
			}
		}
		.flex-between {
			justify-content: space-between;
		}
		.flex-center {
			justify-content: center;
			span {
				margin-right: 8px;
			}
		}
	}
	.rp-detail {
		padding-bottom: 12px;
		flex: 1;
		overflow-y: auto;
	}

	.rp-content {
		display: flex;
		flex-direction: column;
		width: 96%;
		margin-left: 2%;
		.rp-content__title {
			position: relative;
			padding-left: 5px;
			color: #283c63;
			margin-bottom: 5px;
			width: 100%;
			font-weight: 500;
			font-size: 14px;
		}
		.rp-content__title::before {
			content: '';
			position: absolute;
			top: 10%;
			left: 0;
			width: 3px;
			height: 80%;
			background-color: var(--pv-tabbar-active);
		}
		.rp-content__textarea {
			border: 1px solid var(--pv-filter-bgc);
			width: 100%;
			&:deep(.van-cell) {
				padding: 5px;
			}
			&:deep(.van-field) {
				height: 80px;
			}
		}

		.rp-content__input {
			margin: 5px 0px;
			border: 1px solid var(--pv-filter-bgc);
			width: 100%;
			&:deep(.van-cell) {
				padding: 5px;
			}
		}
		.rp-content__not__editable {
			margin: 5px 0px;
			width: 100%;
			&:deep(.van-cell) {
				padding: 5px;
			}
		}

		.rp-content__list {
			width: 100%;
			display: flex;
			flex-direction: column;
			margin-top: 5px;
			padding-left: 2px;
			span {
				color: #283c63;
				font-size: 12px;
				padding-top: 6px;
			}
		}
	}
}
::v-deep(.van-dialog__content) {
	padding: 16px 16px 8px;
	.item {
		font-size: 14px;
		.title {
			color: var(--pv-default-color);
			font-weight: bold;
			margin-bottom: 8px;
		}
		&:nth-child(1) {
			margin-bottom: 8px;
		}
		.van-radio-group {
			padding: 5px 0px;
			display: flex;
			.van-radio:nth-child(1) {
				margin-left: 5px;
				margin-right: 32px;
			}
			.van-icon,
			.van-radio__icon {
				width: 15px;
				height: 15px;
				line-height: initial;
			}
			.van-radio__icon--checked .van-icon {
				background-color: var(--pv-tabbar-active);
			}
			.van-radio__label {
				color: var(--pv-default-color);
			}
		}
	}
	.input-inner {
		background-color: var(--pv-card-bgc);
		border: none;
		border-radius: 2px;
		width: 100%;
		color: var(--pv-default-color);
		padding: 3px 5px;
	}
}
.ppt-postion {
	position: absolute;
	z-index: -1;
}
.tip-popup {
	background-color: rgba(0, 0, 0, 0.6);
	.wrapper {
		height: 100%;
		position: relative;
		color: #ffffff;
		font-size: 14px;
		img {
			position: absolute;
			width: 90px;
			height: 82;
			top: 19px;
			right: 49px;
		}
		.tip {
			position: absolute;
			top: 116px;
			right: 39px;
		}
	}
}
:deep(.custo) {
	background-color: transparent;
	overflow: hidden;
	top: 0;
	transform: initial;
	.img {
		text-align: center;
		padding-top: 30px;
		img {
			width: 35px;
		}
	}
	.tips {
		margin-top: 10px;
		font-size: 16px;
		color: #fff;
	}
	.know {
		margin: 0 auto;
		text-align: center;
		width: 100px;
		height: 44px;
		line-height: 44px;
		border-radius: 50px 50px 50px 50px;
		border: 1px solid #ffffff;
		font-size: 16px;
		color: #ffffff;
		margin-top: 155px;
	}
}
</style>
