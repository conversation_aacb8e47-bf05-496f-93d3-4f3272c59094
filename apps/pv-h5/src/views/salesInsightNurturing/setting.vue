<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">销售洞察培养</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">完成</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 类型 -->
			<div class="mc-content-cotainer">
				<van-uploader :after-read="afterRead">
					<div class="uploader">
						<img src="@/assets/img/demo/medicalKnowledgeMaintained/file.png" class="file" alt="" />
						<img src="@/assets/img/demo/salesInsightNurturing/plus.png" class="plus" alt="" />
					</div>
				</van-uploader>
				<van-field label="名称" maxlength="17" v-model="name" placeholder="请输入名称" show-word-limit></van-field>
				<voice-textarea style="background-color: #f4f5f7" title="智能体描述" :row="2" maxlength="23" :show-word-limit="true" :show-voice="false"></voice-textarea>
			</div>
		</div>
	</div>
</template>
<script setup>
const router = useRouter();
const route = useRoute();
const goBack = () => {
	router.go(-1);
};
const name = ref('');
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		width: 100vw;
		display: flex;
		background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
			&:last-child {
				color: #0052cc;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 15px;
			margin-bottom: 20px;
		}
	}
	:deep(.van-uploader) {
		margin: 0 auto;
		width: 100px;
		margin-bottom: 15px;
	}
	:deep(.van-cell) {
		--van-cell-background: #f4f5f7;
		--van-field-label-width: 40px;
		margin-bottom: 15px;
		border-radius: 6px;
		.van-cell__title {
			font-weight: bold;
		}
		.van-field__value {
			display: flex;
		}
		.van-field__body {
			flex: 1;
			width: 0;
		}
	}
	.uploader {
		width: 100px;
		height: 100px;
		border-radius: 50%;
		// overflow: hidden;
		position: relative;
		margin: 0 auto;
		.file {
			width: 100%;
			height: 100%;
			border-radius: 50%;
			background-color: #fff;
		}
		.plus {
			position: absolute;
			bottom: 0;
			right: 0;
			img {
				width: 20px;
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
