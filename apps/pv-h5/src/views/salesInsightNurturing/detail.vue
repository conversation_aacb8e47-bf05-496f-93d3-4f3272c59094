<template>
	<div id="ap-sales-detail" class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ detail.name }}</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 类型 -->
			<div class="mc-content-cotainer">
				<!-- <van-cell-group inset> -->
				<van-field v-model="detail.name" label="卡片名称" placeholder="卡片名称" class="m-15" clearable />
				<!-- </van-cell-group> -->
				<voice-textarea v-model="detail.description" title="卡片描述" />
				<voice-textarea v-model="detail.attributes.short_description" class="m-15" title="卡片短描述" />
				<div class="question-list m-15">
					<div class="question-list__title">问题清单</div>
					<div class="question-list__content">
						<div class="question-item" v-for="(item, index) in list" :key="index">
							<div class="question-item__content">
								<van-field v-model="list[index]" label="" type="textarea" autosize rows="1" placeholder="问题" clearable />
							</div>
							<div class="question-item__btn" @click="deleteItem(index)">
								<img src="@/assets/img/demo/salesInsightNurturing/delete.png" alt="" />
							</div>
						</div>
					</div>
					<div class="question-list__add" @click="addItem"><img src="@/assets/img/demo/salesInsightNurturing/plus.png" alt="" />新增问题</div>
				</div>
				<div class="question-list question-list--last m-15">
					<div class="question-list__title">索引清单</div>
					<div class="question-list__content">
						<div class="question-item" v-for="(item, index) in indexList" :key="index">
							<div class="question-item__content">
								<van-field v-model="indexList[index]" label="" type="textarea" autosize rows="1" placeholder="索引" clearable />
							</div>
							<div class="question-item__btn" @click="deleteIndexItem(index)">
								<img src="@/assets/img/demo/salesInsightNurturing/delete.png" alt="" />
							</div>
						</div>
					</div>
					<div class="question-list__add" @click="addIndexItem"><img src="@/assets/img/demo/salesInsightNurturing/plus.png" alt="" />新增索引</div>
				</div>
				<div class="btn-group">
					<van-button class="size-color" color="#dde4f2" @click="goBack">取消</van-button>
					<van-button @click="save" color="#0052cc">确认</van-button>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { postEditData } from '@/api/knowledge';
import { componentDetail, componentUpdate } from '@/api/nurture';

const router = useRouter();
const route = useRoute();
const goBack = () => {
	router.go(-1);
};
const list = ref([]);
const addItem = () => {
	list.value.push('');
};
const deleteItem = (index) => {
	list.value.splice(index, 1);
};

const indexList = ref([]);
const addIndexItem = () => {
	indexList.value.push('');
};
const deleteIndexItem = (index) => {
	indexList.value.splice(index, 1);
};
const detail = ref({
	name: '',
	description: '',
	attributes: {},
});
const getDetail = () => {
	componentDetail(route.query.id).then((res) => {
		console.log(res);
		detail.value = res.result;
		list.value = JSON.parse(res.result?.attributes?.faq || '[]');
		indexList.value = JSON.parse(res.result?.attributes?.index || '[]');
		if (route.query.q) {
			list.value.push(route.query.q);
		}
		if (route.query.q1) {
			list.value.push(route.query.q1);
		}
	});
};
const save = async () => {
	detail.value.attributes = detail.value.attributes || {};
	detail.value.attributes.faq = JSON.stringify(list.value);
	detail.value.attributes.index = JSON.stringify(indexList.value);
	if (route.query.id) {
		await componentUpdate([detail.value]);
		const params = {};
		if (detail.value?.attributes?.params) {
			const paramsData = JSON.parse(detail.value.attributes.params);
			paramsData.forEach((item) => {
				params[item.key] = item.value;
			});
		}
		await postEditData({
			collection_id: detail.value.attributes?.collection,
			card_id: String(detail.value.id),
			message_id: route.query.mId,
			q: JSON.stringify({
				id: detail.value.id,
				type: 'cardCom',
				description: detail.value.description,
				name: detail.value.name,
				url: detail.value?.attributes?.url || '',
				previewUrl: detail.value.thumbnail,
				faq: list.value || [],
				params,
			}),
			a: `cardId=${detail.value.id}`,
		});
		if (route.query.q) {
			router.go(-2);
		} else {
			goBack();
		}
	}
};
onMounted(() => {
	if (route.query.id) {
		getDetail();
	}
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/intelligent/salesInsightNurturing/salesDetail.scss';
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	background-color: #f4f5f7;

	.mc-header {
		width: 100vw;
		display: flex;
		background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 0 15px;
			margin-bottom: 20px;
		}
	}
	:deep(.van-cell) {
		border-radius: 6px;
		.van-field__label {
			font-weight: bold;
		}
		--van-field-label-color: #172b4d;
	}
	.m-15 {
		margin: 15px 0;
	}
	.question-list {
		border-radius: 6px;
		background-color: #fff;
		padding: 15px;
		&--last {
			margin-bottom: 40px;
		}
		--van-cell-vertical-padding: 0;
		--van-cell-horizontal-padding: 0;
		&__title {
			color: #172b4d;
			font-weight: bold;
			font-size: 14px;
			margin-bottom: 8px;
		}
		&__add {
			text-align: center;
			color: #0052cc;
			font-weight: 400;
			font-size: 14px;
			img {
				width: 16px;
				vertical-align: middle;
				margin-right: 3px;
			}
		}
		.question-item {
			display: flex;
			margin-bottom: 10px;
			align-items: center;
			&__content {
				flex: 1;
				width: 0;
				border-radius: 6px;
				background-color: #f4f5f7;
				padding: 10px;
				margin-right: 15px;
				--van-cell-background: transparent;
			}
			img {
				width: 16px;
			}
		}
	}
	.btn-group {
		display: flex;
		gap: 7px;
		justify-content: space-between;
		align-items: center;
		margin-top: 12px;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 10px 15px;
		&:deep(.van-button) {
			border-radius: 5px;
			height: 40px;
			width: 169px;
		}

		&:deep(.size-color) {
			color: #0052cc !important;
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
