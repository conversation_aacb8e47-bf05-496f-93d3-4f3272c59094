<template>
	<div id="ap-sales-recommend-list" class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">需求申请</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 类型 -->
			<van-tabs v-model:active="currentIndex" shrink @change="changeTab">
				<van-tab v-for="item in navList" :key="item.id" :title="item.name" :name="item.id"></van-tab>
			</van-tabs>
			<div class="mc-content-cotainer">
				<div class="list">
					<van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="getList">
						<div v-for="item in list" :key="item.id" class="item">
							<div class="item__name">申请人：{{ item.username }}</div>
							<div class="item__body">
								<div class="item__question" v-if="item.question">问题：{{ item.question }}</div>
								<div class="item__time">申请时间：{{ formatDate(item.created_time) }}</div>
								<div class="item__desc">申请描述：{{ item.description }}</div>
							</div>
						</div>
					</van-list>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { getApplyList } from '@/api/knowledge';
import { formatDate } from '@/utils';

const router = useRouter();
const route = useRoute();
const goBack = () => {
	router.go(-1);
};
let navList = ref([
	{ name: '处理中', id: '1' },
	{ name: '已完成', id: '2' },
]);
const currentIndex = ref('1');
const changeTab = () => {
	page.value = 1;
	list.value = [];
	loading.value = false;
	finished.value = false;
	getList();
};
const page = ref(1);
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const getList = () => {
	loading.value = true;
	getApplyList({
		page_num: page.value,
		page_size: 10,
		status: currentIndex.value !== '1',
	}).then((res) => {
		list.value = list.value.concat(res.data);
		loading.value = false;
		if (res.data.length < 10) {
			finished.value = true;
		} else {
			finished.value = false;
		}
		page.value++;
	});
};
</script>
<style lang="scss" scoped>
@import '../../style/pc/intelligent/salesInsightNurturing/recommendList.scss';
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	background-color: #f4f5f7;

	.mc-header {
		width: 100vw;
		display: flex;
		background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 0 15px;
			margin-bottom: 20px;
		}
	}
	.list {
		height: calc(100vh - 124px);
		overflow-y: auto;
		margin-top: 15px;
		.item {
			border-radius: 6px;
			background-color: #fff;
			margin-bottom: 15px;
			padding: 15px;
			color: #172b4d;
			font-weight: 400;
			font-size: 14px;
			&__name {
				border-bottom: 1px solid #dfe1e6;
				padding-bottom: 15px;
				padding-left: 10px;
				margin-bottom: 10px;
				position: relative;
				&::before {
					content: '';
					width: 4px;
					height: 12px;
					background-color: #0052cc;
					position: absolute;
					top: 6px;
					left: 0;
				}
			}
			&__body {
				font-size: 12px;
				padding: 0 10px;
				line-height: 1.4;
			}
			&__question {
				margin-bottom: 10px;
				text-indent: -3em;
				padding-left: 3em;
			}
			&__time {
				margin-bottom: 10px;
			}
			&__desc {
				text-indent: -5em;
				padding-left: 5em;
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
