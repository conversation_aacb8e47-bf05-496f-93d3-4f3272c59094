<template>
	<div id="ap-sales-list" class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ agentName }}</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<van-tabs v-model:active="currentIndex" @change="getList">
				<van-tab v-for="item in navList" :key="item.id" :title="item.name" :name="item.id"></van-tab>
			</van-tabs>
			<div class="mc-content-cotainer">
				<van-search v-model="search" placeholder="请输入搜索关键词" />
				<div class="list">
					<!-- <div class="item" @click="goAdd">
						<div class="item__content">
							<img src="@/assets/img/demo/salesInsightNurturing/add.png" alt="" />
						</div>
						<div class="item__title">新建</div>
					</div> -->
					<div class="item" @click="goDetail(item)" v-for="item in listData" :key="item">
						<div class="item__content">
							<img :src="item.thumbnail" alt="" />
						</div>
						<div class="item__title">{{ item.name }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { componentSearch, getScopeList } from '@/api/nurture';
import { getAgentListApi } from '@/api/agent-tools';
import usePremissionStore from '@/store/modules/premission';
const router = useRouter();
const route = useRoute();

const goBack = () => {
	router.go(-1);
};
let navList = ref([{ name: '全部', id: '' }]);
const currentIndex = ref(Number(route.query.id));
const search = ref('');
const goAdd = () => {
	router.push({
		path: '/salesInsightNurturing/new-recommend',
		query: {
			isTabBar: route.query.isTabBar || '',
			agent_id: route.query.agent_id,
			cId: currentIndex.value,
		},
	});
};
const goDetail = (item) => {
	router.push({
		path: '/salesInsightNurturing/detail',
		query: {
			id: item.id,
			name: item.name,
			isTabBar: route.query.isTabBar,
			mId: route.query.mId,
			agent_id: route.query.agent_id,
			q: route.query.q || undefined,
			q1: route.query.q1 || undefined,
		},
	});
};
const loading = ref(false);
const listData = ref([]);
const showList = computed(() => {
	const arr = [];
	listData.value.forEach((item) => {
		let show = true;
		if (search.value && !item.name.includes(search.value)) {
			show = false;
		}
		if (show && currentIndex.value && Number(item.attributes.businessArea) !== currentIndex.value) {
			show = false;
		}
		if (show) {
			arr.push(item);
		}
	});
	return arr;
});
const getNavList = () => {
	getScopeList().then((res) => {
		navList.value = [{ name: '全部', id: '' }, ...res.result];
		nextTick(() => {
			currentIndex.value = Number(route.query.id);
			// console.log(currentIndex.value);
			getList();
		});
	});
};
const getList = () => {
	loading.value = true;
	// currentIndex.value = route.query.id;
	componentSearch({
		type: 'card',
		scopes: currentIndex.value ? [currentIndex.value] : [],
	})
		.then((res) => {
			listData.value = res.result.content;
		})
		.finally(() => {
			loading.value = false;
		});
};
const agentName = ref('');
const { GET_USER_AGENT } = usePremissionStore();

onMounted(() => {
	GET_USER_AGENT().then((res) => {
		const agentList = res;
		for (const ele of agentList) {
			if (String(ele.id) === String(route.query.agent_id)) {
				agentName.value = ele.name;
				break;
			}
		}
	});
	getNavList();
	getAgentListApi().then((res) => {
		const agentList = res.result.content || [];
		for (const ele of agentList) {
			if (ele.attributes.url.indexOf('salesInsightNurturing') > -1) {
				agentName.value = ele.name;
				break;
			}
		}
	});
	// getList();
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/intelligent/salesInsightNurturing/salesList.scss';
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	background-color: #f4f5f7;

	.mc-header {
		width: 100vw;
		display: flex;
		background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 15px;
			margin-bottom: 20px;
		}
	}
	--van-search-background: #f4f5f7;
	--van-search-padding: 0;
	--van-search-content-background: #fff;
	.list {
		display: flex;
		flex-wrap: wrap;
		gap: 2px 15px;
		margin: 15px 0;
		.item {
			flex: 0 0 calc(50% - 7.5px);
			&__content {
				margin-bottom: 5px;
				img {
					width: 100%;
					height: 106px;
				}
			}
			&__title {
				color: #172b4d;
				font-weight: 400;
				font-size: 13px;
				text-align: center;
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
