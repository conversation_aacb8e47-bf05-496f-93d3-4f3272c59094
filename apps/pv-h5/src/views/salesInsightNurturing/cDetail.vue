<template>
	<div id="ap-sales-c-detail" class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">待确认</div>
			<!-- 右侧icon @click="goAdd" -->
			<div style="visibility: hidden" class="mc-header-icon">新建</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 类型 -->
			<div class="mc-content-cotainer">
				<div class="item">
					<div class="item__tag">
						<van-tag plain type="primary"> #{{ index > 9 ? index : '0' + index }} </van-tag>
					</div>
					<div class="item__title">{{ detail.query }}</div>
					<div class="item__footer">
						<div class="item__info">
							<p>用户名称：{{ detail.real_name || '' }}（{{ detail.username || '' }}）</p>
							<p>使用日期：{{ formatDate(detail.created_time) }}</p>
						</div>
						<div class="item__btn"></div>
					</div>
				</div>
				<div class="content" v-if="detail?.meta_data?.msg && detail?.meta_data?.msg.length" @click="goDetail(detail?.meta_data?.msg[0])">
					<div class="content__title">{{ detail?.meta_data?.msg[0].name }}</div>
					<div class="content__img">
						<!-- <img :src="detail?.meta_data?.msg[0].previewUrl" alt="" /> -->
						<iframe
							class="ifr"
							:style="{
								height: detail?.meta_data?.iframeHeight ? detail?.meta_data?.iframeHeight : '',
							}"
							v-if="detail?.meta_data?.iframeUrl"
							ref="iframeRef"
							:src="detail?.meta_data?.iframeUrl"
						></iframe>
						<div v-if="detail?.meta_data?.summary">
							<ai-text :id="detail?.meta_data?.id" :msg="detail?.meta_data?.summary"></ai-text>
						</div>
					</div>
					<div class="content__footer">
						<img src="@/assets/img/demo/salesInsightNurturing/switch.png" alt="" />
					</div>
				</div>
				<voice-textarea v-model="detail.feedback_reason" readonly style="background-color: #f4f5f7" title="用户反馈" :show-voice="false" placeholder=""></voice-textarea>
				<div class="btn-group">
					<van-button class="size-color" color="#dde4f2" @click="deleteItem">忽略</van-button>
					<van-button @click="goRecommend" color="#0052cc">语义维护</van-button>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { getKnowledgeMissionDetail, putKnowledgeMission } from '@/api/knowledge';
import { formatDate } from '@/utils';
import dayjs from 'dayjs';

const router = useRouter();
const route = useRoute();
const goBack = () => {
	router.go(-1);
};
const index = computed(() => {
	return route.query.index || 1;
});
const goRecommend = () => {
	router.push({
		path: '/salesInsightNurturing/list',
		query: {
			q: detail.value.query,
			mId: route.query.id,
			isTabBar: route.query.isTabBar,
			agent_id: route.query.agent_id,
		},
	});
};
const goDetail = (item) => {
	router.push({
		path: '/salesInsightNurturing/detail',
		query: {
			id: item.id,
			mId: route.query.id,
			agent_id: route.query.agent_id,
			// q: detail.value.query || undefined,
			// q1: route.query.q1 || undefined,
		},
	});
};
const deleteItem = () => {
	putKnowledgeMission({
		id: detail.value.id,
	}).then(() => {
		goBack();
	});
};
const goAdd = () => {
	router.push({
		path: '/salesInsightNurturing/new-recommend',
		query: {
			id: route.query.id,
			isTabBar: route.query.isTabBar || '',
			agent_id: route.query.agent_id,
		},
	});
};
const detail = ref({});
const iframeRef = ref(null);
const afterMessage = (event) => {
	if (event.data.source) return;
	console.log('=======================收到子iframe消息=======================');
	console.log(event);
	// currentClickIndex.value = event.data.index;
	//有明细信息展开按钮的卡片 更新卡片iframe高度
	if (event.data.type === 'updateClientHeight') {
		console.log(event.data.cardHeight);
		iframeRef.value.style.height = event.data.cardHeight + 'px';
		return;
	}
	// 初始化获取内嵌页面的高度
	if (!event.data.cardHeight) return;
	const height = event.data.cardHeight + 'px';
	// 设置 iframe 的高度
	iframeRef.value.style.height = height;
};
const getDetail = () => {
	getKnowledgeMissionDetail(route.query.id).then((res) => {
		detail.value = res.data;
	});
};
onMounted(() => {
	window.addEventListener('message', afterMessage);
	getDetail();
});
onUnmounted(() => {
	window.removeEventListener('message', afterMessage);
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/intelligent/salesInsightNurturing/cDetail.scss';
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		width: 100vw;
		display: flex;
		background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
			&:last-child {
				color: #0052cc;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		padding-bottom: 40px;
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 15px;
			margin-bottom: 20px;
		}
	}
	.item {
		border-radius: 6px;
		background-color: #f4f5f7;
		border: 1px solid #dfe1e6;
		padding: 15px;
		margin-bottom: 15px;
		:deep(.van-tag) {
			background-color: rgba(0, 82, 204, 0.1);
			// border: 1px solid rgba(0, 82, 204, 0.3);
			border-radius: 5px;
			font-size: 10px;
		}
		&__tag {
			margin-bottom: 8px;
		}
		&__title {
			color: #172b4d;
			font-weight: bold;
			font-size: 13px;
			margin-bottom: 8px;
		}
		&__footer {
			display: flex;
		}
		&__info {
			color: #6b778c;
			font-weight: 400;
			font-size: 10px;
			flex: 1;
			width: 0;
		}
		&__btn {
			:deep(.van-button) {
				--van-button-default-height: 24px;
				background-color: rgba(0, 82, 204, 0.1);
				font-size: 12px;
				color: #0052cc;
				border-color: rgba(0, 82, 204, 0.1);
				& + .van-button {
					margin-left: 10px;
					background-color: #0052cc;
					color: #fff;
				}
			}
		}
	}
	.content {
		border-radius: 6px;
		background-color: #f4f5f7;
		border: 1px solid #dfe1e6;
		padding: 15px;
		margin-bottom: 15px;
		position: relative;
		&__title {
			color: #172b4d;
			font-weight: bold;
			font-size: 14px;
			margin-bottom: 10px;
		}
		&__img {
			border-radius: 5px;
			// background-color: #ffffff;
			overflow: hidden;
			img {
				max-width: 100%;
			}
		}
		&__footer {
			position: absolute;
			bottom: 0;
			right: 0;
			img {
				width: 44px;
			}
		}
		.ifr {
			width: 100%;
			border: none;
			height: 1px;
			border-radius: 5px;
			background-color: var(--ac-bg-color--fff);
			min-width: 310px;
		}
	}
	.btn-group {
		display: flex;
		gap: 7px;
		justify-content: space-between;
		align-items: center;
		margin-top: 12px;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 10px 15px;
		background-color: #fff;
		&:deep(.van-button) {
			border-radius: 5px;
			height: 40px;
			flex: 1;
			// width: 169px;
		}

		&:deep(.size-color) {
			color: #0052cc !important;
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
