<template>
	<div id="ap-sales-q-list" class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">待处理</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 类型 -->
			<div class="mc-content-cotainer">
				<van-search v-model="search_text" placeholder="请输入搜索关键词" @search="onSearch" />
				<div class="list">
					<van-list v-model:loading="loading" :finished="finished" @load="getList">
						<div class="item" v-for="(item, index) in list" :key="index">
							<div class="item__tag">
								<van-tag plain type="primary"> #{{ index >= 9 ? index : '0' + (index + 1) }} </van-tag>
								<span class="type">{{ item.data_temp1 === 'AI' ? '待处理' : '待确认' }}</span>
							</div>
							<div class="item__title">{{ item.query }}</div>
							<div class="item__footer">
								<div class="item__info">
									<p>用户名称：{{ item.real_name || '' }}（{{ item.username || '' }}）</p>
									<p>使用日期：{{ formatDate(item.created_time) }}</p>
								</div>
								<div class="item__btn">
									<van-button type="primary" @click="deleteItem(item, index)">忽略</van-button>
									<van-button type="primary" @click="goDetail(item)">详情</van-button>
								</div>
							</div>
						</div>
					</van-list>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { getKnowledgeMissionList, postKnowledgeMissionList, putKnowledgeMission } from '@/api/knowledge';
import { formatDate } from '@/utils';
import dayjs from 'dayjs';

const router = useRouter();
const route = useRoute();
const goBack = () => {
	router.go(-1);
};
const search_text = ref('');
const page = ref(1);
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const onSearch = () => {
	page.value = 1;
	list.value = [];
	getList();
};
const getList = () => {
	postKnowledgeMissionList({
		page_size: 10,
		page_num: page.value,
		search_text: search_text.value,
	}).then((res) => {
		list.value = list.value.concat(res.data);
		loading.value = false;
		if (res.data.length < 10) {
			finished.value = true;
		} else {
			finished.value = false;
		}
		page.value++;
	});
};
const deleteItem = (item, index) => {
	putKnowledgeMission({
		id: item.id,
	}).then(() => {
		list.value.splice(index, 1);
	});
};
const goDetail = (item, index) => {
	router.push({
		path: '/salesInsightNurturing/' + (item.data_temp1 === 'AI' ? 'q' : 'c') + '-detail',
		query: {
			id: item.id,
			index: index,
			agent_id: route.query.agent_id,
			isTabBar: route.query.isTabBar,
		},
	});
};
</script>
<style lang="scss" scoped>
@import '../../style/pc/intelligent/salesInsightNurturing/qList.scss';
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		width: 100vw;
		display: flex;
		background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 0 15px;
			margin-bottom: 20px;
		}
	}
	:deep(.van-search) {
		--van-search-padding: 15px 0;
		--van-radius-sm: 6px;
		.van-search__content {
			border: 1px solid #dfe1e6;
		}
	}
	.list {
		.item {
			border-radius: 6px;
			background-color: #f4f5f7;
			border: 1px solid #dfe1e6;
			padding: 15px;
			margin-bottom: 15px;
			:deep(.van-tag) {
				background-color: rgba(0, 82, 204, 0.1);
				// border: 1px solid rgba(0, 82, 204, 0.3);
				border-radius: 5px;
				font-size: 10px;
			}
			&__tag {
				margin-bottom: 8px;
				.type {
					float: right;
					font-size: 12px;
					color: #0052cc;
				}
			}
			&__title {
				color: #172b4d;
				font-weight: bold;
				font-size: 13px;
				margin-bottom: 8px;
			}
			&__footer {
				display: flex;
			}
			&__info {
				color: #6b778c;
				font-weight: 400;
				font-size: 10px;
				flex: 1;
				width: 0;
			}
			&__btn {
				:deep(.van-button) {
					--van-button-default-height: 24px;
					background-color: rgba(0, 82, 204, 0.1);
					font-size: 12px;
					color: #0052cc;
					border-color: rgba(0, 82, 204, 0.1);
					& + .van-button {
						margin-left: 10px;
						background-color: #0052cc;
						color: #fff;
					}
				}
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
