<template>
	<div id="ap-sales-q-detail" class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">补充</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 类型 -->
			<div class="mc-content-cotainer">
				<div class="item">
					<div class="item__tag">
						<van-tag plain type="primary"> #{{ index > 9 ? index : '0' + index }} </van-tag>
					</div>
					<div class="item__title">{{ detail.query }}</div>
					<div class="item__footer">
						<div class="item__info">
							<p>用户名称：{{ detail.real_name || '' }}（{{ detail.username || '' }}）</p>
							<p>使用日期：{{ formatDate(detail.created_time) }}</p>
						</div>
						<div class="item__btn"></div>
					</div>
				</div>
				<voice-textarea v-model="description" style="background-color: #f4f5f7" title="问题详情描述" :placeholder="'请输入问题需求的详细描述 \n例如：我想看到我负责区域的不同终端的销售额覆盖情况。'"></voice-textarea>
				<div class="btn-group">
					<van-button @click="goRecommend" color="#0052cc">语义维护</van-button>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { getKnowledgeMissionDetail } from '@/api/knowledge';
import { formatDate } from '@/utils';

const router = useRouter();
const route = useRoute();
const goBack = () => {
	router.go(-1);
};
const index = computed(() => {
	return route.query.index || 1;
});
const description = ref('');
const goRecommend = () => {
	router.push({
		path: '/salesInsightNurturing/list',
		query: {
			q: description.value || detail.value.query,
			q1: description.value ? detail.value.query : '',
			agent_id: route.query.agent_id,
			mId: route.query.id,
			isTabBar: route.query.isTabBar,
		},
	});
};
const detail = ref({});
const getDetail = () => {
	getKnowledgeMissionDetail(route.query.id).then((res) => {
		detail.value = res.data;
	});
};
onMounted(() => {
	getDetail();
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/intelligent/salesInsightNurturing/qDetail.scss';
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		width: 100vw;
		display: flex;
		background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 15px;
			margin-bottom: 20px;
		}
	}
	.item {
		border-radius: 6px;
		background-color: #f4f5f7;
		border: 1px solid #dfe1e6;
		padding: 15px;
		margin-bottom: 15px;
		:deep(.van-tag) {
			background-color: rgba(0, 82, 204, 0.1);
			// border: 1px solid rgba(0, 82, 204, 0.3);
			border-radius: 5px;
			font-size: 10px;
		}
		&__tag {
			margin-bottom: 8px;
		}
		&__title {
			color: #172b4d;
			font-weight: bold;
			font-size: 13px;
			margin-bottom: 8px;
		}
		&__footer {
			display: flex;
		}
		&__info {
			color: #6b778c;
			font-weight: 400;
			font-size: 10px;
			flex: 1;
			width: 0;
		}
		&__btn {
			:deep(.van-button) {
				--van-button-default-height: 24px;
				background-color: rgba(0, 82, 204, 0.1);
				font-size: 12px;
				color: #0052cc;
				border-color: rgba(0, 82, 204, 0.1);
				& + .van-button {
					margin-left: 10px;
					background-color: #0052cc;
					color: #fff;
				}
			}
		}
	}
	.btn-group {
		display: flex;
		gap: 7px;
		justify-content: space-between;
		align-items: center;
		margin-top: 12px;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 10px 15px;
		background-color: #fff;
		&:deep(.van-button) {
			border-radius: 5px;
			height: 40px;
			flex: 1;
			// width: 169px;
		}

		&:deep(.size-color) {
			color: #0052cc !important;
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
