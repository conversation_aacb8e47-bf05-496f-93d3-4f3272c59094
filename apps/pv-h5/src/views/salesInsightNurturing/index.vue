<template>
	<div id="ap-sales-insight-nurturing" class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div
				@click="goBack"
				:style="{
					visibility: route?.query?.isHeaderBackShow === 'false' ? 'hidden' : '',
				}"
				class="mc-header-icon"
			>
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ agentName }}</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 类型 -->
			<div class="mc-content-cotainer">
				<div class="header">
					<div class="header__logo">
						<img src="@/assets/img/xsdc.png" alt="" />
					</div>
					<div class="header__info">
						<div class="header__name">{{ agentName }}</div>
						<div class="header__desc">持续调整和优化问题及KPI，培养更智能的销售洞察</div>
					</div>
					<!-- <div class="header__icon" @click="goSetting()">
						<img src="@/assets/img/demo/salesInsightNurturing/settings.png" alt="" />
					</div> -->
				</div>
				<div class="info">您好，我是您的{{ agentName }}智能体，您可以在线查看和维护销售、DDI、准入、市场、拜访的相关指标。也可以在线进行异常问题的确认和补充！</div>
				<div class="count">
					<div class="item">
						<div class="item__info">
							<span class="item__num">{{ total.query_count }}</span>
							<span>个</span>
						</div>
						<div class="item__title">问题数</div>
					</div>
					<div class="item" @click="showToastAccuracy">
						<div class="item__info">
							<span class="item__num">{{ (total.accuracy_rate * 100).toFixed(1) }}</span>
							<span>%</span>
						</div>
						<div class="item__title">准确度<img src="@/assets/img/demo/salesInsightNurturing/info.png" alt="" /></div>
					</div>
					<div class="item" @click="showToastResponse">
						<div class="item__info">
							<span class="item__num">{{ (total.response_rate * 100).toFixed(1) }}</span>
							<span>%</span>
						</div>
						<div class="item__title">回答率<img src="@/assets/img/demo/salesInsightNurturing/info.png" alt="" /></div>
					</div>
					<div class="item" @click="goQList()">
						<div class="item__info">
							<span class="item__num">{{ total.no_answer_count + total.feedback_count }}</span>
							<span> 个</span>
						</div>
						<div class="item__title item__title--link">待处理</div>
					</div>
				</div>
				<div class="list">
					<div class="item" v-for="item in cardList" :key="item.id" @click="goList(item)">
						<div class="item__info">
							<span class="item__num"> {{ item.count }} </span>
							<span> 张</span>
						</div>
						<div class="item__title">
							{{ item.name }}
							<img src="@/assets/img/demo/salesInsightNurturing/arrow.png" alt="" />
						</div>
					</div>
					<!-- <div class="item" @click="goRecommend()">
						<div class="item__info">
							<span class="item__num"> {{ total.pending_total }} </span>
							<span>个</span>
						</div>
						<div class="item__title">
							已处理需求
							<img src="@/assets/img/demo/salesInsightNurturing/arrow.png" alt="" />
						</div>
					</div> -->
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { getKnowledgeStatistics, getKnowledgeTotal, getKnowledgeTotalSimple } from '@/api/knowledge';
import { componentSearch, getComponentCategoryCount, getScopeList } from '@/api/nurture';
import { showDialog, showToast } from 'vant';
import { getAgentListApi } from '@/api/agent-tools';
import usePremissionStore from '@/store/modules/premission';
const router = useRouter();
const route = useRoute();
const agentInfo = ref({});
const agentName = ref('');

const goBack = () => {
	router.go(-1);
};
const goSetting = () => {
	router.push({
		path: '/salesInsightNurturing/setting',
		query: { isTabBar: route.query.isTabBar },
	});
};
const goList = (item) => {
	router.push({
		path: '/salesInsightNurturing/list',
		query: {
			id: item.id,
			name: item.name,
			isTabBar: route.query.isTabBar,
			agent_id: route.query.agent_id,
		},
	});
};
const goCList = () => {
	router.push({
		path: '/salesInsightNurturing/c-list',
		query: { isTabBar: route.query.isTabBar, agent_id: route.query.agent_id },
	});
};
const goQList = () => {
	router.push({
		path: '/salesInsightNurturing/q-list',
		query: { isTabBar: route.query.isTabBar, agent_id: route.query.agent_id },
	});
};
const goRecommend = () => {
	router.push({
		path: '/salesInsightNurturing/recommend-list',
		query: { isTabBar: route.query.isTabBar, agent_id: route.query.agent_id },
	});
};
const cardList = ref({});
const getCardList = async () => {
	const res = await getScopeList();
	for (const item of res.result) {
		cardList.value[item.id] = { ...item, count: 0 };
	}
};
const getCardNum = () => {
	getComponentCategoryCount({
		type: 'card',
	}).then((res) => {
		cardList.value = res.result.scopes;
	});
};
const total = ref({
	pending_total: 0,
	accuracy_rate: 0,
	response_rate: 0,
	no_answer_count: 0,
	feedback_count: 0,
});
const getTotal = () => {
	getKnowledgeStatistics({
		// agent_id: agentInfo.value?.attributes?.agent,
	}).then((res) => {
		total.value = Object.assign(total.value, res.data);
	});
};
const showToastAccuracy = () => {
	showDialog({
		title: '准确度',
		message: `准确度=1-((用户点踩的问题数+未回答的问题数)÷用户提问总数)`,
	});
};
const showToastResponse = () => {
	showDialog({
		title: '响应率',
		message: `回答率=1-(未回答的问题数÷用户提问总数)`,
	});
};
const { GET_USER_AGENT } = usePremissionStore();

onMounted(() => {
	GET_USER_AGENT().then((res) => {
		const agentList = res;
		for (const ele of agentList) {
			if (String(ele.id) === String(route.query.agent_id)) {
				agentName.value = ele.name;
				agentInfo.value = ele;
				console.log(ele);
			}
		}
		getTotal();
		getCardNum();
	});
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/intelligent/salesInsightNurturing/index.scss';
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	background-image: url('@/assets/img/demo/salesInsightNurturing/bg.png');
	background-position: center -20%;
	background-repeat: no-repeat;
	background-size: 100%;
	background-color: #f4f5f7;

	.mc-header {
		width: 100vw;
		display: flex;
		// background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		// border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 0 15px;
			margin-bottom: 20px;
		}
	}
	.header {
		display: flex;
		align-items: center;
		margin-bottom: 13px;
		&__logo {
			width: 50px;
			height: 50px;
			box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.05);
			background-color: #fff;
			text-align: center;
			line-height: 50px;
			margin-right: 15px;
			border-radius: 50%;
			img {
				max-width: 50px;
				max-height: 50px;
			}
		}
		&__info {
			flex: 1;
			width: 0;
		}
		&__name {
			color: #172b4d;
			font-weight: 500;
			font-size: 15px;
		}
		&__desc {
			color: #6b778c;
			font-weight: 400;
			font-size: 10px;
		}
		&__icon {
			img {
				width: 14px;
			}
		}
	}
	.info {
		background-color: rgba(255, 255, 255, 0.8);
		border-radius: 16px;
		padding: 15px;
		margin-bottom: 15px;
		position: relative;
		&:before {
			content: '';
			border-left: 7px solid transparent;
			border-right: 7px solid transparent;
			border-bottom: 7px solid rgba(255, 255, 255, 0.8);
			position: absolute;
			top: -7px;
			left: 20px;
		}
	}
	.count {
		border-radius: 6px;
		background-color: #fff;
		display: flex;
		padding: 15px;
		margin-bottom: 15px;
		.item {
			text-align: center;
			color: #172b4d;
			flex: 1;
			position: relative;
			&__info {
				font-weight: 500;
				font-size: 12px;
			}
			&__num {
				font-size: 20px;
			}
			&__title {
				font-weight: 400;
				font-size: 12px;
				&--link {
					color: #0052cc;
				}
				img {
					width: 12px;
					vertical-align: -1px;
					margin-left: 2px;
				}
			}
			&:after {
				content: '';
				width: 1px;
				height: 30px;
				background-color: #f0f2f5;
				position: absolute;
				top: 50%;
				right: 0;
				transform: translateY(-50%);
			}
			&:last-child {
				&:after {
					display: none;
				}
			}
		}
	}
	.list {
		display: flex;
		gap: 15px;
		flex-wrap: wrap;
		.item {
			// width: 50%;
			flex: 0 0 calc(50% - 7.5px);
			background-color: #fff;
			border-radius: 10px;
			padding: 15px;
			position: relative;
			box-sizing: border-box;
			&__info {
				font-weight: 500;
				font-size: 12px;
			}
			&__num {
				font-size: 20px;
			}
			&__title {
				font-weight: 400;
				font-size: 12px;
				img {
					float: right;
					width: 5px;
				}
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
