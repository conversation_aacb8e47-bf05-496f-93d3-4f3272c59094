<template>
	<div id="ap-sales-recommend" class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">智能建议</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon" @click="goAdd">新建</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 类型 -->
			<van-tabs v-model:active="currentIndex">
				<van-tab v-for="item in navList" :key="item.id" :title="item.name" :name="item.id"></van-tab>
			</van-tabs>
			<div class="mc-content-cotainer">
				<template v-if="currentIndex">
					<div class="list" v-if="showList.length">
						<div class="item" v-for="item in showList" :key="item" @click="goCard(item)">
							<div class="item__thumb">
								<img :src="item.thumbnail" alt="" />
							</div>
							<div class="item__info">
								<div class="item__title van-ellipsis">{{ item.name }}</div>
								<div class="item__desc van-multi-ellipsis--l3">{{ item.description }}</div>
							</div>
						</div>
					</div>
					<div class="empty" v-else>
						<img src="@/assets/img/demo/salesInsightNurturing/empty.png" alt="" />
						暂无数据或权限
					</div>
				</template>
				<template v-else>
					<div class="list" v-if="getLastChat.recommedCards?.length">
						<div class="item" v-for="item in getLastChat.recommedCards" :key="item" @click="goCard(item)">
							<div class="item__thumb">
								<img :src="item.previewUrl" alt="" />
							</div>
							<div class="item__info">
								<div class="item__title van-ellipsis">{{ item.name }}</div>
								<div class="item__desc van-multi-ellipsis--l3">{{ item.description }}</div>
							</div>
						</div>
					</div>
					<div class="empty" v-else>
						<img src="@/assets/img/demo/salesInsightNurturing/empty.png" alt="" />
						暂无数据或权限，请重新筛选或提问。
					</div>
				</template>
			</div>
		</div>
	</div>
</template>
<script setup>
import { componentSearch } from '@/api/nurture';
import useUserStore from '@/store/modules/user';
import { formatParamsDates, uuid } from '@/utils';
import { showToast } from 'vant';
import usefilterStore from '@/store/modules/filter';
const filterStore = usefilterStore();
import useEnterStore from '@/store/modules/enterprise';
import { getAgentListApi } from '@/api/agent-tools';

const enterStore = useEnterStore();
const router = useRouter();
const route = useRoute();
const goBack = () => {
	router.go(-1);
};
// 语言环境
const language = computed(() => {
	return filterStore.language === 'zh' ? '中文' : '英文';
});
const goAdd = () => {
	router.push({
		path: '/salesInsightNurturing/new-recommend',
		query: {
			agent_id: route.query.agent_id,
			isTabBar: route.query.isTabBar || '',
		},
	});
};
const goCard = (item) => {
	router.replace({
		path: '/salesInsightNurturing/detail',
		query: {
			id: item.id,
			mId: route.query.id,
			q: route.query.q || undefined,
			agent_id: route.query.agent_id,
			q1: route.query.q1 || undefined,
		},
	});
};
const userStore = useUserStore();
// 会话ID
const chatId1 = 'xspc' + userStore.userInfo.username;
let appId = '';
let appKey = '';

const fetchRequest = () => {
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async (resolve) => {
		try {
			toast = showToast({
				type: 'loading',
				duration: 0,
				message: '正在解析',
				overlay: true,
			});
			let controller = new AbortController();
			// controllerList.push(controller);
			const { signal } = controller;
			let radom1 = uuid();
			let radom2 = uuid();
			let url = enterStore.enterInfo.agentAddress;
			let res = await fetch(`${url}/api/v1/chat/completions`, {
				signal,
				method: 'POST',
				headers: {
					'Content-Type': 'application/json;charset=utf-8',
					Authorization: appKey,
				},
				body: JSON.stringify({
					appId,
					chatId: chatId1,
					stream: true,
					detail: true,
					variables: {
						language: language.value,
					},
					messages: [{ role: 'user', content: JSON.stringify({ message: { type: 'cardMore', Question: route.query.q }, role: 'ADMIN' }), dataId: radom1 }],
				}),
			});
			if (!res?.body || !res?.ok) {
				throw new Error('Request Error');
			}
			// resolve(res);
			handleStream(res, 'MoreCards');
		} catch (error) {
			console.log(error, 'xxxxxxxxxxxxxxxxxxxxxxx');
		}
	});
};
const getLastChat = ref({
	loading: true,
	temRecommedCards: '',
	recommedCards: [],
});
let toast = null;
const handleStream = (res, type) => {
	let fixedVariable = {
		Suggestion: ['temQuestionList', 'questionList'],
		NextAction: ['temNextAction', 'nextAction'],
		MoreCards: ['temRecommedCards', 'recommedCards'],
	};
	const reader = res.body?.getReader();
	const decoder = new TextDecoder('utf-8');
	let buffer = '';
	function processStreamResult(result) {
		const chunk = decoder.decode(result.value, { stream: !result.done });
		buffer += chunk;
		// 逐条解析后端返回数据
		const lines = buffer.split('\n');
		buffer = lines.pop();
		lines.forEach((line) => {
			if (line.trim().length === 0) return;
			// 错误处理
			if (line.includes('event: error')) {
				if (type === 'cardOrMsg') {
					getLastChat.value.msg = '当前系统繁忙，请稍后再试。';
					isLoading.value = false;
					getLastChat.value.isLoading = false;
					getLastChat.value.loading = false;
					return;
				}
			}

			if (line.indexOf('data:') === -1 || line.split('data:')[1] === ' [DONE]') return;
			const resData = JSON.parse(line.split('data:')[1]);
			if (resData.name === 'AI 对话-每日业绩播报') {
				getLastChat.value.isLoading = false;
			}
			if (resData.isDateValid === '0') {
				getLastChat.value.deleteTime = true;
			}
			if (resData.choices && resData.choices[0].delta.content) {
				const text = resData.choices[0].delta.content;
				console.log(text);
				if (type === 'Summary') {
					getLastChat.value.summary += text;
				} else if (type === 'cardOrMsg') {
					getLastChat.value.msg += text;
					if (getLastChat.value.msg.includes('cardCom')) {
						getLastChat.value.isIframe = true;
						getLastChat.value.summaryLoading = true; //加载summary
						//获取更多卡片
						getLastChat.value.getMoreCards = true;
					}
					if (getLastChat.value.msg.includes('vueCom')) {
						getLastChat.value.isVueCom = true;
						getLastChat.value.summaryLoading = true; //加载summary
						//获取更多卡片
						getLastChat.value.getMoreCards = true;
					}
				} else if (type === 'MoreCards') {
					console.log(getLastChat.value.temRecommedCards, '=======================');
					getLastChat.value.temRecommedCards += text;
					console.log(getLastChat.value.temRecommedCards, '1=======================1', text);
				} else {
					getLastChat.value[fixedVariable[type][0]] += text;
				}
			}
		});
		if (!result.done) {
			return reader.read().then(processStreamResult);
		} else {
			if (type === 'Summary') {
				//如果时summary
				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value.summary);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
				getLastChat.value.summaryLoading = false;
				isLoading.value = false;
				getLastChat.value.loading = false;

				// 命中卡片的情况, 分析结束后同步信息
				asyncChatLog(getLastUser(), getLastChat.value.summary, getLastChat.value, 'QA');
			} else if (type === 'cardOrMsg') {
				if (getLastChat.value.isIframe) {
					// 去除开头和结尾的 ```json 标记
					let trimmedText = getLastChat.value.msg
						.trim()
						.replace(/^```json\s+/, '')
						.replace(/\s+```$/, '');
					// 解析 JSON 字符串为 JavaScript 对象或数组
					let jsonData = JSON.parse(trimmedText);
					console.log(JSON.parse(JSON.stringify(JSON.parse(trimmedText))), '卡片元数据');
					jsonData = formatParamsDates(jsonData);

					//isDateValid判断是否有日期没有日期则删除大模型返回的日期
					if (getLastChat.value.deleteTime) {
						delete jsonData[0].params.startTime;
						delete jsonData[0].params.endTime;
					}
					console.log(jsonData, '格式化每个卡片的日期');
					// 提取 params 和 QA
					const { params, url } = jsonData[0];
					// 构建查询字符串
					let queryString = '';
					if (params) {
						queryString = Object.entries(params)
							.map(([key, value]) => `${key}=${value}`)
							.join('&');
					}
					getLastChat.value.msg = jsonData;
					// getLastChat.value.recommedCards = jsonData.slice(1);
					// console.log('推荐卡片<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
					// console.log(getLastChat.value.recommedCards);
					// console.log('推荐卡片>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
					getLastChat.value.iframeUrl = url + '?' + queryString;
					getLastChat.value.url = url;
					getLastChat.value.isLoading = false;
					//如果有参数是无就不解读
					for (let item of getLastChat.value.msg) {
						for (let i in item.params) {
							if (item.params[i] === '无') {
								getLastChat.value.summaryLoading = false;
								isLoading.value = false;
								getLastChat.value.loading = false;
							}
						}
					}
				} else if (getLastChat.value.isVueCom) {
					// 去除开头和结尾的 ```json 标记
					let trimmedText = getLastChat.value.msg
						.trim()
						.replace(/^```json\s+/, '')
						.replace(/\s+```$/, '');
					// 解析 JSON 字符串为 JavaScript 对象或数组
					let jsonData = JSON.parse(trimmedText);
					console.log(JSON.parse(JSON.stringify(JSON.parse(trimmedText))), '卡片元数据');
					jsonData = formatParamsDates(jsonData);

					//isDateValid判断是否有日期没有日期则删除大模型返回的日期
					if (getLastChat.value.deleteTime) {
						delete jsonData[0].params.startTime;
						delete jsonData[0].params.endTime;
						const foundElementIndex = jsonData[0].params.findIndex((ele) => ele.column === 'pv_ads_sales_local_date');
						jsonData[0].params.splice(foundElementIndex, 1);
					}
					console.log(jsonData, '格式化每个卡片的日期');
					// 提取 params 和 QA
					const { params, vueComName } = jsonData[0];

					getLastChat.value.vueComName = vueComName;

					if (!params.startTime && !params.endTime && vueComName !== 'supersetCard') {
						params.startTime = cardsInfoList.value.find((ele) => ele.reportCd === vueComName).startTime;
						params.endTime = cardsInfoList.value.find((ele) => ele.reportCd === vueComName).endTime;
					}

					getLastChat.value.cardInfo = vueComName !== 'supersetCard' ? params : jsonData[0];
					getLastChat.value.isLoading = false;
					//如果有参数是无就不解读
					for (let item of getLastChat.value.msg) {
						for (let i in item.params) {
							if (item.params[i] === '无') {
								getLastChat.value.summaryLoading = false;
								isLoading.value = false;
								getLastChat.value.loading = false;
							}
						}
					}
				} else if (getLastChat.value.msg.includes('```json') && getLastChat.value.msg.includes('[]')) {
					// 查询卡片为空的情况
					getLastChat.value.isLoading = false;
					getLastChat.value.loading = false;
					getLastChat.value.msg = '当前找不到合适的数据来回答您的问题。以下是为您推荐的其他可用分析：';
					getLastChat.value.noCardFound = true;
					isLoading.value = false;
					stopFetch();

					// 没有命中卡片的情况, 直接同步绘画信息
					asyncChatLog(getLastUser(), getLastChat.value.msg, getLastChat.value, 'AI');
					// let ar = chatList.value.filter((ele) => ele.type === 'user');
					// let msg = ar?.[ar.length - 1]?.msg;
					// setTextMessage({ type: 'cardComSummary', Question: msg });
				} else {
					// 字段afterStopAutoRead等于true说明是每日播报，需要缓存到本地
					if (getLastChat.value.afterStopAutoRead) proxy.$cache.local.setExpirseJSON('scriptAssistantReport', getLastChat.value.msg, 1);
					nextTick(() => {
						if (getLastChat.value.afterStopAutoRead && isGlobalVoice.value) {
							getLastChat.value.voiceStatus = false;
							voiceEv({
								id: getLastChat.value.id,
								vType: 'play',
							});
						}
					});

					isLoading.value = false;
					getLastChat.value.loading = false;
				}
				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value.msg);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
			} else if (type === 'MoreCards') {
				// 去除开头和结尾的 ```json 标记
				let trimmedText = getLastChat.value.temRecommedCards
					.trim()
					.replace(/^```json\s+/, '')
					.replace(/\s+```$/, '');
				console.log(trimmedText, getLastChat.value.temRecommedCards);
				// 解析 JSON 字符串为 JavaScript 对象或数组
				let jsonData = JSON.parse(trimmedText);
				console.log(JSON.parse(JSON.stringify(JSON.parse(trimmedText))), '更多卡片元数据');
				jsonData = formatParamsDates(jsonData);

				//isDateValid判断是否有日期没有日期则删除大模型返回的日期
				if (getLastChat.value.deleteTime) {
					jsonData.forEach((ele) => {
						delete ele.params?.startTime;
						delete ele.params?.endTime;
					});
				}
				console.log(jsonData, '格式化更多每个卡片的日期');
				jsonData.forEach((ele) => {
					if (ele.id !== getLastChat.value.msg?.[0].id) {
						getLastChat.value.recommedCards.push(ele);
					}
				});
				console.log(getLastChat.value.recommedCards, '去重后更多卡片的数据');
			} else {
				//如果时猜你想问和下一步行动
				// 去除开头和结尾的 ```json 标记
				let trimmedText = getLastChat.value[fixedVariable[type][0]]
					.trim()
					.replace(/^```json\s+/, '')
					.replace(/\s+```$/, '');
				getLastChat.value[fixedVariable[type][1]] = JSON.parse(trimmedText).data;
				// 字段afterStopAutoRead等于true说明是每日播报，需要把下一步计划缓存到本地
				// if (getLastChat.value.afterStopAutoRead && fixedVariable[type][1] === 'nextAction') {
				//   proxy.$cache.local.setExpirseJSON('scriptAssistantNextAction', getLastChat.value[fixedVariable[type][1]], 1);
				// }
				// 字段afterStopAutoRead等于true说明是每日播报，需要把猜你想问缓存到本地
				if (getLastChat.value.afterStopAutoRead && fixedVariable[type][1] === 'questionList') {
					proxy.$cache.local.setExpirseJSON('scriptAssistantQuestionList', getLastChat.value[fixedVariable[type][1]], 1);
				}
				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value[fixedVariable[type][1]], type);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
			}
			toast?.close();
			toast = null;
		}
	}
	reader.read().then(processStreamResult);
};
const navList = ref([{ name: '推荐', id: '' }]);
const currentIndex = ref('');
const search = ref('');
const loading = ref(false);
const listData = ref([]);
const showList = computed(() => {
	const arr = [];
	listData.value.forEach((item) => {
		let show = true;
		if (search.value && !item.name.includes(search.value)) {
			show = false;
		}
		if (show && currentIndex.value && Number(item.attributes.businessArea) !== currentIndex.value) {
			show = false;
		}
		if (show) {
			arr.push(item);
		}
	});
	return arr;
});
const getNavList = () => {
	componentSearch({ type: 'businessArea', status: true, searchAttributes: false }).then((res) => {
		navList.value = [{ name: '全部', id: '' }, ...res.result.content];
	});
};
const getList = () => {
	loading.value = true;
	componentSearch({ type: 'card' })
		.then((res) => {
			listData.value = res.result.content;
		})
		.finally(() => {
			loading.value = false;
		});
};
onMounted(() => {
	getAgentListApi().then((res) => {
		const agentList = res.result.content || [];
		for (const ele of agentList) {
			if (ele.attributes.url.indexOf('salesInsightNurturing') > -1) {
				appId = ele.attributes.appID;
				appKey = ele.attributes.appKey;
				break;
			}
		}
		fetchRequest();
		getNavList();
		getList();
	});
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/intelligent/salesInsightNurturing/recommend.scss';
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		width: 100vw;
		display: flex;
		background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
			&:last-child {
				color: #0052cc;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 0 15px;
			margin-bottom: 20px;
		}
	}
	.empty {
		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: #6b778c;
		font-weight: 400;
		font-size: 10px;
		margin-top: 150px;
		img {
			width: 170px;
		}
	}
	.list {
		.item {
			display: flex;
			border-bottom: 1px solid #dfe1e6;
			margin: 15px 0;
			padding-bottom: 15px;
			align-items: center;
			&__thumb {
				width: 165px;
				height: 106px;
				margin-right: 15px;
				img {
					width: 100%;
					height: 100%;
				}
			}
			&__info {
				flex: 1;
				width: 0;
			}
			&__title {
				color: #172b4d;
				font-weight: 400;
				font-size: 13px;
				margin-bottom: 10px;
			}
			&__desc {
				color: #6b778c;
				font-weight: 400;
				font-size: 10px;
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
