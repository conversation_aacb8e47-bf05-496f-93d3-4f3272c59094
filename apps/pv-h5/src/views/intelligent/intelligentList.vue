<template>
	<div class="intelligent">
		<div class="intelligent-nav" ref="navListRef">
			<div class="intelligent-nav-item" :class="{ active: item === currentIndex }" v-for="(item, index) in navList" :key="index" @click="changeNav(item, index, $event)">{{ item }}</div>
		</div>
		<div class="intelligent-list-item" v-for="(item, index) in chatList" :key="index" :style="{ background: item.bgc }" :class="{ 'intelligent-list-isShow': !item.permission }" @click="nativeAia(item)">
			<div class="intelligent-list-item-left">
				<img :src="item.img" alt="" />
			</div>
			<div class="intelligent-list-item-right">
				<div>{{ item.name }}</div>
				<div>{{ item.desc }}</div>
			</div>
			<div class="intelligent-list-item-btn">
				<img v-show="!item.add" src="@/assets/img/add.png" alt="" @click.stop="addMsg(item)" />
				<img v-show="item.add" src="@/assets/img/gou.png" alt="" @click.stop="addMsg(item)" />
			</div>
		</div>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import khdc from '@/assets/img/khdc.png';
import zcdc from '@/assets/img/zcdc.png';
import cpdc from '@/assets/img/cpdc.png';
import xsdc from '@/assets/img/xsdc.png';
import rbzb from '@/assets/img/rbzb.png';
import dkdc from '@/assets/img/dkdc.png';
import xsyc from '@/assets/img/xsyc.png';
import hszs from '@/assets/img/hszs.png';
import bfjh from '@/assets/img/bfjh.png';
import group1 from '@/assets/img/group-1.png';
import group2 from '@/assets/img/group-2.png';
import yxpl from '@/assets/img/yxpl.png';
import jydc from '@/assets/img/jydc.png';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
let currentIndex = ref(t('chat.all'));
const navListRef = ref(null);

import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
const userId = userStore.userInfo.username;
const router = useRouter();
const route = useRoute();

let changeNav = (item, index, e) => {
	let oldIndex = navList.value.findIndex((ele) => ele === currentIndex.value);
	console.log(oldIndex, index);
	currentIndex.value = item;

	const rect = e.target.getBoundingClientRect();
	// if (rect.left + rect.width > (window.innerWidth - 300 || document.documentElement.clientWidth - 300) || rect.left < 0) {

	if (index > oldIndex) {
		console.log(rect.left + '|' + window.innerWidth * 0.7);
		if (rect.left + rect.width > window.innerWidth * 0.7) {
			navListRef.value.scrollTo({
				left: navListRef.value.scrollLeft + 200,
				behavior: 'smooth', // 可选，以平滑动画过渡
			});
		}
	} else {
		console.log(rect.left + rect.width + '|' + window.innerWidth * 0.35);

		if (rect.left + rect.width < window.innerWidth * 0.35) {
			navListRef.value.scrollTo({
				left: -200,
				behavior: 'smooth', // 可选，以平滑动画过渡
			});
		}
	}

	// navListRef.value.scrollTo({
	// 	left: navListRef.value.scrollLeft + (rect.left < 0 ? rect.left - 100 : rect.left + rect.width - window.innerWidth + 100),
	// 	behavior: 'smooth', // 可选，以平滑动画过渡
	// });
	// }
};
let allItem = ref([
	{ type: `${t('chat.all')},${t('chat.market')},${t('chat.saleas')}`, img: khdc, name: t('chat.salesCustomerInsight'), value: '销售智能拜访', desc: t('chat.salesCustomerInsightDesc'), add: false, permission: ['msl_demo'].includes(userId) ? false : true },
	{
		type: `${t('chat.all')},${t('chat.market')},${t('chat.medicine')}`,
		img: khdc,
		name: t('chat.medicalCustomerInsight'),
		value: '医学智能拜访',
		desc: t('chat.medicalCustomerInsightDesc'),
		add: false,
		permission: ['demo02', 'demo01', 'democui', 'demo04', 'demo04mr', 'bud_demo', 'mr_demo'].includes(userId) ? false : true,
	},
	{ type: `${t('chat.all')},${t('chat.saleas')}`, img: xsdc, name: t('chat.scriptAssistant'), value: '销售洞察', desc: t('chat.scriptAssistantDesc'), add: false, permission: ['msl_demo'].includes(userId) ? false : true },
	{ type: `${t('chat.all')},SFE,HR,${t('chat.procure')}`, img: zcdc, name: t('chat.policyInsights'), value: '公司政策查询', desc: t('chat.policyInsightsDesc'), add: false, permission: ['demo02', 'demo01', 'democui', 'demo04', 'demo04mr', 'bud_demo', 'msl_demo'].includes(userId) ? false : true },
	{ type: `${t('chat.all')},${t('chat.market')}`, img: cpdc, name: t('chat.productKnowledgQuery'), value: '产品与医学知识查询', desc: t('chat.productKnowledgQueryDesc'), add: false, permission: ['demo02', 'demo01', 'democui', 'demo04', 'demo04mr', 'bud_demo'].includes(userId) ? false : true },
	{
		type: `${t('chat.all')},SFE,${t('chat.saleas')}`,
		img: xsyc,
		name: t('chat.salesForecast'),
		value: '销售预测',
		desc: t('chat.salesForecastDesc'),
		add: false,
		permission: ['demo02', 'demo01', 'democui', 'demo04', 'demo04mr', 'bud_demo', 'mr_demo', 'rsm_demo', 'msl_demo', 'ucb', 'mr_ucb'].includes(userId) ? false : true,
	},
	// { type: '全部,医学', img: bfjh, name: '医学知识查询', desc: '您关心的医学知识，这里应有尽有', add: false, permission: ['demo02', 'demo01', 'democui', 'demo04', 'demo04mr', 'bud_demo', 'mr_demo', 'rsm_demo'].includes(userId) ? false : true },
	{
		type: `${t('chat.all')},${t('chat.medicine')}`,
		img: yxpl,
		name: t('chat.medicalIntelligenceAccompanyingPractice'),
		value: '医学智能陪练',
		desc: t('chat.medicalIntelligenceAccompanyingPracticeDesc'),
		add: false,
		permission: ['demo02', 'demo01', 'democui', 'demo04', 'demo04mr', 'bud_demo', 'mr_demo', 'rsm_demo', 'msl_demo', 'ucb', 'mr_ucb'].includes(userId) ? false : true,
	},
	{
		type: `${t('chat.all')},${t('chat.market')}`,
		img: hszs,
		name: t('chat.cloundAssistant'),
		value: '智能拜访话术',
		desc: t('chat.cloundAssistantDesc'),
		add: false,
		permission: ['demo02', 'demo01', 'democui', 'demo04', 'demo04mr', 'bud_demo', 'msl_demo', 'ucb', 'mr_ucb'].includes(userId) ? false : true,
	},
	{
		type: `${t('chat.all')},${t('chat.market')}`,
		img: group1,
		name: t('chat.medicineAgent'),
		value: '医保与准入政策洞察',
		desc: t('chat.medicineAgentDesc'),
		add: false,
		permission: ['demo02', 'demo01', 'democui', 'demo04', 'demo04mr', 'bud_demo', 'msl_demo', 'ucb', 'mr_ucb'].includes(userId) ? false : true,
	},
	{
		type: `${t('chat.all')},${t('chat.market')}`,
		img: group2,
		name: t('chat.insurancePayment'),
		value: '医保支付政策洞察',
		desc: t('chat.insurancePaymentDesc'),
		add: false,
		permission: ['demo02', 'demo01', 'democui', 'demo04', 'demo04mr', 'bud_demo', 'mr_demo', 'rsm_demo', 'msl_demo', 'ucb', 'mr_ucb'].includes(userId) ? false : true,
	},
	{
		type: `${t('chat.all')},${t('chat.finance')}`,
		img: jydc,
		name: t('chat.businessInsights'),
		value: '经营分析',
		desc: t('chat.businessInsightsDesc'),
		add: false,
		permission: ['demo02', 'demo01', 'democui', 'demo04', 'demo04mr', 'bud_demo', 'mr_demo', 'rsm_demo', 'msl_demo', 'ucb', 'mr_ucb'].includes(userId) ? false : true,
	},
	{
		type: `${t('chat.all')},${t('chat.saleas')}`,
		img: dkdc,
		name: t('chat.dkActivity'),
		value: '大咖洞察',
		desc: t('chat.dkActivityDesc'),
		route: 'dkActivity',
		permission: ['demo02', 'demo01', 'democui', 'demo04', 'demo04mr', 'bud_demo', 'mr_demo', 'rsm_demo', 'msl_demo', 'ucb', 'mr_ucb'].includes(userId) ? false : true,
	},
]);
let navList = ref([t('chat.all')]);
// 生成 navList
allItem.value.forEach((item) => {
	// 有权限
	if (item.permission) navList.value = navList.value.concat(item.type.split(','));
});
// 对 navList 进行去重
navList.value = [...new Set(navList.value)];

let chatList = computed(() => {
	return allItem.value.filter((ele) => ele.type.includes(currentIndex.value));
});

const nativeAia = ({ value }) => {
	const routes = {
		公司政策查询: 'policyInsights',
		产品与医学知识查询: 'productKnowledgQuery',
		销售洞察: 'scriptAssistant1',
		销售智能拜访: 'customerInsight',
		医学智能拜访: 'customerInsight',
		// '客户洞察2.0': 'customerInsight1',
		智能拜访话术: 'cloundAssistant',
		医保与准入政策洞察: 'medicineAgent',
		医保支付政策洞察: 'insurancePayment',
		医学智能陪练: 'medicalIntelligenceAccompanyingPractice',
		大咖洞察: 'dkActivity',
		经营分析: 'businessInsights',
	};

	if (value === '销售预测') {
		showToast({
			position: 'top',
			message: '敬请期待',
		});
	} else if (routes[value]) {
		const params =
			value === '销售智能拜访'
				? {
						kType: 'sales',
				  }
				: value === '医学智能拜访'
				? { kType: 'medicine' }
				: {};
		router.push({
			name: routes[value],
			query: {
				isTabBar: route.query.isTabBar || '',
				...params,
			},
		});
	}
};
const emit = defineEmits(['sendMsg']);
let addMsg = (item) => {
	item.add = !item.add;
	emit('sendMsg', allItem.value.filter((ele) => ele.add).length);
};
</script>
<style lang="scss" scoped>
.intelligent {
	&-nav {
		display: flex;
		flex-wrap: nowrap;
		padding-left: 20px;
		margin-top: 10px;
		width: 100%;
		overflow-x: scroll;
		scrollbar-width: none; /* firefox */
		-ms-overflow-style: none; /* IE 10+ */
		&::-webkit-scrollbar {
			display: none; /* Chrome Safari */
		}

		&-item {
			text-align: center;
			width: 70px;
			height: 35px;
			line-height: 35px;
			border-radius: 35px;
			background-color: #f4f5f7;
			margin-right: 15px;
			flex-shrink: 0;
			color: #172b4d;
			font-size: 15px;
			transition: all 0.2s;
		}
		.active {
			background-color: #0052cc;
			color: #fff;
		}
	}
	&-list {
		&-item {
			display: flex;
			align-items: center;
			padding: 12px 20px;
			position: relative;
			&-left {
				margin-right: 12px;
				img {
					width: 55px;
				}
			}
			&-right {
				div:nth-child(1) {
					color: #172b4d;
					font-weight: bold;
					font-size: 16px;
				}
				div:nth-child(2) {
					color: #6b778c;
					font-size: 12px;
					margin-top: 2px;
				}
			}
			&-btn {
				// width: 25px;
				position: absolute;
				right: 15px;
				top: 50%;
				transform: translateY(-50%);
				img {
					width: 25px;
				}
			}
		}

		&-isShow {
			display: none;
		}
	}
}
</style>
