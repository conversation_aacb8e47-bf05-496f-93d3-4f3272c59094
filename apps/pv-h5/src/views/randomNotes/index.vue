<template>
	<div class="rn defaultLayout">
		<!-- 常用应用 -->
		<div class="mc-content-middle__app">
			{{ $t('visit.log') }}
		</div>
		<!-- content -->
		<div class="rn-content">
			<!-- 医生搜索 -->
			<form
				class="rn-content__form"
				:class="{
					'rn-content__app': route?.query?.isHideNav === 'true',
					'rn-content__form__active': !isShow,
				}"
				v-if="loadingStatus !== 'loading'"
				action="/"
			>
				<van-search @search="onSearch" :clearable="false" v-model="search" :placeholder="$t('visit.doctorName')"> </van-search>
				<img
					class="rn-content__img"
					:class="{
						'rn-content__app-img': route?.query?.isHideNav === 'true',
						'rn-content__img__active': !isShow,
					}"
					src="@/assets/img/search.png"
					alt=""
				/>
			</form>
			<!-- 记录日志 -->
			<div v-if="loadingStatus === 'finished'" class="rn-content__log">
				<div v-for="(item, index) in visitedList" :key="index" class="report-card">
					<!-- 日期 -->
					<p class="report-card__date">{{ item.date }}</p>
					<!-- 拜访内容 -->
					<div class="report-card__content">
						<div v-for="(s, index) in item.data" :key="index" @click="editVisited(s)" class="content-item">
							<van-swipe-cell>
								<div class="content-item__title">
									<span class="title-name">{{ s.doctorName }}</span>
									<span class="title-text">{{ s.doctorTitle }}</span>
									<img v-if="s.typeKinds === $t('visit.medicalInquiry')" src="@/assets/img/ll-1.png" />
									<img v-else-if="s.typeKinds === $t('visit.notes')" src="@/assets/img/ll-2.png" />
									<img v-else-if="s.typeKinds === $t('visit.visit')" src="@/assets/img/ll-4.png" />
									<img v-else-if="s.typeKinds === $t('visit.visitColl')" src="@/assets/img/ll-5.png" />
									<img v-else src="@/assets/img/ll-3.png" />
									<span class="title-time">{{ s.visitTime ? s.visitTime.split('T')[0].slice(5, 10).replace('-', '.') : '' }}</span>
								</div>
								<div class="content-item__content ell2">{{ s.memorandum }}</div>
								<template #right>
									<van-button @click="delVisited(s, item)" square text="删除" type="danger" class="delete-button" />
								</template>
							</van-swipe-cell>
						</div>
					</div>
				</div>
			</div>
			<!-- skeleton -->
			<div v-else-if="loadingStatus === 'loading'" class="rn-content__skeleton">
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton title :row="2" />
			</div>
			<van-empty v-else :description="$t('visit.noVisit')" />
		</div>
		<!-- 创建btn -->
		<div class="rn-create">
			<van-button color="#0052cc" @click="createVisited" round type="primary">{{ $t('visit.createLog') }}</van-button>
		</div>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import { findVisitReportList, deleteVisitReport } from '@/api/randomNotes';
const route = useRoute();
const router = useRouter();

const isShow = ref(false);

const search = ref('');

const onSearch = () => {
	visitedList.value = [];
	loadingStatus.value = 'loading';
	init();
};

// 创建拜访记录
const createVisited = () => {
	router.push({
		name: 'mAssistant',
		query: {
			isTabBar: route.query.isTabBar || '',
		},
	});
};

// 删除拜访记录
const delVisited = (s, t) => {
	t.data = t.data.filter((item) => item.id !== s.id);
	if (t.data.length === 0) {
		visitedList.value = visitedList.value.filter((item) => item.date !== t.date);
	}
	if (visitedList.value.length === 0) loadingStatus.value = 'empty';
	showToast({
		position: 'top',
		message: '删除成功',
	});
	deleteVisitReport(s.id);
};

// 修改和查看拜访记录
const editVisited = (item) => {
	router.push({
		name: 'mAssistant',
		query: {
			isTabBar: route.query.isTabBar || '',
			id: item.id,
		},
	});
};

// 拜访列表
const visitedList = ref([]);
// 原有数据列表
const loadingStatus = ref('loading');
const init = () => {
	const json = {
		recordStatus: true,
		dateTimeFormat: 'yyyy.MM',
	};
	if (search.value) {
		json.search = search.value;
	}
	findVisitReportList(json)
		.then((res) => {
			if (res.result.length > 0) {
				visitedList.value = res.result || [];
				loadingStatus.value = 'finished';
			} else {
				loadingStatus.value = 'empty';
			}
		})
		.catch(() => {
			loadingStatus.value = 'empty';
		});
};
onMounted(() => {
	document.querySelector('.defaultLayout').style.backgroundColor = '#f4f5f7';
	init();
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.querySelector('.defaultLayout').removeAttribute('style');
});
</script>
<style lang="scss" scoped>
.rn {
	display: flex;
	flex-direction: column;
	height: 100vh;
	.mc-content-middle__app {
		background-color: #ffffff;
		padding: 7px;
		border-bottom: 1px solid rgb(223, 225, 230);
		font-size: 16px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: var(--var-default-color);
		font-weight: bold;
		.app-header {
			display: flex;
			align-items: center;

			.app-header__img {
				width: 16px;
				height: 16px;
				margin-right: 5.4px;
			}

			.app-header__name {
				font-weight: 500;
			}
		}

		.app-bottom {
			display: flex;
			align-items: flex-start;
			justify-content: flex-start;
			margin-top: 8px;

			.app-bottom__item {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 20%;
				.item-top {
					width: 40px;
					height: 40px;
					border-radius: 6px;
					background-color: #f4f5f7;
					display: flex;
					align-items: center;
					justify-content: center;

					img {
						width: 20px;
						height: 20px;
						object-fit: cover;
					}

					.item-top__img {
						width: 28px;
						height: 28px;
					}
				}

				.item-text {
					font-size: 12px;
					line-height: 1.2;
					margin-top: 8px;
					text-align: center;
				}
			}
		}

		.app-bottom--show {
			display: none;
		}
	}
	.rn-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		background-color: #f4f5f7;
		padding: 0px 15px;
		overflow-y: auto;
		.rn-content__form {
			position: fixed;
			right: 15px;
			top: 120px;
			z-index: 999;
		}
		.rn-content__form__active {
			top: 43px;
		}
		.rn-content__app {
			top: 5px !important;
		}
		.rn-content__img {
			position: fixed;
			right: 30px;
			width: 16px;
			height: 16px;
			top: 128px;
		}
		.rn-content__img__active {
			top: 50px;
		}
		.rn-content__app-img {
			top: 13px;
		}
		&:deep(.van-search) {
			padding: 0;
			border-radius: 30px;
		}
		&:deep(.van-search__content) {
			background-color: #ffffff;
			width: 120px;
			padding: 0 30px 0 15px;
			border-radius: 30px;
			.van-search__field {
				height: 30px;
			}
			.van-field__control {
				color: #172b4d;
			}
			.van-icon {
				color: #333333;
			}

			.van-field__left-icon {
				display: none;
			}
		}

		.rn-content__log {
			display: flex;
			flex-direction: column;

			.report-card {
				width: 100%;
				display: flex;
				flex-direction: column;
				.report-card__date {
					color: #172b4d;
					font-weight: bold;
					font-size: 14px;
					height: 40px;
					line-height: 40px;
					position: sticky;
					top: 0;
					background-color: #f4f5f7;
					z-index: 99;
				}

				.report-card__content {
					background-color: #ffffff;
					margin: 0px 0 8px 0;
					border-radius: 5px;
					display: flex;
					flex-direction: column;
					.content-item {
						border-bottom: 1px solid #dfe1e6;
						display: flex;
						flex-direction: column;
						.content-item__title {
							display: flex;
							align-items: baseline;
							padding: 10px 12px 0;
							.title-name {
								color: #172b4d;
								font-weight: 600;
								margin-right: 5px;
							}
							.title-text {
								margin-right: 5px;
								font-size: 10px;
								color: #172b4d;
							}
							img {
								height: 7px;
								object-fit: cover;
							}

							.title-time {
								color: #6b778c;
								font-size: 10px;
								flex: 1;
								text-align: right;
							}
						}

						.content-item__content {
							color: #6b778c;
							font-size: 12px;
							margin-top: 5px;
							padding: 0px 12px 0px;
						}
						&:deep(.van-swipe-cell__wrapper) {
							padding-bottom: 10px;
						}
						&:deep(.van-swipe-cell__right) {
							text-align: right;
						}
						&:deep(.van-button__content) {
							span {
								width: 24px;
								display: inline-block;
							}
						}
						&:deep(.van-button) {
							height: 100%;
							width: 100%;
							border-bottom-right-radius: 5px;
							border-top-right-radius: 5px;
						}
						&:deep(.van-button__text) {
							font-size: 12px;
						}
					}

					.content-item:last-child {
						border-bottom: none;
						padding-bottom: 0px;
						margin-bottom: 0px;
					}
				}
			}
		}

		.rn-content__skeleton {
			padding: 12px 0;
			background-color: #ffffff;
			border-radius: 6px;
		}
	}

	.rn-create {
		display: flex;
		justify-content: center;
		margin: 12px 0;
		&:deep(button) {
			height: 35px;
			width: 200px;
		}
	}
}
</style>
