<template>
	<div class="discovery" id="ap-discovery">
		<notice-bar :showTime="true"></notice-bar>
		<div class="discovery-nav">
			<div class="discovery-nav-list" ref="navListRef">
				<div class="discovery-nav-list-item" :class="{ active: currentIndex === item.value, hidden: item.isHiddren }" v-for="item in navList" :key="item" @click="navChange(item, $event)">
					{{ item.desc }}
				</div>
			</div>
		</div>
		<div class="list">
			<div v-if="loading">
				<van-skeleton title :row="5" />
				<van-skeleton title :row="5" />
				<van-skeleton title :row="5" />
			</div>
			<div v-else>
				<!-- <businessSalesAnalytics></businessSalesAnalytics>
				<keyProductsAchieved></keyProductsAchieved> -->
				<!-- 全局筛选 -->
				<div class="pd15">
					<globalFilter v-if="currentLabelGolbalFilter?.value === 'true'" :filterConfig="currentLabelGolbalFilterConfig"></globalFilter>
				</div>

				<div v-for="(item, index) in listData" :key="index">
					<component :is="com(item)" :info="item" @focus="focus" :isRecommend="currentIndex === '推荐'"></component>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
// import businessSalesAnalytics from '@/views/observe/compoents/businessSalesAnalytics.vue';
// import keyProductsAchieved from '@/views//observe/compoents/keyProductsAchieved.vue';
import { queryCardByName, focusChange } from '@/api/sales';
import { debounce, findChildrenById } from '@/utils/index';
import { showToast } from 'vant';
import usePremissionStore from '@/store/modules/premission';
import globalFilter from './compoents/cube/globalFilter.vue';
const perStore = usePremissionStore();
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const { proxy } = getCurrentInstance();
let { com } = useCard();
let navList = ref([]);
let currentIndex = ref('');
const navListRef = ref(null);
//页签切换
const navChange = (item, e) => {
	loading.value = true;
	currentIndex.value = item.value;
	const rect = e.target.getBoundingClientRect();
	if (rect.left + rect.width > (window.innerWidth || document.documentElement.clientWidth) || rect.left < 0) {
		navListRef.value.scrollTo({
			left: navListRef.value.scrollLeft + (rect.left < 0 ? rect.left : rect.left + rect.width - window.innerWidth),
			behavior: 'smooth', // 可选，以平滑动画过渡
		});
	}
	listData.value = [];
	getDate();
	proxy.$umeng('点击', '洞察-发现', item.text);
	sessionStorage.setItem('discovery-index', item.text);
	currentLabelGolbalFilter.value = arrList.find((ele) => ele.path === currentIndex.value)?.attributes?.find((ele) => ele.name === 'globalFilter');

	currentLabelGolbalFilterConfig.value = arrList.find((ele) => ele.path === currentIndex.value)?.attributes?.find((ele) => ele.name === 'filterConfig')?.value;
};
//列表数据
let listData = ref([]);
//获取列表
let loading = ref(true);
let getDate = async () => {
	const list = findChildrenById(perStore.discoveryList, currentIndex.value);
	const listArr = [];
	if (list.length > 0) {
		for (const ele of list) {
			if (ele.type === 'CARD') {
				listArr.push(ele.path);
			}
		}
	}
	let res = await queryCardByName(listArr);
	if (res.result.length > 0) {
		// 需要把筛选器的相关信息和报告信息关联在一起
		const arrList = perStore.discoveryList.filter((ele) => ele.path === currentIndex.value)[0].children;
		for (const ele of res.result) {
			const attributes = arrList.find((ite) => ite.path === ele.reportCd)?.attributes || [];
			ele.filterConfig = attributes.find((ite) => ite.name === 'filterConfig')?.value || '';
		}
	}
	listData.value = res.result;
	loading.value = false;
};
const focus = debounce(async ({ id: reportCd, name, isLike }) => {
	let res = await focusChange(reportCd);
	listData.value.find((ele) => ele.reportCd === reportCd).isLike = isLike;
	let tip = isLike ? '关注成功' : '取消关注';
	showToast(tip);
	let navItem = navList.value.filter((item) => item.value === currentIndex.value);
	proxy.$umeng('点击', `洞察-发现-${navItem[0].text}-${name}`, tip);
}, 500);
let route = useRoute();
// 页签
const getPermissionsTabs = () => {
	navList.value = [];
	perStore.discoveryList.forEach((item) =>
		navList.value.push({
			text: item.name,
			desc: t(item.shortName),
			value: item.path,
		})
	);
};
//获取权限树
let arrList = perStore.discoveryList;
function flattenArray(arr) {
	let result = [];
	// 递归函数
	function flatten(item) {
		// 如果有子节点，则递归处理
		if (item.children && item.children.length > 0) {
			item.children.forEach((child) => flatten(child));
		}
		result.push(item); // 将当前节点推入结果数组
	}
	arr.forEach((item) => flatten(item));
	return result;
}
arrList = flattenArray(arrList);
let currentLabelGolbalFilter = ref({});
let currentLabelGolbalFilterConfig = ref({});
currentLabelGolbalFilter.value = arrList.find((ele) => ele.path === currentIndex.value)?.attributes?.find((ele) => ele.name === 'globalFilter');

currentLabelGolbalFilterConfig.value = arrList.find((ele) => ele.path === currentIndex.value)?.attributes?.find((ele) => ele.name === 'filterConfig')?.value;
onMounted(() => {
	// 有权限的页签
	getPermissionsTabs();
	route.query.type ? (currentIndex.value = route.query.type) : (currentIndex.value = navList.value[0].value);
	sessionStorage.setItem('discovery-index', currentIndex.value);
	getDate();
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/observe/discovery.scss';
.discovery {
	&-nav {
		// height: 36px;
		position: sticky;
		top: 0;
		z-index: 11;
		&-list {
			display: flex;
			align-items: center;
			height: 100%;
			overflow-x: scroll;
			width: 100vw;
			// border-bottom: 1px solid #dfe1e6;
			background-color: var(--pv-bgc);
			padding: 7px 0;
			// position: relative;
			&::-webkit-scrollbar {
				height: 1px; /* 宽度 */
			}
			&-item {
				flex: 1;
				font-size: 14px;
				color: var(--pv-no-active-color);
				padding: 0 16px;
				flex-shrink: 0;
				text-align: center;
				white-space: nowrap;
			}
			.active {
				font-weight: 400;
				color: var(--pv-default-color);
				position: relative;
				font-weight: bold;
			}
			.active::after {
				content: '';
				position: absolute;
				z-index: 1;
				background-color: var(--pv-tabbar-active);
				width: 30px;
				height: 3px;
				left: 50%;
				bottom: -7px;
				transform: translateX(-50%);
				border-radius: 2px;
			}

			.hidden {
				display: none;
			}
		}
		&::after {
			content: '';
			position: absolute;
			height: 1px;
			width: 100%;
			background-color: #dfe1e6;
			bottom: 0;
		}
	}
	.list {
		// height: calc(100vh - 35px - var(--nav-height) - 75px);
		width: 100vw;
		// overflow-y: scroll;
		// margin-top: 43px;
	}
	.pd15 {
		padding: 10px 15px 0;
	}
}
</style>
