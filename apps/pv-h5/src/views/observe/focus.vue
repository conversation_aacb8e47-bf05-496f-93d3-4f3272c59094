<template>
	<div class="focus" id="ap-focus">
		<div v-if="loading">
			<van-skeleton title :row="5" />
			<van-skeleton title :row="5" />
			<van-skeleton title :row="5" />
		</div>
		<div v-else>
			<notice-bar></notice-bar>
			<div class="focus-card" v-for="(item, index) in listData" :key="item.reportCd">
				<component :is="com(item)" :info="item" @focus="focus"></component>
				<div v-if="!item.reportCd.includes('-')" class="copy">
					<img class="icon" src="@/assets/img/copy.png" alt="" @click="copy(item, index)" />
				</div>
				<img v-if="item.reportCd.includes('-')" class="icon1" src="@/assets/img/delete.png" alt="" @click="deleted(item, index)" />
			</div>
		</div>
	</div>
</template>
<script setup>
import { queryFollowList, focusChange } from '@/api/sales';
import { debounce, deepClone, generateRandomNumber } from '@/utils/index';
import { showToast, showConfirmDialog } from 'vant';
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
const { proxy } = getCurrentInstance();

const premissionList = computed(() => {
	let list = [];
	perStore.discoveryList.forEach((ele) => {
		if (ele.path !== '推荐' && ele.path !== 'Recommend') {
			for (const item of ele.children) {
				if (item.type === 'CARD') {
					list.push(item);
				}
			}
		}
	});
	return list;
});
let { com } = useCard();
//列表数据
let listData = ref([]);
//获取列表
let loading = ref(true);
let getDate = async () => {
	let res = await queryFollowList({ page: 0, size: 100 });
	// 关联筛选器信息
	for (const ele of res.result.content) {
		const attributes = premissionList.value.find((item) => item.path === ele.path)?.attributes || [];
		ele.filterConfig = attributes.find((ele) => ele.name === 'filterConfig')?.value || '';
	}
	listData.value = res.result.content;
	loading.value = false;
};
const focus = debounce(async ({ id: reportCd, name, isLike }) => {
	const processFocus = async () => {
		await focusChange(reportCd);
		listData.value.find((ele) => ele.reportCd === reportCd).isLike = isLike;
		const tip = isLike ? '关注成功' : '取消关注';
		showToast(tip);
		getDate();
		proxy.$umeng('点击', `洞察-关注-${name}`, tip);
	};
	const length = listData.value.filter((ele) => ele.reportCd.includes(reportCd)).length;
	if (length > 1) {
		await showConfirmDialog({
			title: '提示',
			message: '取消关注会删除当前已复制的卡片',
		});
		await processFocus();
	} else {
		await processFocus();
	}
}, 500);
const copy = debounce(
	(item, index) => {
		let obj = deepClone(item);
		obj.reportCd = obj.reportCd + '-' + generateRandomNumber();
		console.log(obj);
		listData.value.splice(index + 1, 0, obj);
		showToast('复制成功');
		proxy.$umeng('点击', `洞察-关注-${item.nameCn}`, '复制');
	},
	500,
	true
);
const deleted = async (item, index) => {
	await showConfirmDialog({
		title: '提示',
		message: '是否删除',
	});
	listData.value.splice(index, 1);
	showToast('删除成功');
	proxy.$umeng('点击', `洞察-关注-${item.nameCn}`, '删除');
};
onMounted(() => {
	getDate();
});
</script>
<style lang="scss" scoped>
@import '../../style/pc/observe/focus.scss';
.focus-card {
	position: relative;
	.copy {
		position: absolute;
		bottom: 0;
		right: 15px;
		.icon {
			width: 44px;
			height: 44px;
		}
	}
	.icon1 {
		position: absolute;
		bottom: 0;
		right: 15px;
		width: 44px;
		height: 44px;
	}
	&-mask {
		position: absolute;
		right: 20px;
		top: 10px;
		width: 40px;
		height: 30px;
		background-color: red;
	}
}
</style>
