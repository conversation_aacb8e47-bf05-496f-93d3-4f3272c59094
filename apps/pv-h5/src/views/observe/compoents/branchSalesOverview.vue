<template>
	<div class="branch-sales-overview">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span>
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
			<div class="q-box">
				<!-- 时间 -->
				<div @click="openTime" class="q-width-100 q-fileter-item">
					<span class="item-title ell">{{ query.startDate + ' - ' + query.endDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div class="q-box">
				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.brandName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div @click="openOrg" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部医院 -->
				<div @click="openHospital" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.hospitalName === '全部医院' ? '全部终端' : query.hospitalName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div v-if="isRecommend" class="branch-sales-overview-tip" v-html="chartData.message"></div>
		</div>

		<div class="branch-sales-overview-card">
			<filterCom :filterList="filterList" :currentIndex="currentIndex" @filterChange="filterChange"></filterCom>
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="branch-sales-overview-card-content">
				<div class="branch-sales-overview-card-echarts" :id="`branch-sales-overview-card-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :path="`${page}${props.info?.nameCn}`" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { branchHospitalSalesOverviewAmount } from '@/api/sales';
import { getNamesByIdsInArray, generateRandomNumber, translateFont, getFilterTime, spaces, getNameByPath } from '@/utils/index';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let filterStore = usefilterStore();
let props = defineProps(['info', 'isRecommend']);
let myEcharts = echarts;

// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
});
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};

// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (brandData.value.length === 0) {
		// for (const item of filterStore.skuInfo) {
		// 	brandData.value.push({ id: item.skuCode, name: item.skuCn });
		// }
		brandData.value = filterStore.skuInfoTree;
	}
	brand.value.show = true;
};

let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');

	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgList.value = filterStore.treeInfo;
	orgZ.value.show = true;
};

const orgConfirm = async ({ userId: ids, userName: names }) => {
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};

// 医院筛选
const hospital = ref(null);
const openHospital = () => {
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};

let chart;
let initChart = () => {
	const userAgent = navigator.userAgent;
	const isIOS = /iPad|iPhone|iPod/.test(userAgent);
	const isAndroid = /Android/.test(userAgent);
	const renderer = isIOS ? 'svg' : isAndroid ? 'canvas' : 'auto';
	chart = myEcharts.init(document.querySelector(`#branch-sales-overview-card-echarts-${echartsId}`), null, { renderer });
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};

let setOption = () => {
	let hours = chartData.value.axisMonth;
	const days = chartData.value.nameArray;
	const data = chartData.value.dateArray;
	let option = {
		tooltip: {
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = `<div class="product">${days[value.value[1]]}</div>`;
				return `<div class="tooltip-saletrend">${str}<div class="tooltip-heatmap"><span class="dot" style="background-color:${value.color}"></span><span class="date">${value.name}</span><span class="value">${value.value[2]}</span></div></div>`;
			},
			extraCssText: 'z-index:9',
		},
		grid: {
			top: translateFont(18),
			bottom: translateFont(45),
			left: translateFont(72),
			right: translateFont(10),
		},
		xAxis: {
			type: 'category',
			data: hours,
			axisLabel: {
				fontSize: translateFont(8),
				color: '#6B778C',
				interval: spaces(chartData.value.axisMonth.length),
			},
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			splitArea: {
				show: true,
				interval: 0,
			},
			position: 'top',
		},
		yAxis: {
			type: 'category',
			data: days,
			axisLabel: {
				//是否显示
				inside: false, //坐标轴刻度文字的风格        (可选楷体  宋体  华文行楷等等)
				formatter: (value) => {
					return value.length > 7 ? `{a|${value.slice(0, 7)}...}` : `{a|${value}}`;
				},
				rich: {
					a: {
						color: '#6B778C',
						fontSize: translateFont(8),
						align: 'left',
						width: translateFont(100),
						backgroundColor: 'none',
						padding: [0, translateFont(-43), 0, 0],
					},
				},
			},
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			splitArea: {
				show: true,
				interval: 0,
			},
			name: 'TOP200医院',
			nameTextStyle: {
				color: '#172B4D',
				fontSize: translateFont(9),
				fontWeight: 'bold',
				padding: [0, translateFont(120), translateFont(-8), translateFont(50)],
			},
		},
		visualMap: {
			show: true,
			min: 0,
			max: chartData.value.max,
			calculable: true,
			orient: 'horizontal',
			left: 'center',
			bottom: translateFont(10),
			color: ['#01A2BF', '#ffffff'],
			align: 'right',
			itemWidth: translateFont(10),
			itemHeight: translateFont(85),
			formatter: (value) => {
				if (value === 0) return `{b|0}`; //命中两端手柄
				if (value.toString().includes('.')) {
					return `{c|${new Decimal(value).toDecimalPlaces(0) + 'K'}}`; //命中指示器
				}
				if (value === chartData.value.max) {
					return `{a|${value + 'K'}}`; //命中两端手柄
				}
				return value;
			},
			textStyle: {
				rich: {
					a: {
						color: '#172B4D',
						fontSize: translateFont(8),
						padding: [translateFont(-23), translateFont(-40), 0, 0],
					},
					b: {
						color: '#172B4D',
						fontSize: translateFont(8),
						padding: [translateFont(-23), translateFont(40), 0, 0],
					},
				},
			},
		},
		series: [
			{
				// name: 'Punch Card',
				type: 'heatmap',
				data: data,
				label: {
					show: true,
					fontSize: translateFont(7),
					color: '#172B4D',
					formatter: ({ value }) => {
						if (currentIndex.value !== '销售(K)') {
							return value[2] + '%';
						} else {
							return value[2];
						}
					},
				},
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowColor: 'rgba(0, 0, 0, 0.5)',
					},
				},
				heatmapSize: [10, 30],
			},
		],
	};
	chart.setOption(option);
};
//获取数据
let comStatus = ref('loading');
let chartData = ref({});
const getData = async () => {
	try {
		comStatus.value = 'loading';
		let res = await branchHospitalSalesOverviewAmount({
			skuCode: query.brandId ? query.brandId.split(',') : [],
			territoryCode: query.personId ? query.personId.split(',') : [],
			hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
			type: typeFilter.value,
			startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
			endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		});
		chartData.value = res.result;
		let itemsLength = res.result.nameArray.length;
		comStatus.value = res.result.nameArray.length < 1 ? 'empty' : 'normal';
		document.querySelector(`#branch-sales-overview-card-echarts-${echartsId}`).style.height = 7 * itemsLength + 17 + 'vw';
		return Promise.resolve();
	} catch (error) {
		return Promise.reject(error);
	}
};

const emit = defineEmits(['focus']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let echartsId = generateRandomNumber();
let typeFilter = ref('sales');
let filterList = ref(['销售(K)', '按达成%', '按同比增长%']);
let currentIndex = ref('销售(K)');
let filterChange = async (item) => {
	currentIndex.value = item;
	if (item === '按达成%') {
		typeFilter.value = 'ach';
	} else if (item === '按同比增长%') {
		typeFilter.value = 'growth';
	} else {
		typeFilter.value = 'sales';
	}
	chartData.value = {};
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-展示方式`, item);
};
onMounted(async () => {
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
.branch-sales-overview {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	&-tip {
		padding: 5px 5px 0;
		font-size: 12px;
		color: #7c88a6;
	}
	&-card {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		padding-right: 3px;
		&-content {
			height: 174px;
			overflow-y: auto;
			overflow-x: hidden;
			padding-top: 8px;
			/* 设置纵向滚动条样式 */
			&::-webkit-scrollbar {
				width: 4px; /* 设置纵向滚动条宽度 */
			}

			&::-webkit-scrollbar-thumb {
				background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
				border-radius: 3px;
			}

			&::-webkit-scrollbar-track {
				background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
				border-radius: 5px;
				// margin: 50px 0px;
			}
		}
		&-echarts {
			min-width: 100%;
		}
	}
}
</style>
