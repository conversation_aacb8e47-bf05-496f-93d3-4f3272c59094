<template>
	<div class="admission-situation" id="ap-admission-situation">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="operation">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
					<switchCom ref="switchComRef" :defaultValue="props.info" @currencySwitching="currencySwitching"></switchCom>
				</span>
			</div>
			<!-- 通用筛选器 -->
			<globalFilter
				:filters="[
					{ name: 'monthName', size: '49', queryName: 'dateRange' },
					{ name: 'org', size: '49', queryName: 'personName' },
					{ name: 'brand', size: '32', queryName: 'brandName' },
					{ name: 'city', size: '32', queryName: 'countyName' },
					{ name: 'hospital', size: '32', queryName: 'hospitalName' },
				]"
				:query="query"
				:loading="filterLoading"
				@openTime="openTime"
				@openOrg="openOrg"
				@openBrand="openBrand"
				@openCity="openCity"
				@openHospital="openHospital"
			></globalFilter>
		</div>
		<loading v-show="comStatus === 'loading'"></loading>
		<empty-com v-show="comStatus === 'empty'"></empty-com>
		<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>
		<div v-show="comStatus === 'normal'" class="admission-table">
			<div class="admission-table-detail">
				<div class="admission-table-box">
					<div class="admission-table-box-title">
						<div class="admission-table-box-title-desc left-item">排名</div>
						<div class="admission-table-box-title-desc right-item">医院名称</div>
						<div class="admission-table-box-title-desc right-item">{{ query.salesOrAmount === '数量' ? '销售量（K）' : '销售额（K）' }}</div>
					</div>
					<div class="admission-table-box-list" v-for="(item, index) in state.sList" :key="index">
						<div class="admission-table-box-list-item left-item">{{ item.rank }}</div>
						<div class="admission-table-box-list-item right-item">{{ item.hospitalName }}</div>
						<div class="admission-table-box-list-item right-item">{{ formatNumber(item.sales) }}</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :defaultHosCheckedAll="defaultHosCheckedAll" :path="`${page}${props.info?.nameCn}`" :personalId="query.personId" :province="query.province" :city="query.city" :skuCode="query.brandId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<!-- <DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery> -->
		<!-- 市场筛选 -->
		<OneLevelRadio ref="time" :path="`${page}${props.info?.nameCn || pageTitle}-月份`" @_confirm="dateConfirm" :defaultCheck="query.month" :list="monthList"></OneLevelRadio>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import { getFilterTime, getNameByPath, formatNumber } from '@/utils/index';
import { getAdmissionSales } from '@/api/sales';
import usefilterStore from '@/store/modules/filter';
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
// 筛选条件
const query = reactive({
	// startDate: getFilterTime(props.info?.startTime),
	// endDate: getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
	month: '6',
	monthName: '准入后6个月',
});
// 默认选中的人员
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const monthList = ref([
	{ id: '1', name: '准入后1个月' },
	{ id: '2', name: '准入后2个月' },
	{ id: '3', name: '准入后3个月' },
	{ id: '4', name: '准入后4个月' },
	{ id: '5', name: '准入后5个月' },
	{ id: '6', name: '准入后6个月' },
	{ id: '7', name: '准入后7个月' },
	{ id: '8', name: '准入后8个月' },
	{ id: '9', name: '准入后9个月' },
	{ id: '10', name: '准入后10个月' },
	{ id: '11', name: '准入后11个月' },
	{ id: '12', name: '准入后12个月' },
]);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSalesHospital',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async (value) => {
	query.month = value.id;
	query.monthName = value.name;
	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	getDetailData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-月份`, `${value.name}`);
};
// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSalesHospital',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSalesHospital',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city, hospital, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	defaultPerson.value = ids;
	cascaderChange('org');
	getDetailData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');

	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	getDetailData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 城市

const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSalesHospital',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	getDetailData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
// 医院筛选
const openHospital = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSalesHospital',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	defaultHos.value = params;
	cascaderChange('hospital');
	getDetailData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};
let props = defineProps(['info', 'defaultValue', 'summary', 'inChat']);
const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};

//获取底部明细数据
// 分页
let state = reactive({
	sList: [],
	isLoading: false, // 列表的loading
	finished: false, //结束
});
let comStatus = ref('loading');

let getDetailData = async () => {
	try {
		comStatus.value = 'loading';
		let res = await getAdmissionSales({
			skuCode: query.brandId ? query.brandId.split(',') : [],
			territoryCode: query.personId ? query.personId.split(',') : [],
			hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
			month: query.month,
			province: query.province || [],
			city: query.city || [],
			salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
		});
		query.salesOrAmount = switchComRef.value.type;
		if (res.result.isExceededAuthority) {
			comStatus.value = 'isExceededAuthority';
			emit('summaryIng', { noSummary: true });
			return;
		}
		const rows = res.result.agentInfo;
		state.isLoading = false;
		state.sList = state.sList.concat(rows);
		comStatus.value = res.result.agentInfo.length < 1 ? 'empty' : 'normal';
		if (props.summary) {
			emit('summaryIng', { data: { ...res.result.agentInfo }, params: query });
		}
		return Promise.resolve();
	} catch (error) {
		state.isLoading = false;
		state.finished = true;
		return Promise.reject(error);
	}
};

const onLoad = () => {
	// if (state.finished) return;
	// state.page++;
	// state.isLoading = true;
	// getDetailData();
};
const switchComRef = ref(null);
const currencySwitching = async (type) => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSalesHospital',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	getDetailData();
};
const defaultSku = ref('');
const defaultHos = ref('');
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			brand, // 传入 ref
			orgZ, // 传入 ref
			defaultSku, // 传入 ref
			defaultHos, // 传入 ref
			defaultPerson, // 传入 ref
			defaultProvinceCity, // 传入 ref
			switchComRef,
		});
	}
	//初始化级联
	initFilters();
	getDetailData();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/admissionSituation.scss';
.admission-situation {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	&-card {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		padding-bottom: 15px;
		min-height: 140px;
		max-height: 175px;
		overflow-y: auto;
		overflow-x: hidden;
		/* 设置纵向滚动条样式 */
		&::-webkit-scrollbar {
			width: 4px; /* 设置纵向滚动条宽度 */
		}

		&::-webkit-scrollbar-thumb {
			background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
			border-radius: 5px;
		}
	}
	.admission-table {
		background-color: #fff;
		margin-top: 8px;
		overflow: hidden;
		&-detail {
			margin: 0 7px;
		}
		&-box {
			max-height: 250px;
			overflow-y: auto;
			margin-top: 5px;
			position: relative;
			font-size: 9px;
			border: 1px solid #dfe1e6;
			border-bottom: none;
			&-title {
				display: flex;
				position: sticky;
				top: 0;
				z-index: 1;
				border-bottom: 1px solid #dfe1e6;
				background-color: var(--pv-card-bgc);
				&-desc {
					flex: 1;
					font-weight: bold;
					text-align: center;
					padding: 8px 9px;
					border-right: 1px solid #dfe1e6;
				}
				&-desc:last-child {
					border-right: none;
				}
			}
			&-list {
				display: flex;
				&-item {
					// flex: 1;
					text-align: center;
					padding: 8px 9px;
					border-right: 1px solid #dfe1e6;
					border-bottom: 1px solid #dfe1e6;
				}
				&-item:last-child {
					border-right: none;
				}
			}
			.left-item {
				flex: 1;
			}
			.right-item {
				flex: 3;
			}
			:deep(.van-list__finished-text) {
				font-size: 9px;
				line-height: 3.1;
			}
			:deep(.van-loading__text) {
				font-size: 9px;
				line-height: 3.1;
			}
		}
	}
}
</style>
