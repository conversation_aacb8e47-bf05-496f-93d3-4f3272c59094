<template>
	<div class="marek-share" id="ap-marek-share">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="operation" v-if="!hiddenStar">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
					<switchCom ref="switchComRef" :defaultValue="props.info" @currencySwitching="currencySwitching"></switchCom>
				</span>
			</div>
		</div>
		<!-- 通用筛选器 -->
		<globalFilter
			:filters="[
				{ name: 'time', size: '100', queryName: 'dateRange' },
				{ name: 'city', size: '32', queryName: 'countyName' },
				{ name: 'market', size: '32', queryName: 'marketName' },
				{ name: 'hospital', size: '32', queryName: 'hospitalName' },
			]"
			:query="query"
			:loading="filterLoading"
			@openTime="openTime"
			@openMarket="openMarket"
			@openCity="openCity"
			@openHospital="openHos"
		></globalFilter>
		<div class="content">
			<filterCom :filterList="filterList" :currentIndex="currentIndex" @filterChange="filterChange" :downloadExcel="false"></filterCom>
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'">
				<div class="marek-share-legend">
					<div class="marek-share-legend-item" :class="{ gray: !item.active }" v-for="(item, index) in legend" :key="item" :style="{ '--bar-color': color[index % color.length] }" @click="legendClick(item)">
						<span class="bar"></span>
						<span>{{ item.name }}</span>
					</div>
				</div>
				<div class="marek-share-echarts" :id="`marek-share-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
			<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>
		</div>

		<div v-if="showTags" class="tags">
			<div class="tags-item"># BU/RSM Report</div>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy v-if="showUpdateTime" :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
		<!-- 市场筛选 -->
		<OneLevelRadio ref="market" :path="`${page}${props.info?.nameCn || pageTitle}-市场`" @_confirm="marketConfirm" :defaultCheck="query.marketId" :list="msList"></OneLevelRadio>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :province="query.province" :city="query.city" :path="`${page}${props.info?.nameCn}`" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import { marketShareTrends } from '@/api/sales';
import { generateRandomNumber, getFilterTime, translateFont, deepClone, getPreviousMonth, getNameByPath, isPCTrue } from '@/utils/index';
import * as echarts from 'echarts';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
import useReport from '@/store/modules/report.js';
let useReportStore = useReport();
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let showUpdateTime = ref(true);
let pageTitle = '市场份额趋势分析';
if (route.fullPath.includes('/report')) {
	showUpdateTime.value = false;
}
const props = defineProps(['showTags', 'info', 'hiddenStar', 'defaultValue', 'summary', 'inChat']);
// 筛选条件
const query = reactive({
	startDate: useReportStore.zxtTime.length > 0 ? getFilterTime(useReportStore.zxtTime[0]) : getFilterTime(props.info?.startTime),
	endDate: useReportStore.zxtTime.length > 0 ? getFilterTime(useReportStore.zxtTime[1]) : getFilterTime(props.info?.endTime),
	marketId: filterStore.msList[0],
	marketName: filterStore.msList[0],
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
	province: [],
	city: [],
});

// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'marekShare',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info?.nameCn || pageTitle}-日期`, `${_startDate}-${_endDate}`);
};

// 市场
const msList = ref([]);
const market = ref(null);
const openMarket = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'marekShare',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	if (msList.value.length === 0) {
		for (const item of filterStore.msList) {
			msList.value.push({ id: item, name: item });
		}
	}
	market.value.show = true;
};
const marketConfirm = async ({ id, name }) => {
	query.brandId = '';
	query.marketId = id;
	query.marketName = name;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info?.nameCn || pageTitle}-市场`, name);
};

// 城市
const hospital = ref(null);
const city = ref(null);
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city, hospital });
const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'marekShare',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};

const openHos = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'marekShare',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info?.nameCn || pageTitle}-医院`, query.hospitalName);
};

let myEcharts = echarts;
let chart;
let color = ['#0052CC', '#FF991F', '#01A2BF', '#DD340A', '#2A3E66', '#B7723D', '#836BB5'];
const legend = ref([]);
const result = ref(null);
const lineList = ref([]);
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#marek-share-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let option = {
		legend: {
			show: false,
			data: legend.value,
			top: translateFont(5),
			left: translateFont(10),
			type: 'scroll',
			pageIconColor: '#0052CC',
			itemGap: translateFont(15),
			itemWidth: translateFont(15),
			itemHeight: translateFont(4),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		color,
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				console.log(value);
				let str = '';
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${item.value}%</span></div>`;
				}
				console.log(str);
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			extraCssText: 'z-index:9',
		},
		grid: {
			left: translateFont(10),
			right: translateFont(20),
			bottom: translateFont(10),
			top: translateFont(15),
			containLabel: true,
		},
		xAxis: {
			type: 'category',
			show: true,
			data: result.value.axisMonth,
			axisLabel: {
				show: true, // 是否显示坐标轴刻度标签。
				interval: 2, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
				inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
				color: ' #6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
				fontSize: translateFont(10),
			},
			axisLine: {
				show: true, // 是否显示坐标轴轴线
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
					type: 'solid',
				},
			},
			axisTick: {
				show: true,
			},
		},
		yAxis: {
			type: 'value',
			axisLabel: {
				show: true, // 是否显示坐标轴刻度标签。
				interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
				inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
				color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
				fontSize: translateFont(10),
				formatter: function (params) {
					return `${params}%`;
				},
			},
			splitLine: {
				show: true, // 隐藏 Y 轴横线
				lineStyle: {
					color: 'rgba(223,225,230,.5)',
				},
			},
			splitNumber: 4,
		},
		series: lineList.value,
	};
	chart.setOption(option, true);
};
let comStatus = ref('loading');

let getData = async () => {
	comStatus.value = 'loading';

	let res = await marketShareTrends({
		marketShare: query.brandId ? query.brandId : query.marketId,
		hospCode: query.hospitalId,
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		province: query.province.join(',') || '',
		city: query.city.join(',') || '',
		skuCode: query.brandId ? query.brandId : '',
		marketLevel: 'hospital',
		type: typeFilter.value,
		salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
	});
	query.salesOrAmount = switchComRef.value.type;
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}
	query.marketName = res.result.marketShare?.[0] ? res.result.marketShare?.[0] : '';
	query.marketId = res.result.marketShare?.[0] ? res.result.marketShare?.[0] : '';
	result.value = res.result;
	legend.value = res.result.product.map((ele) => {
		return { name: ele, active: true };
	});
	lineList.value = [];
	for (const item of res.result.data) {
		lineList.value.push({
			name: item.name,
			data: item.data.map((ele) => {
				return new Decimal(ele.ach === 'NaN' ? 0 : ele.ach).toString();
			}),
			type: 'line',
			lineStyle: {
				width: translateFont(1),
			},
			symbolSize: translateFont(5),
		});
	}
	comStatus.value = res.result.data.length < 1 ? 'empty' : 'normal';

	let year = query.endDate.substring(0, 4); // 截取年份部分
	let month = query.endDate.substring(4); // 截取月份部分
	let year1 = query.startDate.substring(0, 4); // 截取年份部分
	let month1 = query.startDate.substring(4); // 截取月份部分
	let startLocalDate = year1 + '年' + month1 + '月'; // 拼接年份和月份信息
	let endLocalDate = year + '年' + month + '月';
	let name = `${query.province.join(',')}${query.marketName}趋势分析`;
	emit('filterChange', {
		name: 'marekShareReportPPT2',
		info: { date: { startLocalDate, endLocalDate }, name, province: query.province, marketName: query.marketName, data: { lineList: lineList, legend: legend, result: result, message: res.result.message } },
	});
	if (props.summary) {
		emit('summaryIng', { data: { ...res.result }, params: query });
	}
};
let legendClick = (item) => {
	let index = lineList.value.findIndex((ele) => ele.name === item.name);
	item.active = !item.active;
	if (!item.active) {
		console.log(lineList.value[index].data, '?');
		lineList.value[index].sourceData = deepClone(lineList.value[index].data);
		lineList.value[index].data = [];
	} else {
		lineList.value[index].data = lineList.value[index].sourceData;
	}
	setOption();
};
const emit = defineEmits(['focus', 'filterChange', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let echartsId = generateRandomNumber();
const defaultHos = ref('');
const defaultProvinceCity = ref({});

let typeFilter = ref('ach');
// 定义映射关系
const filterMap = {
	'占比(%)': 'ach',
	'同比(%)': 'growth',
	'环比(%)': 'mom',
};
let filterList = ref(['占比(%)', '同比(%)', '环比(%)']);
let currentIndex = ref('占比(%)');
let filterChange = async (item) => {
	currentIndex.value = item;
	typeFilter.value = filterMap[item] || 'ach'; // 如果没有匹配，默认 'ach'
	query.indicatorType = typeFilter.value;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-展示方式`, item);
};
const switchComRef = ref(null);
const currencySwitching = async (type) => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'marekShare',

			defaultValue: {
				...toRaw(query),
			},
		});

		return;
	}
	// proxy.$umeng('点击', `${props.umengPath}-销量金额筛选器`, type);
	await getData();
	chart.resize();
	setOption();
};
onMounted(async () => {
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			time,
			defaultHos, // 传入 ref
			defaultProvinceCity, // 传入 ref
			currentIndex,
			typeFilter,
			switchComRef,
		});
	}
	initFilters();
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/marekShare.scss';
.marek-share {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.content {
		background-color: var(--pv-nav-bar-active);
		margin-top: 8px;
		border-radius: 5px;
	}
	&-legend {
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		padding: 4px 8px;
		max-height: 65px;
		overflow-y: auto;
		/* 设置纵向滚动条样式 */
		&::-webkit-scrollbar {
			width: 4px; /* 设置纵向滚动条宽度 */
		}

		&::-webkit-scrollbar-thumb {
			background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
			border-radius: 5px;
		}
		&-item {
			font-size: 11px;
			color: var(--pv-no-active-color);
			margin-right: 20px;
			margin-top: 4px;
			display: flex;
			align-items: center;
			.bar {
				height: 1.1px;
				width: 16px;
				background-color: var(--bar-color);
				margin-right: 8px;
				position: relative;
				&::before {
					position: absolute;
					content: '';
					width: 4px;
					height: 4px;
					border-radius: 50%;
					border: 1px solid var(--bar-color);
					background-color: #fff;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
				}
			}
		}
		.gray {
			filter: grayscale(100%);
			opacity: 0.5;
		}
	}
	&-echarts {
		width: 100%;
		height: 215px;
	}
	.tags {
		font-size: 9px;
		padding: 0 10px 8px;
		display: flex;
		&-item {
			background: #f2f5f8;
			border-radius: 0.533vw;
			color: #a9b2c2;
			margin-bottom: 1.333vw;
			margin-right: 1.333vw;
			text-align: center;
			padding: 3px;
		}
	}
}
</style>
