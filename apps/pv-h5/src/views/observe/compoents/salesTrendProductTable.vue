<template>
	<div :class="{ 'ignore-sale-table': isPPT }">
		<vxe-table v-if="show" max-height="400" :show-footer="footerShow" :data="props.tableData" :sort-config="{ trigger: 'cell' }" :footer-method="footerMethod" :scroll-y="{ enabled: true, gt: 0, oSize: 100 }">
			<vxe-column
				v-for="(item, index) in props.columns"
				:key="index"
				:field="item.field"
				:title="item.title !== '产品' ? item.title + '(K)' : item.title"
				:min-width="isPPT ? '' : item.width"
				sortable
				:sort-by="sortRule"
				:align="item.field === 'product' ? 'left' : 'right'"
				:fixed="item.field === 'product' ? 'left' : ''"
			></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
import { sortRule } from '@/utils/index';
const props = defineProps(['tableData', 'columns', 'isPPT', 'lastResult']);
let show = ref(false);
const footerShow = ref(false);
const footerData = ref([]);
watch(
	() => props.columns,
	(n) => {
		console.log(n, '?????????');
		if (n) {
			show.value = true;
		}
	}
);

watch(
	() => props.lastResult,
	(val) => {
		if (val) {
			footerData.value = [];
			let num = Object.keys(val).length;
			let tempData = [val.product];
			for (let i = 0; i < num - 2; i++) {
				tempData.push(val[props.columns[i + 1].field]);
			}
			tempData.push(val.salesSum);
			footerData.value.push(tempData);
			footerShow.value = true;
		}
	},
	{ immediate: true }
);

const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};
</script>
<style lang="scss" scoped>
.ignore-sale-table {
	::v-deep(.vxe-table) {
		.vxe-cell {
			padding: 3px 1px;
		}
	}
}
</style>
