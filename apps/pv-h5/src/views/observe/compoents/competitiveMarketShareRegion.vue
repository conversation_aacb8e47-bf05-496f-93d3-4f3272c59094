<template>
	<div class="cm" id="ap-cm">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="operation" v-if="!hiddenStar">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
					<switchCom ref="switchComRef" :defaultValue="props.info" @currencySwitching="currencySwitching"></switchCom>
				</span>
			</div>
			<!-- 通用筛选器 -->
			<globalFilter
				:filters="[
					{ name: 'time', size: '100', queryName: 'dateRange', single: true },
					{ name: 'city', size: '49', queryName: 'countyName' },
					{ name: 'market', size: '49', queryName: 'marketName' },
				]"
				:query="query"
				:loading="filterLoading"
				@openTime="openTime"
				@openMarket="openMarket"
				@openCity="openCity"
			></globalFilter>
		</div>
		<!-- 图表 -->
		<loading v-show="comStatus === 'loading'"></loading>
		<div v-show="comStatus === 'normal'" class="content">
			<div class="legend">
				<div class="legend-item" :class="{ gray: !item.active }" v-for="(item, index) in legendList" :key="item" :style="{ '--bar-color': color[index % color.length] }" @click="legendClick(item)">
					<span class="bar"></span>
					<span>{{ item.name }}</span>
				</div>
			</div>
			<div :id="`cm-chat-${echartsId}`" class="cm-chat"></div>
		</div>
		<empty-com v-show="comStatus === 'empty'"></empty-com>
		<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>

		<!-- 数据更新至 -->
		<dataUpdateBy v-if="showUpdateTime" :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 市场筛选 -->
		<OneLevelRadio ref="market" :list="msList" :path="`${page}${props.info?.nameCn || pageTitle}-市场`" @_confirm="marketConfirm" :defaultCheck="query.marketId"></OneLevelRadio>
		<!-- 城市选择 -->
		<cityFilter v-if="sh" ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import { marketShareAnalysis } from '@/api/sales';
import { getFilterTime, getNameByPath, translateFont, formatNumbeW, isPCTrue, formatKNumber, generateRandomNumber } from '@/utils/index';
import * as echarts from 'echarts';
import usefilterStore from '@/store/modules/filter';
import useReport from '@/store/modules/report.js';
import { filterMarketShare } from '@/api/filter';
let useReportStore = useReport();
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let showUpdateTime = ref(true);
let pageTitle = '市场份额占比分析';
if (route.fullPath.includes('/report')) {
	showUpdateTime.value = false;
}
let props = defineProps(['info', 'hiddenStar', 'defaultValue', 'summary', 'inChat']);
const emit = defineEmits(['focus', 'filterChange', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
// 筛选条件
const query = reactive({
	startDate: useReportStore.btTime.length > 0 ? getFilterTime(useReportStore.btTime[0]) : getFilterTime(props.info?.startTime),
	endDate: useReportStore.btTime.length > 0 ? getFilterTime(useReportStore.btTime[1]) : getFilterTime(props.info?.endTime),
	marketId: '',
	marketName: '',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
	province: [],
	city: [],
});

// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'competitiveMarketShareRegion',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info?.nameCn || pageTitle}-日期`, `${_startDate}-${_endDate}`);
};

// 市场
const msList = ref([]);
const market = ref(null);
const openMarket = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'competitiveMarketShareRegion',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	market.value.show = true;
};
const marketConfirm = async ({ id, name }) => {
	query.brandId = '';
	query.marketId = id;
	query.marketName = name;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info?.nameCn || pageTitle}-市场`, name);
};
const city = ref(null);
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city });
// 城市

const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'competitiveMarketShareRegion',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
// 初始化报表数据
let chart = null;
const initChart = () => {
	chart = echarts.init(document.querySelector(`#cm-chat-${echartsId}`), { devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio });
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let color = ['#FFD400', '#FF9E5A', '#8A8A8A', '#FF001C', '#CCCCCC', '#86C669', '#3E86B1', '#6FB5E3', '#0052CC', '#277C00'];
const setOption = () => {
	const option = {
		legend: {
			left: 'center',
			show: false,
			top: translateFont(10),
			icon: 'circle',
			itemWidth: translateFont(6),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(9),
			},
			data: legendList.value,
		},
		grid: {
			bottom: translateFont(10),
			left: translateFont(15),
			right: translateFont(15),
			top: translateFont(40),
		},
		color,
		series: [
			{
				type: 'pie',
				radius: [translateFont(35), translateFont(62)],
				left: 'center',
				label: {
					width: translateFont(80),
					fontSize: translateFont(9),
					formatter: (params) => {
						// 调用 formatNumberW 函数处理 {c} 的值
						const formattedC = formatNumbeW(params.value); // params.value 对应 {c}
						return `{name|${params.name}}\n${formattedC},${params.percent}%`;
					},
					rich: {
						name: {
							fontSize: translateFont(9),
							color: '#6B778C',
						},
					},
				},
				itemStyle: {
					borderColor: '#ffffff',
					borderWidth: 1,
				},
				labelLine: {
					length: 6,
					lengt2: 3,
					lineStyle: {
						color: '#DFE1E6',
					},
				},
				data: tableList.value,
			},
		],
	};
	chart.setOption(option);
};

const tableList = ref([]);
const legendList = ref([]);
let comStatus = ref('loading');

let getData = async () => {
	comStatus.value = 'loading';
	let res = await marketShareAnalysis({
		marketShare: query.brandId ? query.brandId : query.marketId,
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		province: query.province.join(',') || '',
		city: query.city.join(',') || '',
		marketLevel: 'region',
		salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
	});
	query.salesOrAmount = switchComRef.value.type;
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}
	tableList.value = [];
	legendList.value = [];
	query.marketName = res.result.marketShare?.[0] ? res.result.marketShare?.[0] : '';
	query.marketId = res.result.marketShare?.[0] ? res.result.marketShare?.[0] : '';
	for (const item of res.result.data) {
		tableList.value.push({ name: item.product, value: item.amount });
	}
	legendList.value = res.result.data.map((ele) => {
		return { name: ele.product, active: true };
	});
	comStatus.value = res.result.data.length < 1 ? 'empty' : 'normal';
	if (props.summary) {
		emit('summaryIng', { data: { ...res.result }, params: query });
	}
};
let legendClick = (item) => {
	let index = tableList.value.findIndex((ele) => ele.name === item.name);
	item.active = !item.active;
	if (!item.active) {
		tableList.value[index].sourceData = tableList.value[index].value;
		delete tableList.value[index].value;
	} else {
		tableList.value[index].value = tableList.value[index].sourceData;
	}
	setOption();
};
let echartsId = generateRandomNumber();
const switchComRef = ref(null);
const currencySwitching = async (type) => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'competitiveMarketShareRegion',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	// proxy.$umeng('点击', `${props.umengPath}-销量金额筛选器`, type);
	await getData();
	chart.resize();
	setOption();
};
const defaultProvinceCity = ref({});
let sh = ref(false);
onMounted(async () => {
	let res = await filterMarketShare({ marketLevel: 'region' });
	for (const item of res.result) {
		msList.value.push({ id: item, name: item });
	}
	sh.value = true;
	query.marketName = msList.value[0].id;
	query.marketId = msList.value[0].id;
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			time,
			defaultProvinceCity, // 传入 ref
			switchComRef,
		});
	}
	initFilters();
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/competitiveMarketShare.scss';
.cm {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	.content {
		background-color: var(--pv-nav-bar-active);
		margin-top: 8px;
		border-radius: 5px;
	}
	.legend {
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		padding: 4px 8px;
		max-height: 65px;
		overflow-y: auto;
		/* 设置纵向滚动条样式 */
		&::-webkit-scrollbar {
			width: 4px; /* 设置纵向滚动条宽度 */
		}

		&::-webkit-scrollbar-thumb {
			background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
			border-radius: 5px;
		}
		&-item {
			font-size: 11px;
			color: var(--pv-no-active-color);
			margin-right: 20px;
			margin-top: 4px;
			display: flex;
			align-items: center;
			.bar {
				height: 1.1px;
				width: 16px;
				background-color: var(--bar-color);
				margin-right: 8px;
				position: relative;
				&::before {
					position: absolute;
					content: '';
					width: 4px;
					height: 4px;
					border-radius: 50%;
					border: 1px solid var(--bar-color);
					background-color: #fff;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
				}
			}
		}
		.gray {
			filter: grayscale(100%);
			opacity: 0.5;
		}
	}
	.cm-chat {
		width: 100%;
		height: 214px;
		background-color: var(--pv-nav-bar-active);
		margin-top: 8px;
		border-radius: 5px;
	}
}
</style>
