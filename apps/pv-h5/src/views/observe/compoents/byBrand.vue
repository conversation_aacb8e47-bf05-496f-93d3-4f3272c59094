<template>
	<div>
		<vxe-table show-overflow :show-footer="show" max-height="400" :data="props.ddiResult" :footer-method="footerMethod" :sort-config="{ trigger: 'cell' }" :scroll-y="{ enabled: false }">
			<vxe-column field="name" align="left" title="品牌" width="80px"></vxe-column>
			<vxe-column field="salesV" align="right" title="销售额K" sortable width="80px"></vxe-column>
			<vxe-column field="targetV" align="right" title="指标额K" sortable width="80px"></vxe-column>
			<vxe-column field="achievementRate" align="right" title="达成%" sortable width="80px"></vxe-column>
			<vxe-column field="yearOnYear" align="right" title="同比%" sortable width="80px"></vxe-column>
			<vxe-column field="yearOnYearGrowth" align="right" title="同比净增长额K" sortable width="100px"></vxe-column>
			<vxe-column field="chain" align="right" title="环比%" sortable width="80px"></vxe-column>
			<vxe-column field="moMGrowth" align="right" title="环比净增长额K" sortable width="100px"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
const props = defineProps(['ddiResult', 'lastResult']);
const footerData = ref([]);
const show = ref(false);
watch(
	() => props.lastResult,
	(val) => {
		if (val) {
			footerData.value = [];
			let tempData = [val.name, val.salesV, val.targetV, val.achievementRate, val.yearOnYear, val.yearOnYearGrowth, val.chain, val.moMGrowth];
			footerData.value.push(tempData);
			show.value = true;
		}
	},
	{ immediate: true }
);
// 返回一个二维数组的表尾合计
// const footerData = ref([['合计', '2', '44', '67', '-', '/', '/', '/']]);
const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};
</script>
<style lang="scss" scoped></style>
