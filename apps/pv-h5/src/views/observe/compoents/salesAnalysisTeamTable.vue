<template>
	<div :class="{ 'ignore-sale-table': true, 'ignore-sale-table-no-ppt': !isPPT }">
		<vxe-table :show-footer="show" :max-height="isPPT ? '' : '400'" :data="props.tableData" :footer-method="footerMethod" :sort-config="{ trigger: 'cell' }" :scroll-y="{ enabled: true, gt: 0, oSize: 100 }">
			<vxe-column field="territoryCodeCn" align="left" title="团队" :width="isPPT ? '70px' : '100px'" fixed="left"></vxe-column>
			<vxe-column field="salesM" align="right" :title="isPPT ? '月累计' : '月累计(K)'" :width="isPPT ? '70px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="targetM" align="right" :title="isPPT ? '月指标' : '月指标(K)'" :width="isPPT ? '60px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="achM" align="right" :title="isPPT ? '月达成' : '月达成(%)'" :width="isPPT ? '50px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="growthM" align="right" :title="isPPT ? '月同比' : '月同比(%)'" :width="isPPT ? '50px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="mom" align="right" :title="isPPT ? '月环比' : '月环比(%)'" :width="isPPT ? '50px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="quarterOnQuarterRate" align="right" :title="isPPT ? '季环比' : '季环比(%)'" :width="isPPT ? '50px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="salesQ" align="right" :title="isPPT ? '季累计' : '季累计(K)'" :width="isPPT ? '70px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="targetQ" align="right" :title="isPPT ? '季指标' : '季指标(K)'" :width="isPPT ? '60px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="achQ" align="right" :title="isPPT ? '季达成' : '季达成(%)'" :width="isPPT ? '50px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="growthQ" align="right" :title="isPPT ? '季同比' : '季同比(%)'" :width="isPPT ? '50px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="salesY" align="right" :title="isPPT ? '年累计' : '年累计(K)'" :width="isPPT ? '70px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="targetY" align="right" :title="isPPT ? '年指标' : '年指标(K)'" :width="isPPT ? '60px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="achY" align="right" :title="isPPT ? '年达成' : '年达成(%)'" :width="isPPT ? '50px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="growthY" align="right" :title="isPPT ? '年同比' : '年同比(%)'" :width="isPPT ? '50px' : '85px'" sortable :sort-by="sortRule"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
import { sortRule } from '@/utils/index';
const props = defineProps(['tableData', 'isPPT', 'lastResult']);
const show = ref(false);
const footerData = ref([]);

watch(
	() => props.lastResult,
	(val) => {
		if (val) {
			footerData.value = [];
			let tempData = [val.territoryCodeCn, val.salesM, val.targetM, val.achM, val.growthM, val.mom, val.quarterOnQuarterRate, val.salesQ, val.targetQ, val.achQ, val.growthQ, val.salesY, val.targetY, val.achY, val.growthY];
			footerData.value.push(tempData);
			show.value = true;
		}
	},
	{ immediate: true }
);

const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};
</script>
<style lang="scss" scoped>
.ignore-sale-table-no-ppt {
	::v-deep(.vxe-table) {
		.vxe-table--fixed-left-wrapper {
			.fixed-left--wrapper {
				width: 1120px !important;
			}
		}
	}
}
</style>
