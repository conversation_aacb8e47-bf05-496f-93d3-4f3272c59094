<template>
	<div style="position: relative">
		<div v-if="Object.keys(personalInfo).length > 0" class="personal">
			<span class="ignore-w">{{ personalInfo?.name }}</span>
			<span class="ignore-w">Incentive</span>
			<span>
				<div class="bar"></div>
				<span>{{ personalInfo?.achievementRate }}</span>
			</span>
		</div>
		<vxe-table
			ref="vxetable"
			show-overflow
			:show-footer="show"
			max-height="400"
			:data="props.ddiResult"
			:footer-method="footerMethod"
			:sort-config="{ trigger: 'cell' }"
			@sort-change="sortChangeEvent"
			@scroll="onScroll"
			@cell-click="cellClick"
			:loading-config="{ icon: 'vxe-icon-indicator roll', text: '加载中...' }"
			:loading="peopleLoading"
			:row-class-name="activeRow"
		>
			<vxe-column field="name" align="left" title="姓名" width="100px"></vxe-column>
			<vxe-column field="salesV" align="right" :title="salesOrAmount === '金额' ? '销售额K' : '销售量K'" sortable width="80px"></vxe-column>
			<vxe-column field="targetV" align="right" :title="salesOrAmount === '金额' ? '指标额K' : '指标量K'" sortable width="80px"></vxe-column>
			<vxe-column field="achievementRate" align="right" title="达成%" sortable width="80px"></vxe-column>
			<vxe-column field="yearOnYear" align="right" title="同比%" sortable width="80px"></vxe-column>
			<vxe-column field="yearOnYearGrowth" align="right" :title="salesOrAmount === '金额' ? '同比净增长额K' : '同比净增长量K'" sortable width="100px"></vxe-column>
			<vxe-column field="chain" align="right" title="环比%" sortable width="80px"></vxe-column>
			<vxe-column field="moMGrowth" align="right" :title="salesOrAmount === '金额' ? '环比净增长额K' : '环比净增长量K'" sortable width="100px"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
import { translateFont } from '@/utils/index';

const props = defineProps(['ddiResult', 'lastResult', 'peopleLoading', 'personalCard', 'salesOrAmount']);
const emits = defineEmits(['pullPData']);
const footerData = ref([]);
const show = ref(false);
const personalInfo = ref({});
const setBarStyle = (num) => {
	let personalDom = document.querySelector('.personal');
	let barDom = document.querySelector('.bar');
	let tableThDom = document.querySelector('.vxe-header--row').clientHeight;
	let tableTrDom = document.querySelectorAll('.vxe-body--row')?.[0]?.clientHeight;
	let vxeTbDom = document.querySelector('.vxe-table--body-wrapper');
	barDom.style.flex = num / 100;
	vxeTbDom.style.marginTop = tableTrDom + 'px';
	personalDom.style.top = tableThDom + 'px';
	personalDom.style.height = tableTrDom + 'px';
	personalDom.style.lineHeight = tableTrDom + 'px';
};

watch(
	() => props.lastResult,
	(val) => {
		if (val) {
			footerData.value = [];
			let tempData = [val.name, val.salesV, val.targetV, val.achievementRate, val.yearOnYear, val.yearOnYearGrowth, val.chain, val.moMGrowth];
			footerData.value.push(tempData);
			show.value = true;
		}
	},
	{ immediate: true }
);
const currentRow = ref(null);
const activeRow = (row) => {
	if (row.rowid === currentRow.value) {
		return 'active-row';
	}
};
const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};
const sortChangeEvent = ({ field, order }) => {
	console.log(field, order);
};

let onScroll = (e) => {
	if (e.isY && e.scrollHeight - e.scrollTop - translateFont(4) <= e.bodyHeight) {
		emits('pullPData');
	}
};
const cellClick = (e) => {
	// console.log(e);
	// currentRow.value = e.rowid;
	// personalInfo.value = e.row;
	// setBarStyle(personalInfo.value.achievementRate.slice(0, -1));
};
const vxetable = ref(null);
// watch(
// 	() => props.personalCard,
// 	async (val) => {
// 		if (val) {
// 			personalInfo.value = val;
// 			requestAnimationFrame(() => {
// 				let data = vxetable.value.getTableData();
// 				if (!data?.tableData?.[0]) return;
// 				currentRow.value = data?.tableData?.[0]?._X_ROW_KEY;
// 				setBarStyle(personalInfo.value.achievementRate.slice(0, -1));
// 			});
// 		}
// 	}
// );
// onMounted(() => {
// 	if (Object.keys(props.personalCard).length === 0) return;
// 	personalInfo.value = props.personalCard;
// 	setTimeout(() => {
// 		setBarStyle(personalInfo.value.achievementRate.slice(0, -1));
// 		let data = vxetable.value.getTableData();
// 		currentRow.value = data.tableData[0]._X_ROW_KEY;
// 	}, 500);
// });
defineExpose({ setBarStyle });
</script>
<style lang="scss" scoped>
::v-deep(.vxe-table--body-wrapper) {
	.active-row {
		background-color: #f5f7fa;
	}
}
.personal {
	position: absolute;
	left: 0;
	right: 0;
	font-size: 10px;
	font-weight: bold;
	display: flex;
	background-color: #faeee6;
	text-align: center;
	.ignore-w {
		width: 80px;
	}
	& > span:nth-last-of-type(1) {
		flex: 1;
		display: flex;
		align-items: center;
		.bar {
			flex: 1;
			height: 10px;
			background-color: #d86027;
		}
		span {
			width: 40px;
		}
	}
}
</style>
