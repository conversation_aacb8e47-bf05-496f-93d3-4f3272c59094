<template>
	<div class="sale-trend">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span>
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<!-- 销售趋势 -->
		<div>
			<div class="q-box">
				<!-- 时间 -->
				<div @click="openTime" class="q-width-100 q-fileter-item">
					<span class="item-title ell">{{ query.startDate + ' - ' + query.endDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div class="q-box">
				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.brandName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div @click="openOrg" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部team -->
				<div @click="openHospital" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.teamName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
		</div>
		<loading v-show="comStatus === 'loading'"></loading>
		<div v-show="comStatus === 'normal'" class="sale-trend-echarts-card">
			<div class="sale-trend-echarts" :id="`sale-trend-echarts-${echartsId}`"></div>
		</div>
		<empty-com v-show="comStatus === 'empty'"></empty-com>

		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 全部产品 -->
		<OnelevelCheckbox ref="brand" :list="brandData" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></OnelevelCheckbox>
		<!-- channel筛选 -->
		<OrgTreeList1 ref="orgZ" :listData="orgList" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList1>
		<!-- team -->
		<OnelevelCheckbox ref="hospital" :list="hospitalList" :path="`${page}${props.info?.nameCn}`" @_confirm="hospitalConfirm"></OnelevelCheckbox>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { getChannelTrends } from '@/api/sales';
import { formatNumber, generateRandomNumber, getFilterTime, translateFont, saleTrendFilter, getNamesByIdsInArray, spaces, getNameByPath, isPCTrue } from '@/utils/index';
import { getSkuCn, getTeam, getChannel } from '@/api/filter';
import usefilterStore from '@/store/modules/filter';
let props = defineProps(['info']);
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);

// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	brandName: '全部产品',
	personId: '',
	personName: '全部渠道',
	teamId: '',
	teamName: '全部团队',
});
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	document.querySelector('.sale-trend-table-content').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};

// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = async () => {
	if (brandData.value.length === 0) {
		let res = await getSkuCn();
		for (const item of res.result) {
			brandData.value.push({ id: item, name: item });
		}
	}
	brand.value.show = true;
};

let brandConfirm = async (params) => {
	if (params.length > 0 && params.length < brandData.value.length) {
		const names = getNamesByIdsInArray(brandData.value, params);
		query.brandName = names.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.join(',');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);

const openOrg = async () => {
	// orgList.value = filterStore.treeInfo;
	if (orgList.value.length === 0) {
		let res = await getChannel();
		function resetDate(list) {
			for (let index in list) {
				if (typeof list[index] === 'string') {
					list[index] = { text: list[index], id: list[index], state: { checked: false, expanded: false } };
				} else {
					list[index].text = list[index].child;
					list[index].nodes = list[index].children;
					list[index].state = { checked: false, expanded: false };
					delete list[index].child;
					delete list[index].children;
				}
				if (list[index].nodes.length > 0) {
					resetDate(list[index].nodes);
				}
			}
		}
		resetDate(res.result);
		orgList.value = res.result;
	}
	orgZ.value.show = true;
};

const channelCategory = ref([]);
const channelSubclass = ref([]);
const channel = ref([]);

const restData = (list, level) => {
	let r = [];
	for (let item of list) {
		if (item.level === level) {
			r.push(item.text);
		}
	}
	return r;
};
const orgConfirm = async (value) => {
	console.log(value);
	channelCategory.value = restData(value, 'channelCategory');
	channelSubclass.value = restData(value, 'channelSubclass');
	channel.value = restData(value, 'channel');
	query.personName = [...channelCategory.value, ...channelSubclass.value, ...channel.value].join(',') || '全部渠道';
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};

// 医院筛选
const hospital = ref(null);
const hospitalList = ref([]);
const openHospital = async () => {
	if (hospitalList.value.length === 0) {
		let res = await getTeam();
		for (const item of res.result) {
			hospitalList.value.push({ id: item, name: item });
		}
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params) => {
	if (params.length > 0 && params.length < hospitalList.value.length) {
		const names = getNamesByIdsInArray(hospitalList.value, params);
		query.teamName = names.join(',');
	} else {
		query.teamName = '全部团队';
	}
	query.teamId = params.join(',');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};

let myEcharts = echarts;
let chart;
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#sale-trend-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let x = tableList.value.map((ele) => ele.date);
	let xs = tableList.value.map((ele) => ele.sales);
	let zb = tableList.value.map((ele) => ele.target);
	let dc = tableList.value.map((ele) => ele.ach);
	let option = {
		grid: {
			top: translateFont(60),
			left: translateFont(58),
			right: translateFont(35),
			bottom: translateFont(30),
		},
		legend: {
			data: ['指标额', '销售额', '达成'],
			left: 'center',
			top: translateFont(15),
			itemGap: translateFont(15),
			itemWidth: translateFont(15),
			itemHeight: translateFont(4),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = '';
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${saleTrendFilter(item)}</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			axisPointer: {
				type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
				},
			},
			extraCssText: 'z-index:9',
		},
		xAxis: [
			{
				type: 'category',
				data: x,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: spaces(x.length), // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumber(value);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumber(value) + '%';
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
			},
		],
		color: ['#E1EBFF', '#0052CC', '#E95600', '#01A2BF'],
		series: [
			{
				name: '指标额',
				type: 'bar',
				data: zb,
				barWidth: translateFont(12),
				z: 2,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '销售额',
				type: 'bar',
				data: xs,
				barWidth: translateFont(6),
				barGap: '-74%',
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '达成',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: dc,
				z: 4,
			},
		],
	};
	chart.setOption(option);
};

const tableList = ref([]);
let comStatus = ref('loading');
let getData = async () => {
	comStatus.value = 'loading';
	let res = await getChannelTrends({
		skuCode: query.brandId ? query.brandId.split(',') : [],
		team: query.teamId ? query.teamId.split(',') : [],
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		channelCategory: channelCategory.value,
		channelSubclass: channelSubclass.value,
		channel: channel.value,
	});
	tableList.value = res.result;
	comStatus.value = res.result.length < 1 ? 'empty' : 'normal';
};

const emit = defineEmits(['focus']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let echartsId = generateRandomNumber();
onMounted(async () => {
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
.sale-trend {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	&-echarts {
		height: 220px;
		&-card {
			background-color: var(--pv-bgc);
			margin-top: 8px;
			border-radius: 5px;
		}
	}

	&-table {
		background-color: #fff;
		margin-top: 8px;
		overflow: hidden;
		&-title {
			font-size: 12px;
			color: var(--pv-default-color);
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 6px;
			position: relative;
			.icon {
				font-size: 7px;
				position: absolute;
				right: 14px;
				transition: all 0.5s;
			}
			.down {
				transform: rotate(180deg);
			}
		}
		&-content {
			padding: 0 8px 0px;
		}
	}
}
</style>
