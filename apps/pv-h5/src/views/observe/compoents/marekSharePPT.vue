<template>
	<div class="ignore-marek-share">
		<div class="card-title">
			<span>{{ query.province }}&ensp;市场份额趋势分析</span>
		</div>
		<div class="content">
			<div class="ignore-marek-share-echarts" :id="`marek-share-echarts-${echartsId}`"></div>
			<div class="legend">
				<div class="legend-item" :style="{ '--bar-color': color[index % color.length] }" v-for="(item, index) in legend" :key="index">
					<div class="dot"></div>
					<span>{{ item.name }}</span>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { marketShareTrends } from '@/api/sales';
import { generateRandomNumber, getYearMonthBefore, getCurrentYearMonth, isPCTrue } from '@/utils/index';
import * as echarts from 'echarts';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
let filterStore = usefilterStore();
const props = defineProps(['showTags', 'info', 'hiddenStar']);

// 筛选条件
const query = reactive({
	startDate: getYearMonthBefore(5),
	endDate: getCurrentYearMonth(),
	marketId: filterStore.msList.length > 0 ? filterStore.msList[0] : '',
	marketName: filterStore.msList.length > 0 ? filterStore.msList[0] : '',
	hospitalId: '',
	hospitalName: '全部医院',
	province: '全部省市',
});

let myEcharts = echarts;
let chart;
let color = ['#0052CC', '#FF991F', '#01A2BF', '#DD340A', '#2A3E66', '#B7723D', '#836BB5'];
const legend = ref([]);
const result = ref([]);
const lineList = ref([]);
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#marek-share-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let option = {
		legend: {
			show: false,
		},
		color,
		tooltip: {
			show: false,
		},
		grid: {
			left: 20,
			right: 20,
			bottom: 10,
			top: 40,
			containLabel: true,
		},
		xAxis: {
			type: 'category',
			show: true,
			data: result.value.axisMonth,
			axisLabel: {
				show: true, // 是否显示坐标轴刻度标签。
				interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
				inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
				color: ' #6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
				fontSize: '10px',
			},
			axisLine: {
				show: true, // 是否显示坐标轴轴线
				lineStyle: {
					color: '#fff',
					width: 1,
					type: 'solid',
				},
			},
			axisTick: {
				show: true,
			},
		},
		yAxis: {
			type: 'value',
			axisLabel: {
				show: true, // 是否显示坐标轴刻度标签。
				interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
				inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
				color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
				fontSize: '10px',
				formatter: function (params) {
					return `${params}%`;
				},
			},
			splitLine: {
				show: true, // 隐藏 Y 轴横线
				lineStyle: {
					color: 'rgba(223,225,230,.5)',
				},
			},
			splitNumber: 10,
		},
		series: lineList.value,
	};
	chart.setOption(option);
};

let echartsId = generateRandomNumber();
watch(
	() => props.info,
	(n) => {
		if (n.info.data) {
			console.log(n);
			query.province = n.info.province;
			lineList.value = n.info.data.lineList;
			legend.value = n.info.data.legend;
			result.value = n.info.data.result;
			setOption();
		}
	}
);
onMounted(async () => {
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.ignore-marek-share {
	font-size: 10px;
	width: 100%;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 4px 4px;
	.card-title {
		width: 100%;
		border-radius: 4px 4px 0 0;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: #283b5c;
		height: 23px;
		span {
			font-size: 10px;
			color: #fff;
		}
	}
	.content {
		height: calc(100% - 23px);
		display: flex;
		flex-direction: column;
	}
	&-echarts {
		width: 100%;
		height: calc(100% - 70px);
	}
	.legend {
		flex: 1;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 15px;
		justify-content: center;
		&-item {
			color: #6b778c;
			font-size: 12px;
			display: flex;
			align-items: center;
			.dot {
				width: 6px;
				height: 6px;
				margin-right: 7px;
				background-color: var(--bar-color);
				border-radius: 50%;
			}
		}
	}
}
</style>
