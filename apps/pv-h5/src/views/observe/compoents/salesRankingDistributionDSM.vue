<template>
	<div class="sales-ranking-distribution" id="ap-sales-ranking-distribution" :style="{ margin: hiddenSomething ? '0 0' : '' }">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="operation" v-if="!hiddenSomething">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
					<switchCom ref="switchComRef" :defaultValue="props.info" @currencySwitching="currencySwitching"></switchCom>
				</span>
			</div>
			<!-- 通用筛选器 -->
			<globalFilter
				:filters="[
					{ name: 'time', size: '49', queryName: 'dateRange' },
					{ name: 'org', size: '49', queryName: 'personName' },
					{ name: 'brand', size: '32', queryName: 'brandName' },
					{ name: 'city', size: '32', queryName: 'countyName' },
					{ name: 'hospital', size: '32', queryName: 'hospitalName' },
				]"
				:query="query"
				:loading="filterLoading"
				@openTime="openTime"
				@openOrg="openOrg"
				@openBrand="openBrand"
				@openCity="openCity"
				@openHospital="openHospital"
			></globalFilter>
		</div>
		<div class="sales-ranking-distribution-card">
			<filterCom :filterList="filterList" :currentIndex="currentIndex" @filterChange="filterChange" :downloadExcel="false" @_downExcel="downExcel"></filterCom>
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="sales-ranking-distribution-card-content">
				<div class="sales-ranking-distribution-card-content-title">
					<span>
						<span>排名</span>
						<span>人名</span>
					</span>
					<span>{{ currentIndex === '按达成%' ? '达成' : currentIndex === '销售(K)' ? (query.salesOrAmount === '金额' ? '金额' : '数量') : '增长' }}</span>
				</div>
				<div class="sales-ranking-distribution-list">
					<div class="sales-ranking-distribution-list-item" :class="{ isUserPart: item.isUserPart }" v-for="(item, index) in rankingData.chartData" :key="index">
						<span>
							<span v-if="item.ranking === 1"><img src="@/assets/img/num-one.png" alt="" /></span>
							<span v-else-if="item.ranking === 2"><img src="@/assets/img/num-two.png" alt="" /></span>
							<span v-else-if="item.ranking === 3"> <img src="@/assets/img/num-three.png" alt="" /></span>
							<span v-else>{{ item.ranking }}</span>
						</span>
						<span>{{ item.userInfo && item.userInfo.e }}</span>
						<span class="bar">
							<span :style="{ width: widthComputed(item) }"></span>
						</span>
						<span>{{ valueComputed(item) }}</span>
					</div>
					<div v-if="!isShow" class="sales-ranking-distribution-list-average" :style="{ left: averageComputed(rankingData.avg) }">
						<span>
							平均&ensp;<span>{{ currentIndex === '销售(K)' ? formatNumbeWT(rankingData.avg) : rankingData.avg }}{{ currentIndex === '销售(K)' ? 'K' : '%' }}</span>
						</span>
					</div>
				</div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
			<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>
		</div>
		<div class="sale-table">
			<div class="sale-table-title" @click="zk = !zk">
				<span>点击查看明细</span>
				<svg-icon class="icon" :class="{ down: zk }" icon-class="down2"></svg-icon>
			</div>
			<!-- <transition @beforeEnter="handleBeforeEnter" @enter="handleEnter" @leave="handleLeave"> -->
			<div v-show="zk" class="sale-table-content">
				<salesRankingDistributionTable :tableData="tableData" :lastResult="lastResult" :salesOrAmount="query.salesOrAmount"></salesRankingDistributionTable>
			</div>
			<!-- </transition> -->
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :path="`${page}${props.info?.nameCn}`" :defaultCheck="defaultPerson" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :path="`${page}${props.info?.nameCn}`" :personalId="query.personId" :province="query.province" :city="query.city" :skuCode="query.brandId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import { showToast, showLoadingToast } from 'vant';
import Decimal from 'decimal.js';
import { salesRankingDistribution, salesInfoDownload } from '@/api/sales';
import { getFilterTime, formatPrecentOne, formatNumbeWT, getNameByPath, deepClone, filterByLevel } from '@/utils/index';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/user';
import salesRankingDistributionTable from './salesRankingDistributionTable';
let props = defineProps(['info', 'isRecommend', 'hiddenSomething', 'defaultValue', 'summary', 'inChat']);
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
	indicatorType: 'sales',
});
// 默认选中的人员
const defaultPerson = ref('');
//传入一个data数组和level，递归返回其中level大于传入level的项
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = filterByLevel(deepClone(result.orgList), 6);
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterByLevel(deepClone(filterStore.treeInfo), 6);
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterByLevel(deepClone(filterStore.treeInfo), 6);
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
let zk = ref(false);
const tableData = ref([]);
// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesRankingDistributionDSM',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	// fix: 达成率为0时，bar还显示
	rankingData.value.chartData = [];
	await getData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};
// 下载表格
const downExcel = async (emial) => {
	showLoadingToast({
		message: '下载中...',
		forbidClick: true,
		duration: 0,
		class: 'ppt-pc-loading',
	});
	const json = {
		headerData: [
			{ name: '姓名', key: 'territoryCodeCn' },
			{ name: '销售额', key: 'salesM' },
			{ name: '指标额', key: 'targetM' },
			{ name: '达成率 ', key: 'achM' },
			{ name: '同比', key: 'growthM' },
		],
		bodyData: tableData.value,
		footerData: lastResult.value,
	};
	const query = {
		name: props.info?.nameCn,
		email: emial,
		tableInfo: json,
	};
	await salesInfoDownload(query);
	showToast({
		message: '数据下载成功，稍后会发送到您的邮箱。',
		position: 'top',
	});
};

// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesRankingDistributionDSM',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);

const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesRankingDistributionDSM',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city, hospital, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	await getData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	await getData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 城市

const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesRankingDistributionDSM',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	console.log(params);
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
// 医院筛选
const openHospital = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesRankingDistributionDSM',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	await getData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};

const emit = defineEmits(['focus', 'hiddenSomething', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let rankingData = ref({
	avg: 0,
	chartData: [],
	salesRankingMessageFirst: '',
	salesRankingMessageSecond: '',
});

// 获取数据
let isShow = ref(true);
let comStatus = ref('loading');
const lastResult = ref({});
let typeMap = {
	'销售(K)': 'sales',
	'按达成%': 'ach',
	'按同比增长%': 'growth',
	'环比(%)': 'mom',
	'同比增量贡献(%)': 'growthAch',
	'环比增量贡献(%)': 'momAch',
};
let getData = async () => {
	isShow.value = true;
	try {
		comStatus.value = 'loading';
		let res = await salesRankingDistribution({
			type: typeMap[currentIndex.value],
			skuCode: query.brandId ? query.brandId.split(',') : [],
			territoryCode: query.personId ? query.personId.split(',') : [],
			hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
			startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
			endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
			province: query.province || [],
			city: query.city || [],
			level: '6',
			salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
		});
		query.salesOrAmount = switchComRef.value.type;
		if (res.result.isExceededAuthority) {
			comStatus.value = 'isExceededAuthority';
			emit('summaryIng', { noSummary: true });
			return;
		}
		isShow.value = false;
		rankingData.value = res.result;
		rankingData.value.avg = res.result.avg ? new Decimal(res.result.avg).toDecimalPlaces(1) : 0;
		if (res.result.userPart.length > 0) {
			res.result.userPart = res.result.userPart.map((ele) => {
				return { ...ele, isUserPart: true };
			});
			rankingData.value.chartData = [...res.result.userPart, ...res.result.firstPart, ...res.result.remainingPart];
		} else {
			if (res.result.firstPart && res.result.remainingPart) {
				rankingData.value.chartData = [...res.result.firstPart, ...res.result.remainingPart];
			}
		}
		comStatus.value = rankingData.value.chartData.length < 1 ? 'empty' : 'normal';

		// 表格数据
		const tData = [...res.result.firstPart, ...res.result.remainingPart];
		const larr = [];
		tData.forEach((item) => {
			larr.push({
				territoryCodeCn: item.userInfo.e,
				targetM: item.target === 0 ? 0 : formatNumbeWT(item.target),
				salesM: item.sales === 0 ? 0 : formatNumbeWT(item.sales),
				achM: `${formatPrecentOne(item.ach)}`,
				growthM: `${formatPrecentOne(item.growth)}`,
				growthAch: `${formatPrecentOne(item.growthAch)}`,
				momAch: `${formatPrecentOne(item.momAch)}`,
			});
		});
		tableData.value = larr;
		lastResult.value = {
			territoryCodeCn: '合计',
			salesM: res.result.total.sales === 0 ? 0 : formatNumbeWT(res.result.total.sales),
			targetM: res.result.total.target === 0 ? 0 : formatNumbeWT(res.result.total.target),
			achM: `${formatPrecentOne(res.result.total.ach)}`,
			growthM: `${formatPrecentOne(res.result.total.growth)}`,
			growthAch: `${formatPrecentOne(res.result.total.growthAch)}`,
			momAch: `${formatPrecentOne(res.result.total.momAch)}`,
		};
		if (props.summary) {
			let data = {
				chartData: res.result.chartData.slice(0, 10),
			};
			emit('summaryIng', { data, params: query });
		}
		return Promise.resolve();
	} catch (error) {
		return Promise.reject(error);
	}
};

let filterList = ref(['销售(K)', '按达成%', '按同比增长%', '同比增量贡献(%)', '环比(%)', '环比增量贡献(%)']);

let currentIndex = ref('销售(K)');
let filterChange = (item) => {
	currentIndex.value = item;
	query.indicatorType = typeMap[currentIndex.value];
	getData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-展示方式`, item);
};
let valueComputed = computed(() => {
	return function (item) {
		console.log(currentIndex.value);

		let value = item[typeMap[currentIndex.value]];
		if (currentIndex.value === '销售(K)') {
			return (formatNumbeWT(value) + '').replace(/,/g, '') + 'K';
		} else {
			return new Decimal(value).toDecimalPlaces(0) + '%';
		}
	};
});
let widthComputed = computed(() => {
	return function (item) {
		let value = item[typeMap[currentIndex.value]];
		// 最大高度为最大达成
		//rankingData.value.achMax = 56vw
		if (value <= 0) return '1%';
		return (value / rankingData.value.max) * 100 + '%';
	};
});
let averageComputed = computed(() => {
	return function (value) {
		//rankingData.value.achMax = 56vw
		if (value <= 0) {
			return 18 + 'vw';
		}
		return `${18 + ((value * 56) / Number(rankingData.value.max) ? (value * 56) / Number(rankingData.value.max) : 0)}vw`;
	};
});
const switchComRef = ref(null);
const currencySwitching = async (type) => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesRankingDistributionDSM',

			defaultValue: {
				...toRaw(query),
			},
		});

		return;
	}
	// proxy.$umeng('点击', `${props.umengPath}-销量金额筛选器`, type);
	await getData();
};
const defaultSku = ref('');
const defaultHos = ref('');
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			brand, // 传入 ref
			orgZ, // 传入 ref
			time,
			defaultSku, // 传入 ref
			defaultHos, // 传入 ref
			defaultPerson, // 传入 ref
			defaultProvinceCity, // 传入 ref
			currentIndex,
			switchComRef,
		});
	}
	//初始化级联
	initFilters();
	await getData();
});
defineExpose({
	query,
});
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/salesRankingDistribution.scss';
.sales-ranking-distribution {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}

	&-tip {
		padding: 5px 5px 0;
		font-size: 12px;
		color: #7c88a6;
	}
	&-card {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		&-content {
			height: 175px;
			overflow-y: auto;
			overflow-x: hidden;
			/* 设置纵向滚动条样式 */
			&::-webkit-scrollbar {
				width: 4px; /* 设置纵向滚动条宽度 */
			}

			&::-webkit-scrollbar-thumb {
				background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
				border-radius: 3px;
			}

			&::-webkit-scrollbar-track {
				background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
				border-radius: 5px;
			}
			&-title {
				margin-bottom: 4px;
				font-size: 9px;
				padding: 8px 10px 0px 10px;
				position: sticky;
				top: -1px;
				background-color: #fff;
				z-index: 10;
				display: flex;
				justify-content: space-between;
				color: var(--pv-default-color);
				span:nth-child(1) {
					span:nth-child(1) {
						margin-right: 13px;
					}
				}
			}
		}
	}
	&-list {
		font-size: 9px;
		padding: 8px 10px 0px 10px;
		position: relative;
		// &-title {
		// 	display: flex;
		// 	justify-content: space-between;
		// 	color: var(--pv-default-color);
		// 	padding-bottom: 8px;
		// 	span:nth-child(1) {
		// 		span:nth-child(1) {
		// 			margin-right: 13px;
		// 		}
		// 	}
		// }
		&-item {
			display: flex;
			align-items: center;
			margin-bottom: 8px;
			position: relative;
			z-index: 2;
			span:nth-child(1),
			span:nth-child(2) {
				width: 30px;
				text-align: center;
				margin-right: 4px;
				flex-shrink: 0;
				color: rgba(0, 0, 0, 0.45);
			}
			span:nth-child(1) {
				width: 20px;
				img {
					width: 20px;
					height: 13px;
				}
			}
			.bar {
				width: 210px !important;
				flex-shrink: 0;
				margin-right: 4px;
				height: 6px;
				span {
					width: 0;
					background-color: var(--pv-gl);
					display: block;
					height: 6px;
				}
			}
			span:nth-last-child(1) {
				flex-shrink: 0;
				flex-grow: 1;
				text-align: right;
			}
		}
		&-item:nth-child(1) {
			margin-top: 8px;
		}
		.isUserPart {
			background-color: #f4f5f7;
			box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
			padding: 7px 0;
			position: relative;
			span {
				color: var(--pv-default-color) !important;
			}
			&::before {
				content: '';
				display: block;
				position: absolute;
				width: 2px;
				height: 100%;
				background-color: var(--pv-gl);
				border-radius: 8px 0 0 8px;
				left: -2px;
			}
		}
		&-average {
			position: absolute;
			top: 0px;
			color: #172b4d;
			font-weight: 600;
			height: 100%;
			z-index: 2;
			width: 1px;
			& > span {
				position: absolute;
				transform: translateX(-50%);
				width: 100px;
				text-align: center;
				span {
					font-weight: 600;
					color: var(--pv-gl);
				}
			}
			&::after {
				content: '';
				position: absolute;
				z-index: 2;
				top: 21px;
				left: 50%;
				transform: translateX(-50%);
				width: 1px;
				height: calc(100% - 20px);
				border-left: 1px dashed #dfe1e6;
			}
		}
	}
	.q-box-mt {
		margin-top: 10px;
	}
}

.sale-table {
	background-color: #fff;
	margin-top: 8px;
	overflow: hidden;
	&-title {
		font-size: 12px;
		color: var(--pv-default-color);
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 6px;
		position: relative;
		.icon {
			font-size: 7px;
			position: absolute;
			right: 14px;
			transition: all 0.5s;
		}
		.down {
			transform: rotate(180deg);
		}
	}
	&-content {
		padding: 0 8px 0px;
	}
}
</style>
