<template>
	<div class="sale-brand" id="ap-sale-brand">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span>
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<!-- 通用筛选器 -->
		<globalFilter
			:filters="[
				{ name: 'time', size: '49', queryName: 'dateRange' },
				{ name: 'org', size: '49', queryName: 'personName' },
				{ name: 'brand', size: '49', queryName: 'brandName' },
				{ name: 'city', size: '49', queryName: 'countyName' },
			]"
			:query="query"
			:loading="filterLoading"
			@openTime="openTime"
			@openOrg="openOrg"
			@openBrand="openBrand"
			@openCity="openCity"
			@openHospital="openHospital"
		></globalFilter>
		<div class="sale-brand-filter">
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="sale-brand-filter-echarts-card">
				<div class="sale-brand-filter-echarts" :id="`sale-brand-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
			<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>
		</div>
		<div class="shop-table">
			<div class="shop-table-title" @click="zk = !zk">
				<span>点击查看明细</span>
				<svg-icon class="icon" :class="{ down: zk }" icon-class="down2"></svg-icon>
			</div>
			<transition @beforeEnter="handleBeforeEnter" @enter="handleEnter" @leave="handleLeave">
				<div v-show="zk" class="shop-table-detail">
					<div class="shop-table-detail-title">
						<span>{{ detailDate }}</span>
						<span>无销售额终端明细</span>
					</div>
					<div class="shop-table-detail-list">
						<div class="shop-table-detail-list-item" v-for="item in detailList" :key="item.hospCode">{{ item.hospName }}</div>
					</div>
				</div>
			</transition>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 复选框 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { salesShop } from '@/api/sales';
import { generateRandomNumber, getFilterTime, translateFont, formatNumber, getNamesByIdsInArray, spaces, getNameByPath, isPCTrue } from '@/utils/index';
import usefilterStore from '@/store/modules/filter';
let props = defineProps(['info', 'defaultValue', 'summary', 'inChat']);
let zk = ref(false);
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	countyName: '全部省市',
});
// 默认选中人员
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesShop',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};

// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesShop',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesShop',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgZ.value.show = true;
};
const city = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, initFilters, filterLoading } = useCascader(query, { city, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 城市

const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesShop',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	console.log(params);
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
let myEcharts = echarts;
let chart;
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#sale-brand-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	chart.on('click', function (params) {
		detailDate.value = params.name;
		detailList.value = shopData.value.data[params.dataIndex].noSalesHospData;
	});
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};

let setOption = () => {
	let mList = shopData.value.xAxis;
	let tNum = shopData.value.data.map((ele) => ele.total);
	let sNum = shopData.value.data.map((ele) => ele.noSalesCount);
	let option = {
		grid: {
			top: translateFont(55),
			left: translateFont(48),
			right: translateFont(35),
			bottom: translateFont(30),
		},
		legend: {
			data: ['架构终端数', '无销售额终端'],
			left: 'center',
			top: translateFont(15),
			itemGap: translateFont(15),
			itemWidth: translateFont(15),
			itemHeight: translateFont(3),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = '';
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${formatNumber(item.value)}</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			axisPointer: {
				type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
				},
			},
			extraCssText: 'z-index:9',
		},
		xAxis: [
			{
				type: 'category',
				data: mList,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: spaces(mList.length), // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumber(value);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
		],
		color: ['#E1EBFF', '#5342AA'],
		series: [
			{
				name: '架构终端数',
				type: 'bar',
				data: tNum,
				barWidth: translateFont(12),
				z: 2,
				emphasis: { disabled: true },
			},
			{
				name: '无销售额终端',
				type: 'bar',
				data: sNum,
				barWidth: translateFont(6),
				barGap: '-74%',
				z: 4,
				emphasis: { disabled: true },
			},
		],
	};
	chart.setOption(option);
};

let shopData = ref({});
let detailDate = ref('');
let detailList = ref([]);
let comStatus = ref('loading');

let getData = async () => {
	comStatus.value = 'loading';

	let res = await salesShop({
		skuCode: query.brandId ? query.brandId.split(',') : [],
		territoryCode: query.personId ? query.personId.split(',') : [],
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		province: query.province || [],
		city: query.city || [],
	});
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}
	shopData.value = res.result;
	if (res.result.data.length) {
		detailDate.value = res.result.data[0].date;
		detailList.value = res.result.data[0].noSalesHospData;
	}
	comStatus.value = res.result.data?.length < 1 ? 'empty' : 'normal';
	if (props.summary) {
		let data = { ...res.result };
		data.data.forEach((ele) => {
			//如果有ele.noSalesHospData，ele.noSalesHospData取前三个
			if (ele.noSalesHospData) {
				ele.noSalesHospData = ele.noSalesHospData.slice(0, 3);
			}
		});
		emit('summaryIng', { data, params: query });
	}
};

// 表格过渡动画
const handleBeforeEnter = (el) => {
	el.style.height = '0px';
};
const handleEnter = (el) => {
	el.style.height = 'auto';
	let h = el.clientHeight;
	el.style.height = '0px';
	requestAnimationFrame(() => {
		el.style.height = h + 'px';
		el.style.transition = '.5s';
	});
	proxy.$umeng('点击', `${page}${props.info.nameCn}`, '点击查看明细');
};
const handleLeave = (el) => {
	requestAnimationFrame(() => {
		el.style.height = '0px';
		el.style.transition = '.5s';
	});
};

const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let echartsId = generateRandomNumber();
const defaultSku = ref('');
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			brand, // 传入 ref
			orgZ, // 传入 ref
			time,
			defaultSku, // 传入 ref
			defaultPerson, // 传入 ref
			defaultProvinceCity, // 传入 ref
		});
	}
	//初始化级联
	initFilters();
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/salesShop.scss';
.sale-brand {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	&-filter {
		margin-top: 8px;
		border-radius: 5px;
		background-color: var(--pv-bgc);

		padding-bottom: 15px;
		&-echarts {
			height: 220px;
		}
	}
	.shop-table {
		background-color: #fff;
		margin-top: 8px;
		overflow: hidden;
		&-title {
			font-size: 12px;
			color: var(--pv-default-color);
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 6px;
			position: relative;
			.icon {
				font-size: 7px;
				position: absolute;
				right: 14px;
				transition: all 0.5s;
			}
			.down {
				transform: rotate(180deg);
			}
		}
		&-detail {
			font-size: 9px;
			border: 1px solid #dfe1e6;
			border-bottom: none;
			margin: 0 7px;
			&-title {
				font-weight: bold;
				border-bottom: 1px solid #dfe1e6;
				padding: 8px 9px;
				background-color: var(--pv-card-bgc);
				span:nth-child(1) {
					color: var(--pv-tabbar-active);
					margin-right: 4px;
				}
			}
			&-list {
				height: 214px;
				overflow-y: auto;
				&::-webkit-scrollbar {
					width: 4px; /* 设置纵向滚动条宽度 */
				}

				&::-webkit-scrollbar-thumb {
					background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
					border-radius: 3px;
				}

				&::-webkit-scrollbar-track {
					background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
					border-radius: 5px;
					// margin: 50px 0px;
				}
				&-item {
					padding: 8px 9px;
					border-bottom: 1px solid #dfe1e6;
				}
			}
		}
	}
}
</style>
