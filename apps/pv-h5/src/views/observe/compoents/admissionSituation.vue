<template>
	<div class="admission-situation" id="ap-admission-situation">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span>
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
			<!-- 通用筛选器 -->
			<globalFilter
				:filters="[
					{ name: 'time', size: '49', queryName: 'dateRange', single: true },
					{ name: 'org', size: '49', queryName: 'personName' },
					{ name: 'brand', size: '32', queryName: 'brandName' },
					{ name: 'city', size: '32', queryName: 'countyName' },
					{ name: 'hospital', size: '32', queryName: 'hospitalName' },
				]"
				:query="query"
				:loading="filterLoading"
				@openTime="openTime"
				@openOrg="openOrg"
				@openBrand="openBrand"
				@openCity="openCity"
				@openHospital="openHospital"
			></globalFilter>
		</div>
		<loading v-show="comStatus === 'loading'"></loading>

		<div v-show="comStatus === 'normal'" class="admission-situation-card">
			<div class="admission-situation-echarts" :id="`admission-situation-echarts-${echartsId}`"></div>
		</div>
		<empty-com v-show="comStatus === 'empty'"></empty-com>
		<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>

		<div class="admission-table">
			<div class="admission-table-title" @click="zk = !zk">
				<span>点击查看明细</span>
				<svg-icon class="icon" :class="{ down: zk }" icon-class="down2"></svg-icon>
			</div>
			<transition @beforeEnter="handleBeforeEnter" @enter="handleEnter" @leave="handleLeave">
				<div v-show="zk" class="admission-table-detail">
					<filterCom :filterList="filterList" :currentIndex="currentIndex" @filterChange="filterChange"></filterCom>
					<div class="admission-table-box">
						<van-list v-model:loading="state.isLoading" :offset="30" :finished="state.finished" finished-text="没有更多了" @load="onLoad">
							<div class="admission-table-box-title">
								<div class="admission-table-box-title-desc left-item">产品名称</div>
								<div class="admission-table-box-title-desc right-item">医院名称</div>
							</div>
							<div class="admission-table-box-list" v-for="(item, index) in state.sList" :key="index">
								<div class="admission-table-box-list-item left-item">{{ item.skuCn }}</div>
								<div class="admission-table-box-list-item right-item">{{ item.hospName }}</div>
							</div>
						</van-list>
					</div>
				</div>
			</transition>
		</div>
		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :defaultHosCheckedAll="defaultHosCheckedAll" :path="`${page}${props.info?.nameCn}`" :personalId="query.personId" :skuCode="query.brandId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { generateRandomNumber, translateFont, formatNumber, getFilterTime, getNameByPath, isPCTrue, deepClone } from '@/utils/index';
import { accessStatus, accessStatusInfo } from '@/api/sales';
import usefilterStore from '@/store/modules/filter';
let props = defineProps(['info', 'defaultValue', 'summary', 'inChat']);
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
	currentStatus: '正式进药',
});
// 默认选中的人员
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSituation',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	getDetailData();
	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};

const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();
	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	getDetailData();
	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSituation',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSituation',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city, hospital, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	getDetailData();
	await getData();
	chart.resize();
	setOption();
	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	await getData();
	chart.resize();
	setOption();

	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	getDetailData();

	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 城市
const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSituation',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};

// 医院筛选
const openHospital = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSituation',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	await getData();
	chart.resize();
	setOption();
	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	getDetailData();

	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};

let myEcharts = echarts;
let chart;
let chartLegend = ref([]);
let chartData = ref([]);
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#admission-situation-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	const series = chartLegend.value.map((legend) => {
		// 提取该 admissionStatus 下所有药品的 count 值
		const data = chartData.value.map((item) => {
			const statusData = item.data.find((d) => d.admissionStatus === legend);
			return statusData ? statusData.count : 0;
		});
		return {
			name: legend,
			type: 'bar',
			data: data, // data 是一个包含所有值的数组
			stack: 'total',
			barWidth: translateFont(6), // 假设 translateFont 是已定义的函数
			emphasis: { disabled: true },
		};
	});
	console.log(series);

	let names = chartData.value.map((i) => i.name); //产品

	let option = {
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				console.log(value);
				let str = `<div class="product">${value[0].axisValue}</div>`;
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${formatNumber(item.value)}</span></div>`;
				}
				console.log(str);
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			axisPointer: {
				type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
				lineStyle: {
					color: '#fff',
					width: translateFont(0),
				},
			},
			extraCssText: 'z-index:9',
		},
		legend: {
			data: chartLegend.value,
			left: 'center',
			top: translateFont(15),
			itemGap: translateFont(5),
			itemWidth: translateFont(15),
			itemHeight: translateFont(3),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		grid: {
			top: translateFont(50),
			left: 0,
			right: translateFont(10),
			bottom: 0,
			containLabel: true,
		},
		xAxis: {
			type: 'value',
			axisLabel: {
				show: false, // 是否显示坐标轴刻度标签。
			},
			axisLine: {
				show: false, // 是否显示坐标轴轴线
			},
			splitLine: {
				show: false,
			},
		},
		yAxis: {
			type: 'category',
			data: names,
			axisLine: {
				show: true, // 是否显示坐标轴轴线
				lineStyle: {
					color: '#eee',
					width: translateFont(1),
					type: 'solid',
				},
			},
			axisTick: {
				show: false, // 是否显示坐标轴刻度。
			},
			axisLabel: {
				show: true, // 是否显示坐标轴刻度标签。
				interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
				inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
				rich: {
					name: {
						width: translateFont(68),
						backgroundColor: 'none',
						align: 'left',
						fontSize: translateFont(9),
						color: '#6B778C',
						padding: [0, 0, 0, translateFont(9)],
					},
				},
				formatter: function (value) {
					if (value.length > 8) {
						return `{name|${value.slice(0, 8)}...}`;
					} else {
						return `{name|${value}}`;
					}
				},
			},
		},
		color: ['#0251CB', '#0379FF', '#79E1F2', '#DFE1E6'],
		series,
	};
	chart.setOption(option);
};
let filterList = ref(['正式进药', '临时采购', '院内决策药房', '未准入']);
let currentIndex = ref('正式进药');
const filterChange = (item) => {
	currentIndex.value = item;
	query.currentStatus = currentIndex.value;
	state.page = 0;
	state.isLoading = true;
	state.finished = false;
	state.sList = [];
	getDetailData();
};

let zk = ref(false);
// 表格过渡动画
const handleBeforeEnter = (el) => {
	el.style.height = '0px';
};
const handleEnter = (el) => {
	el.style.height = 'auto';
	let h = el.clientHeight;
	el.style.height = '0px';
	requestAnimationFrame(() => {
		el.style.height = h + 'px';
		el.style.transition = '.5s';
	});
	proxy.$umeng('点击', `${page}${props.info.nameCn}`, '点击查看明细');
};
const handleLeave = (el) => {
	requestAnimationFrame(() => {
		el.style.height = '0px';
	});
};
const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let echartsId = generateRandomNumber();

let comStatus = ref('loading');

let getData = async () => {
	comStatus.value = 'loading';
	let res = await accessStatus({
		skuCode: query.brandId ? query.brandId : '',
		territoryCode: query.personId ? query.personId : '',
		hospCode: query.hospitalId ? query.hospitalId : '',
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		province: Array.isArray(query.province) ? query.province?.join(',') || '' : '',
		city: Array.isArray(query.city) ? query.city?.join(',') || '' : '',
	});
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}

	chartLegend.value = res.result.legend;
	chartData.value = res.result.data?.reverse();
	comStatus.value = res.result.data.length < 1 ? 'empty' : 'normal';

	let chartDiv = document.querySelector(`#admission-situation-echarts-${echartsId}`);
	if (isPCTrue()) {
		chartDiv.style.height = `${35 + chartData.value.length * 25}px`;
	} else {
		chartDiv.style.height = `${(35 + chartData.value.length * 25) / 3.75}vw`;
	}
	if (props.summary) {
		let data = deepClone(res.result);
		data.data.forEach((ele) => {
			ele.data.forEach((i) => {
				i['医院数量'] = i.count;
				Reflect.deleteProperty(i, 'count');
			});
		});
		if (query.hospitalId) {
			data = await Promise.all([getDetail('正式进药'), getDetail('临时采购'), getDetail('未准入'), getDetail('院内决策药房')]);
		}
		emit('summaryIng', { data, params: query });
	}
};

//获取底部明细数据
// 分页
let state = reactive({
	sList: [],
	isLoading: false, // 列表的loading
	finished: false, //结束
	total: 0, //总数
	page: 0,
	size: 20,
});
let getDetailData = async (type) => {
	try {
		let res = await accessStatusInfo({
			skuCode: query.brandId,
			currentStatus: type ? type : currentIndex.value,
			page: state.page,
			size: state.size,
			territoryCode: query.personId ? query.personId : '',
			hospCode: query.hospitalId ? query.hospitalId : '',
			startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
			endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
			province: Array.isArray(query.province) ? query.province?.join(',') || '' : '',
			city: Array.isArray(query.city) ? query.city?.join(',') || '' : '',
		});
		const rows = res.result.content;
		state.isLoading = false;
		state.total = res.result.totalElements;
		if (rows == null || rows.length === 0) {
			state.finished = true;
			return;
		}
		state.sList = state.sList.concat(rows);
		state.finished = false;
		if (state.sList.length >= state.total) {
			state.finished = true;
		}
		return Promise.resolve();
	} catch (error) {
		state.isLoading = false;
		state.finished = true;
		return Promise.reject(error);
	}
};

const onLoad = () => {
	if (state.finished) return;
	state.page++;
	state.isLoading = true;
	getDetailData();
};
const defaultSku = ref('');
const defaultHos = ref('');
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			brand, // 传入 ref
			orgZ, // 传入 ref
			time,
			defaultSku, // 传入 ref
			defaultHos, // 传入 ref
			defaultPerson, // 传入 ref
			defaultProvinceCity, // 传入 ref
			currentIndex,
		});
	}
	//初始化级联
	initFilters();
	await getData();
	initChart();
	getDetailData();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/admissionSituation.scss';
.admission-situation {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	&-card {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		padding-bottom: 15px;
		min-height: 140px;
		max-height: 175px;
		overflow-y: auto;
		overflow-x: hidden;
		/* 设置纵向滚动条样式 */
		&::-webkit-scrollbar {
			width: 4px; /* 设置纵向滚动条宽度 */
		}

		&::-webkit-scrollbar-thumb {
			background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
			border-radius: 5px;
		}
	}
	.admission-table {
		background-color: #fff;
		margin-top: 8px;
		overflow: hidden;
		&-title {
			font-size: 12px;
			color: var(--pv-default-color);
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 6px;
			position: relative;
			.icon {
				font-size: 7px;
				position: absolute;
				right: 14px;
				transition: all 0.5s;
			}
			.down {
				transform: rotate(180deg);
			}
		}
		&-detail {
			margin: 0 7px;
		}
		&-box {
			max-height: 180px;
			overflow-y: auto;
			margin-top: 5px;
			position: relative;
			font-size: 9px;
			border: 1px solid #dfe1e6;
			border-bottom: none;
			&-title {
				display: flex;
				position: sticky;
				top: 0;
				z-index: 1;
				border-bottom: 1px solid #dfe1e6;
				background-color: var(--pv-card-bgc);
				&-desc {
					flex: 1;
					font-weight: bold;
					text-align: center;
					padding: 8px 9px;
					border-right: 1px solid #dfe1e6;
				}
				&-desc:last-child {
					border-right: none;
				}
			}
			&-list {
				display: flex;
				&-item {
					// flex: 1;
					text-align: center;
					padding: 8px 9px;
					border-right: 1px solid #dfe1e6;
					border-bottom: 1px solid #dfe1e6;
				}
				&-item:last-child {
					border-right: none;
				}
			}
			.left-item {
				flex: 1;
			}
			.right-item {
				flex: 3;
			}
			:deep(.van-list__finished-text) {
				font-size: 9px;
				line-height: 3.1;
			}
			:deep(.van-loading__text) {
				font-size: 9px;
				line-height: 3.1;
			}
			// &::-webkit-scrollbar {
			// 	width: 4px; /* 设置纵向滚动条宽度 */
			// }
			// &::-webkit-scrollbar-thumb {
			// 	background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
			// 	border-radius: 3px;
			// }
			// &::-webkit-scrollbar-track {
			// 	background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
			// 	border-radius: 5px;
			// 	margin-top: 30px;
			// }
		}
	}
}
</style>
