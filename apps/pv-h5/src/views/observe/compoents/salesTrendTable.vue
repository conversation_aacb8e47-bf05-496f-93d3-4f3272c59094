<template>
	<div class="ignore-sale-table">
		<vxe-table show-overflow :show-footer="show" max-height="400" :sort-config="{ trigger: 'cell' }" :footer-method="footerMethod" :data="props.detailList" @scroll="onScroll">
			<vxe-column field="date" title="年月" align="left" width="70px" fixed="left"></vxe-column>
			<vxe-column field="sales" :title="salesOrAmount === '金额' ? '销售额(K)' : '销售量(K)'" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="target" :title="salesOrAmount === '金额' ? '指标额(K)' : '指标量(K)'" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="ach" title="达成(%)" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="growth" title="同比(%)" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="mom" title="环比(%)" align="right" sortable width="70px"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
const props = defineProps(['detailList', 'lastResult', 'salesOrAmount']);
const footerData = ref([]);
const show = ref(false);
watch(
	() => props.lastResult,
	(val) => {
		if (val) {
			footerData.value = [];
			let tempData = [val.date, val.sales, val.target, val.ach, val.growth, val.mom];
			footerData.value.push(tempData);
			show.value = true;
		}
	},
	{ immediate: true }
);

const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};

let onScroll = (e) => {
	if (e.isY && e.scrollHeight - e.scrollTop === e.bodyHeight) {
		console.log('触底了');
	}
};
</script>
<style lang="scss" scoped></style>
