<template>
	<div class="visitdetails" id="ap-visitdetails">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span>
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<!-- 销售趋势 -->
		<div>
			<div class="q-box">
				<!-- 时间 -->
				<div @click="openTime" class="q-width-100 q-fileter-item">
					<span class="item-title ell">{{ query.startDate + ' - ' + query.endDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div class="q-box">
				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.productName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div @click="openOrg" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部医院 -->
				<div @click="openHospital" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.hospitalName === '全部医院' ? '全部终端' : query.hospitalName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
		</div>
		<div class="visitdetails-echarts-card">
			<filterCom :downloadExcel="isSalesDownLoad" @_downExcel="downExcel" :filterList="filterList" :currentIndex="currentIndex" @filterChange="filterChange"></filterCom>
			<div v-show="comStatus !== 'isExceededAuthority'" class="table">
				<visitDetailsTable :detailList="detailList" @pullData="onLoad" :loading="state.isLoading"></visitDetailsTable>
			</div>
			<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy v-if="showUpdateTime" :updateTime="props.info?.dataSyncTime" :isYMD="true" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 品牌 -->
		<OneLevelRadio ref="brand" :list="brandData" :path="`${page}${props.info?.nameCn}-产品`" :defaultCheck="query.productId" @_confirm="brandConfirm"></OneLevelRadio>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :path="`${page}${props.info?.nameCn}`" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
	</div>
</template>
<script setup>
import { visitInfo, visitInfoDownload } from '@/api/report';
import { getFilterTime, getNameByPath } from '@/utils/index';
import visitDetailsTable from './visitDetailsTable';
import usefilterStore from '@/store/modules/filter';
import usePremissionStore from '@/store/modules/premission';
import { showLoadingToast, showToast } from 'vant';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const relam = enterStore.enterInfo.id;
const perStore = usePremissionStore();
let filterStore = usefilterStore();
let props = defineProps(['info']);
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let showUpdateTime = ref(true);
if (route.fullPath.includes('/report')) {
	showUpdateTime.value = false;
}

const isSalesDownLoad = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'isSalesDownloadExcel');
});

const brandData = ref([]);
const productList = relam === 'muqiao' ? filterStore.productMQInfo : filterStore.productInfo;
for (const item of productList) {
	brandData.value.push({ id: item.productCode, name: item.productCn });
}

// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	productId: brandData.value.length > 0 ? brandData.value[0].id : '',
	productName: brandData.value.length > 0 ? brandData.value[0].name : '',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
});
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	reload();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};

// 品牌筛选
const brand = ref(null);
const openBrand = () => {
	brand.value.show = true;
};

let brandConfirm = async ({ id, name }) => {
	query.productName = name;
	query.productId = id;
	reload();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, name);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgZ.value.show = true;
};

const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	reload();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};

// 医院筛选
const hospital = ref(null);
const openHospital = () => {
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	reload();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};

//获取明细数据
// 分页
let state = reactive({
	isLoading: true, // 列表的loading
	finished: false, //结束
	total: 0, //总数
	page: 0,
	size: 20,
});
let detailList = ref([]);
let comStatus = ref('loading');
const getDetailData = async () => {
	try {
		state.isLoading = true;
		let res = await visitInfo({
			productCode: query.productId ? query.productId.split(',') : [],
			territoryCode: query.personId ? query.personId.split(',') : [],
			hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
			visitMethod: visitMethod.value ? [visitMethod.value] : [],
			page: state.page,
			sort: 'visitTime Desc',
			size: state.size,
			startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
			endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		});
		if (res.result.isExceededAuthority) return (comStatus.value = 'isExceededAuthority');
		const rows = res.result.content;
		state.isLoading = false;
		state.total = res.result.totalElements;
		if (rows == null || rows.length === 0) {
			state.finished = true;
			return;
		}
		for (const item of rows) {
			detailList.value.push({
				visitTime: item.visitTime.split('T')[0].replace(/-/g, '/'),
				hospital: item.hospName,
				doctor: item.doctorName,
				username: item.username,
				visitMethod: item.visitMethod,
				doctorFeedback: item.doctorFeedback,
				visitTarget: item.visitTarget,
				visitContent: item.visitContent,
				productKeyInformation: item.productKeyInformation,
			});
		}
		state.finished = false;
		if (detailList.value.length >= state.total) {
			state.finished = true;
		}
		return Promise.resolve();
	} catch (error) {
		state.isLoading = false;
		state.finished = true;
		return Promise.reject(error);
	}
};

// 下载明细数据
const downExcel = async (emial) => {
	try {
		showLoadingToast({
			message: '下载中...',
			forbidClick: true,
			duration: 0,
			className: 'ppt-pc-loading',
		});
		await visitInfoDownload({
			productCode: query.productId ? query.productId.split(',') : [],
			territoryCode: query.personId ? query.personId.split(',') : [],
			hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
			visitMethod: visitMethod.value ? [visitMethod.value] : [],
			page: 0,
			size: 100000,
			startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
			endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
			email: emial,
		});
		showToast({
			message: '数据下载成功，稍后会发送到您的邮箱。',
			position: 'top',
		});
	} catch (error) {
		console.log(error);
	}
};

const onLoad = () => {
	state.page++;
	if (state.finished) return;
	getDetailData();
};

const reload = () => {
	detailList.value = [];
	state.page = 0;
	state.finished = false;
	getDetailData();
};

const emit = defineEmits(['focus']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let filterList = ref(['全部', '面对面', '电话', '微信']);
let currentIndex = ref('全部');
let visitMethod = ref('');
const filterChange = (item) => {
	currentIndex.value = item;
	if (item === '全部') {
		visitMethod.value = '';
	} else {
		visitMethod.value = item;
	}
	reload();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-拜访方式`, item);
};
onMounted(async () => {
	initFilter();
	getDetailData();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/visitDetails.scss';
.visitdetails {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}

	&-echarts {
		&-card {
			background-color: var(--pv-bgc);
			margin-top: 8px;
			border-radius: 5px;
			// padding: 0 15px 15px;
		}
	}
}
</style>
