<template>
	<div class="ignore-visit-summary">
		<div class="card-title">
			<span>{{ info.info.name }}</span>
		</div>
		<div class="ignore-visit-summary-echarts-card">
			<!-- <div class="visit-summary-echarts" :id="`visit-summary-echarts-${echartsId}`"></div> -->
			<div class="item" v-for="(item, index) in visitList" :key="index">
				<div class="item-name">{{ item.doctorLevel }}级客户</div>
				<div class="item-fg">
					<span
						>覆盖：<span>{{ item.visitDoctor }} 人</span></span
					>
					<span class="item-fg-right"
						>平均每位客户被拜访<span>{{ item.visitAvg }}</span
						>次</span
					>
				</div>
				<div class="item-detail">
					<span v-for="(iten, idx) in item.list" :key="idx" :style="{ backgroundColor: colors[idx] }"> {{ iten.visitMethod + ': ' + iten.visitNum }}</span>
				</div>
				<div class="item-tip">
					实际拜访<span>{{ item.visitNum }}</span
					>次，计划拜访<span>{{ item.targetNum }}</span
					>次，计划执行力<span>{{ new Decimal(item.visitAch).toDecimalPlaces(0) + '%' }}</span>
				</div>
				<div class="item-echarts" :id="`item-echarts${echartsIds[index]}`"></div>
			</div>
		</div>
	</div>
</template>
<script setup>
import Decimal from 'decimal.js';
import * as echarts from 'echarts';
import { visitSummaryInfo } from '@/api/report';
import { generateRandomNumber, getYearMonthBefore, getCurrentYearMonth, isPCTrue } from '@/utils/index';

let props = defineProps(['info', 'hiddenStar']);

// 筛选条件
const query = reactive({
	startDate: getYearMonthBefore(5),
	endDate: getCurrentYearMonth(),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
});
let colors = ['#7568BB', '#FFC403', '#79E1F2', '#F4F5F7', '#7568BB', '#FFC403', '#79E1F2', '#F4F5F7'];
let echartsIds = ref([]);
let coverList = ref([]);
let myEcharts = echarts;
let charts = [];

let initChart = () => {
	for (let item of echartsIds.value) {
		charts.push(myEcharts.init(document.querySelector(`#item-echarts${item}`), { devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio }));
	}
	setChartData();
};

let setChartData = () => {
	for (let [index, chart] of charts.entries()) {
		setOption(index, chart);
		window.addEventListener('resize', function () {
			chart.resize();
		});
	}
};

let setOption = (index, chart) => {
	let option = {
		series: [
			{
				type: 'gauge',
				center: ['50%', '100%'],
				startAngle: 180,
				endAngle: 0,
				min: 0,
				max: 100,
				radius: '200%',
				itemStyle: {
					color: '#0052CC',
				},
				progress: {
					show: true,
					roundCap: false,
					width: 16,
				},
				axisLine: {
					roundCap: false,
					lineStyle: {
						width: 16,
					},
				},
				axisTick: {
					show: false,
				},
				pointer: {
					show: false,
				},
				splitLine: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				title: {
					show: false,
				},
				detail: {
					show: true,
					offsetCenter: [0, -7],
					valueAnimation: true,
					formatter: function (value) {
						return '{value|' + value.toFixed(0) + '%' + '}';
					},
					rich: {
						value: {
							fontSize: 12,
							color: '#172B4D',
						},
					},
				},
				data: [
					{
						value: coverList.value[index],
					},
				],
			},
		],
	};
	chart.setOption(option);
};

let visitList = ref([]);
// let getData = async () => {
// 	let res = await visitSummaryInfo({
// 		skuCode: query.brandId,
// 		territoryCode: query.personId,
// 		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
// 		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
// 	});
// 	visitList.value = res.result.data;
// 	for (const item of visitList.value) {
// 		echartsIds.value.push(generateRandomNumber());
// 		coverList.value.push(item.visitCoverAch);
// 	}
// };

watch(
	() => props.info,
	(n) => {
		if (n.info.data) {
			console.log(n, '??');
			// for (let item of charts) {
			// 	item && item.dispose();
			// }
			echartsIds.value = [];
			coverList.value = [];
			visitList.value = n.info.data.visitList;
			console.log(visitList.value, '>>');
			for (const item of visitList.value) {
				echartsIds.value.push(generateRandomNumber());
				coverList.value.push(item.visitCoverAch);
			}
			nextTick(() => {
				if (charts.length > 0) {
					setChartData();
				} else {
					initChart();
				}
			});
		}
	}
);
const emit = defineEmits(['focus']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};

onMounted(async () => {
	// await getData();
	// initChart();
});
onUnmounted(() => {
	for (let item of charts) {
		item && item.dispose();
	}
});
</script>
<style lang="scss" scoped>
.ignore-visit-summary {
	font-size: 10px;
	width: 100%;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 4px 4px;

	.card-title {
		width: 100%;
		border-radius: 4px 4px 0 0;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: #283b5c;
		height: 23px;
		span {
			font-size: 10px;
			color: #fff;
		}
	}

	&-echarts {
		&-card {
			width: 100%;
			height: calc(100% - 23px);
			padding: 0 8px;
			.item {
				padding-top: 15px;
				padding-bottom: 8px;
				border-bottom: 1px dashed #dfe1e6;
				position: relative;
				&-name {
					font-size: 15px;
					font-weight: bold;
					color: var(--pv-default-color);
				}
				&-fg {
					margin-top: 10px;
					font-size: 12px;
					color: var(--pv-no-active-color);
					display: flex;
					align-items: center;
					justify-content: space-between;
					& > span {
						span {
							color: var(--pv-default-color);
						}
					}
					&-right {
						font-size: 9px;
						position: relative;
						top: 5px;
						span {
							color: var(--pv-tabbar-active) !important;
						}
					}
				}
				&-detail {
					display: flex;
					gap: 4px;
					margin-top: 6px;
					span {
						flex: 1;
						border-radius: 2px;
						padding: 3px 0px;
						font-size: 9px;
						color: #fff;
						text-align: center;
					}
				}
				&-tip {
					margin-top: 8px;
					color: var(--pv-default-color);
					font-size: 12px;
					text-align: right;
					span {
						color: var(--pv-tabbar-active);
					}
				}
				&-echarts {
					position: absolute;
					top: 10px;
					right: 0;
					width: 70px;
					height: 35px;
					&::after {
						content: '覆盖率';
						font-size: 9px;
						color: var(--pv-no-active-color);
						position: absolute;
						top: 12px;
						left: -28px;
					}
				}
			}
			.item:last-child {
				padding-bottom: 0;
				border: none;
			}
		}
	}
}
</style>
