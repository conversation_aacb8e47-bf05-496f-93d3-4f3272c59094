<template>
	<div class="visit-management">
		<div class="top">
			<div class="card-title">
				<span>{{ pageTitle }}</span>
				<span v-if="!hiddenStar">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<div>
			<div class="q-box">
				<!-- 时间 -->
				<div @click="openTime" class="q-width-100 q-fileter-item">
					<span class="item-title ell">{{ query.startDate + ' - ' + query.endDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div class="q-box">
				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-49 q-fileter-item">
					<span class="item-title ell">{{ query.productName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部加购 -->
				<div @click="openOrg" class="q-width-49 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
		</div>
		<loading v-show="comStatus === 'loading'"></loading>
		<div v-show="comStatus === 'normal'" class="visit-management-echarts-card">
			<div class="visit-management-echarts" :id="`visit-management-echarts-${echartsId}`"></div>
		</div>
		<empty-com v-show="comStatus === 'empty'"></empty-com>

		<!-- 数据更新至 -->
		<dataUpdateBy v-if="showUpdateTime" :updateTime="props.info?.dataSyncTime" :isYMD="true" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 品牌 -->
		<OneLevelRadio ref="brand" :list="brandData" :path="`${page}${pageTitle}-产品`" :defaultCheck="query.productId" @_confirm="brandConfirm"></OneLevelRadio>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :path="`${page}${pageTitle}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import * as echarts from 'echarts';
import { visitManagement } from '@/api/report';
import { generateRandomNumber, getFilterTime, translateFont, getNameByPath, isPCTrue } from '@/utils/index';
import Decimal from 'decimal.js';
import useReport from '@/store/modules/report.js';
let useReportStore = useReport();
import usefilterStore from '@/store/modules/filter';
let filterStore = usefilterStore();
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const relam = enterStore.enterInfo.id;
let props = defineProps(['info', 'hiddenStar']);
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let showUpdateTime = ref(true);
let pageTitle = props.info?.nameCn || '拜访管理及表现';
if (route.fullPath.includes('/report')) {
	showUpdateTime.value = false;
}

const brandData = ref([]);
const productList = relam === 'muqiao' ? filterStore.productMQInfo : filterStore.productInfo;
for (const item of productList) {
	brandData.value.push({ id: item.productCode, name: item.productCn });
}
// 筛选条件
const query = reactive({
	startDate: useReportStore.bfbxTime.length > 0 ? getFilterTime(useReportStore.bfbxTime[0]) : getFilterTime(props.info?.startTime),
	endDate: useReportStore.bfbxTime.length > 0 ? getFilterTime(useReportStore.bfbxTime[1]) : getFilterTime(props.info?.endTime),
	productId: brandData.value.length > 0 ? brandData.value[0].id : '',
	productName: brandData.value.length > 0 ? brandData.value[0].name : '',
	personId: '',
	personName: '全部人员',
});
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${pageTitle}-日期`, `${_startDate}-${_endDate}`);
};

// 品牌筛选
const brand = ref(null);
const openBrand = () => {
	brand.value.show = true;
};

let brandConfirm = async ({ id, name }) => {
	query.productName = name;
	query.productId = id;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${pageTitle}-产品`, name);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgList.value = filterStore.treeInfo;
	orgZ.value.show = true;
};

const orgConfirm = async ({ userId: ids, userName: names }) => {
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${pageTitle}-人员`, query.personName);
};

let myEcharts = echarts;
let chart;

let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#visit-management-echarts-${echartsId}`), { devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio });
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};

let setOption = () => {
	let ab = visitList.value.ab.map((ele) => {
		return new Decimal(ele.abDoctorAch === 'NaN' ? 0 : ele.abDoctorAch).toString();
	});
	let ach = visitList.value.ach.map((ele) => {
		return new Decimal(ele.visitAvh === 'NaN' ? 0 : ele.visitAvh).toString();
	});
	let option = {
		color: colors,
		grid: [
			{ top: translateFont(70), left: translateFont(10), right: translateFont(10), height: translateFont(65), show: true, borderColor: '#eee' },
			{ top: translateFont(145), left: translateFont(10), right: translateFont(10), bottom: translateFont(20), show: true, borderColor: '#eee' },
		],
		legend: {
			itemGap: translateFont(12),
			itemWidth: translateFont(9),
			itemHeight: translateFont(9),
			left: 'center',
			top: translateFont(10),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
			data: [
				{
					name: 'A级客户拜访频次',
					icon: 'rect',
				},
				{
					name: 'B级客户拜访频次',
					icon: 'rect',
				},
				{
					name: 'C级客户拜访频次',
					icon: 'rect',
				},
				{
					name: 'AB客户覆盖率',
				},
				{
					name: '拜访计划执行率',
				},
			],
		},
		xAxis: [
			{
				type: 'category',
				data: visitList.value.axisMonth,
				gridIndex: 0,
				axisLabel: { show: false },
				axisTick: { show: false },
				splitLine: {
					show: false,
				},
				axisLine: { lineStyle: { color: '#eee' } },
			},
			{ type: 'category', data: visitList.value.axisMonth, gridIndex: 1, axisLabel: { show: true, color: '#6B778C', fontSize: translateFont(9) }, axisTick: { show: false }, splitLine: { show: true, lineStyle: { color: '#eee' }, interval: 0 }, axisLine: { lineStyle: { color: '#eee' } } },
		],
		yAxis: [
			{
				type: 'value',
				gridIndex: 0,
				axisLabel: { show: false },
				axisTick: { show: false },
				splitLine: {
					show: false,
				},
			},
			{ type: 'value', gridIndex: 1, axisLabel: { show: false }, axisTick: { show: false }, splitLine: { show: false } },
		],
		series: [
			{
				name: 'A级客户拜访频次',
				type: 'bar',
				data: abcList.value[0],
				xAxisIndex: 1,
				yAxisIndex: 1,
				barWidth: 11,
				label: {
					show: true,
					position: 'top',
					distance: 1,
					color: '#172B4D',
					fontSize: translateFont(7),
				},
			},
			{
				name: 'B级客户拜访频次',
				type: 'bar',
				data: abcList.value[1],
				xAxisIndex: 1,
				yAxisIndex: 1,
				barWidth: 11,
				label: {
					show: true,
					position: 'top',
					distance: 1,
					color: '#172B4D',
					fontSize: translateFont(7),
				},
			},
			{
				name: 'C级客户拜访频次',
				type: 'bar',
				data: abcList.value[2],
				xAxisIndex: 1,
				yAxisIndex: 1,
				barWidth: 11,
				label: {
					show: true,
					position: 'top',
					distance: 1,
					color: '#172B4D',
					fontSize: translateFont(7),
				},
			},
			{
				name: 'AB客户覆盖率',
				type: 'line',
				lineStyle: {
					width: 1,
				},
				data: ab,
				xAxisIndex: 0,
				yAxisIndex: 0,
				label: {
					show: true,
					distance: 2,
					fontSize: translateFont(7),
					color: '#172B4D',
					formatter: ({ value }) => {
						return value + '%';
					},
				},
				symbolSize: translateFont(5),
			},
			{
				name: '拜访计划执行率',
				type: 'line',
				lineStyle: {
					width: 1,
				},
				data: ach,
				xAxisIndex: 0,
				yAxisIndex: 0,
				label: {
					show: true,
					distance: 2,
					fontSize: translateFont(7),
					color: '#172B4D',
					formatter: ({ value }) => {
						return value + '%';
					},
				},
				symbolSize: translateFont(5),
			},
		],
	};

	chart.setOption(option);
};

let visitList = ref([]);
let abcList = ref([]);
let comStatus = ref('loading');

let getData = async () => {
	comStatus.value = 'loading';

	let res = await visitManagement({
		productCode: query.productId ? query.productId.split(',') : [],
		territoryCode: query.personId ? query.personId.split(',') : [],
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
	});
	visitList.value = res.result;
	abcList.value = [];
	for (const item of res.result.frequencyData) {
		let ach = item.data.map((ele) => {
			return new Decimal(ele.visitAch === 'NaN' ? 0 : ele.visitAch).toString();
		});
		abcList.value.push(ach);
	}
	comStatus.value = res.result.axisMonth?.length < 1 ? 'empty' : 'normal';

	let year = query.endDate.substring(0, 4); // 截取年份部分
	let month = query.endDate.substring(4); // 截取月份部分
	let year1 = query.startDate.substring(0, 4); // 截取年份部分
	let month1 = query.startDate.substring(4); // 截取月份部分
	let startLocalDate = year1 + '年' + month1 + '月'; // 拼接年份和月份信息
	let endLocalDate = year + '年' + month + '月';
	let name = `${startLocalDate}-${endLocalDate}${pageTitle}`;
	emit('filterChange', {
		name: 'processManagementAndAnalysisPPT2',
		info: { date: { startLocalDate, endLocalDate }, name, people: query.personName, data: { visitList: visitList.value, abcList: abcList.value, message: res.result.message } },
	});
};

const emit = defineEmits(['focus', 'filterChange']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let colors = ['#0052CC', '#0379FF', '#79E1F2', '#FF991F', '#01A2BF'];
let echartsId = generateRandomNumber();
onMounted(async () => {
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
.visit-management {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}

	&-echarts-card {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		padding: 0 0px 15px;
	}
	&-echarts {
		width: 100%;
		height: 240px;
	}
}
</style>
