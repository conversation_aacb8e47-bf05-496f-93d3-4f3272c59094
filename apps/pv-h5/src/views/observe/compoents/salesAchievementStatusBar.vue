<template>
	<div class="sales-achievement-status" id="ap-sales-achievement-status-bar">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="operation" @click="openFilter">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
					<switchCom ref="switchComRef" :defaultValue="props.info" :inChat="props.inChat" @currencySwitching="currencySwitching"></switchCom>
				</span>
			</div>
			<!-- 通用筛选器 -->
			<globalFilter
				:filters="[
					{ name: 'time', size: '49', queryName: 'dateRange', single: true },
					{ name: 'org', size: '49', queryName: 'personName' },
					{ name: 'brand', size: '32', queryName: 'brandName' },
					{ name: 'city', size: '32', queryName: 'countyName' },
					{ name: 'hospital', size: '32', queryName: 'hospitalName' },
				]"
				:query="query"
				:loading="filterLoading"
				@openTime="openTime"
				@openOrg="openOrg"
				@openBrand="openBrand"
				@openCity="openCity"
				@openHospital="openHospital"
			></globalFilter>
			<div v-if="isRecommend" class="sales-achievement-status-tip" v-html="chartData.message"></div>
		</div>
		<loading v-show="comStatus === 'loading'"></loading>
		<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>
		<div v-show="comStatus === 'normal'" class="card-echart">
			<div class="card-echart-title">
				月度总览
				<span class="mes">{{ chartData.month?.message }}</span>
			</div>
			<progress-bar :progress="chartData.month && formatPrecentOne(chartData.month?.ach)" background="#d7e4ff" color="#0052cc"></progress-bar>
			<div class="card-echart-desc">
				<div class="card-echart-desc-item">
					月销售：<span>{{ chartData.month && formatNumberT(chartData.month.sale) }}</span>
				</div>
				<div class="card-echart-desc-item">
					同比：<span>{{ chartData.month && formatPrecentOne(chartData.month?.yoy) + '%' }}</span>
					<span v-if="chartData.month?.yoy > 0"> <svg-icon class="icon" icon-class="sz"></svg-icon></span>
					<span v-else><svg-icon class="icon" icon-class="xd"></svg-icon></span>
				</div>
				<div class="card-echart-desc-item">
					月指标：<span>{{ chartData.month && formatNumberT(chartData.month.target) }}</span>
				</div>
				<div class="card-echart-desc-item">
					环比：<span>{{ chartData.month && formatPrecentOne(chartData.month?.mom) + '%' }}</span>
					<span v-if="chartData.month?.mom > 0">
						<svg-icon class="icon" icon-class="sz"></svg-icon>
					</span>
					<span v-else>
						<svg-icon class="icon" icon-class="xd"></svg-icon>
					</span>
				</div>
			</div>
			<div class="card-echart-title">
				季度总览
				<span class="mes">{{ chartData.quarter?.message }}</span>
			</div>
			<progress-bar :progress="chartData.quarter && formatPrecentOne(chartData.quarter.ach)" background="#e4f9fc" color="#79e1f2"></progress-bar>
			<div class="card-echart-desc">
				<div class="card-echart-desc-item">
					季销售：<span>{{ chartData.quarter && formatNumberT(chartData.quarter.sale) }}</span>
				</div>
				<div class="card-echart-desc-item">
					同比：<span>{{ chartData.quarter && formatPrecentOne(chartData.quarter?.yoy) + '%' }}</span>
					<span v-if="chartData.quarter?.yoy > 0"> <svg-icon class="icon" icon-class="sz"></svg-icon></span>
					<span v-else><svg-icon class="icon" icon-class="xd"></svg-icon></span>
				</div>
				<div class="card-echart-desc-item">
					季指标：<span>{{ chartData.quarter && formatNumberT(chartData.quarter.target) }}</span>
				</div>
				<div class="card-echart-desc-item">
					环比：<span>{{ chartData.quarter && formatPrecentOne(chartData.quarter?.mom) + '%' }}</span>
					<span v-if="chartData.quarter?.mom > 0">
						<svg-icon class="icon" icon-class="sz"></svg-icon>
					</span>
					<span v-else>
						<svg-icon class="icon" icon-class="xd"></svg-icon>
					</span>
				</div>
			</div>
			<div class="card-echart-title">
				年度总览
				<span class="mes">{{ chartData.year?.message }}</span>
			</div>
			<progress-bar :progress="chartData.year && formatPrecentOne(chartData.year?.ach)" background="#fdeee6" color="#e95600"></progress-bar>
			<div class="card-echart-desc">
				<div class="card-echart-desc-item">
					年度销售：<span>{{ chartData.year ? formatNumberT(chartData.year.sale) : 0 }}</span>
				</div>
				<div class="card-echart-desc-item">
					同比：<span>{{ chartData.year ? formatPrecentOne(chartData.year?.yoy) + '%' : '0%' }}</span>
					<span v-if="chartData.year?.yoy > 0"> <svg-icon class="icon" icon-class="sz"></svg-icon></span>
					<span v-else><svg-icon class="icon" icon-class="xd"></svg-icon></span>
				</div>
				<div class="card-echart-desc-item">
					年度指标：<span>{{ chartData.year ? formatNumberT(chartData.year.target) : 0 }}</span>
				</div>
			</div>
		</div>

		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :dataAccountingTime="props.info?.dataAccountingTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 复选框 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :path="`${page}${props.info?.nameCn}`" :personalId="query.personId" :province="query.province" :city="query.city" :skuCode="query.brandId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { salesAchievementStatus } from '@/api/sales';
import { formatNumberT, formatPrecentOne, generateRandomNumber, getNameByPath, getFilterTime } from '@/utils/index';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';

import useUserStore from '@/store/modules/user';
import ProgressBar from './echarts/progressBar.vue';
let props = defineProps(['info', 'isRecommend', 'defaultValue', 'summary', 'inChat']);

let myEcharts = echarts;
let chart; //图实例
let chartData = ref({}); //图数据
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);

// 计算当季度开始月份与结束月份
const salesDate = reactive({
	startYm: '',
	endYm: '',
});
let month = 0;
if (props.info.startTime?.length > 6) {
	let year = props.info.startTime.substring(0, 5);
	if (props.info.startTime.substring(5, 6) === '0') {
		month = props.info.startTime.substring(6, 7);
	} else {
		month = props.info.startTime.substring(5, 7);
	}
	let endM = getQuarterStartAndEnd(month);
	let startM = endM - 2;
	endM = endM < 10 ? '0' + endM : endM;
	startM = startM < 10 ? '0' + startM : startM;
	salesDate.startYm = year + startM;
	salesDate.endYm = year + endM;
}
function getQuarterStartAndEnd(month) {
	if (month < 1 || month > 12) return;
	return Math.ceil(month / 3) * 3; // 计算季度的结束月份
}

// 筛选条件
const query = reactive({
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
});
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};

// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAchievementStatusBar',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	// chart.resize();
	// setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};

// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAchievementStatusBar',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAchievementStatusBar',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, initFilters, filterLoading } = useCascader(query, { city, hospital, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	await getData();
	// chart.resize();
	// setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	await getData();
	// chart.resize();
	// setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 城市

const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAchievementStatusBar',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	console.log(params);
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
// 医院筛选
const openHospital = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAchievementStatusBar',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	await getData();
	// chart.resize();
	// setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};

//获取数据
let comStatus = ref('loading');
let getData = async () => {
	try {
		comStatus.value = 'loading';
		let res = await salesAchievementStatus({
			skuCode: query.brandId ? query.brandId.split(',') : [],
			territoryCode: query.personId ? query.personId.split(',') : [],
			hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
			nowLocalDateTime: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
			endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
			province: query.province || [],
			city: query.city || [],
			salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
		});
		query.salesOrAmount = switchComRef.value.type;
		if (res.result.isExceededAuthority) {
			comStatus.value = 'isExceededAuthority';
			emit('summaryIng', { noSummary: true });
			return;
		}
		chartData.value = res.result;
		comStatus.value = 'normal';
		if (props.summary) {
			emit('summaryIng', { data: { ...res.result }, params: query });
		}
		return Promise.resolve();
	} catch (error) {
		return Promise.reject(error);
	}
};

const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let echartsId = generateRandomNumber();
const switchComRef = ref(null);
const currencySwitching = async (type) => {
	// proxy.$umeng('点击', `${props.umengPath}-销量金额筛选器`, type);
	await getData();
};
const openFilter = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAchievementStatusBar',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
};
const defaultSku = ref('');
const defaultHos = ref('');
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			brand, // 传入 ref
			orgZ, // 传入 ref
			time,
			defaultSku, // 传入 ref
			defaultHos, // 传入 ref
			defaultPerson, // 传入 ref
			defaultProvinceCity, // 传入 ref
			switchComRef,
		});
	}
	//初始化级联
	initFilters();

	await getData();
	// initChart();
});
onUnmounted(() => {
	// chart && chart.dispose();
});

const userStore = useUserStore();
const beituiUser = ['wangximeng5', 'xionghanzhou', 'wanghongyi3'];
const isBeitui = computed(() => {
	return beituiUser.includes(userStore.userInfo.username);
});
if (isBeitui.value) {
	query.startDate = getFilterTime('2024-06-01');
	query.endDate = getFilterTime('2024-06-30');
} else {
	query.startDate = getFilterTime(props.info.startTime);
	query.endDate = getFilterTime(props.info.endTime);
}

defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/salesAchievementStatusBar.scss';
.sales-achievement-status {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	&-tip {
		padding: 5px 5px 0;
		font-size: 12px;
		color: #7c88a6;
	}
	.card-echart {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		overflow: hidden;
		padding: 0px 10px;
		position: relative;
		&-title {
			margin: 8px 0;
			font-weight: 700;
			display: flex;
			align-items: center;
		}
		&-desc {
			display: flex;
			flex-wrap: wrap;
			&-item {
				flex: 1 0 50%;
				margin: 5px 0;
				color: var(--pv-no-active-color);
				font-size: 12px;
				span {
					color: var(--pv-default-color);
				}
			}
		}
		.ym-month {
			position: absolute;
			color: var(--pv-default-color);
			left: 25%;
			transform: translateX(-50%);
			top: 10px;
			// width: 100px;
			text-align: center;
			font-size: 12px;
		}
		.ym-quarter {
			position: absolute;
			color: var(--pv-default-color);
			right: 25%;
			transform: translateX(50%);
			top: 10px;
			// width: 120px;
			text-align: center;
			font-size: 12px;
		}
		.info1 {
			position: absolute;
			left: 45px;
			top: 43px;
			text-align: center;
			line-height: 18px;
			width: 70px;
			div:nth-child(1) {
				font-size: 10px;
				color: #6b778c;
			}
			div:nth-child(2) {
				font-size: 13px;
				font-weight: bold;
			}
		}
		.info2 {
			position: absolute;
			left: 215px;
			top: 43px;
			text-align: center;
			line-height: 18px;
			width: 70px;
			div:nth-child(1) {
				font-size: 10px;
				color: #6b778c;
			}
			div:nth-child(2) {
				font-size: 13px;
				font-weight: bold;
			}
		}
		.value1 {
			font-size: 10px;
			position: absolute;
			width: 140px;
			display: flex;
			justify-content: space-between;
			left: 20px;
		}
		.value2 {
			font-size: 10px;
			position: absolute;
			width: 137px;
			display: flex;
			justify-content: space-between;
			left: 189px;
		}
		&-info {
			font-size: 12px;
			position: relative;
			span:nth-child(1) {
				position: absolute;
				left: 23px;
			}
			span:nth-child(2) {
				position: absolute;
				left: 50px;
			}
			span:nth-child(3) {
				position: absolute;
				left: 268px;
				font-weight: bold;
				width: 55px;
				word-break: break-all;
			}
			.month {
				position: absolute;
				left: 50%;
				top: -75px;
				transform: translateX(-50%);
				text-align: center;
				div:nth-child(1) {
					font-size: 30px;
					font-weight: bold;
				}
				div:nth-child(2) {
					font-size: 12px;
					color: var(--pv-no-active-color);
					top: -30px;
				}
			}
			.season {
				font-size: 12px;
				position: absolute;
				top: -140px;
				text-align: center;
				div:nth-child(1) {
					color: var(--pv-no-active-color);
				}
				&.left {
					left: 20px;
				}
				&.right {
					right: 20px;
				}
			}
		}
		&-detail {
			margin: 18px 11px 8px;
			display: flex;
			gap: 8px;
			&-item {
				flex: 1;
				border: 1px dashed #dfe1e6;
				padding: 3px 6px;
				border-radius: 5px;
				.title {
					font-weight: bold;
				}
				.other {
					color: var(--pv-no-active-color);
					margin-top: 5px;
					font-size: 12px;
					span {
						color: var(--pv-default-color);
					}
					.icon {
						font-size: 15px;
					}
				}
			}
		}
	}
	&-echarts {
		width: 100%;
		height: 20px;
	}
	.mes {
		font-size: 11px;
		color: #6b778c;
		font-weight: normal;
		margin-left: 5px;
	}
}
</style>
