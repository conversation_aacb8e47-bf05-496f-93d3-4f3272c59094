<template>
	<div class="branch-sales-overview">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span>
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<div class="branch-sales-overview-filter">
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="branch-sales-overview-filter-echarts-card">
				<div class="branch-sales-overview-filter-echarts" :id="`branch-sales-overview-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
	</div>
</template>
<script setup>
import { getcubejsData } from '@/api/sales';
import * as echarts from 'echarts';
import { generateRandomNumber, getYearMonthBefore, getFilterTime, translateFont, spaces, getNameByPath, isPCTrue } from '@/utils/index';
import Decimal from 'decimal.js';
let props = defineProps(['info', 'defaultValue']);
function formatNumbeW(number) {
	if (number == null) return 0;
	const isNegative = number < 0;
	const absoluteValue = Math.abs(number);
	if (absoluteValue < 10000) {
		const formatted = new Decimal(absoluteValue).toDecimalPlaces(2).toNumber().toLocaleString();
		return isNegative ? `-${formatted}` : formatted;
	} else {
		const formatted = `${new Decimal(absoluteValue).dividedBy(10000).toDecimalPlaces(2).toNumber().toLocaleString()}W`;
		return isNegative ? `-${formatted}` : formatted;
	}
}
function saleTrendFilter(item) {
	if (item.seriesName === '指标' || item.seriesName === '销售' || item.seriesName === '纯销' || item.seriesName === '同期') {
		return formatNumbeW(item.value);
	}
	if (item.seriesName === '达成' || item.seriesName === '同比增长' || item.seriesName === '占比' || item.seriesName === '同比' || item.seriesName === '达成率' || item.seriesName === '完成率' || item.seriesName === '同比') {
		return new Decimal(item.value).toDecimalPlaces(0) + '%';
	}
}
const query = {
	query: {
		timezone: 'Asia/Shanghai',
		dimensions: ['key_product_sales_analysis.brand_group_type'],
		measures: ['key_product_sales_analysis.sales_v', 'key_product_sales_analysis.sales_v_ly', 'key_product_sales_analysis.sales_v_achievement_rate', 'key_product_sales_analysis.sales_v_yoy_rate'],
	},
};
let myEcharts = echarts;
let chart;
let echartsId = generateRandomNumber();
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#branch-sales-overview-echarts-${echartsId}`), { devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio });
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let x = tableList.value.map((ele) => ele['key_product_sales_analysis.brand_group_type']);
	let cx = tableList.value.map((ele) => ele['key_product_sales_analysis.sales_v']);
	let tq = tableList.value.map((ele) => ele['key_product_sales_analysis.sales_v_ly']);
	let wcl = tableList.value.map((ele) => ele['key_product_sales_analysis.sales_v_achievement_rate']);
	let tb = tableList.value.map((ele) => ele['key_product_sales_analysis.sales_v_yoy_rate']);
	let option = {
		grid: {
			top: translateFont(40),
			left: translateFont(58),
			right: translateFont(40),
			bottom: translateFont(30),
		},
		legend: {
			data: ['纯销', '同期', '完成率', '同比'],
			left: 'center',
			top: translateFont(13),
			itemGap: translateFont(15),
			itemWidth: translateFont(8),
			itemHeight: translateFont(4),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(9),
			},
		},
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = '';
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${saleTrendFilter(item)}</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			axisPointer: {
				type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
				},
			},
			extraCssText: 'z-index:9',
		},
		xAxis: [
			{
				type: 'category',
				data: x,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: spaces(x.length), // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumbeW(value);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return new Decimal(value).toDecimalPlaces(0) + '%';
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
			},
		],
		color: ['#0052CC', '#01A2BF', '#E95600', '#01A2BF'],
		series: [
			{
				name: '纯销',
				type: 'bar',
				data: cx,
				barWidth: translateFont(6),
				z: 2,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '同期',
				type: 'bar',
				data: tq,
				barWidth: translateFont(6),
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '完成率',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: wcl,
				z: 4,
			},
			{
				name: '同比',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: tb,
				z: 4,
			},
		],
	};
	chart.setOption(option);
};
let comStatus = ref('loading');
const tableList = ref([]);
const getData = async () => {
	try {
		comStatus.value = 'loading';
		let res = await getcubejsData(query);
		tableList.value = res.data;
		console.log(tableList.value);
		comStatus.value = res.data.length < 1 ? 'empty' : 'normal';
	} catch (error) {
		console.log(error);
	}
};

onMounted(async () => {
	await getData();
	initChart();
});
</script>
<style lang="scss" scoped>
.branch-sales-overview {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	&-filter {
		border-radius: 5px;
		margin-top: 8px;
		background-color: var(--pv-bgc);
		&-echarts {
			height: 220px;
			&-card {
				background-color: var(--pv-bgc);
			}
		}
	}
}
</style>
