<template>
	<div class="ddi" id="ap-ddi">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="operation">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
					<switchCom ref="switchComRef" :defaultValue="props.info" @currencySwitching="currencySwitching"></switchCom>
				</span>
			</div>
		</div>
		<div>
			<!-- 通用筛选器 -->
			<globalFilter
				:filters="[
					{ name: 'time', size: '100', queryName: 'dateRange', single: true },
					{ name: 'org', size: '32', queryName: 'personName' },
					{ name: 'brand', size: '32', queryName: 'brandName' },
					{ name: 'hospital', size: '32', queryName: 'hospitalName' },
				]"
				:query="query"
				:loading="filterLoading"
				@openTime="openTime"
				@openOrg="openOrg"
				@openBrand="openBrand"
				@openHospital="openHospital"
			></globalFilter>
		</div>
		<div class="ddi-echarts-card">
			<filterCom title="DDI每日销售情况分析"></filterCom>
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="scroll">
				<div class="ddi-echarts" :id="`ddi-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
			<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>
		</div>
		<div v-show="comStatus === 'normal'" class="ddi-echarts-card">
			<filterCom :downloadExcel="isDDIDownLoad" @_downExcel="downExcel" :filterList="filterList" :currentIndex="currentIndex" @filterChange="filterChange"></filterCom>
			<div class="table">
				<transition :name="oldV > newV ? 'van-slide-left' : 'van-slide-right'">
					<component
						v-show="isShow"
						:is="com(currentIndex)"
						:ddiResult="ddiList"
						:lastResult="lastResult"
						@pullPData="onPeopleload"
						@pullData="onLoad"
						@pullHData="onLoad1"
						:peopleLoading="pstate.isLoading"
						:detailLoading="state.isLoading"
						:hospitalLoading="st.isLoading"
						:brandLoading="brandLoading"
						:personalCard="personalInfo"
						:query="query"
						:salesOrAmount="query.salesOrAmount"
					></component>
				</transition>
			</div>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :isYMD="true" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :path="`${page}${props.info?.nameCn}`" :personalId="query.personId" :skuCode="query.brandId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
	</div>
</template>
<script setup>
import { ddiTransactionChart, ddiTransactionSalesPeople, ddiTransactionSales, ddiTransactionDetails, ddiTransactionSalesHospital, ddiInfoDownload } from '@/api/sales';
import { getNamesByIdsInArray, getFilterTime, generateRandomNumber, translateFont, formatNumber, formatNumberT, formatKNumber, formatKNumberT, getNameByPath, isPCTrue } from '@/utils/index';
import { showToast, showLoadingToast } from 'vant';
import * as echarts from 'echarts';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const relam = enterStore.enterInfo.id;
let filterStore = usefilterStore();
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let { com } = useDDI(); //动态组件
let props = defineProps(['info', 'defaultValue', 'summary', 'inChat']);

const isDDIDownLoad = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'isDDIDownloadExcel');
});
// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
});
// 默认选中的人员
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'ddi',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getChartData();
	chart.resize();
	setOption();
	getDDIData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};

// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'ddi',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'ddi',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgZ.value.show = true;
};
const hospital = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, initFilters, filterLoading } = useCascader(query, { hospital, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	await getChartData();
	chart.resize();
	setOption();
	getDDIData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	await getChartData();
	chart.resize();
	setOption();
	getDDIData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 医院筛选
const openHospital = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'ddi',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	await getChartData();
	chart.resize();
	setOption();
	getDDIData();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};

//获取数据
const chartData = ref([]);
let comStatus = ref('loading');
const getChartData = async () => {
	try {
		comStatus.value = 'loading';
		let res = await ddiTransactionChart(
			{
				skuCode: query.brandId ? query.brandId.split(',') : [],
				territoryCode: query.personId ? query.personId.split(',') : [],
				hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
				startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
				endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
				salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
			},
			relam
		);
		query.salesOrAmount = switchComRef.value.type;
		if (res.result.isExceededAuthority) {
			comStatus.value = 'isExceededAuthority';
			emit('summaryIng', { noSummary: true });
			return;
		}
		chartData.value = res.result;
		comStatus.value = res.result.length < 1 ? 'empty' : 'normal';
		let clientWidth = document.body.clientWidth || document.documentElement.clientWidth;
		if (clientWidth > 600) {
			document.querySelector(`#ddi-echarts-${echartsId}`).style.width = 6 * 3.75 * res.result.length + 4 * 3.75 + 'px';
		} else {
			document.querySelector(`#ddi-echarts-${echartsId}`).style.width = 6 * res.result.length + 4 + 'vw';
		}
		if (props.summary) {
			emit('summaryIng', { data: { ...res.result }, params: query });
		}
		return Promise.resolve();
	} catch (error) {
		return Promise.reject(error);
	}
};

const ddiList = ref([]);
const personList = ref([]);
const skuList = ref([]);
const productList = ref([]);
const lastResult = ref({});
const hospitalList = ref([]);
// 获取底部人员列表数据
// 分页
let pstate = reactive({
	isLoading: true, // 列表的loading
	finished: false, //结束
	total: 0, //总数
	page: 0,
	size: 20,
});
const getDataByPeople = async () => {
	try {
		pstate.isLoading = true;
		let res = await ddiTransactionSalesPeople(
			{
				skuCode: query.brandId ? query.brandId.split(',') : [],
				territoryCode: query.personId ? query.personId.split(',') : [],
				hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
				startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
				endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
				page: pstate.page,
				size: pstate.size,
				salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
			},
			relam
		);
		const rows = res.result.content;
		pstate.isLoading = false;
		pstate.total = res.result.totalElements;
		if (rows == null || rows.length === 0) {
			pstate.finished = true;
			return;
		}
		for (const item of rows) {
			personList.value.push({
				name: item.name,
				salesV: formatKNumberT(item.salesV),
				targetV: formatKNumberT(item.targetV),
				achievementRate: new Decimal(item.achievementRate * 100).toDecimalPlaces(1) + '%',
				yearOnYear: new Decimal(item.yearOnYear * 100).toDecimalPlaces(1) + '%',
				yearOnYearGrowth: formatKNumberT(item.yearOnYearGrowth),
				chain: new Decimal(item.chain * 100).toDecimalPlaces(1) + '%',
				moMGrowth: formatKNumberT(item.moMGrowth),
			});
		}
		pstate.finished = false;
		if (personList.value.length >= pstate.total) {
			pstate.finished = true;
		}
		reloadDetailData();
		return Promise.resolve();
	} catch (error) {
		pstate.isLoading = false;
		pstate.finished = true;
		return Promise.reject(error);
	}
};
const onPeopleload = () => {
	if (pstate.finished) return;
	pstate.page++;
	getDataByPeople();
};

// 获取底部列表数据
let brandLoading = ref(true);
const getData = async () => {
	try {
		brandLoading.value = true;
		let res = await ddiTransactionSales(
			{
				skuCode: query.brandId ? query.brandId.split(',') : [],
				territoryCode: query.personId ? query.personId.split(',') : [],
				hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
				startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
				endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
				salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
			},
			relam
		);
		brandLoading.value = false;
		for (const item of res.result.salesBrand) {
			skuList.value.push({
				name: item.name,
				salesV: formatKNumberT(item.salesV),
				targetV: formatKNumberT(item.targetV),
				achievementRate: new Decimal(item.achievementRate * 100).toDecimalPlaces(1) + '%',
				yearOnYear: new Decimal(item.yearOnYear * 100).toDecimalPlaces(1) + '%',
				yearOnYearGrowth: formatKNumberT(item.yearOnYearGrowth),
				chain: new Decimal(item.chain * 100).toDecimalPlaces(1) + '%',
				moMGrowth: formatKNumberT(item.moMGrowth),
			});
		}
		lastResult.value = skuList.value.pop();
		for (const item of res.result.salesProduct) {
			productList.value.push({
				name: item.name,
				salesV: formatKNumberT(item.salesV),
				targetV: formatKNumberT(item.targetV),
				achievementRate: new Decimal(item.achievementRate * 100).toDecimalPlaces(1) + '%',
				yearOnYear: new Decimal(item.yearOnYear * 100).toDecimalPlaces(1) + '%',
				yearOnYearGrowth: formatKNumberT(item.yearOnYearGrowth),
				chain: new Decimal(item.chain * 100).toDecimalPlaces(1) + '%',
				moMGrowth: formatKNumberT(item.moMGrowth),
			});
		}
		lastResult.value = productList.value.pop();
		reloadDetailData();
		return Promise.resolve();
	} catch (error) {
		return Promise.reject(error);
	}
};

//获取底部明细数据
// 分页
let state = reactive({
	isLoading: true, // 列表的loading
	finished: false, //结束
	total: 0, //总数
	page: 0,
	size: 50,
});
const detailList = ref([]);
const getDetailData = async () => {
	try {
		state.isLoading = true;
		let res = await ddiTransactionDetails({
			skuCode: query.brandId ? query.brandId.split(',') : [],
			territoryCode: query.personId ? query.personId.split(',') : [],
			hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
			startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
			endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
			page: state.page,
			size: state.size,
			salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
		});
		const rows = res.result.content;
		state.isLoading = false;
		state.total = res.result.totalElements;
		if (rows == null || rows.length === 0) {
			state.finished = true;
			return;
		}
		for (const item of rows) {
			detailList.value.push({
				date: item.date,
				username: item.username,
				productName: item.productName,
				hospitalName: item.hospitalName,
				salesValue: formatKNumberT(item.salesValue) === 0 ? '' : formatKNumberT(item.salesValue),
				wsName: item.wsName,
				units: item.units,
			});
		}
		state.finished = false;
		if (detailList.value.length >= state.total) {
			state.finished = true;
		}
		reloadDetailData();
		return Promise.resolve();
	} catch (error) {
		state.isLoading = false;
		state.finished = true;
		return Promise.reject(error);
	}
};
const onLoad = () => {
	if (state.finished) return;
	state.page++;
	getDetailData();
};

//获取底按医院数据
// 分页
let st = reactive({
	isLoading: true, // 列表的loading
	finished: false, //结束
	total: 0, //总数
	page: 0,
	size: 50,
});
const getHospitalData = async () => {
	try {
		st.isLoading = true;
		let res = await ddiTransactionSalesHospital(
			{
				skuCode: query.brandId ? query.brandId.split(',') : [],
				territoryCode: query.personId ? query.personId.split(',') : [],
				hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
				startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
				endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
				page: st.page,
				size: st.size,
				salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
			},
			relam
		);
		const rows = res.result.content;
		st.isLoading = false;
		st.total = res.result.totalElements;
		if (rows == null || rows.length === 0) {
			st.finished = true;
			return;
		}
		for (const item of rows) {
			hospitalList.value.push({
				name: item.name,
				salesV: formatKNumberT(item.salesV),
				targetV: formatKNumberT(item.targetV),
				achievementRate: new Decimal(item.achievementRate * 100).toDecimalPlaces(1) + '%',
				yearOnYear: new Decimal(item.yearOnYear * 100).toDecimalPlaces(1) + '%',
				yearOnYearGrowth: formatKNumberT(item.yearOnYearGrowth),
				chain: new Decimal(item.chain * 100).toDecimalPlaces(1) + '%',
				moMGrowth: formatKNumberT(item.moMGrowth),
			});
		}
		st.finished = false;
		if (hospitalList.value.length >= st.total) {
			st.finished = true;
		}
		reloadDetailData();
		return Promise.resolve();
	} catch (error) {
		st.isLoading = false;
		st.finished = true;
		return Promise.reject(error);
	}
};
const onLoad1 = () => {
	if (st.finished) return;
	st.page++;
	getHospitalData();
};

const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', {
		id: props.info.reportCd,
		name: props.info.nameCn,
		isLike: !props.info.isLike,
	});
};
let myEcharts = echarts;
let chart;
let echartsId = generateRandomNumber();
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#ddi-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let xDate = chartData.value.map((ele) => ele.date.slice(-2));
	let xl = chartData.value.map((ele) => ele.value);
	let mxl = chartData.value.map((ele) => ele.monthSales);
	let mtxl = chartData.value.map((ele) => ele.monthTarget);
	let ly = chartData.value.map((ele) => ele.saleLy);
	let option = {
		grid: {
			top: translateFont(50),
			left: translateFont(48),
			right: translateFont(35),
			bottom: translateFont(30),
		},
		legend: {
			data: ['销售', '月累计销售', '月累计指标', '去年同期销售'],
			left: translateFont(15),
			top: translateFont(15),
			itemGap: translateFont(15),
			itemWidth: translateFont(15),
			itemHeight: translateFont(3),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = '';
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${formatNumberT(item.value)}</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			axisPointer: {
				type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
				},
			},
			extraCssText: 'z-index:9',
		},
		xAxis: [
			{
				type: 'category',
				data: xDate,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumber(value);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
		],
		color: ['#0052CC', '#FF991F', '#DD340A', '#01A2BF'],
		series: [
			{
				name: '销售',
				type: 'bar',
				data: xl,
				barWidth: translateFont(12),
				z: 2,
				emphasis: { disabled: true },
			},
			{
				name: '月累计销售',
				type: 'line',
				symbol: 'none',
				lineStyle: {
					width: 1,
				},
				data: mxl,
			},
			{
				name: '月累计指标',
				type: 'line',
				symbol: 'none',
				lineStyle: {
					width: 1,
				},
				data: mtxl,
			},
			{
				name: '去年同期销售',
				type: 'line',
				symbol: 'none',
				lineStyle: {
					width: 1,
				},
				data: ly,
			},
		],
	};
	chart.setOption(option);
};
let filterList = ref(['按品牌', '按产品', '按人员', '按医院', '明细']);
let currentIndex = ref('按品牌');
let isShow = ref(true);
let oldV = ref(0);
let newV = ref(0);
const filterChange = (item) => {
	let obj = [
		{ name: '按品牌', index: 0 },
		{ name: '按产品', index: 1 },
		{ name: '按人员', index: 2 },
		{ name: '明细', index: 3 },
		{ name: '按医院', index: 4 },
	];
	oldV.value = obj.find((ele) => ele.name === currentIndex.value).index;
	isShow.value = false;
	currentIndex.value = item;
	newV.value = obj.find((ele) => ele.name === currentIndex.value).index;
	switch (newV.value) {
		case 0:
			ddiList.value = skuList.value;
			break;
		case 1:
			ddiList.value = productList.value;
			break;
		case 3:
			ddiList.value = detailList.value;
			personalInfo.value = detailList.value[0];
			break;
		case 4:
			ddiList.value = hospitalList.value;
			break;
		default:
			ddiList.value = personList.value;
			personalInfo.value = personList.value[0];
			break;
	}
	nextTick(() => {
		isShow.value = true;
	});
};
// 重新筛选后刷新数据
let personalInfo = ref({});
const reloadDetailData = () => {
	if (currentIndex.value === '按品牌') {
		ddiList.value = skuList.value;
	} else if (currentIndex.value === '按产品') {
		ddiList.value = productList.value;
	} else if (currentIndex.value === '明细') {
		ddiList.value = detailList.value;
		personalInfo.value = detailList.value[0];
	} else if (currentIndex.value === '按医院') {
		ddiList.value = hospitalList.value;
	} else {
		ddiList.value = personList.value;
		personalInfo.value = personList.value[0];
		console.log(ddiList.value);
		console.log(personalInfo.value);
	}
};

const getDDIData = async () => {
	// 重置页面数据
	ddiList.value = [];
	personList.value = [];
	skuList.value = [];
	productList.value = [];
	lastResult.value = {};
	// 获取人员、品牌、产品数据
	pstate.page = 0;
	pstate.finished = false;
	// 获取明细数据
	state.page = 0;
	state.finished = false;
	detailList.value = [];
	//
	st.page = 0;
	st.finished = false;
	hospitalList.value = [];
	personalInfo.value = {};
	await getData();
	getDataByPeople();
	getDetailData();
	getHospitalData();
};

const downExcel = async (emial) => {
	try {
		showLoadingToast({
			message: '下载中...',
			forbidClick: true,
			duration: 0,
			class: 'ppt-pc-loading',
		});
		await ddiInfoDownload(
			{
				skuCode: query.brandId ? query.brandId.split(',') : [],
				territoryCode: query.personId ? query.personId.split(',') : [],
				hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
				startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
				endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
				email: emial,
				page: 0,
				size: 100000,
			},
			{ page: 0, size: 100000 }
		);
		showToast({
			message: '数据下载成功，稍后会发送到您的邮箱。',
			position: 'top',
		});
	} catch (error) {
		console.log(error);
	}
};
const switchComRef = ref(null);
const currencySwitching = async (type) => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'ddi',

			defaultValue: {
				...toRaw(query),
			},
		});

		return;
	}
	// proxy.$umeng('点击', `${props.umengPath}-数量金额筛选器`, type);
	await getChartData();
	chart.resize();
	setOption();
	getDDIData();
};
const defaultSku = ref('');
const defaultHos = ref('');
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			brand, // 传入 ref
			orgZ, // 传入 ref
			time,
			defaultSku, // 传入 ref
			defaultHos, // 传入 ref
			defaultPerson, // 传入 ref
			switchComRef,
		});
	}
	//初始化级联
	initFilters();
	await getChartData();
	initChart();
	getDDIData();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/ddi.scss';
.ddi {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
}
.scroll {
	height: 240px;
	min-width: 100%;
	overflow-x: auto;
	overflow-y: hidden;
	&::-webkit-scrollbar {
		height: 4px; /* 设置纵向滚动条宽度 */
	}

	&::-webkit-scrollbar-thumb {
		background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
		border-radius: 3px;
	}

	&::-webkit-scrollbar-track {
		background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
		border-radius: 5px;
	}
}
.ddi-echarts {
	height: 100%;
	&-card {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
	}
}
.table {
	padding: 10px;
	min-height: 200px;
	overflow: hidden;
}
</style>
