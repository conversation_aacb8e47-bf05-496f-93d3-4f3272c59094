<template>
	<div class="sale-trend" :style="{ margin: hiddenSomething ? '0 0' : '' }">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="operation" v-if="!hiddenSomething">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<!-- 销售趋势 -->
		<div v-if="!hiddenSomething">
			<div class="q-box">
				<!-- 时间 -->
				<div @click="openTime" class="q-width-100 q-fileter-item">
					<span class="item-title ell">{{ query.startDate + ' - ' + query.endDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div class="q-box">
				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.brandName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div @click="openOrg" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<div @click="openCity" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.countyName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
		</div>
		<div class="wrap">
			<filterCom :filterList="filterList" :currentIndex="currentIndex" @filterChange="filterChange"></filterCom>
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="sale-trend-echarts-card">
				<div class="sale-trend-echarts" :id="`branch-sales-overview-card-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :dataAccountingTime="props.info?.dataAccountingTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 全部产品 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${props.umengPath}-人员筛选器`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm" :path="`${props.umengPath}-城市筛选器`"></cityFilter>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { getProvinceSalesAnalysis } from '@/api/cube';
import { formatNumbeWT, generateRandomNumber, getYearMonthBefore, getFilterTime, translateFont, getNamesByIdsInArray, spaces, getNameByPath } from '@/utils/index';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
let props = defineProps(['info', 'hiddenSomething', 'umengPath', 'summary', 'inChat', 'defaultValue']);
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);

// 筛选条件
const query = reactive({
	startDate: props.hiddenSomething ? getYearMonthBefore(7) : getFilterTime(props.info?.startTime),
	endDate: props.hiddenSomething ? getYearMonthBefore(2) : getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	countyName: '全部省市',
});
// 默认选中的人员
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'provinceSales',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	document.querySelector('.sale-trend-table-content').style.height = 'auto';
	proxy.$umeng('筛选', `${props.umengPath}-时间筛选器`, `${query.startDate}-${query.endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.endDate;
		query.endDate = n.endDate;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.dinfo?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);
const defaultSku = ref('');
watch(
	() => reportStore.globalFilterInfo.brand,
	async (n) => {
		brand.value.handleReset();
		brandData.value = filterStore.skuInfoTree;
		defaultSku.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		brand.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		brand.value.confirm();
	}
);
watch(
	() => reportStore.globalFilterInfo.people,
	async (n) => {
		orgZ.value.handleReset();
		defaultPerson.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		orgZ.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		orgZ.value.confirm();
	}
);
watch(
	() => reportStore.globalFilterInfo.area,
	async (n) => {
		console.log(n);
		city.value.golbalInit(n.provinceData);
		query.countyName = n.countyName;
		query.city = n.city;
		query.province = n.province;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.dinfo.nameCn}-城市`, query.countyName);
	}
);
// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'provinceSales',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部品牌';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${props.umengPath}-产品筛选器`, query.brandName);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'provinceSales',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgList.value = filterStore.treeInfo;
	orgZ.value.show = true;
};

const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${props.umengPath}-人员筛选器`, query.personName);
};

// 城市
const city = ref(null);
const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'provinceSales',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${props.umengPath}-城市筛选器`, query.countyName);
};

let myEcharts = echarts;
let chart;
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#branch-sales-overview-card-echarts-${echartsId}`));
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let option = {
		tooltip: {
			show: false,
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = `<div class="product">${days[value.value[1]]}</div>`;
				return `<div class="tooltip-saletrend">${str}<div class="tooltip-heatmap"><span class="dot" style="background-color:${value.color}"></span><span class="date">${value.name}</span><span class="value">${formatNumbeWT(value.value[2])}</span></div></div>`;
			},
			extraCssText: 'z-index:9',
		},
		grid: {
			top: translateFont(18),
			bottom: translateFont(45),
			left: translateFont(60),
			right: translateFont(10),
		},
		xAxis: {
			type: 'category',
			data: xData.value,
			axisLabel: {
				fontSize: translateFont(8),
				color: '#6B778C',
				interval: 0,
			},
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			splitArea: {
				show: true,
				interval: 0,
			},
			position: 'top',
		},
		yAxis: {
			type: 'category',
			data: yData.value,
			axisLabel: {
				//是否显示
				inside: false, //坐标轴刻度文字的风格        (可选楷体  宋体  华文行楷等等)
				formatter: (value) => {
					return `{a|${value.replace(/(.{5})/g, '$1\n')}}`;
					// 使用正则表达式每 6 个字符插入一个换行符
					return value.replace(/(.{7})/g, '$1\n');
				},
				rich: {
					a: {
						color: '#6B778C',
						fontSize: translateFont(8),
						align: 'left',
						width: translateFont(40),
						// backgroundColor: 'red',
						// padding: [0, translateFont(-22), 0, 0],
					},
				},
			},
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			splitArea: {
				show: true,
				interval: 0,
			},
			name: '省份',
			nameTextStyle: {
				color: '#172B4D',
				fontSize: translateFont(9),
				fontWeight: 'bold',
				padding: [0, translateFont(120), translateFont(-8), translateFont(50)],
			},
		},
		visualMap: {
			show: true,
			min: minData.value,
			max: maxData.value,
			calculable: true,
			orient: 'horizontal',
			left: 'center',
			bottom: translateFont(10),
			color: ['#01A2BF', '#ffffff'],
			align: 'right',
			itemWidth: translateFont(10),
			itemHeight: translateFont(85),
			textStyle: {
				rich: {
					a: {
						color: '#172B4D',
						fontSize: translateFont(8),
						padding: [translateFont(-23), translateFont(-40), 0, 0],
					},
					b: {
						color: '#172B4D',
						fontSize: translateFont(8),
						padding: [translateFont(-23), translateFont(40), 0, 0],
					},
				},
			},
		},
		series: [
			{
				type: 'heatmap',
				data: detailList.value,
				progressive: 800, // 每次渲染的数据点数量
				progressiveThreshold: 800, // 触发渐进渲染的最小数据量
				label: {
					show: true,
					fontSize: translateFont(7),
					color: '#172B4D',
					formatter: ({ value }) => {
						return formatNumbeWT(value[2]);
					},
				},
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowColor: 'rgba(0, 0, 0, 0.5)',
					},
				},
			},
		],
	};
	chart.setOption(option, { notMerge: true });
};

let zk = ref(false);

const xData = ref([]);
const yData = ref([]);
const maxData = ref(0);
const minData = ref(0);

//表格数据
const detailList = ref([]);
let comStatus = ref('loading');
let typeMap = {
	发出: 'shipment',
	销售: 'sales',
	库存: 'inventory',
};
let getData = async () => {
	comStatus.value = 'loading';
	let res = await getProvinceSalesAnalysis({
		tab: typeMap[currentIndex.value],
		product_filter: query.brandId || '',
		staff_filter: query.personId || '',
		province_filter: query.province?.join(',') || '',
		city_filter: query.city?.join(',') || '',
		start_time: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		end_time: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
	});
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}
	detailList.value = res.result.date_array;
	xData.value = res.result.x_axis;
	yData.value = res.result.y_axis;
	maxData.value = res.result.max;
	minData.value = res.result.min;
	let itemsLength = res.result.y_axis?.length;
	let itensDateLength = res.result.x_axis?.length;
	document.querySelector(`#branch-sales-overview-card-echarts-${echartsId}`).style.height = 7 * itemsLength + 17 + 'vw';
	document.querySelector(`#branch-sales-overview-card-echarts-${echartsId}`).style.width = 10 * itensDateLength + 18 + 'vw';
	comStatus.value = res.result.date_array.length < 1 ? 'empty' : 'normal';
	if (props.summary) {
		await nextTick();
		emit('summaryIng', { data: res.result, params: query });
	}
};
const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	proxy.$umeng('点击', `${props.umengPath}-`, `${props.info.isLike ? '取消关注' : '关注'}`);
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let filterList = ref(['发出', '销售', '库存']);

let currentIndex = ref('发出');
let filterChange = async (item) => {
	currentIndex.value = item;
	query.indicatorType = typeMap[currentIndex.value];
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-展示方式`, item);
};
let echartsId = generateRandomNumber();
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(
			props.defaultValue,
			{
				query,
				brand, // 传入 ref
				orgZ, // 传入 ref
				time,
				defaultSku, // 传入 ref
				defaultPerson, // 传入 ref
				defaultProvinceCity, // 传入 ref
				currentIndex,
			},
			true
		);
	}
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
.sale-trend {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	.wrap {
		background-color: var(--pv-bgc);
		border-radius: 5px;
		margin-top: 8px;
		.sale-trend-echarts {
			min-width: 100%;
		}
	}
	&-echarts-card {
		height: 174px;
		overflow-y: auto;
		overflow-x: auto;
		padding-top: 8px;
		/* 设置纵向滚动条样式 */
		&::-webkit-scrollbar {
			width: 4px; /* 设置纵向滚动条宽度 */
			height: 4px;
		}

		&::-webkit-scrollbar-thumb {
			background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
			border-radius: 5px;
			// margin: 50px 0px;
		}
	}
}
</style>
