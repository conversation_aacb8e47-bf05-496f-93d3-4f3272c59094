<template>
	<div class="sale-trend" :style="{ margin: hiddenSomething ? '0 0' : '' }">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="operation" v-if="!hiddenSomething">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<!-- 销售趋势 -->
		<div v-if="!hiddenSomething">
			<div class="q-box">
				<!-- 时间 -->
				<div @click="openTime" class="q-width-100 q-fileter-item">
					<span class="item-title ell">{{ query.endDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div class="q-box">
				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.brandName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div @click="openOrg" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<div @click="openCity" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.countyName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
		</div>
		<div class="wrap">
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="sale-trend-card">
				<div class="top">
					<span class="top-title">时间进度</span>
					<span class="top-tx">
						<div class="bar">
							<div :style="{ width: detailList.time_completion_rate + '%' }"></div>
						</div>
						<div class="value">{{ detailList.time_completion_rate }}%</div>
					</span>
				</div>
				<div class="second">
					<div class="fc">
						<div class="info">
							<div>
								<div class="name">商业发出</div>
								<div class="value">{{ formatNumbeWT(detailList?.shipment?.value) }}K</div>
							</div>
							<div class="cir">
								<van-circle v-model:current-rate="currentRate1" :speed="100" stroke-linecap="butt" :stroke-width="translateFont(150)" :text="detailList?.shipment?.completion_rate + '%'" :size="translateFont(55)" :rate="currentRate2" color="#0052CC" layer-color="#F4F5F7" start-position="left" />
							</div>
						</div>
						<div class="zhi">
							<span>
								<span class="name">差值</span>
								<span class="value">{{ formatNumbeWT(detailList?.shipment?.diff) }}K</span>
							</span>
							<span>
								<span class="name">同比</span>
								<span class="value">
									<span>{{ detailList?.shipment?.month_on_month }}%</span>
									<img v-if="detailList?.shipment?.month_on_month > 0" src="@/assets/img/down.png" alt="" />
									<img v-else src="@/assets/img/up.png" alt="" />
								</span>
							</span>
							<span>
								<span class="name">环比</span>
								<span class="value">
									<span>{{ detailList?.shipment?.year_on_year }}%</span>
									<img v-if="detailList?.shipment?.year_on_year > 0" src="@/assets/img/down.png" alt="" />
									<img v-else src="@/assets/img/up.png" alt="" />
								</span>
							</span>
						</div>
					</div>
					<div class="xs">
						<div class="info">
							<div>
								<div class="name">销售</div>
								<div class="value">{{ formatNumbeWT(detailList?.sales?.value) }}K</div>
							</div>
							<div class="cir">
								<van-circle v-model:current-rate="currentRate3" :speed="100" stroke-linecap="butt" :stroke-width="translateFont(150)" :text="detailList?.sales?.completion_rate + '%'" :size="translateFont(55)" :rate="currentRate4" color="#79E1F2" layer-color="#F4F5F7" start-position="left" />
							</div>
						</div>
						<div class="zhi">
							<span>
								<span class="name">差值</span>
								<span class="value">{{ formatNumbeWT(detailList?.sales?.diff) }}K</span>
							</span>
							<span>
								<span class="name">同比</span>
								<span class="value">
									<span>{{ detailList?.sales?.month_on_month }}%</span>
									<img v-if="detailList?.sales?.month_on_month > 0" src="@/assets/img/down.png" alt="" />
									<img v-else src="@/assets/img/up.png" alt="" />
								</span>
							</span>
							<span>
								<span class="name">环比</span>
								<span class="value">
									<span>{{ detailList?.sales?.year_on_year }}%</span>
									<img v-if="detailList?.sales?.year_on_year > 0" src="@/assets/img/down.png" alt="" />
									<img v-else src="@/assets/img/up.png" alt="" />
								</span>
							</span>
						</div>
					</div>
				</div>
				<div class="third">
					<div>
						<div class="big">
							<span class="title">整体库存</span>
							<span>
								<span class="max">{{ detailList?.inventory?.overall_days }}</span>
								<span class="mini">天</span>
							</span>
						</div>
						<div class="big">
							<span class="title">安全库存</span>
							<span>
								<span class="max">{{ detailList?.inventory?.safety_days }}</span>
								<span class="mini">天</span>
							</span>
						</div>
					</div>
					<div>
						<div class="small">
							<span class="title">一级</span>
							<span class="value">
								<span>{{ formatNumbeWT(detailList?.inventory?.first_level?.amount) }}K</span>
								|
								<span style="color: #0052cc">{{ detailList?.inventory?.first_level?.day }}</span>
							</span>
						</div>
						<div class="small">
							<span class="title">二级</span>
							<span class="value">
								<span>{{ formatNumbeWT(detailList?.inventory?.second_level?.amount) }}K</span>
								|
								<span>{{ detailList?.inventory?.second_level?.day }}</span>
							</span>
						</div>
						<div class="small">
							<span class="title">连锁</span>
							<span class="value">
								<span>{{ formatNumbeWT(detailList?.inventory?.chain?.amount) }}K</span>
								|
								<span style="color: #ff0000">{{ detailList?.inventory?.chain?.day }}</span>
							</span>
						</div>
					</div>
				</div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :dataAccountingTime="props.info?.dataAccountingTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 全部产品 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${props.umengPath}-人员筛选器`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm" :path="`${props.umengPath}-城市筛选器`"></cityFilter>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
	</div>
</template>
<script setup>
import { getSalesAnalysis } from '@/api/cube';
import { formatNumbeWT, getYearMonthBefore, getFilterTime, translateFont, getNamesByIdsInArray, spaces, getNameByPath } from '@/utils/index';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
let props = defineProps(['info', 'hiddenSomething', 'umengPath', 'summary', 'inChat', 'defaultValue']);
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);

// 筛选条件
const query = reactive({
	startDate: props.hiddenSomething ? getYearMonthBefore(7) : getFilterTime(props.info?.endTime),
	endDate: props.hiddenSomething ? getYearMonthBefore(2) : getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	countyName: '全部省市',
});
// 默认选中的人员
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesSituation',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _endDate;
	query.endDate = _endDate;
	await getData();
	proxy.$umeng('筛选', `${props.umengPath}-时间筛选器`, `${query.startDate}-${query.endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.endDate;
		query.endDate = n.endDate;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.dinfo?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);
const defaultSku = ref('');
watch(
	() => reportStore.globalFilterInfo.brand,
	async (n) => {
		brand.value.handleReset();
		brandData.value = filterStore.skuInfoTree;
		defaultSku.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		brand.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		brand.value.confirm();
	}
);
watch(
	() => reportStore.globalFilterInfo.people,
	async (n) => {
		orgZ.value.handleReset();
		defaultPerson.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		orgZ.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		orgZ.value.confirm();
	}
);
watch(
	() => reportStore.globalFilterInfo.area,
	async (n) => {
		console.log(n);
		city.value.golbalInit(n.provinceData);
		query.countyName = n.countyName;
		query.city = n.city;
		query.province = n.province;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.dinfo.nameCn}-城市`, query.countyName);
	}
);
// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesSituation',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部品牌';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	await getData();
	proxy.$umeng('筛选', `${props.umengPath}-产品筛选器`, query.brandName);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesSituation',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgList.value = filterStore.treeInfo;
	orgZ.value.show = true;
};

const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	await getData();
	proxy.$umeng('筛选', `${props.umengPath}-人员筛选器`, query.personName);
};

// 城市
const city = ref(null);
const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesSituation',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	await getData();
	proxy.$umeng('筛选', `${props.umengPath}-城市筛选器`, query.countyName);
};
//表格数据

const currentRate1 = ref(0);
const currentRate2 = ref(0);
const currentRate3 = ref(0);
const currentRate4 = ref(0);
const detailList = ref([]);
let comStatus = ref('loading');
let getData = async () => {
	comStatus.value = 'loading';
	let res = await getSalesAnalysis({
		product_filter: query.brandId || '',
		staff_filter: query.personId || '',
		province_filter: query.province?.join(',') || '',
		city_filter: query.city?.join(',') || '',
		start_time: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		end_time: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
	});
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}
	detailList.value = res.result.data;
	currentRate2.value = new Decimal(res.result.data.shipment.completion_rate).dividedBy(2).toDecimalPlaces(2).toString();
	currentRate4.value = new Decimal(res.result.data.sales.completion_rate).dividedBy(2).toDecimalPlaces(2).toString();

	comStatus.value = !res.result.data ? 'empty' : 'normal';
	if (props.summary) {
		await nextTick();
		emit('summaryIng', { data: res.result, params: query });
	}
};
const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	proxy.$umeng('点击', `${props.umengPath}-`, `${props.info.isLike ? '取消关注' : '关注'}`);
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(
			props.defaultValue,
			{
				query,
				brand, // 传入 ref
				orgZ, // 传入 ref
				time,
				defaultSku, // 传入 ref
				defaultPerson, // 传入 ref
				defaultProvinceCity, // 传入 ref
			},
			true
		);
	}
	await getData();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
.sale-trend {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	.wrap {
		background-color: var(--pv-bgc);
		border-radius: 5px;
		margin-top: 8px;
		.sale-trend-card {
			padding: 12px 8px;
			background: linear-gradient(135deg, rgba(0, 138, 232, 0.1) 0%, rgba(255, 255, 255, 0.1) 100%);
			border-radius: 6px;
			.top {
				display: flex;
				justify-content: space-between;
				&-title {
					font-weight: bold;
					font-size: 9px;
					color: rgba(23, 43, 77, 0.8);
				}
				&-tx {
					display: flex;
					align-items: center;
					.bar {
						width: 84px;
						height: 6px;
						background: #f4f5f7;
						div {
							background-color: #94caf8;
							// width: 100%;
							height: 100%;
						}
					}
					.value {
						font-size: 9px;
						color: #172b4d;
						margin-left: 10px;
					}
				}
			}
			.second {
				display: flex;
				margin-top: 6px;
				gap: 8px;
				.fc,
				.xs {
					background: #ffffff;
					border-radius: 5px;
					padding: 12px;
					flex: 1;
					.info {
						display: flex;
						justify-content: space-between;
						& > div:nth-child(1) {
							.name {
								font-size: 9px;
								color: #6b778c;
							}
							.value {
								font-weight: bold;
								font-size: 12px;
								color: #172b4d;
							}
						}
						.cir {
							position: relative;
							top: 5px;
							:deep(.van-circle) {
								.van-circle__text {
									font-size: 10px;
									color: #33476b;
									font-weight: normal;
									transform: translateY(-15px);
								}
							}
							&::after {
								content: '';
								position: absolute;
								display: block;
								width: 100%;
								height: 32px;
								background-color: #fff;
								bottom: 0;
							}
						}
					}
					.zhi {
						display: flex;
						margin-top: -12px;
						position: relative;
						z-index: 1;
						gap: 9px;
						& > span {
							flex: 1;
							display: flex;
							flex-direction: column;
							justify-content: center;
						}
						.name {
							font-size: 9px;
							color: #6b778c;
							text-align: left;
							text-indent: 2px;
						}
						.value {
							text-align: center;
							font-size: 9px;
							color: #172b4d;
							display: flex;
							align-items: center;
							img {
								width: 8px;
								// margin-left: 4px;
								transform: rotate(180deg);
							}
						}
					}
				}
			}
			.third {
				margin-top: 11px;
				background: #ffffff;
				border-radius: 5px;
				padding: 12px;
				display: flex;
				align-items: center;
				& > div:nth-child(1) {
					flex: 1;
					display: flex;
				}
				& > div:nth-child(2) {
					flex: 2.2;
					display: flex;
				}
				.big {
					flex: 1;
					display: flex;
					flex-direction: column;
					.title,
					.mini {
						font-size: 9px;
						color: #6b778c;
					}
					.max {
						font-weight: bold;
						font-size: 12px;
						color: #172b4d;
						margin-right: 3px;
					}
				}
				.small {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-top: 8px;
					.title {
						font-size: 9px;
						color: #6b778c;
					}
					.value {
						font-size: 9px;
						color: #172b4d;
					}
				}
			}
		}
	}
}
</style>
