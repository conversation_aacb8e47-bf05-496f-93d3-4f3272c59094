<template>
	<div class="filter" id="golbal-filter">
		<div class="title" @click="expand = !expand">
			<span>全局筛选</span>
			<div class="tips">注意：此筛选将会影响所有卡片的数据范围</div>
			<svg-icon class="icon" :class="{ el: expand }" icon-class="down2"></svg-icon>
		</div>
		<div v-if="expand" class="fa">
			<div class="q-box">
				<div @click="openTime" class="q-width-100 q-fileter-item">
					<span class="item-title ell">{{ query.endDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div class="q-box">
				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.brandName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div @click="openOrg" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<div @click="openCity" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.countyName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
		</div>

		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 城市选择 -->
		<cityFilter ref="city" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import usefilterStore from '@/store/modules/filter';
import useReportStore from '@/store/modules/report';
import { deepClone } from '@/utils/index';
let filterStore = usefilterStore();
let reportStore = useReportStore();
const props = defineProps(['filterConfig']);

const expand = ref(false);

// 筛选条件
const query = reactive({
	startDate: '',
	endDate: '日期',
	brandName: '全部产品',
	brandId: '',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
});
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _endDate.slice(0, 4) + '01';
	query.endDate = _endDate;
	if (document.querySelector('.sale-trend-table-content')) {
		console.log(document.querySelector('.sale-trend-table-content'));
		document.querySelector('.sale-trend-table-content').style.height = 'auto';
	}

	reportStore.setGlobalFilterInfo('time', { startDate: query.startDate, endDate: query.endDate });
};
// 城市
const city = ref(null);
const openCity = () => {
	city.value.show = true;
};
const cityConfirm = async (params) => {
	console.log(params);
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	reportStore.setGlobalFilterInfo('area', { countyName: query.countyName, city: query.city, province: query.province, provinceData: deepClone(params.provinceData) });
};
// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (brandData.value.length === 0) {
		// for (const item of filterStore.skuInfo) {
		// 	brandData.value.push({ id: item.skuCode, name: item.skuCn });
		// }
		brandData.value = filterStore.skuInfoTree;
	}
	brand.value.show = true;
};

let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	reportStore.setGlobalFilterInfo('brand', { brandName: query.brandName, brandId: query.brandId, checked: params.checked });
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgZ.value.show = true;
	// orgList.value = filterStore.treeInfo;
};
const orgConfirm = async ({ userId: ids, userName: names }) => {
	// defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	reportStore.setGlobalFilterInfo('people', { personName: query.personName, personId: query.personId, checked: ids });
};

// 医院筛选
const hospital = ref(null);
const openHospital = () => {
	hospital.value.show = true;
};
const hospitalList = ref([]);
const hospitalSearch = ref('');
const gobalFilterSearchChange = (v) => {
	console.log(v);

	hospitalSearch.value = v.searchValue;
	hospitalList.value = v.searchList;
};
let hospitalConfirm = async (params, name, checked, isCheckAll, isIndeterminate) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	reportStore.setGlobalFilterInfo('hospital', { checked, isCheckAll, isIndeterminate, hospitalSearch: hospitalSearch.value, hospitalList: hospitalList.value });
};
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.filterConfig) {
		const filterConfig = JSON.parse(props.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			// defaultPerson.value = result.defaultPerson;
			// query.personId = result.personId;
			// query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
};
onMounted(async () => {
	initFilter();
});
</script>
<style lang="scss" scoped>
@import '../../../report/pc-filter.scss';
.filter {
	background: #e5eefa;
	border-radius: 5px 5px 5px 5px;
	border: 1px solid rgba(0, 82, 204, 0.3);
	padding: 15px 0px 7px;
	margin-bottom: 10px;
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: bold;
		display: flex;
		align-items: center;
		padding-left: 7px;
		padding-right: 8px;
		position: relative;
		.tips {
			font-size: 9px;
			color: #ff0000;
			margin-left: 8px;
		}
		.icon {
			position: absolute;
			right: 10px;
			top: 50%;
			transform: translateY(-50%);
			font-size: 7px;
			transition: all 0.2s;
		}
		.el {
			transform: translateY(-50%) rotate(180deg);
		}
	}
	.fa {
		padding: 0 8px 8px;
	}
	.q-fileter-item {
		background-color: #fff;
	}
}
</style>
