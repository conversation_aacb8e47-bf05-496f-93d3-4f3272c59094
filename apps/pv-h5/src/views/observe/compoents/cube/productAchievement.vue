<template>
	<div class="sale-trend" :style="{ margin: hiddenSomething ? '0 0' : '' }">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="operation" v-if="!hiddenSomething">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<!-- 销售趋势 -->
		<div v-if="!hiddenSomething">
			<div class="q-box">
				<!-- 时间 -->
				<div @click="openTime" class="q-width-100 q-fileter-item">
					<span class="item-title ell">{{ query.startDate + ' - ' + query.endDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div class="q-box">
				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.brandName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div @click="openOrg" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<div @click="openCity" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.countyName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
		</div>
		<div class="wrap">
			<filterCom :filterList="filterList" :currentIndex="currentIndex" @filterChange="filterChange"></filterCom>
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="sale-trend-echarts-card">
				<div class="sale-trend-echarts" :id="`sale-trend-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
		</div>
		<div class="sale-trend-table">
			<div class="sale-trend-table-title" @click="viewDetail">
				<span>点击查看明细</span>
				<svg-icon class="icon" :class="{ down: zk }" icon-class="down2"></svg-icon>
			</div>
			<div v-show="zk" class="sale-trend-table-content">
				<productAchievementTable :detailList="detailList" :lastResult="lastResult" :columns="columns"></productAchievementTable>
			</div>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :dataAccountingTime="props.info?.dataAccountingTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 全部产品 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${props.umengPath}-人员筛选器`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm" :path="`${props.umengPath}-城市筛选器`"></cityFilter>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { getAchievementAnalysis } from '@/api/cube';
import { formatNumbeWT, generateRandomNumber, getYearMonthBefore, getFilterTime, translateFont, saleTrendFilter, getNamesByIdsInArray, spaces, getNameByPath } from '@/utils/index';
import Decimal from 'decimal.js';
import productAchievementTable from './productAchievementTable';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
let props = defineProps(['info', 'hiddenSomething', 'umengPath', 'summary', 'inChat', 'defaultValue']);
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);

// 筛选条件
const query = reactive({
	startDate: props.hiddenSomething ? getYearMonthBefore(7) : getFilterTime(props.info?.startTime),
	endDate: props.hiddenSomething ? getYearMonthBefore(2) : getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	countyName: '全部省市',
});
// 默认选中的人员
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'productAchievement',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	document.querySelector('.sale-trend-table-content').style.height = 'auto';
	proxy.$umeng('筛选', `${props.umengPath}-时间筛选器`, `${query.startDate}-${query.endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.endDate;
		query.endDate = n.endDate;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.dinfo?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);
const defaultSku = ref('');
watch(
	() => reportStore.globalFilterInfo.brand,
	async (n) => {
		brand.value.handleReset();
		brandData.value = filterStore.skuInfoTree;
		defaultSku.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		brand.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		brand.value.confirm();
	}
);
watch(
	() => reportStore.globalFilterInfo.people,
	async (n) => {
		orgZ.value.handleReset();
		defaultPerson.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		orgZ.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		orgZ.value.confirm();
	}
);
watch(
	() => reportStore.globalFilterInfo.area,
	async (n) => {
		console.log(n);
		city.value.golbalInit(n.provinceData);
		query.countyName = n.countyName;
		query.city = n.city;
		query.province = n.province;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.dinfo.nameCn}-城市`, query.countyName);
	}
);
// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'productAchievement',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部品牌';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${props.umengPath}-产品筛选器`, query.brandName);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'productAchievement',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgList.value = filterStore.treeInfo;
	orgZ.value.show = true;
};

const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${props.umengPath}-人员筛选器`, query.personName);
};

// 城市
const city = ref(null);
const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'productAchievement',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${props.umengPath}-城市筛选器`, query.countyName);
};

let myEcharts = echarts;
let chart;
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#sale-trend-echarts-${echartsId}`));
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let option = {
		grid: {
			top: translateFont(55),
			left: translateFont(50),
			right: translateFont(45),
			bottom: translateFont(30),
		},
		legend: {
			data: legend.value,
			left: 'center',
			top: translateFont(15),
			itemGap: translateFont(15),
			itemWidth: translateFont(15),
			itemHeight: translateFont(4),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = `<span class="title">产品：${value[0].name}</span>`;
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${saleTrendFilter(item, props.info.unit, props.info.unitValue)}</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			axisPointer: {
				type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
				},
			},
			extraCssText: 'z-index:9',
		},
		xAxis: [
			{
				type: 'category',
				data: xData.value,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: spaces(xData.value.length), // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumbeWT(value);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return new Decimal(value).toDecimalPlaces(1) + '%';
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
			},
		],
		color: ['#0052CC', '#79E1F2', '#E95600', '#01A2BF'],
		series: series.value,
	};
	if (currentIndex.value.includes('贡献')) {
		option.yAxis.splice(1, 1); // 删除 yAxis 数组中的第二个元素（索引 1）
		option.tooltip.formatter = (value) => {
			if (value[1].data === 0) {
				console.log(value);
				let v = value.filter((ele) => ele.seriesIndex === 3);
				let s = value.find((ele) => ele.seriesIndex === 1);
				let str = `<span class="title">产品：${value[0].name}</span>`;
				for (let item of v) {
					let num = formatNumbeWT(Math.abs(item.value) + Math.abs(detailList.value.find((ele) => ele.name === item.name)?.auxiliary_down) + Math.abs(detailList.value.find((ele) => ele.name === item.name)?.auxiliary_up));
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${s.seriesName}</span><span class="value">-${num}</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			} else {
				console.log(value);
				let v = value.filter((ele) => ele.seriesIndex === 1);
				let str = `<span class="title">产品：${value[0].name}</span>`;
				for (let item of v) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${saleTrendFilter(item, props.info.unit, props.info.unitValue)}</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			}
		};
	}
	chart.setOption(option, { notMerge: true });
};

let zk = ref(false);

const legend = ref([]);
const series = ref([]);
const xData = ref([]);
//表格数据
const detailList = ref([]);
const lastResult = ref([]);
const columns = ref([]);
let comStatus = ref('loading');
let typeMap = {
	发出: 'shipment',
	发出贡献: 'shipment_contribution',
	销售: 'sales',
	销售贡献: 'sales_contribution',
	库存: 'inventory',
};
let getData = async () => {
	comStatus.value = 'loading';
	let res = await getAchievementAnalysis({
		tab: typeMap[currentIndex.value],
		product_filter: query.brandId || '',
		staff_filter: query.personId || '',
		province_filter: query.province?.join(',') || '',
		city_filter: query.city?.join(',') || '',
		start_time: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		end_time: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
	});
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}
	detailList.value = res.result.data;
	xData.value = res.result.data.map((ele) => ele.name);
	if (currentIndex.value === '发出' || currentIndex.value === '销售') {
		legend.value = ['实际', '指标', '达成', '同比'];
		series.value = [
			{
				name: '实际',
				type: 'bar',
				data: res.result.data.map((item) => item.sales),
				barWidth: translateFont(8),
				z: 2,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '指标',
				type: 'bar',
				data: res.result.data.map((item) => item.target),
				barWidth: translateFont(8),
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '达成',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: res.result.data.map((item) => item.achievement_rate_v),
				z: 4,
			},
			{
				name: '同比',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: res.result.data.map((item) => item.yoy_growth_rate_v),
				z: 4,
			},
		];
		columns.value = [
			{ field: 'name', title: '产品' },
			{ field: 'sales', title: '实际(K)' },
			{ field: 'target', title: '指标(K)' },
			{ field: 'achievement_rate_v', title: '达成(%)' },
			{ field: 'yoy_growth_rate_v', title: '同比(%)' },
		];
		lastResult.value = [res.result.summary.name, formatNumbeWT(res.result.summary.sales), formatNumbeWT(res.result.summary.target), `${new Decimal(res.result.summary.achievement_rate_v).toDecimalPlaces(1) + '%'}`, `${new Decimal(res.result.summary.yoy_growth_rate_v).toDecimalPlaces(1) + '%'}`];
	} else if (currentIndex.value === '库存') {
		legend.value = ['库存金额', '库存周转率'];
		series.value = [
			{
				name: '库存金额',
				type: 'bar',
				data: res.result.data.map((item) => item.total_theoretical_inventory_v),
				barWidth: translateFont(8),
				z: 2,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '库存周转率',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: res.result.data.map((item) => item.inventory_efficiency_rate),
				z: 4,
				color: '#01A2BF',
			},
		];
		columns.value = [
			{ field: 'name', title: '产品', width: '30%' },
			{ field: 'total_theoretical_inventory_v', title: '库存金额(K)', width: '35%' },
			{ field: 'inventory_efficiency_rate', title: '库存周转率(%)', width: '35%' },
		];
		lastResult.value = [res.result.summary.name, formatNumbeWT(res.result.summary.total_theoretical_inventory_v), `${new Decimal(res.result.summary.inventory_efficiency_rate).toDecimalPlaces(1) + '%'}`];
	} else if (currentIndex.value === '发出贡献') {
		legend.value = ['发出金额'];
		series.value = [
			{
				name: '辅助',
				type: 'bar',
				data: res.result.data.map((item) => item.auxiliary),
				barWidth: translateFont(8),
				emphasis: { disabled: true },
				stack: '总量',
				itemStyle: {
					normal: {
						barBorderColor: 'rgba(0,0,0,0)',
						color: 'rgba(0,0,0,0)',
					},
				},
			},
			{
				name: '发出金额',
				type: 'bar',
				data: res.result.data.map((item) => item.increase),
				barWidth: translateFont(8),
				emphasis: { disabled: true },
				stack: '总量',
				color: '#0052CC',
			},
			{
				name: '发出金额辅助',
				type: 'bar',
				data: res.result.data.map((item) => item.auxiliary_up),
				barWidth: translateFont(8),
				emphasis: { disabled: true },
				stack: '总量',
				color: '#0052CC',
			},
			{
				name: '发出金额负',
				type: 'bar',
				symbolSize: translateFont(5),
				data: res.result.data.map((item) => item.decrease),
				color: '#E95600',
				stack: '总量',
			},
			{
				name: '发出金额负辅助',
				type: 'bar',
				symbolSize: translateFont(5),
				data: res.result.data.map((item) => item.auxiliary_down),
				color: '#E95600',
				stack: '总量',
			},
		];
		columns.value = [
			{ field: 'name', title: '产品', width: '30%' },
			{ field: 'sales', title: '发出金额(K)', width: '70%' },
		];
	} else {
		legend.value = ['销售金额'];
		series.value = [
			{
				name: '辅助',
				type: 'bar',
				data: res.result.data.map((item) => item.auxiliary),
				barWidth: translateFont(8),
				emphasis: { disabled: true },
				stack: '总量',
				itemStyle: {
					normal: {
						barBorderColor: 'rgba(0,0,0,0)',
						color: 'rgba(0,0,0,0)',
					},
				},
			},
			{
				name: '销售金额',
				type: 'bar',
				data: res.result.data.map((item) => item.increase),
				barWidth: translateFont(8),
				emphasis: { disabled: true },
				stack: '总量',
				color: '#0052CC',
			},
			{
				name: '销售金额辅助',
				type: 'bar',
				data: res.result.data.map((item) => item.auxiliary_up),
				barWidth: translateFont(8),
				emphasis: { disabled: true },
				stack: '总量',
				color: '#0052CC',
			},
			{
				name: '销售金额负',
				type: 'bar',
				symbolSize: translateFont(5),
				data: res.result.data.map((item) => item.decrease),
				color: '#E95600',
				stack: '总量',
			},
			{
				name: '销售金额负辅助',
				type: 'bar',
				symbolSize: translateFont(5),
				data: res.result.data.map((item) => item.auxiliary_down),
				color: '#E95600',
				stack: '总量',
			},
		];
		columns.value = [
			{ field: 'name', title: '产品', width: '30%' },
			{ field: 'sales', title: '销售金额(K)', width: '70%' },
		];
	}
	comStatus.value = res.result.data.length < 1 ? 'empty' : 'normal';
	if (props.summary) {
		await nextTick();
		emit('summaryIng', { data: res.result, params: query });
	}
};
const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	proxy.$umeng('点击', `${props.umengPath}-`, `${props.info.isLike ? '取消关注' : '关注'}`);
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
const viewDetail = () => {
	zk.value = !zk.value;
	proxy.$umeng('点击', `${props.umengPath}-`, '点击查看明细');
};
let filterList = ref(['发出', '发出贡献', '销售', '销售贡献', '库存']);

let currentIndex = ref('发出');
let filterChange = async (item) => {
	currentIndex.value = item;
	query.indicatorType = typeMap[currentIndex.value];
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-展示方式`, item);
};
let echartsId = generateRandomNumber();
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(
			props.defaultValue,
			{
				query,
				brand, // 传入 ref
				orgZ, // 传入 ref
				time,
				defaultSku, // 传入 ref
				defaultPerson, // 传入 ref
				defaultProvinceCity, // 传入 ref
				currentIndex,
			},
			true
		);
	}
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
.sale-trend {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	.wrap {
		background-color: var(--pv-bgc);
		border-radius: 5px;
		margin-top: 8px;
	}
	&-echarts {
		height: 220px;
	}

	&-table {
		background-color: #fff;
		margin-top: 8px;
		overflow: hidden;
		&-title {
			font-size: 12px;
			color: var(--pv-default-color);
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 6px;
			position: relative;
			.icon {
				font-size: 7px;
				position: absolute;
				right: 14px;
				transition: all 0.5s;
			}
			.down {
				transform: rotate(180deg);
			}
		}
		&-content {
			padding: 0 8px 0px;
		}
	}
}
</style>
