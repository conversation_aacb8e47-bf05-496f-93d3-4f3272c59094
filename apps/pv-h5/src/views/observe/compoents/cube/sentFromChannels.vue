<template>
	<div class="sale-trend" :style="{ margin: hiddenSomething ? '0 0' : '' }">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="operation" v-if="!hiddenSomething">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<!-- 销售趋势 -->
		<div v-if="!hiddenSomething">
			<div class="q-box">
				<!-- 时间 -->
				<div @click="openTime" class="q-width-100 q-fileter-item">
					<span class="item-title ell">{{ query.startDate + ' - ' + query.endDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div class="q-box">
				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.brandName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div @click="openOrg" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<div @click="openCity" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.countyName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
		</div>
		<div class="wrap">
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="sale-trend-echarts-card">
				<div class="sale-trend-echarts" :id="`sale-trend-echarts-${echartsId}`"></div>
				<!-- <div class="sale-trend-echarts" :id="`sale-trend-echarts-${echartsId1}`"></div> -->
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
		</div>
		<div class="sale-trend-table">
			<div class="sale-trend-table-title" @click="viewDetail">
				<span>点击查看明细</span>
				<svg-icon class="icon" :class="{ down: zk }" icon-class="down2"></svg-icon>
			</div>
			<div v-show="zk" class="sale-trend-table-content">
				<productAchievementTable :detailList="detailList" :columns="columns"></productAchievementTable>
			</div>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :dataAccountingTime="props.info?.dataAccountingTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 全部产品 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${props.umengPath}-人员筛选器`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm" :path="`${props.umengPath}-城市筛选器`"></cityFilter>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { getChannelAnalysis } from '@/api/cube';
import { formatNumbeWT, generateRandomNumber, getYearMonthBefore, getFilterTime, translateFont, saleTrendFilter, getNamesByIdsInArray, spaces, getNameByPath } from '@/utils/index';
import Decimal from 'decimal.js';
import productAchievementTable from './productAchievementTable';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
let props = defineProps(['info', 'hiddenSomething', 'umengPath', 'summary', 'inChat', 'defaultValue']);
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);

// 筛选条件
const query = reactive({
	startDate: props.hiddenSomething ? getYearMonthBefore(7) : getFilterTime(props.info?.startTime),
	endDate: props.hiddenSomething ? getYearMonthBefore(2) : getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	countyName: '全部省市',
});
// 默认选中的人员
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'sentFromChannels',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	document.querySelector('.sale-trend-table-content').style.height = 'auto';
	proxy.$umeng('筛选', `${props.umengPath}-时间筛选器`, `${query.startDate}-${query.endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.endDate;
		query.endDate = n.endDate;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.dinfo?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);
const defaultSku = ref('');
watch(
	() => reportStore.globalFilterInfo.brand,
	async (n) => {
		brand.value.handleReset();
		brandData.value = filterStore.skuInfoTree;
		defaultSku.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		brand.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		brand.value.confirm();
	}
);
watch(
	() => reportStore.globalFilterInfo.people,
	async (n) => {
		orgZ.value.handleReset();
		defaultPerson.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		orgZ.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		orgZ.value.confirm();
	}
);
watch(
	() => reportStore.globalFilterInfo.area,
	async (n) => {
		console.log(n);
		city.value.golbalInit(n.provinceData);
		query.countyName = n.countyName;
		query.city = n.city;
		query.province = n.province;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.dinfo.nameCn}-城市`, query.countyName);
	}
);
// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'sentFromChannels',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部品牌';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${props.umengPath}-产品筛选器`, query.brandName);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'sentFromChannels',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgList.value = filterStore.treeInfo;
	orgZ.value.show = true;
};

const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${props.umengPath}-人员筛选器`, query.personName);
};

// 城市
const city = ref(null);
const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'sentFromChannels',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${props.umengPath}-城市筛选器`, query.countyName);
};

let myEcharts = echarts;
let chart;
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#sale-trend-echarts-${echartsId}`));
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let option = {
		grid: [
			{
				width: '38%',
				top: translateFont(55),
				left: '4%',
				bottom: translateFont(30),
			},
			{
				top: translateFont(55),
				left: '51%',
				right: translateFont(0),
				bottom: translateFont(30),
			},
			{
				width: '36%',
				top: translateFont(55),
				// left: '53%',
				right: '8%',
				bottom: translateFont(30),
			},
		],
		legend: {
			data: legend.value,
			left: 'center',
			top: translateFont(15),
			itemGap: translateFont(15),
			itemWidth: translateFont(15),
			itemHeight: translateFont(4),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		tooltip: {
			// trigger: 'none', // 'item', 'axis', 'none'
			// position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			show: true,
			formatter: (value) => {
				console.log(value);

				let str = ``;
				// for (let item of value) {
				let num = saleTrendFilter(value, props.info.unit, props.info.unitValue).includes('-') ? saleTrendFilter(value, props.info.unit, props.info.unitValue).substring(1) : saleTrendFilter(value, props.info.unit, props.info.unitValue);
				str += `<div><span class="dot" style="background-color:${value.color}"></span><span class="key">${value.seriesName}</span><span class="value">${num}</span></div>`;
				// }
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			extraCssText: 'z-index:9',
		},
		xAxis: [
			{
				type: 'value',
				axisLabel: {
					show: false, // 是否显示坐标轴刻度标签。
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
				splitNumber: 4,
				gridIndex: 0,
			},
			{
				type: 'value',
				axisLabel: {
					show: false, // 是否显示坐标轴刻度标签。
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
				splitNumber: 4,
				gridIndex: 1,
			},
			{
				type: 'value',
				axisLabel: {
					show: false, // 是否显示坐标轴刻度标签。
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
				splitNumber: 4,
				gridIndex: 2,
			},
		],
		yAxis: [
			{
				type: 'category',
				data: xData.value,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: false, // 是否显示坐标轴刻度标签。
				},
				gridIndex: 0,
			},
			{
				type: 'category',
				position: 'center',
				data: xData.value,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					backgroundColor: '#F4F5F7',
					padding: [translateFont(0), translateFont(10)],
					align: 'center',
				},
				gridIndex: 1,
			},
			{
				type: 'category',
				data: xData.value,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: false, // 是否显示坐标轴刻度标签。
				},
				gridIndex: 2,
			},
		],
		color: ['#0052CC', '#E95600'],
		series: series.value,
	};
	chart.setOption(option, { notMerge: true });
};
let zk = ref(false);

const legend = ref([]);
const series = ref([]);
const xData = ref([]);
//表格数据
const detailList = ref([]);
const lastResult = ref([]);
const columns = ref([]);
let comStatus = ref('loading');
let getData = async () => {
	comStatus.value = 'loading';
	let res = await getChannelAnalysis({
		product_filter: query.brandId || '',
		staff_filter: query.personId || '',
		province_filter: query.province?.join(',') || '',
		city_filter: query.city?.join(',') || '',
		start_time: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		end_time: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
	});
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}
	detailList.value = res.result.data;
	xData.value = res.result.data.map((ele) => ele.channel_type);
	legend.value = ['客户数', '发出金额'];
	series.value = [
		{
			name: '客户数',
			type: 'bar',
			data: res.result.data.map((item) => -item.customer_count),
			barWidth: translateFont(8),
			z: 2,
			emphasis: {
				itemStyle: {
					shadowBlur: 10,
					shadowColor: '#eee',
				},
			},
			label: {
				show: true,
				position: 'left',
				color: '#6B778C',
				fontSize: translateFont(9),
				formatter: (v) => {
					if (formatNumbeWT(v.value).includes('-')) {
						return formatNumbeWT(v.value).substr(1) + 'K';
					}
					return formatNumbeWT(v.value) + 'K';
				},
			},
			xAxisIndex: 0,
			yAxisIndex: 0,
		},
		{
			name: '发出金额',
			type: 'bar',
			data: res.result.data.map((item) => item.shipment_amount),
			barWidth: translateFont(8),
			z: 2,
			emphasis: {
				itemStyle: {
					shadowBlur: 10,
					shadowColor: '#eee',
				},
			},
			label: {
				show: true,
				position: 'right',
				color: '#6B778C',
				fontSize: translateFont(9),
				formatter: (v) => {
					return formatNumbeWT(v.value) + 'K';
				},
			},
			xAxisIndex: 2,
			yAxisIndex: 2,
		},
	];
	columns.value = [
		{ field: 'channel_type', title: '渠道', width: '33.333%' },
		{ field: 'customer_count', title: '客户数(K)', width: '33.333%' },
		{ field: 'shipment_amount', title: '发出金额(K)', width: '33.333%' },
	];
	// lastResult.value = [res.result.summary.name, formatNumbeWT(res.result.summary.sales), formatNumbeWT(res.result.summary.target), `${new Decimal(res.result.summary.achievement_rate_v).toDecimalPlaces(1) + '%'}`, `${new Decimal(res.result.summary.yoy_growth_rate_v).toDecimalPlaces(1) + '%'}`];
	comStatus.value = res.result.data.length < 1 ? 'empty' : 'normal';
	if (props.summary) {
		await nextTick();
		emit('summaryIng', { data: res.result, params: query });
	}
};
const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	proxy.$umeng('点击', `${props.umengPath}-`, `${props.info.isLike ? '取消关注' : '关注'}`);
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
const viewDetail = () => {
	zk.value = !zk.value;
	proxy.$umeng('点击', `${props.umengPath}-`, '点击查看明细');
};

let echartsId = generateRandomNumber();
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(
			props.defaultValue,
			{
				query,
				brand, // 传入 ref
				orgZ, // 传入 ref
				time,
				defaultSku, // 传入 ref
				defaultPerson, // 传入 ref
				defaultProvinceCity, // 传入 ref
			},
			true
		);
	}
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
.sale-trend {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	.wrap {
		background-color: var(--pv-bgc);
		border-radius: 5px;
		margin-top: 8px;
	}
	&-echarts-card {
		display: flex;
		& > div {
			flex: 1;
		}
	}
	&-echarts {
		height: 220px;
	}

	&-table {
		background-color: #fff;
		margin-top: 8px;
		overflow: hidden;
		&-title {
			font-size: 12px;
			color: var(--pv-default-color);
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 6px;
			position: relative;
			.icon {
				font-size: 7px;
				position: absolute;
				right: 14px;
				transition: all 0.5s;
			}
			.down {
				transform: rotate(180deg);
			}
		}
		&-content {
			padding: 0 8px 0px;
		}
	}
}
</style>
