<template>
	<div>
		<vxe-table ref="tableRef" show-overflow :show-footer="show" max-height="400" :sort-config="{ trigger: 'cell' }" :footer-method="footerMethod" :data="props.detailList">
			<vxe-column v-for="(item, index) in props.columns" :key="index" :field="item.field" :title="item.title" align="center" :width="item.width || 100" sortable :sort-by="sortRule"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
import Decimal from 'decimal.js';
import { formatNumbeWT, deepClone, sortRule } from '@/utils/index';
const props = defineProps(['detailList', 'lastResult', 'columns']);
const footerData = ref([]);
const show = ref(false);
const tableRef = ref(null);
watch(
	() => props.detailList,
	(data) => {
		nextTick(() => {
			let d = deepClone(data);
			let lastData = d.find((ele) => ele.name === '合计');
			d = d.filter((ele) => ele.name !== '合计');
			const formatFields = {
				sales: (value) => formatNumbeWT(value || 0),
				target: (value) => formatNumbeWT(value || 0),
				shipments: (value) => formatNumbeWT(value || 0),
				pure_sales: (value) => formatNumbeWT(value || 0),
				warehouse_stock: (value) => formatNumbeWT(value || 0),
				total_theoretical_inventory_v: (value) => formatNumbeWT(value || 0),
				customer_count: (value) => formatNumbeWT(value || 0),
				shipment_amount: (value) => formatNumbeWT(value || 0),
				achievement_rate_v: (value) => `${new Decimal(value || 0).toDecimalPlaces(1)}%`,
				yoy_growth_rate_v: (value) => `${new Decimal(value || 0).toDecimalPlaces(1)}%`,
				inventory_efficiency_rate: (value) => `${new Decimal(value || 0).toDecimalPlaces(1)}%`,
				shipments_achievement_rate: (value) => `${new Decimal(value || 0).toDecimalPlaces(1)}%`,
				pure_sales_achievement_rate: (value) => `${new Decimal(value || 0).toDecimalPlaces(1)}%`,
			};
			for (let item of d) {
				for (let [key, formatter] of Object.entries(formatFields)) {
					if (key in item) {
						item[key] = formatter(item[key]);
					}
				}
			}
			//处理data中有总计的情况
			if (lastData) {
				footerData.value = [];
				footerData.value.push(['总计', formatNumbeWT(lastData.sales || 0)]);
			}
			tableRef.value.loadData(d);
		});
	}
);
watch(
	() => props.lastResult,
	(val) => {
		if (val) {
			footerData.value = [];
			footerData.value.push(val);
			show.value = true;
		} else {
			show.value = false;
		}
	},
	{ immediate: true }
);

const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};
</script>
<style lang="scss" scoped></style>
