<template>
	<div class="sale-trend" id="ap-sale-trend" :style="{ margin: hiddenSomething ? '0' : '' }">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="operation" v-if="!hiddenSomething">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
					<switchCom ref="switchComRef" :defaultValue="props.info" @currencySwitching="currencySwitching"></switchCom>
				</span>
			</div>
		</div>
		<!-- 销售趋势 -->
		<!-- 通用筛选器 -->
		<globalFilter
			:filters="[
				{ name: 'time', size: '49', queryName: 'dateRange', single: true },
				{ name: 'org', size: '49', queryName: 'personName' },
				{ name: 'brand', size: '32', queryName: 'brandName' },
				{ name: 'city', size: '32', queryName: 'countyName' },
				{ name: 'hospital', size: '32', queryName: 'hospitalName' },
			]"
			:query="query"
			:loading="filterLoading"
			@openTime="openTime"
			@openOrg="openOrg"
			@openBrand="openBrand"
			@openCity="openCity"
			@openHospital="openHospital"
		></globalFilter>
		<div class="sale-trend-filter">
			<!-- <filterCom v-if="!props.isPPT" :filterList="['不展示---']" :downloadExcel="true" @_downExcel="downExcel"></filterCom> -->
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="sale-trend-filter-echarts-card">
				<div class="sale-trend-filter-echarts" :id="`sale-trend-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
			<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>
		</div>
		<div v-if="!hiddenSomething" class="sale-trend-table">
			<div class="sale-trend-table-title" @click="zk = !zk">
				<span>点击查看明细</span>

				<svg-icon class="icon" :class="{ down: zk }" icon-class="down2"></svg-icon>
			</div>
			<!-- <transition @beforeEnter="handleBeforeEnter" @enter="handleEnter" @leave="handleLeave"> -->
			<div v-show="zk" class="sale-trend-table-content">
				<salesAnalysisTeamTable :tableData="detailList" :lastResult="lastResult" :isPPT="isPPT"></salesAnalysisTeamTable>
			</div>
			<!-- </transition> -->
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :defaultHosCheckedAll="defaultHosCheckedAll" :path="`${page}${props.info?.nameCn}`" :personalId="query.personId" :province="query.province" :city="query.city" :skuCode="query.brandId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { showToast, showLoadingToast } from 'vant';
import { getTeamSalesTrends, salesInfoDownload } from '@/api/sales';
import { formatNumbeW, formatNumbeWT, formatPrecentOne, generateRandomNumber, getYearMonthBefore, getFilterTime, translateFont, saleTrendFilter, spaces, getNameByPath, isPCTrue } from '@/utils/index';
import salesAnalysisTeamTable from './salesAnalysisTeamTable.vue';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
let props = defineProps(['info', 'hiddenSomething', 'isPPT', 'pathAddress', 'defaultValue', 'summary', 'inChat']);
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
const lastResult = ref({});

// 筛选条件
const query = reactive({
	startDate: props.hiddenSomething ? getYearMonthBefore(2) : getFilterTime(props.info?.endTime),
	endDate: props.hiddenSomething ? getYearMonthBefore(2) : getFilterTime(props.info?.endTime),
	brandName: '全部产品',
	brandId: '',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
});
// 默认选中人员
const defaultPerson = ref('');

// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};

// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAnalysisTeam',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	document.querySelector('.sale-trend-table-content').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.endDate;
		query.endDate = n.endDate;
		// showPicker.value = false;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.info?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);
watch(
	() => reportStore.globalFilterInfo.area,
	async (n) => {
		console.log(n);
		city.value.golbalInit(n.provinceData);
		query.countyName = n.countyName;
		query.city = n.city;
		query.province = n.province;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
	}
);
// 下载明细
const downExcel = async (emial) => {
	showLoadingToast({
		message: '下载中...',
		forbidClick: true,
		duration: 0,
		class: 'ppt-pc-loading',
	});
	const json = {
		headerData: [
			{ name: '团队', key: 'territoryCodeCn' },
			{ name: '月累计', key: 'salesM' },
			{ name: '月指标', key: 'targetM' },
			{ name: '月达成', key: 'achM' },
			{ name: '月同比', key: 'growthM' },
			{ name: '季累计', key: 'salesQ' },
			{ name: '季指标', key: 'targetQ' },
			{ name: '季达成', key: 'achQ' },
			{ name: '季同比', key: 'growthQ' },
			{ name: '年累计', key: 'salesY' },
			{ name: '年指标', key: 'targetY' },
			{ name: '年达成', key: 'achY' },
			{ name: '年同比', key: 'growthY' },
		],
		bodyData: detailList.value,
		footerData: lastResult.value,
	};
	const query = {
		name: props.info?.nameCn,
		email: emial,
		tableInfo: json,
	};
	await salesInfoDownload(query);
	showToast({
		message: '数据下载成功，稍后会发送到您的邮箱。',
		position: 'top',
	});
};

// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAnalysisTeam',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

const defaultSku = ref('');
watch(
	() => reportStore.globalFilterInfo.brand,
	async (n) => {
		brand.value.handleReset();
		brandData.value = filterStore.skuInfoTree;
		defaultSku.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		brand.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		brand.value.confirm();
	}
);
// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAnalysisTeam',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city, hospital, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 城市

const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAnalysisTeam',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	console.log(params);
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
watch(
	() => reportStore.globalFilterInfo.people,
	async (n) => {
		orgZ.value.handleReset();
		defaultPerson.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		orgZ.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		orgZ.value.confirm();
	}
);
// 医院筛选
const openHospital = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAnalysisTeam',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};
const defaultHos = ref('');
const defaultHosCheckedAll = ref('');

watch(
	() => reportStore.globalFilterInfo.hospital,
	async (n) => {
		console.log(n);

		hospital.value.handleReset();
		defaultHos.value = n.checked; //默认选中
		defaultHosCheckedAll.value = n.isCheckAll;
		await nextTick(); // 确保 DOM 更新完成后再执行
		hospital.value.handleDefault(n);
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		hospital.value.confirm();
	}
);
let myEcharts = echarts;
let chart;
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#sale-trend-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let x = tableList.value.map((ele) => ele.territoryCodeCn);
	let xs = tableList.value.map((ele) => ele.sales);
	let zb = tableList.value.map((ele) => ele.target);
	// let zhanbi = tableList.value.map((ele) => ele.achTotal);
	let tb = tableList.value.map((ele) => ele.growth);
	let dc = tableList.value.map((ele) => ele.ach);
	let hb = tableList.value.map((ele) => ele.mom);

	let option = {
		grid: {
			top: translateFont(60),
			left: translateFont(58),
			right: translateFont(40),
			bottom: translateFont(30),
		},
		// { name: '占比', icon: 'rect' },
		legend: {
			data: ['指标', '销售', '同比', '达成', '环比'],
			left: 'center',
			top: translateFont(15),
			itemGap: translateFont(15),
			itemWidth: translateFont(15),
			itemHeight: translateFont(4),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = '';
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${saleTrendFilter(item)}</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			axisPointer: {
				type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
				},
			},
			extraCssText: 'z-index:9',
		},
		xAxis: [
			{
				type: 'category',
				data: x,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: spaces(x.length), // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(8),
					formatter: function (value) {
						// 使用正则表达式每 6 个字符插入一个换行符
						return value.replace(/(.{5})/g, '$1\n');
					},
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumbeW(value);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumbeW(value) + '%';
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
			},
		],
		// '#E7FAFC',
		color: ['#E1EBFF', '#0052CC', '#F08C52', '#62C5D7', 'rgba(255, 209, 13)'],
		series: [
			{
				name: '指标',
				type: 'bar',
				data: zb,
				barWidth: translateFont(12),
				z: 2,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '销售',
				type: 'bar',
				data: xs,
				barWidth: translateFont(6),
				barGap: '-74%',
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			// {
			// 	name: '占比',
			// 	type: 'line',
			// 	yAxisIndex: 1,
			// 	lineStyle: {
			// 		width: translateFont(1),
			// 	},
			// 	symbolSize: translateFont(5),
			// 	data: zhanbi,
			// 	z: 4,
			// 	areaStyle: {
			// 		color: 'rgba(135, 206, 250, 0.5)', // 区域背景颜色
			// 	},
			// },
			{
				name: '同比',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: tb,
				z: 4,
			},
			{
				name: '达成',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: dc,
				z: 4,
			},
			{
				name: '环比',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: hb,
				z: 4,
			},
		],
	};
	chart.setOption(option);
};

let zk = ref(false);
const tableList = ref([]);
const detailList = ref([]);
let comStatus = ref('loading');
// 定义表格字段
const tableFields = ['territoryCodeCn', 'salesY', 'targetY', 'achY', 'growthY', 'salesQ', 'targetQ', 'achQ', 'growthQ', 'salesM', 'targetM', 'achM', 'growthM', 'mom', 'quarterOnQuarterRate'];
let getData = async () => {
	comStatus.value = 'loading';
	let res = await getTeamSalesTrends({
		skuCode: query.brandId ? query.brandId.split(',') : [],
		territoryCode: query.personId ? query.personId.split(',') : [],
		hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		province: query.province || [],
		city: query.city || [],
		pathAddress: props.pathAddress || '',
		salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
	});
	query.salesOrAmount = switchComRef.value.type;
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}
	tableList.value = res.result.data;
	detailList.value = [];
	// 整合数据
	detailList.value = res.result.tableData?.map((item) => {
		const formattedData = {};
		tableFields.forEach((field) => {
			if (field.startsWith('ach') || field.startsWith('growth') || ['mom', 'quarterOnQuarterRate'].includes(field)) {
				// 百分比字段
				formattedData[field] = `${formatPrecentOne(item[field]) + (props.isPPT ? '%' : '')}`;
			} else if (typeof item[field] === 'number') {
				// 数字字段
				formattedData[field] = formatNumbeWT(item[field]);
			} else {
				// 其他字段，直接赋值
				formattedData[field] = item[field] || '-';
			}
		});

		return formattedData;
	});
	comStatus.value = res.result.data.length < 1 ? 'empty' : 'normal';
	// 合计数据
	console.log(res.result.total);
	lastResult.value = {
		territoryCodeCn: '合计',
		salesM: res.result.total.salesM === 0 ? 0 : formatNumbeWT(res.result.total.salesM),
		targetM: res.result.total.targetM === 0 ? 0 : formatNumbeWT(res.result.total.targetM),
		achM: `${formatPrecentOne(res.result.total.achM) + (props.isPPT ? '%' : '')}`,
		growthM: `${formatPrecentOne(res.result.total.growthM) + (props.isPPT ? '%' : '')}`,
		mom: `${formatPrecentOne(res.result.total.mom) + (props.isPPT ? '%' : '')}`,
		quarterOnQuarterRate: `${formatPrecentOne(res.result.total.quarterOnQuarterRate) + (props.isPPT ? '%' : '')}`,
		salesQ: res.result.total.salesQ === 0 ? 0 : formatNumbeWT(res.result.total.salesQ),
		targetQ: res.result.total.targetQ === 0 ? 0 : formatNumbeWT(res.result.total.targetQ),
		achQ: `${formatPrecentOne(res.result.total.achQ) + (props.isPPT ? '%' : '')}`,
		growthQ: `${formatPrecentOne(res.result.total.growthQ) + (props.isPPT ? '%' : '')}`,
		salesY: res.result.total.salesY === 0 ? 0 : formatNumbeWT(res.result.total.salesY),
		targetY: res.result.total.targetY === 0 ? 0 : formatNumbeWT(res.result.total.targetY),
		achY: `${formatPrecentOne(res.result.total.achY) + (props.isPPT ? '%' : '')}`,
		growthY: `${formatPrecentOne(res.result.total.growthY) + (props.isPPT ? '%' : '')}`,
	};
	reportStore.setReportInfo({
		name: '各团队累计销售分析报告',
		info: {
			monthText: `${query.endDate.slice(0, 4)}年${query.endDate.slice(4, 6)}月`,
			name: '各团队累计销售分析',
			people: query.personName,
			hospital: query.hospitalName,
			product: query.brandName,
			data: {
				tableList: tableList.value,
				detailList: detailList.value,
				lastResult: lastResult.value,
				message: res.result.message,
			},
		},
	});
	if (props.summary) {
		emit('summaryIng', { data: { ...res.result.tableData, total: res.result.total }, params: query });
	}
};

// 表格过渡动画
// const handleBeforeEnter = (el) => {
// 	el.style.height = '0px';
// };
// const handleEnter = (el) => {
// 	el.style.height = 'auto';
// 	let h = el.clientHeight;
// 	el.style.height = '0px';
// 	requestAnimationFrame(() => {
// 		el.style.height = h + 'px';
// 		el.style.transition = '.5s';
// 	});
// 	proxy.$umeng('点击', `${page}${props.info.nameCn}`, '点击查看明细');
// };
// const handleLeave = (el) => {
// 	requestAnimationFrame(() => {
// 		el.style.height = '0px';
// 	});
// };
const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', {
		id: props.info.reportCd,
		name: props.info.nameCn,
		isLike: !props.info.isLike,
	});
};
let echartsId = generateRandomNumber();
const switchComRef = ref(null);
const currencySwitching = async (type) => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesAnalysisTeam',
			defaultValue: {
				...toRaw(query),
			},
		});

		return;
	}
	// proxy.$umeng('点击', `${props.umengPath}-销量金额筛选器`, type);
	await getData();
	chart.resize();
	setOption();
};
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			brand, // 传入 ref
			orgZ, // 传入 ref
			time,
			defaultSku, // 传入 ref
			defaultHos, // 传入 ref
			defaultPerson, // 传入 ref
			defaultProvinceCity, // 传入 ref
			switchComRef,
		});
	}
	//初始化级联
	initFilters();
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/salesAnalysisTeam.scss';
.sale-trend {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	&-filter {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		&-echarts {
			height: 220px;
		}
	}

	&-table {
		background-color: #fff;
		margin-top: 8px;
		overflow: hidden;
		&-title {
			font-size: 12px;
			color: var(--pv-default-color);
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 6px;
			position: relative;
			.icon {
				font-size: 7px;
				position: absolute;
				right: 14px;
				transition: all 0.5s;
			}
			.down {
				transform: rotate(180deg);
			}
		}
		&-content {
			padding: 0 8px 0px;
		}
	}
}
</style>
