<template>
	<div class="listing" id="ap-listing">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span>
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<!-- 通用筛选器 -->
		<globalFilter
			:filters="[
				{ name: 'time', size: '49', queryName: 'dateRange', single: true },
				{ name: 'org', size: '49', queryName: 'personName' },
				{ name: 'brand', size: '32', queryName: 'brandName' },
				{ name: 'city', size: '32', queryName: 'countyName' },
				{ name: 'hospital', size: '32', queryName: 'hospitalName' },
			]"
			:query="query"
			:loading="filterLoading"
			@openTime="openTime"
			@openOrg="openOrg"
			@openBrand="openBrand"
			@openCity="openCity"
			@openHospital="openHospital"
		></globalFilter>
		<loading v-show="comStatus === 'loading'"></loading>

		<div v-show="comStatus === 'normal'" class="listing-card">
			<div class="listing-echarts" :id="`listing-echarts-${echartsId}`"></div>
			<!-- 三个提醒框 -->

			<div class="listing-tip" :class="positionCard(index)" v-for="(item, index) in tipInfo" :key="index">
				<div class="listing-tip-text">准入:{{ infoData(item.name, '准入', 'count') }}家/{{ infoData(item.name, '准入', 'ach') }}%</div>
				<div class="listing-tip-text">未准入:{{ infoData(item.name, '未准入', 'count') }}家/{{ infoData(item.name, '未准入', 'ach') }}%</div>
				<div class="listing-tip-text">正式进药:{{ infoData(item.name, '正式进药', 'count') }}家/{{ infoData(item.name, '正式进药', 'ach') }}%</div>
				<div class="listing-tip-text">临时采购:{{ infoData(item.name, '临时采购', 'count') }}家/{{ infoData(item.name, '临时采购', 'ach') }}%</div>
				<div class="listing-tip-text">院内决策药房:{{ infoData(item.name, '院内决策药房', 'count') }}家/{{ infoData(item.name, '院内决策药房', 'ach') }}%</div>

				<!-- <div class="listing-tip-text">目标医院:{{ infoData(item.name, '目标医院', 'count') }}家/{{ infoData(item.name, '目标医院', 'ach') }}%</div> -->
			</div>
		</div>
		<empty-com v-show="comStatus === 'empty'"></empty-com>
		<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>

		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :defaultHosCheckedAll="defaultHosCheckedAll" :path="`${page}${props.info?.nameCn}`" :personalId="query.personId" :province="query.province" :city="query.city" :skuCode="query.brandId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { generateRandomNumber, translateFont, formatNumber, getNameByPath, deepClone, isPCTrue, getFilterTime } from '@/utils/index';
import { salesListing } from '@/api/sales';
import usefilterStore from '@/store/modules/filter';
let filterStore = usefilterStore();
let props = defineProps(['info', 'defaultValue', 'summary', 'inChat']);
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);

// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
});
// 默认选中的人员
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'listing',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();

	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};

// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'listing',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'listing',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city, hospital, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	await getData();
	chart.resize();
	setOption();

	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	await getData();
	chart.resize();
	setOption();

	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 城市

const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'listing',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();

	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
// 医院筛选
const openHospital = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'listing',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	await getData();
	chart.resize();
	setOption();

	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};

let chartLegend = ref([]);
let chartData = ref([]);

let myEcharts = echarts;
let chart;
let colors = ['#0052CC', '#79E1F2', '#6B778C', '#ff8e1d', '#d82e0c'];
let indicator = ref([]);
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#listing-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let data = chartData.value.map((ele, index) => {
		return { name: ele.name, value: ele.data.map((ele) => ele.ach), symbol: ele.name === '目标医院' ? 'none' : 'circle', areaStyle: { color: ele.name === '目标医院' ? 'none' : colors[index], opacity: '0.2' } };
	});
	let option = {
		tooltip: {
			trigger: 'item', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let valueClone = deepClone(value);
				let indicatorClone = deepClone(indicator.value);
				let chartDataClone = deepClone(chartData.value);
				let str = `<div class="product">${valueClone.name}</div>`;
				for (let [index, num] of valueClone.data.value.slice(0, 3).entries()) {
					str += `<div><span class="dot" style="background-color:${colors[valueClone.dataIndex]}"></span><span class="key">${indicatorClone[index].name}</span><span class="value">${chartDataClone.find((ele) => ele.name === valueClone.name).data[index].count}家/${
						chartDataClone.find((ele) => ele.name === valueClone.name).data[index].ach
					}%</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			axisPointer: {
				type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
				},
			},
			extraCssText: 'z-index:9',
		},
		legend: {
			data: chartLegend.value,
			left: 'center',
			top: translateFont(15),
			itemGap: translateFont(5),
			itemWidth: translateFont(8),
			itemHeight: translateFont(3),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		radar: {
			center: [translateFont(164), translateFont(200)], //位置
			axisName: {
				//指示器名字
				color: '#172B4D',
				fontSize: translateFont(12),
			},
			axisNameGap: translateFont(6),
			axisLine: { show: false }, //轴线
			splitNumber: 5,
			radius: ['0%', '68%'], //数组的第一项是内半径，第二项是外半径
			splitArea: {
				show: false,
			},
			indicator: indicator.value,
		},
		color: colors,
		series: [
			{
				name: 'Budget vs spending',
				type: 'radar',
				symbolSize: translateFont(6), //点的大小
				label: {
					color: 'blue',
				},
				data,
			},
		],
	};
	chart.setOption(option);
};
const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let echartsId = generateRandomNumber();
let tipInfo = ref([]);
let comStatus = ref('loading');

let getData = async () => {
	chartData.value = [];
	comStatus.value = 'loading';
	let res = await salesListing({
		skuCode: query.brandId ? query.brandId : '',
		territoryCode: query.personId ? query.personId : '',
		hospCode: query.hospitalId ? query.hospitalId : '',
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		province: Array.isArray(query.province) ? query.province?.join(',') || '' : '',
		city: Array.isArray(query.city) ? query.city?.join(',') || '' : '',
	});
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}
	chartLegend.value = res.result.legend;
	let max = Math.max(...[res.result.AMax, res.result.BMax, res.result.CMax]);
	let indicatorArr = ['核心医院', '重点医院', '其它医院'];
	indicator.value = indicatorArr.map((ele) => {
		return { name: ele, max, min: 0 };
	});
	tipInfo.value = res.result.data.slice(0, 3);
	const arr = res.result.data.slice(0, 3).flatMap((ele) => ele.data);
	for (let i in chartLegend.value) {
		let obj = { name: chartLegend.value[i] };
		obj.data = arr.filter((ele) => ele.admissionStatus === chartLegend.value[i]);
		chartData.value[i] = obj;
	}
	console.log(chartData.value);
	comStatus.value = res.result.data.length < 1 ? 'empty' : 'normal';
	if (props.summary) {
		emit('summaryIng', { data: { ...res.result }, params: query });
	}
};
let infoData = computed(() => {
	return (type, name, val) => {
		return tipInfo.value.find((ele) => ele.name === type)?.data?.find((ele) => ele.admissionStatus === name)?.[val];
	};
});
let positionCard = computed(() => {
	return (index) => {
		return ['a', 'b', 'd', 'c'][index];
	};
});
const defaultSku = ref('');
const defaultHos = ref('');
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			brand, // 传入 ref
			orgZ, // 传入 ref
			time,
			defaultSku, // 传入 ref
			defaultHos, // 传入 ref
			defaultPerson, // 传入 ref
			defaultProvinceCity, // 传入 ref
		});
	}
	//初始化级联
	initFilters();
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/listing.scss';
.listing {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	&-echarts {
		width: 100%;
		height: 340px;
	}
	&-card {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		padding-bottom: 15px;
		position: relative;
	}
	&-tip {
		border-radius: 2.5px;
		background-color: transparent;
		box-shadow: 0px 2px 12px 0px rgba(67, 166, 170, 0.1);
		font-size: 7px;
		padding: 3px 6px;
		position: absolute;
		color: #6b778c;
		&-title {
			font-weight: 700;
		}
		&.a {
			top: 74px;
			left: 25px;
		}
		&.b {
			top: 270px;
			left: 5px;
		}
		&.c {
			top: 80px;
			right: 10px;
		}
		&.d {
			top: 270px;
			right: 20px;
		}
	}
}
</style>
