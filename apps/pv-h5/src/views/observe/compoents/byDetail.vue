<template>
	<div class="by-detail" style="position: relative">
		<div v-if="Object.keys(personalInfo).length > 0" class="personal">
			<span class="ignore-w">{{ personalInfo?.name }}</span>
			<span class="ignore-w">Incentive</span>
			<span>
				<div class="bar"></div>
				<span>{{ personalInfo?.achievementRate }}</span>
			</span>
		</div>
		<vxe-table
			ref="vxetable"
			max-height="400"
			:data="props.ddiResult"
			:scroll-y="{ enabled: false }"
			:sort-config="{ trigger: 'cell' }"
			@scroll="onScroll"
			@cell-click="cellClick"
			:loading-config="{ icon: 'vxe-icon-indicator roll', text: '加载中...' }"
			:loading="detailLoading"
			:row-class-name="activeRow"
		>
			<vxe-column field="date" align="left" title="日期" sortable width="85px"></vxe-column>
			<vxe-column field="username" align="left" title="姓名" width="70px"></vxe-column>
			<vxe-column field="productName" align="left" title="产品" width="100px"></vxe-column>
			<vxe-column field="hospitalName" align="left" title="终端" width="100px"></vxe-column>
			<vxe-column field="salesValue" align="right" title="销售额K" sortable width="100px"></vxe-column>
			<vxe-column field="wsName" align="left" title="经销商" width="140px"></vxe-column>
			<vxe-column field="units" align="right" title="销量" sortable width="60px"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
import { getPeopleSales } from '@/api/sales';
import { translateFont } from '@/utils/index';
const props = defineProps(['ddiResult', 'detailLoading', 'personalCard', 'query']);
const emits = defineEmits(['pullData']);
let onScroll = (e) => {
	console.log(e.scrollHeight - e.scrollTop);
	console.log(e.bodyHeight);
	if (e.isY && e.scrollHeight - e.scrollTop - translateFont(4) <= e.bodyHeight) {
		emits('pullData');
	}
};

const personalInfo = ref({});
const setBarStyle = (num) => {
	let personalDom = document.querySelector('.personal');
	let barDom = document.querySelector('.bar');
	let tableThDom = document.querySelector('.vxe-header--row').clientHeight;
	let tableTrDom = document.querySelectorAll('.vxe-body--row')?.[0]?.clientHeight;
	let vxeTbDom = document.querySelector('.vxe-table--body-wrapper');
	barDom.style.flex = num / 100;
	vxeTbDom.style.marginTop = tableTrDom + 'px';
	personalDom.style.top = tableThDom + 'px';
	personalDom.style.height = tableTrDom + 'px';
	personalDom.style.lineHeight = tableTrDom + 'px';
};
const currentRow = ref(null);
const activeRow = (row) => {
	if (row.rowid === currentRow.value) {
		return 'active-row';
	}
};
const cellClick = async (e) => {
	console.log(e);
	// currentRow.value = e.rowid;
	// await getPersonalInfo(e.row.username);
	// setBarStyle(personalInfo.value.achievementRate.slice(0, -1));
};
const vxetable = ref(null);
watch(
	() => props.personalCard,
	async (val) => {
		if (val?.username) {
			personalInfo.value = {};
			// await getPersonalInfo(val.username);
			requestAnimationFrame(() => {
				// let data = vxetable.value.getTableData();
				// currentRow.value = data.tableData[0]._X_ROW_KEY;
				// setBarStyle(personalInfo.value.achievementRate.slice(0, -1));
			});
		}
	}
);
const getPersonalInfo = async (mrName) => {
	let res = await getPeopleSales({ startLocalDate: `${props.query.startDate.slice(0, 4)}-${props.query.startDate.slice(4, 6)}-01`, endLocalDate: `${props.query.endDate.slice(0, 4)}-${props.query.endDate.slice(4, 6)}-01`, mrName });
	console.log(res);
	res.result.achievementRate = res.result.achievementRate * 100 + '%';
	personalInfo.value = res.result;
};
onMounted(async () => {
	// if (!props.personalCard.username) return;
	// await getPersonalInfo(props.personalCard.username);
	// setTimeout(() => {
	// 	let data = vxetable.value.getTableData();
	// 	currentRow.value = data.tableData[0]._X_ROW_KEY;
	// 	setBarStyle(personalInfo.value.achievementRate.slice(0, -1));
	// }, 500);
});
</script>
<style lang="scss" scoped>
::v-deep(.vxe-table--body-wrapper) {
	.active-row {
		background-color: #f5f7fa;
	}
}
.personal {
	position: absolute;
	left: 0;
	right: 0;
	font-size: 10px;
	font-weight: bold;
	display: flex;
	background-color: #faeee6;
	text-align: center;
	.ignore-w {
		width: 80px;
	}
	& > span:nth-last-of-type(1) {
		flex: 1;
		display: flex;
		align-items: center;
		.bar {
			flex: 1;
			height: 10px;
			background-color: #d86027;
		}
		span {
			width: 40px;
		}
	}
}

.by-detail {
	&:deep(.vxe-header--column.col--left) {
		.vxe-cell {
			justify-content: flex-start !important;
			padding: 8px !important;
		}
	}

	&:deep(.vxe-body--column.col--left) {
		.vxe-cell {
			padding-left: 8px !important;
			padding-right: 8px !important;
		}
	}
}

@media screen and (min-width: 600px) {
	.by-detail {
		&:deep(.vxe-header--column.col--left) {
			.vxe-cell {
				padding: 8px !important;
			}
		}

		&:deep(.vxe-body--column.col--left) {
			.vxe-cell {
				padding-left: 8px !important;
				padding-right: 8px !important;
			}
		}
	}
}
</style>
