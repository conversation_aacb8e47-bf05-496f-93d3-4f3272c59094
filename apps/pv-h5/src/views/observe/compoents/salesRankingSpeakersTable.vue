<template>
	<div class="ignore-sale-table">
		<vxe-table max-height="300" :data="props.tableData" :sort-config="{ trigger: 'cell' }" :scroll-y="{ enabled: false }">
			<vxe-column field="territoryCodeCn" title="排名" width="80px" fixed="left"></vxe-column>
			<vxe-column field="salesM1" title="MR" width="80px"></vxe-column>
			<vxe-column field="salesM" title="所属DSM" width="80px"></vxe-column>
			<vxe-column field="targetM2" title="所属RSM" width="80px"></vxe-column>
			<vxe-column field="growthM3" title="签约讲者数" width="80px"></vxe-column>
			<vxe-column field="growthM4" title="目标讲者数" width="80px"></vxe-column>
			<vxe-column field="growthM" title="签约率" width="80px" sortable :sort-by="sortRule"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
import { sortRule } from '@/utils/index';
const props = defineProps(['tableData']);
</script>
<style lang="scss" scoped>
.ignore-sale-table {
	::v-deep(.vxe-table) {
		.vxe-table--fixed-left-wrapper {
			.fixed-left--wrapper {
				width: 560px !important;
			}
		}
	}
}
</style>
