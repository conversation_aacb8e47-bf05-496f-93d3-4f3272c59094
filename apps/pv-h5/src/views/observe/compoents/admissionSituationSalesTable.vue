<template>
	<div class="ignore-sale-table">
		<vxe-table show-overflow :show-footer="show" max-height="400" :sort-config="{ trigger: 'cell' }" :footer-method="footerMethod" :data="props.detailList" @scroll="onScroll">
			<vxe-column field="date" title="年月" align="left" width="70px" fixed="left"></vxe-column>
			<vxe-column field="totalNumber" title="总数" align="right" width="70px"></vxe-column>
			<vxe-column field="admission" title="准入" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="admissionRate" title="准入(%)" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="formalMedicineEntry" title="正式进药" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="formalMedicineEntryRate" title="正式进药(%)" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="temporaryProcurement" title="临时采购" align="right" sortable width="70px"></vxe-column>
			<!-- <vxe-column field="temporaryProcurementRate" title="临时采购(%)" align="right" sortable width="70px"></vxe-column> -->
			<vxe-column field="inHospitalDecisionPharmacy" title="院内决策药房" align="right" sortable width="100px"></vxe-column>
			<!-- <vxe-column field="inHospitalDecisionPharmacyRate" title="院内决策药房(%)" align="right" sortable width="100px"></vxe-column> -->
			<vxe-column field="notAdmitted" title="未准入" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="notAdmittedRate" title="未准入(%)" align="right" sortable width="70px"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
const props = defineProps(['detailList', 'lastResult']);
const footerData = ref([]);
const show = ref(false);
watch(
	() => props.lastResult,
	(val) => {
		if (val) {
			footerData.value = [];
			let tempData = [
				val.date, // 年月
				val.totalNumber, // 总数(K)
				val.admission, // 准入(K)
				val.admissionRate, // 准入(%)
				val.formalMedicineEntry, // 正式进药(K)
				val.formalMedicineEntryRate, // 正式进药(%)
				val.temporaryProcurement, // 临时采购(K)（假设拼写错误已修正）
				val.temporaryProcurementRate, // 临时采购(%)
				val.inHospitalDecisionPharmacy,
				val.inHospitalDecisionPharmacyRate,
				val.notAdmitted, // 未准入(K)
				val.notAdmittedRate, // 未准入(%)
			];
			footerData.value.push(tempData);
			console.log(footerData.value);

			show.value = true;
		}
	},
	{ immediate: true }
);

const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};

let onScroll = (e) => {
	if (e.isY && e.scrollHeight - e.scrollTop === e.bodyHeight) {
		console.log('触底了');
	}
};
</script>
<style lang="scss" scoped></style>
