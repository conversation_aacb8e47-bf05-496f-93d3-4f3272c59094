<template>
	<div class="ignore-sale-table">
		<vxe-table :show-footer="show" max-height="300" :data="props.tableData" :sort-config="{ trigger: 'cell' }" :footer-method="footerMethod" :scroll-y="{ enabled: false }">
			<vxe-column field="territoryCodeCn" title="姓名" align="left" width="100px" fixed="left"></vxe-column>
			<vxe-column field="salesM" title="销售额(K)" align="right" width="100px" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="targetM" title="指标额(K)" align="right" width="100px" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="achM" title="达成率(%)" align="right" width="80px" sortable :sort-by="sortRule"></vxe-column>
			<vxe-column field="growthM" title="同比(%)" align="right" width="80px" sortable :sort-by="sortRule"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
import { sortRule } from '@/utils/index';
const props = defineProps(['tableData', 'lastResult']);
const show = ref(false);
const footerData = ref([]);
watch(
	() => props.lastResult,
	(val) => {
		if (val) {
			footerData.value = [];
			let tempData = [val.territoryCodeCn, val.salesM, val.targetM, val.achM, val.growthM];
			footerData.value.push(tempData);
			show.value = true;
		}
	},
	{ immediate: true }
);

const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};
</script>
<style lang="scss" scoped>
.ignore-sale-table {
	::v-deep(.vxe-table) {
		.vxe-table--fixed-left-wrapper {
			.fixed-left--wrapper {
				width: 460px !important;
			}
		}

		.vxe-cell--sort {
			display: inline-block !important;
		}
	}
}
</style>
