<template>
	<div class="branch-sales-overview" id="ap-branch-sales-overview">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span>
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
			<!-- 通用筛选器 -->
			<globalFilter
				:filters="[
					{ name: 'time', size: '49', queryName: 'dateRange', single: true },
					{ name: 'org', size: '49', queryName: 'personName' },
					{ name: 'brand', size: '32', queryName: 'brandName' },
					{ name: 'city', size: '32', queryName: 'countyName' },
					{ name: 'hospital', size: '32', queryName: 'hospitalName' },
				]"
				:query="query"
				:loading="filterLoading"
				@openTime="openTime"
				@openOrg="openOrg"
				@openBrand="openBrand"
				@openCity="openCity"
				@openHospital="openHospital"
			></globalFilter>
			<div v-if="isRecommend" class="branch-sales-overview-tip" v-html="chartData.message"></div>
		</div>

		<div class="branch-sales-overview-card">
			<filterCom :filterList="filterList" :currentIndex="currentIndex" @filterChange="filterChange" :downloadExcel="false" @_downExcel="downExcel"></filterCom>
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="branch-sales-overview-card-content">
				<div class="branch-sales-overview-card-echarts" :id="`branch-sales-overview-card-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
			<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :defaultHosCheckedAll="defaultHosCheckedAll" :path="`${page}${props.info?.nameCn}`" :personalId="query.personId" :province="query.province" :city="query.city" :skuCode="query.brandId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import { showToast, showLoadingToast } from 'vant';
import * as echarts from 'echarts';
import { getTeamAdmissionAnalysis, salesHeatmapDownload } from '@/api/sales';
import { generateRandomNumber, translateFont, getFilterTime, spaces, getNameByPath, isPCTrue } from '@/utils/index';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let filterStore = usefilterStore();
let props = defineProps(['info', 'isRecommend', 'pathAddress', 'defaultValue', 'summary', 'inChat']);
let myEcharts = echarts;
// 下载明细
const downExcel = async (emial) => {
	try {
		showLoadingToast({
			message: '下载中...',
			forbidClick: true,
			duration: 0,
			class: 'ppt-pc-loading',
		});
		await salesHeatmapDownload({
			skuCode: query.brandId ? query.brandId.split(',') : [],
			territoryCode: query.personId ? query.personId.split(',') : [],
			hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
			type: typeFilter.value,
			startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
			endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
			email: emial,
		});
		showToast({
			message: '数据下载成功，稍后会发送到您的邮箱。',
			position: 'top',
		});
	} catch (error) {
		console.log(error);
	}
};

// 默认选中人员
const defaultPerson = ref('');
// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime) || '20250101',
	endDate: getFilterTime(props.info?.endTime) || '20250301',
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
	indicatorType: 'sales',
});
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};

// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSituationSubordinateTeam',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.startDate;
		query.endDate = n.endDate;
		// showPicker.value = false;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.info?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);
watch(
	() => reportStore.globalFilterInfo.area,
	async (n) => {
		console.log(n);
		city.value.golbalInit(n.provinceData);
		query.countyName = n.countyName;
		query.city = n.city;
		query.province = n.province;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
	}
);
// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSituationSubordinateTeam',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

const defaultSku = ref('');
watch(
	() => reportStore.globalFilterInfo.brand,
	async (n) => {
		brand.value.handleReset();
		brandData.value = filterStore.skuInfoTree;
		defaultSku.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		brand.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		brand.value.confirm();
	}
);
// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSituationSubordinateTeam',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city, hospital, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 城市

const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSituationSubordinateTeam',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
watch(
	() => reportStore.globalFilterInfo.people,
	async (n) => {
		orgZ.value.handleReset();
		defaultPerson.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		orgZ.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		orgZ.value.confirm();
	}
);
// 医院筛选
const openHospital = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionSituationSubordinateTeam',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};
const defaultHos = ref('');
const defaultHosCheckedAll = ref('');

watch(
	() => reportStore.globalFilterInfo.hospital,
	async (n) => {
		console.log(n);

		hospital.value.handleReset();
		defaultHos.value = n.checked; //默认选中
		defaultHosCheckedAll.value = n.isCheckAll;
		await nextTick(); // 确保 DOM 更新完成后再执行
		hospital.value.handleDefault(n);
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		hospital.value.confirm();
	}
);
let chart;
let initChart = () => {
	const userAgent = navigator.userAgent;
	const isIOS = /iPad|iPhone|iPod/.test(userAgent);
	const isAndroid = /Android/.test(userAgent);
	const renderer = isIOS ? 'svg' : isAndroid ? 'canvas' : 'auto';
	chart = myEcharts.init(document.querySelector(`#branch-sales-overview-card-echarts-${echartsId}`), null, {
		renderer,
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};

let setOption = () => {
	let hours = chartData.value.axisMonth;
	const days = chartData.value.nameArray;
	const data = chartData.value.dateArray;
	let option = {
		tooltip: {
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let v = value.value[2] + '%';
				let str = `<div class="product">${days[value.value[1]]}</div>`;
				return `<div class="tooltip-saletrend">${str}<div class="tooltip-heatmap"><span class="dot" style="background-color:${value.color}"></span><span class="date">${value.name}</span><span class="value">${v}</span></div></div>`;
			},
			extraCssText: 'z-index:9',
		},
		grid: {
			top: translateFont(18),
			bottom: translateFont(45),
			left: translateFont(72),
			right: translateFont(10),
		},
		xAxis: {
			type: 'category',
			data: hours,
			axisLabel: {
				fontSize: translateFont(8),
				color: '#6B778C',
				// interval: spaces(chartData.value.axisMonth.length),
			},
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			splitArea: {
				show: true,
				interval: 0,
			},
			position: 'top',
		},
		yAxis: {
			type: 'category',
			data: days,
			axisLabel: {
				//是否显示
				inside: false, //坐标轴刻度文字的风格        (可选楷体  宋体  华文行楷等等)
				formatter: (value) => {
					return value.length > 7 ? `{a|${value.slice(0, 7)}...}` : `{a|${value}}`;
				},
				interval: 0,
				rich: {
					a: {
						color: '#6B778C',
						fontSize: translateFont(8),
						align: 'left',
						width: translateFont(100),
						backgroundColor: 'none',
						padding: [0, translateFont(-43), 0, 0],
					},
				},
			},
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			splitArea: {
				show: true,
				interval: 0,
			},
			name: '',
			nameTextStyle: {
				color: '#172B4D',
				fontSize: translateFont(9),
				fontWeight: 'bold',
				padding: [0, translateFont(120), translateFont(-8), translateFont(50)],
			},
		},
		visualMap: {
			show: true,
			min: 0,
			max: chartData.value.max,
			calculable: true,
			orient: 'horizontal',
			left: 'center',
			bottom: translateFont(10),
			color: ['#01A2BF', '#ffffff'],
			align: 'right',
			itemWidth: translateFont(10),
			itemHeight: translateFont(85),
			formatter: (value) => {
				if (value === 0) return `{b|0}`; //命中两端手柄
				if (value.toString().includes('.')) {
					return `{c|${new Decimal(value).toDecimalPlaces(0)}}`; //命中指示器
				}
				if (value === chartData.value.max) {
					return `{a|${value}}`; //命中两端手柄
				}
				return value;
			},
			textStyle: {
				rich: {
					a: {
						color: '#172B4D',
						fontSize: translateFont(8),
						padding: [translateFont(-23), translateFont(-40), 0, 0],
					},
					b: {
						color: '#172B4D',
						fontSize: translateFont(8),
						padding: [translateFont(-23), translateFont(40), 0, 0],
					},
				},
			},
		},
		series: [
			{
				// name: 'Punch Card',
				type: 'heatmap',
				data: data,
				progressive: 200, // 每次渲染的数据点数量
				progressiveThreshold: 200, // 触发渐进渲染的最小数据量
				label: {
					show: true,
					fontSize: translateFont(7),
					color: '#172B4D',
					formatter: ({ value }) => {
						return value[2] + '%';
					},
				},
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowColor: 'rgba(0, 0, 0, 0.5)',
					},
				},
				heatmapSize: [10, 30],
			},
		],
	};
	chart.setOption(option);
};
//获取数据
let comStatus = ref('loading');
let chartData = ref({});
const getData = async () => {
	try {
		comStatus.value = 'loading';
		let res = await getTeamAdmissionAnalysis({
			skuCode: query.brandId ? query.brandId.split(',') : [],
			territoryCode: query.personId ? query.personId.split(',') : [],
			hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
			type: typeFilter.value,
			startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
			endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
			province: query.province || [],
			city: query.city || [],
			pathAddress: props.pathAddress || '',
			groupByName: 'subordinateTeam',
		});
		if (res.result.isExceededAuthority) {
			comStatus.value = 'isExceededAuthority';
			emit('summaryIng', { noSummary: true });
			return;
		}
		chartData.value = res.result;
		let itemsLength = res.result.nameArray.length;
		let itensDateLength = res.result.axisMonth?.length;
		comStatus.value = res.result.nameArray.length < 1 ? 'empty' : 'normal';
		let clientWidth = document.body.clientWidth || document.documentElement.clientWidth;
		if (clientWidth > 600) {
			document.querySelector(`#branch-sales-overview-card-echarts-${echartsId}`).style.height = 7 * 3.75 * itemsLength + 17 * 3.75 + 'px';
			document.querySelector(`#branch-sales-overview-card-echarts-${echartsId}`).style.width = 10 * 3.75 * itensDateLength + 26 * 3.75 + 'px';
		} else {
			document.querySelector(`#branch-sales-overview-card-echarts-${echartsId}`).style.height = 7 * itemsLength + 17 + 'vw';
			document.querySelector(`#branch-sales-overview-card-echarts-${echartsId}`).style.width = 10 * itensDateLength + 26 + 'vw';
		}
		if (props.summary) {
			await nextTick();
			// 创建一个临时的DOM元素
			const tempElement = document.createElement('div');
			// 将HTML字符串设置为临时元素的innerHTML
			tempElement.innerHTML = res.result.message;
			emit('summaryIng', { data: tempElement.innerText, params: query });
		}
		return Promise.resolve();
	} catch (error) {
		return Promise.reject(error);
	}
};

const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', {
		id: props.info.reportCd,
		name: props.info.nameCn,
		isLike: !props.info.isLike,
	});
};
let echartsId = generateRandomNumber();
let typeFilter = ref('formalMedicineEntryRate');
// 定义映射关系
const filterMap = {
	'准入率(%)': 'admissionRate',
	'正式准入率(%)': 'formalMedicineEntryRate',
};
let filterList = ref(['准入率(%)', '正式准入率(%)']);
let currentIndex = ref('准入率(%)');
let filterChange = async (item) => {
	currentIndex.value = item;
	typeFilter.value = filterMap[item] || 'admissionRate'; // 如果没有匹配，默认 'sales'
	query.indicatorType = typeFilter.value;
	chartData.value = {};
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-展示方式`, item);
};
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			brand, // 传入 ref
			orgZ, // 传入 ref
			time,
			defaultSku, // 传入 ref
			defaultHos, // 传入 ref
			defaultPerson, // 传入 ref
			defaultProvinceCity, // 传入 ref
			currentIndex,
			typeFilter,
		});
	}
	//初始化级联
	initFilters();
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({
	query,
});
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/branchSalesOverview2.scss';
.branch-sales-overview {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	&-tip {
		padding: 5px 5px 0;
		font-size: 12px;
		color: #7c88a6;
	}
	&-card {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		padding-right: 3px;
		&-content {
			height: 174px;
			overflow-y: auto;
			overflow-x: auto;
			padding-top: 8px;
			/* 设置纵向滚动条样式 */
			&::-webkit-scrollbar {
				width: 4px; /* 设置纵向滚动条宽度 */
				height: 4px;
			}

			&::-webkit-scrollbar-thumb {
				background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
				border-radius: 3px;
			}

			&::-webkit-scrollbar-track {
				background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
				border-radius: 5px;
				// margin: 50px 0px;
			}
		}
		&-echarts {
			min-width: 100%;
		}
	}
}
</style>
