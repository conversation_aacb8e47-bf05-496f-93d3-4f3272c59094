<template>
	<div class="product-competition-analysis">
		<div class="top">
			<div class="card-title">
				<span>
					<span style="color: var(--pv-filter-color)">北京</span>
					重点产品竞争情况分析
				</span>
				<span>
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<div class="q-box">
			<!-- 地区 -->
			<div @click="openPro" style="justify-content: space-between" class="q-width-100 q-fileter-item q-box-padding">
				<span style="width: auto" class="item-title ell">{{ query.province }}</span>
				<!-- icon -->
				<svg-icon class="icon" icon-class="down2"></svg-icon>
			</div>
		</div>
		<div class="product-competition-analysis-content">
			<div class="legend">
				<div class="legend-item" v-for="(item, index) in lengendList" :key="index">
					<div class="legend-item-dot" :style="{ backgroundColor: item.color }"></div>
					<div class="legend-item-text">{{ item.name }}</div>
				</div>
			</div>
			<div class="product-competition-analysis-echarts" :id="`product-competition-analysis-echarts-${echartsId}`"></div>
			<div class="revenue">
				<van-divider :style="{ color: 'var(--pv-default-color)', marginTop: '8px', marginBottom: '8px' }" dashed>各产品销售额</van-divider>
				<div class="revenue-list">
					<div v-for="(item, index) in pointList" :key="index" :class="{ 'revenue-list-item': true, 'revenue-active': item.isChecked }" @click="revenueEv(item)">
						<div class="revenue-list-item-info">
							<div>{{ item.skuCn }}</div>
							<div>{{ item.ei }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- <div class="tip">
			<div>X轴：各定义市场规模的同比增长率（vs去年同期）</div>
			<div>Y轴：各品牌的EI(VS去年同期)</div>
			<div>气泡大小：YTD市场份额 DC 渠道销售金额</div>
		</div> -->
		<div v-if="showTags" class="tags">
			<div class="tags-item"># BU/RSM Report</div>
		</div>
		<!-- 省份列表组件 -->
		<OneLevelRadio ref="pro" @_confirm="proConfirm" :path="`${page}${props.info?.nameCn}-省份`" :defaultCheck="query.province" :list="proviceList"></OneLevelRadio>
	</div>
</template>
<script setup>
import { marketShareAnalysis } from '@/api/sales';
import { filterProvince } from '@/api/filter';
import * as echarts from 'echarts';
import Decimal from 'decimal.js';
import { generateRandomNumber, isPCTrue } from '@/utils/index';

const props = defineProps(['showTags', 'info']);
let myEcharts = echarts;
let chart;
const pointList = ref([]);
let colors = ref(['#DD340A', '#01A2BF', '#0052CC', '#FF991F', '#5342AA']);

const query = reactive({
	province: '',
});

const lengendList = ref([
	{ color: '#DD340A', name: 'YTD市场份额 0%～5%' },
	{ color: '#01A2BF', name: 'YTD市场份额 5%～10%' },
	{ color: '#0052CC', name: 'YTD市场份额 10%～20%' },
	{ color: '#FF991F', name: 'YTD市场份额 20%～30%' },
	{ color: '#5342AA', name: 'YTD市场份额 ≥ 30%' },
]);

// 产品销售额权限
const revenueEv = (item) => {
	const bool = item.isChecked;
	for (const i of pointList.value) {
		i.isChecked = false;
	}
	if (!bool) item.isChecked = true;
};

let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#product-competition-analysis-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let eiList = pointList.value.map((e) => e.ei);
	let pList = pointList.value.map((e) => e.percentage);
	let option = {
		color: colors.value,
		legend: {
			show: false,
		},
		grid: {
			show: true,
			top: '10',
			left: '10',
			right: '10',
			bottom: '10',
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: pList,
			axisLine: {
				lineStyle: {
					color: '#E6E5E6',
				},
			},
			axisLabel: {
				show: true, // 是否显示坐标轴刻度标签。
				interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
				inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
				color: '#fff', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
				fontSize: 10,
				formatter: function (params) {
					return `${params}%`;
				},
			},
			//网格样式
			splitLine: {
				show: true,
				lineStyle: {
					color: ['#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#fff'],
					width: 1,
				},
			},
			axisTick: {
				show: false,
			},
			nameTextStyle: {
				color: '#fff',
			},
		},
		yAxis: {
			type: 'category',
			boundaryGap: false,
			name: 'EI',
			nameTextStyle: {
				color: '#fff',
				padding: [0, 40, -5, 0],
			},
			//网格样式
			splitLine: {
				show: true,
				lineStyle: {
					color: ['#7F7E8E', '#7F7E8E', '#fff', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E', '#7F7E8E'],
					width: 1,
				},
			},
			axisTick: {
				show: false,
			},
			axisLine: {
				lineStyle: {
					color: '#E6E5E6',
				},
			},
			axisLabel: {
				show: true, // 是否显示坐标轴刻度标签。
				interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
				inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
				color: '#fff', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
				fontSize: 10,
			},
			data: eiList,

			scale: true,
		},
		series: [
			{
				name: 'YTD市场份额%:0~5%',
				//  先X,后y
				data: [
					[0, 0, 122],
					[1, 1, 123],
					[2, 2, 223],
					[3, 3, 123],
					[4, 2, 123],
					[5, 4, 323],
					[6, 5, 123],
					[7, 5, 123],
					[8, 1, 123],
					[9, 3, 123],
					[10, 5, 223],
					[11, 2, 123],
				],
				type: 'scatter',
				itemStyle: {
					opacity: 1,
				},
				symbolSize: function (data) {
					return Math.sqrt(data[2]);
				},
			},
			{
				name: 'YTD市场份额%:5~10%',
				data: [
					[1, 3, 223],
					[2, 4, 223],
					[3, 2, 123],
					[4, 5, 123],
					[5, 3, 123],
					[6, 1, 123],
					[7, 2, 123],
					[8, 3, 323],
					[9, 5, 123],
					[10, 2, 223],
					[11, 5, 123],
				],
				type: 'scatter',
				itemStyle: {
					opacity: 1,
				},
				// symbolSize: function (data) {
				// 	return Math.sqrt(data[2]) * 3;
				// },
			},
			{
				name: 'YTD市场份额%:10~20%',
				data: [
					[1, 3, 223],
					[2, 4, 223],
					[3, 2, 123],
					[4, 5, 123],
					[5, 3, 123],
					[6, 1, 123],
					[7, 2, 123],
					[8, 3, 323],
					[9, 5, 123],
					[10, 2, 223],
					[11, 5, 123],
				],
				type: 'scatter',
				itemStyle: {
					opacity: 1,
				},
				// symbolSize: function (data) {
				// 	return Math.sqrt(data[2]) * 3;
				// },
			},
			{
				name: 'YTD市场份额%:20~30%',
				data: [
					[1, 3, 223],
					[2, 4, 223],
					[3, 2, 123],
					[4, 5, 123],
					[5, 3, 123],
					[6, 1, 123],
					[7, 2, 123],
					[8, 3, 323],
					[9, 5, 123],
					[10, 2, 223],
					[11, 5, 123],
				],
				type: 'scatter',
				itemStyle: {
					opacity: 1,
				},
				// symbolSize: function (data) {
				// 	return Math.sqrt(data[2]) * 3;
				// },
			},
			{
				name: 'YTD市场份额% >=30%',
				data: [
					[0, 2, 22],
					[1, 3, 223],
					[2, 4, 223],
					[3, 2, 123],
					[4, 5, 123],
					[5, 3, 1203],
					[6, 1, 123],
					[7, 2, 123],
					[8, 3, 3203],
					[9, 5, 123],
					[10, 2, 223],
					[11, 5, 1023],
				],
				type: 'scatter',
				itemStyle: {
					opacity: 1,
				},
				symbolSize: function (data) {
					return Math.sqrt(data[2]);
				},
			},
		], //end series
	};
	chart.setOption(option);
};
// 获取省份列表
const proviceList = ref([]);
const getProvinceList = async () => {
	const res = await filterProvince({});
	for (const ite of res.result) {
		proviceList.value.push({
			id: ite,
			name: ite,
		});
	}
	query.province = proviceList.value[0].id;
};
const pro = ref(null);
const openPro = () => {
	pro.value.show = true;
};
const proConfirm = async ({ id }) => {
	query.province = id;
	await getData();
	initChart();
};

let getData = async () => {
	let res = await marketShareAnalysis({ province: query.province });
	for (const item of res.result.marketShare) {
		item.isChecked = false;
	}
	pointList.value = res.result.marketShare;
};
const emit = defineEmits(['focus']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let echartsId = generateRandomNumber();
onMounted(async () => {
	await getProvinceList();
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.product-competition-analysis {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}

	.q-box-padding {
		padding-left: 12px;
		padding-right: 12px;
	}
	.product-competition-analysis-content {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		padding-left: 10px;
		padding-right: 10px;
		display: flex;
		flex-direction: column;
	}
	&-echarts {
		width: 100%;
		height: 235px;
		margin-bottom: 8px;
	}
	.legend {
		padding: 8px 0px 8px;
		display: flex;
		flex-wrap: wrap;

		&-item {
			display: flex;
			align-items: center;
			margin: 0 10px 0 0;
			&-dot {
				width: 8px;
				height: 8px;
				margin-right: 5px;
				border-radius: 50%;
			}
			&-text {
				font-size: 10px;
				color: var(--pv-no-active-color);
			}
		}
	}

	.revenue-list {
		padding-bottom: 8px;
		display: flex;
		flex-wrap: wrap;
		padding-left: 4px;
		padding-right: 4px;
		.revenue-list-item {
			width: 95px;
			height: 56px;
			border-radius: 5px;
			background-color: var(--pv-fliter-product-sales-bgc);
			border: 1px dashed var(--pv-filter-bgc);
			padding-left: 6.5px;
			padding-top: 8.5px;
			margin-bottom: 8px;
			margin-right: 8px;
			.revenue-list-item-info div:nth-child(1) {
				font-size: 12px;
			}
			.revenue-list-item-info div:nth-child(2) {
				font-size: 14px;
				color: var(--pv-filter-color);
				font-weight: 500;
			}
		}

		.revenue-active {
			background-color: var(--pv-tabbar-active);
			border-color: var(--pv-tabbar-active);

			& div:nth-child(1) {
				color: var(--pv-bgc);
			}
			& div:nth-child(2) {
				color: var(--pv-bgc) !important;
			}
		}

		& > .revenue-list-item:nth-child(3n) {
			margin-right: 0;
		}
	}
	.tags {
		font-size: 9px;
		padding: 0 10px 8px;
		display: flex;
		&-item {
			background: #f2f5f8;
			border-radius: 0.533vw;
			color: #a9b2c2;
			margin-bottom: 1.333vw;
			margin-right: 1.333vw;
			text-align: center;
			padding: 3px;
		}
	}
}
</style>
