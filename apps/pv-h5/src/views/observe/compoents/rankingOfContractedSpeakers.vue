<template>
	<div class="sales-ranking-distribution" id="ap-sales-ranking-speakers" :style="{ margin: hiddenSomething ? '0 0' : '' }">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span class="card-title-sub">签约讲者数/目标讲者数</span>
				<span v-if="!hiddenSomething">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<div class="sales-ranking-distribution-card">
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="sales-ranking-distribution-card-content">
				<div class="sales-ranking-distribution-card-content-title">
					<span>
						<span>排名</span>
						<span>人名</span>
					</span>
					<span>签约率</span>
				</div>
				<div class="sales-ranking-distribution-list">
					<div class="sales-ranking-distribution-list-item" :class="{ isUserPart: item.isUserPart }" v-for="(item, index) in rankingData.chartData" :key="index">
						<span>
							<span v-if="item.ranking === 1"><img src="@/assets/img/num-one.png" alt="" /></span>
							<span v-else-if="item.ranking === 2"><img src="@/assets/img/num-two.png" alt="" /></span>
							<span v-else-if="item.ranking === 3"> <img src="@/assets/img/num-three.png" alt="" /></span>
							<span v-else>{{ item.ranking }}</span>
						</span>
						<span>{{ item.userInfo && item.userInfo.e }}</span>
						<span class="bar">
							<span :style="{ width: widthComputed(item.growth) }"></span>
						</span>
						<span>{{ new Decimal(item.growth).toDecimalPlaces(0) + '%' }}</span>
					</div>
				</div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
		</div>
		<div class="sale-table">
			<div class="sale-table-title" @click="zk = !zk">
				<span>点击查看明细</span>
				<svg-icon class="icon" :class="{ down: zk }" icon-class="down2"></svg-icon>
			</div>
			<div v-show="zk" class="sale-table-content">
				<salesRankingSpeakersTable :tableData="tableData"></salesRankingSpeakersTable>
			</div>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
	</div>
</template>
<script setup>
import Decimal from 'decimal.js';
import { salesRankingDistribution } from '@/api/sales';
import { getFilterTime, formatNumbeW, getNameByPath } from '@/utils/index';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/user';
import salesRankingSpeakersTable from './salesRankingSpeakersTable';
let props = defineProps(['info', 'isRecommend', 'hiddenSomething']);
let filterStore = usefilterStore();
// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
});
let zk = ref(false);
const tableData = ref([]);

// 新增逻辑
//从store user中获取territory_code
const user = useUserStore();
//如果filterStore.skuInfoTree第一层级有两个元素，则将query.personal设为第一个，否则将query.personId设为territory_code
if (filterStore.treeInfo.length === 2) {
	query.personId = filterStore.treeInfo[0].h;
	query.personName = filterStore.treeInfo[0].e;
} else {
	query.personId = user.territory_code;
	query.personName = user.userInfo.firstName;
}

const emit = defineEmits(['focus', 'hiddenSomething']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let rankingData = ref({
	chartData: [],
});

// 获取数据
let isShow = ref(true);
let comStatus = ref('loading');
let getData = async () => {
	isShow.value = true;
	try {
		comStatus.value = 'loading';
		let res = await salesRankingDistribution({
			type: 'growth',
			skuCode: query.brandId ? query.brandId.split(',') : [],
			territoryCode: query.personId ? query.personId.split(',') : [],
			hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
			startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
			endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		});
		isShow.value = false;
		rankingData.value = res.result;
		if (res.result.userPart.length > 0) {
			res.result.userPart = res.result.userPart.map((ele) => {
				return { ...ele, isUserPart: true };
			});
			rankingData.value.chartData = [...res.result.userPart, ...res.result.firstPart, ...res.result.remainingPart];
		} else {
			if (res.result.firstPart && res.result.remainingPart) {
				rankingData.value.chartData = [...res.result.firstPart, ...res.result.remainingPart];
			}
		}
		comStatus.value = rankingData.value.chartData.length < 1 ? 'empty' : 'normal';

		// 表格数据
		const tData = [...res.result.firstPart, ...res.result.remainingPart];
		const larr = [];
		tData.forEach((item) => {
			larr.push({
				territoryCodeCn: item.userInfo.e,
				targetM: item.target === 0 ? 0 : formatNumbeW(item.target),
				salesM: item.sales === 0 ? 0 : formatNumbeW(item.sales),
				achM: `${new Decimal(item.ach).toDecimalPlaces(0) + '%'}`,
				growthM: `${new Decimal(item.growth).toDecimalPlaces(0) + '%'}`,
			});
		});
		tableData.value = larr;
		return Promise.resolve();
	} catch (error) {
		return Promise.reject(error);
	}
};

let widthComputed = computed(() => {
	return function (value) {
		if (value <= 0) return '1%';
		return (value / rankingData.value.growthMax) * 100 + '%';
	};
});
onMounted(async () => {
	await getData();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/salesRankingSpeakers.scss';
.sales-ranking-distribution {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
		.card-title {
			align-items: center;
			.card-title-sub {
				margin-right: 12px;
				margin-left: 6px;
				flex: 1;
				font-size: 10px;
				font-weight: 400;
			}
		}
	}

	&-tip {
		padding: 5px 5px 0;
		font-size: 12px;
		color: #7c88a6;
	}
	&-card {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		overflow: hidden;
		&-content {
			height: 250px;
			overflow-y: auto;
			overflow-x: hidden;
			/* 设置纵向滚动条样式 */
			&::-webkit-scrollbar {
				width: 4px; /* 设置纵向滚动条宽度 */
			}

			&::-webkit-scrollbar-thumb {
				background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
				border-radius: 3px;
			}

			&::-webkit-scrollbar-track {
				background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
				border-radius: 5px;
			}
			&-title {
				font-size: 9px;
				padding: 8px 5px 0px 10px;
				position: sticky;
				top: -1px;
				background-color: #fff;
				z-index: 10;
				display: flex;
				justify-content: space-between;
				color: var(--pv-default-color);
				span:nth-child(1) {
					span:nth-child(1) {
						margin-right: 13px;
					}
				}
			}
		}
	}
	&-list {
		font-size: 9px;
		padding: 8px 10px 0px 10px;
		position: relative;
		&-item {
			display: flex;
			align-items: center;
			margin-bottom: 8px;
			position: relative;
			z-index: 2;
			span:nth-child(1),
			span:nth-child(2) {
				width: 30px;
				text-align: center;
				margin-right: 4px;
				flex-shrink: 0;
				color: rgba(0, 0, 0, 0.45);
				word-break: break-word;
			}
			span:nth-child(1) {
				width: 20px;
				img {
					width: 20px;
					height: 13px;
				}
			}
			.bar {
				width: 225px !important;
				flex-shrink: 0;
				margin-right: 4px;
				height: 6px;
				span {
					width: 0;
					background-color: #79e1f2;
					display: block;
					height: 6px;
				}
			}
			span:nth-last-child(1) {
				flex-shrink: 0;
			}
		}
		.isUserPart {
			background-color: #f4f5f7;
			box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
			padding: 7px 0;
			position: relative;
			span {
				color: var(--pv-default-color) !important;
			}
			&::before {
				content: '';
				display: block;
				position: absolute;
				width: 2px;
				height: 100%;
				background-color: var(--pv-gl);
				border-radius: 8px 0 0 8px;
				left: -2px;
			}
		}
		&-average {
			position: absolute;
			top: 0px;
			color: rgba(0, 0, 0, 0.45);
			height: 100%;
			z-index: 2;
			span {
				color: #ff991f;
			}
			&::after {
				content: '';
				position: absolute;
				z-index: 2;
				top: 14px;
				left: 50%;
				transform: translateX(-50%);
				width: 1px;
				height: calc(100% - 20px);
				border-left: 1px dashed #dfe1e6;
			}
		}
	}
	.q-box-mt {
		margin-top: 10px;
	}
}

.sale-table {
	background-color: #fff;
	margin-top: 8px;
	overflow: hidden;
	&-title {
		font-size: 12px;
		color: var(--pv-default-color);
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 6px;
		position: relative;
		.icon {
			font-size: 7px;
			position: absolute;
			right: 14px;
			transition: all 0.5s;
		}
		.down {
			transform: rotate(180deg);
		}
	}
	&-content {
		padding: 0 8px 0px;
	}
}
</style>
