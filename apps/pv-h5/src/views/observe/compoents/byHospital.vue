<template>
	<div>
		<vxe-table
			:scroll-y="{ enabled: false }"
			:show-footer="show"
			max-height="400"
			:data="props.ddiResult"
			:footer-method="footerMethod"
			:sort-config="{ trigger: 'cell' }"
			@sort-change="sortChangeEvent"
			@scroll="onScroll"
			:loading-config="{ icon: 'vxe-icon-indicator roll', text: '加载中...' }"
			:loading="hospitalLoading"
		>
			<vxe-column field="name" title="终端" align="left" width="120px"></vxe-column>
			<vxe-column field="salesV" :title="salesOrAmount === '金额' ? '销售额K' : '销售量K'" align="right" sortable width="80px"></vxe-column>
			<vxe-column field="targetV" :title="salesOrAmount === '金额' ? '指标额K' : '指标量K'" align="right" sortable width="80px"></vxe-column>
			<vxe-column field="achievementRate" title="达成%" align="right" sortable width="80px"></vxe-column>
			<vxe-column field="yearOnYear" title="同比%" align="right" sortable width="80px"></vxe-column>
			<vxe-column field="yearOnYearGrowth" :title="salesOrAmount === '金额' ? '同比净增长额K' : '同比净增长量K'" align="right" sortable width="100px"></vxe-column>
			<vxe-column field="chain" title="环比%" align="right" sortable width="80px"></vxe-column>
			<vxe-column field="moMGrowth" :title="salesOrAmount === '金额' ? '环比净增长额K' : '环比净增长量K'" align="right" sortable width="100px"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
import { translateFont } from '@/utils/index';

const props = defineProps(['ddiResult', 'lastResult', 'hospitalLoading', 'salesOrAmount']);
const emits = defineEmits(['pullHData']);
const footerData = ref([]);
const show = ref(false);
watch(
	() => props.lastResult,
	(val) => {
		if (val) {
			footerData.value = [];
			let tempData = [val.name, val.salesV, val.targetV, val.achievementRate, val.yearOnYear, val.yearOnYearGrowth, val.chain, val.moMGrowth];
			footerData.value.push(tempData);
			show.value = true;
		}
	},
	{ immediate: true }
);

const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};
const sortChangeEvent = ({ field, order }) => {
	console.log(field, order);
};

let onScroll = (e) => {
	if (e.isY && e.scrollHeight - e.scrollTop - translateFont(4) <= e.bodyHeight) {
		emits('pullHData');
	}
};
</script>
<style lang="scss" scoped></style>
