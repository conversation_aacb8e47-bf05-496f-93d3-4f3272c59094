<template>
	<div>
		<vxe-table
			:scroll-y="{ enabled: false }"
			:show-footer="show"
			max-height="400"
			:data="props.ddiResult"
			:footer-method="footerMethod"
			:sort-config="{ trigger: 'cell' }"
			@sort-change="sortChangeEvent"
			@scroll="onScroll"
			:loading-config="{ icon: 'vxe-icon-indicator roll', text: '加载中...' }"
			:loading="hospitalLoading"
		>
			<vxe-column field="name" title="终端" align="left" width="120px"></vxe-column>
			<vxe-column field="salesV" title="销售额K" align="right" sortable width="80px"></vxe-column>
			<vxe-column field="targetV" title="指标额K" align="right" sortable width="80px"></vxe-column>
			<vxe-column field="achievementRate" title="达成%" align="right" sortable width="80px"></vxe-column>
			<vxe-column field="yearOnYear" title="同比%" align="right" sortable width="80px"></vxe-column>
			<vxe-column field="yearOnYearGrowth" title="同比净增长额K" align="right" sortable width="100px"></vxe-column>
			<vxe-column field="chain" title="环比%" align="right" sortable width="80px"></vxe-column>
			<vxe-column field="moMGrowth" title="环比净增长额K" align="right" sortable width="100px"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
import { translateFont } from '@/utils/index';

const props = defineProps(['ddiResult', 'lastResult', 'hospitalLoading']);
const emits = defineEmits(['pullHData']);
const footerData = ref([]);
const show = ref(false);
watch(
	() => props.lastResult,
	(val) => {
		if (val) {
			footerData.value = [];
			let tempData = [val.name, val.salesV, val.targetV, val.achievementRate, val.yearOnYear, val.yearOnYearGrowth, val.chain, val.moMGrowth];
			footerData.value.push(tempData);
			show.value = true;
		}
	},
	{ immediate: true }
);

const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};
const sortChangeEvent = ({ field, order }) => {
	console.log(field, order);
};

let onScroll = (e) => {
	if (e.isY && e.scrollHeight - e.scrollTop - translateFont(4) <= e.bodyHeight) {
		emits('pullHData');
	}
};
</script>
<style lang="scss" scoped></style>
