<template>
	<div class="progress-bar" ref="progressRef"></div>
</template>
<script setup>
import * as echarts from 'echarts';
import { translateFont } from '@/utils/index';
const props = defineProps(['progress', 'background', 'color']);
const option = computed(() => {
	const max = Number(props.progress || 0) < 100 ? 100 : Number(props.progress || 0);
	return {
		grid: {
			left: '0%',
			right: '14%', // 为右侧文字预留空间
			top: 'center',
			width: '86%',
			height: translateFont(20),
		},
		xAxis: {
			show: false,
			min: 0,
			max: max,
			type: 'value',
		},
		yAxis: {
			show: false,
			type: 'category',
		},
		series: [
			{
				type: 'bar',
				data: [100],
				barWidth: translateFont(20), // 背景比进度条宽4px
				itemStyle: {
					color: props.background || '#d7e4ff',
					borderRadius: 60,
				},
				z: 1, // 确保背景在下层
				label: {
					show: false,
				},
			},
			{
				type: 'bar',
				data: [Number(props.progress || 0)],
				// showBackground: true,
				// backgroundStyle: {
				// 	color: props.background || '#d7e4ff',
				// 	borderRadius: 40,
				// },
				itemStyle: {
					color: props.color || '#0052cc',
					borderRadius: 40,
				},
				z: 2,
				barGap: '-80%',
				barWidth: translateFont(10),
				label: {
					show: true,
					position: 'right',
					offset: [0, 0], // 向右偏移
					// distance: 25, // 增加与进度条的距离
					formatter: '{c}%',
					fontSize: translateFont(12),
					color: '#253858',
				},
				labelLayout: {
					x: '90%',
					y: '50%',
					dy: -5,
					dx: -10,
				},
			},
		],
	};
});
let myEcharts = echarts;
const progressRef = ref(null);
let chart;
let initChart = () => {
	if (!chart) {
		nextTick(() => {
			chart = myEcharts.init(progressRef.value);
			setOption();
			window.addEventListener('resize', function () {
				chart.resize();
			});
		});
	} else {
		setOption();
	}
};
let setOption = () => {
	if (chart) {
		console.log(option.value);
		chart.setOption(option.value);
		// chart.resize();
	}
};
watch(
	() => props.progress,
	() => {
		setTimeout(() => {
			initChart();
		}, 400);
	}
);
// onMounted(() => {
// 	nextTick(() => {
// 		initChart();
// 	});
// });
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.progress-bar {
	width: 100%;
	height: 20px;
}
@media screen and (min-width: 600px) {
	.progress-bar {
		height: 20px;
	}
}
</style>
