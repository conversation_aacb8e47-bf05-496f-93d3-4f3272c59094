<template>
	<div class="ignore-cm">
		<div class="card-title">
			<span>{{ query.province }} &ensp;市场份额占比分析</span>
		</div>
		<div class="content">
			<!-- 图表 -->
			<div :id="`cm-chat-${echartsId}`" class="cm-chat"></div>
			<div class="legend">
				<div class="legend-item" :style="{ '--bar-color': color[index % color.length] }" v-for="(item, index) in legendList" :key="index">
					<div class="dot"></div>
					<span>{{ item.name }}</span>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { generateRandomNumber, isPCTrue } from '@/utils/index';
import * as echarts from 'echarts';
import usefilterStore from '@/store/modules/filter';
let filterStore = usefilterStore();
let props = defineProps(['info', 'hiddenStar']);
const emit = defineEmits(['focus']);
// 筛选条件
const query = reactive({
	province: '全部省市',
});

let echartsId = generateRandomNumber();
// 初始化报表数据
let chart = null;
const initChart = () => {
	chart = echarts.init(document.querySelector(`#cm-chat-${echartsId}`), { devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio });
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
const color = ['#E85600', '#004FC4', '#4993F3', '#A2ABB8', '#52E4A7', '#D840AB', '#9750EB', '#B3E452', '#58A4A0', '#7842F1'];
const setOption = () => {
	const option = {
		legend: {
			show: false,
		},
		grid: {
			bottom: 0,
			left: 'auto',
			top: 0,
		},
		color,
		series: [
			{
				type: 'pie',
				radius: [68, 110],
				left: 'center',
				label: {
					width: 75,
					fontSize: 9,
					formatter: '{name|{b}}\n{c}，{d}%',
					rich: {
						name: {
							fontSize: 9,
							color: '#6B778C',
						},
					},
				},
				itemStyle: {
					borderColor: '#ffffff',
					borderWidth: 1,
				},
				labelLine: {
					length: 6,
					lengt2: 3,
					lineStyle: {
						color: '#DFE1E6',
					},
				},
				data: tableList.value,
			},
		],
	};
	chart.setOption(option);
};

const tableList = ref([]);
const legendList = ref([]);
watch(
	() => props.info,
	(n) => {
		if (n.info.data) {
			query.province = n.info.province;
			tableList.value = n.info.data.tableList;
			legendList.value = n.info.data.legendList;
			setOption();
		}
	}
);
onMounted(async () => {
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.ignore-cm {
	font-size: 10px;
	width: 100%;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 4px 4px;
	.card-title {
		width: 100%;
		border-radius: 4px 4px 0 0;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: #283b5c;
		height: 23px;
		span {
			font-size: 10px;
			color: #fff;
		}
	}
	.content {
		height: calc(100% - 23px);
		display: flex;
		align-items: center;
	}
	.cm-chat {
		width: 480px;
		height: 100%;
	}
	.legend {
		flex: 1;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 23px;
		&-item {
			color: #6b778c;
			font-size: 12px;
			display: flex;
			align-items: center;
			.dot {
				width: 6px;
				height: 6px;
				margin-right: 7px;
				background-color: var(--bar-color);
				border-radius: 50%;
			}
		}
	}
}
</style>
