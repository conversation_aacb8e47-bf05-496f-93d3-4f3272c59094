<template>
	<div class="sales-achievement-status" id="ap-sales-achievement-status">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span>
					<!-- 下载明细 -->
					<img v-if="isHzDownLoad || isMxDownLoad" class="icon-down" src="@/assets/img/download.png" @click="downloadExcel" alt="" />
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
			<div class="q-box">
				<!-- 时间 -->
				<div @click="openTime" class="q-width-49 q-fileter-item">
					<span class="item-title ell">{{ query.endDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div @click="openOrg" class="q-width-49 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>

				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.brandName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<div @click="openCity" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.countyName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部医院 -->
				<div @click="openHospital" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.hospitalName === '全部医院' ? '全部终端' : query.hospitalName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div v-if="isRecommend" class="sales-achievement-status-tip" v-html="chartData.message"></div>
		</div>
		<loading v-show="comStatus === 'loading'"></loading>
		<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>
		<div v-show="comStatus === 'normal'" class="card-echart">
			<div class="ym-month">
				<span>{{ query.startDate && formatDate(query.startDate + '01').slice(0, -3) }}</span>
			</div>
			<div class="ym-quarter">
				<span>{{ salesDate.startYm }} 至 {{ salesDate.endYm }}</span>
			</div>
			<div class="sales-achievement-status-echarts" :id="`sales-achievement-status-echarts-${echartsId}`"></div>
			<div class="value1">
				<span>0</span>
				<span>{{ chartData.month && formatNumber(chartData.month.target) }}</span>
			</div>
			<div class="value2">
				<span>0</span>
				<span>{{ chartData.quarter && formatNumber(chartData.quarter.target) }}</span>
			</div>
			<div class="card-echart-detail">
				<div class="card-echart-detail-item">
					<div class="other">
						月度销售：<span>{{ chartData.month && formatNumberT(chartData.month.sale) }}</span>
					</div>
					<div class="other">
						月度达成：<span>{{ chartData.month && formatPrecentOne(chartData.month?.ach) + '%' }}</span>
					</div>
					<div class="other">
						月度同比：<span>{{ chartData.month && formatPrecentOne(chartData.month?.yoy) + '%' }}</span>
						<span v-if="chartData.month?.yoy > 0"> <svg-icon class="icon" icon-class="sz"></svg-icon></span>
						<span v-else><svg-icon class="icon" icon-class="xd"></svg-icon></span>
					</div>
					<div class="other">
						月度环比：<span>{{ chartData.month && formatPrecentOne(chartData.month?.mom) + '%' }}</span>
						<span v-if="chartData.month?.mom > 0">
							<svg-icon class="icon" icon-class="sz"></svg-icon>
						</span>
						<span v-else>
							<svg-icon class="icon" icon-class="xd"></svg-icon>
						</span>
					</div>
				</div>
				<div class="card-echart-detail-item">
					<div class="other">
						季度销售：<span>{{ chartData.quarter && formatNumberT(chartData.quarter.sale) }}</span>
					</div>
					<div class="other">
						季度达成：<span>{{ chartData.quarter && formatPrecentOne(chartData.quarter?.ach) }}%</span>
					</div>
					<div class="other">
						季度同比：<span>{{ chartData.quarter && formatPrecentOne(chartData.quarter?.yoy) }}%</span>
						<span v-if="chartData.quarter?.yoy > 0"><svg-icon class="icon" icon-class="sz"></svg-icon></span>
						<span v-else><svg-icon class="icon" icon-class="xd"></svg-icon></span>
					</div>
					<div class="other">
						季度环比：<span>{{ chartData.quarter && formatPrecentOne(chartData.quarter?.mom) }}%</span>
						<span v-if="chartData.quarter?.mom > 0"><svg-icon class="icon" icon-class="sz"></svg-icon></span>
						<span v-else><svg-icon class="icon" icon-class="xd"></svg-icon></span>
					</div>
				</div>
			</div>
		</div>

		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 复选框 -->
		<skuFilter ref="brand" :listData="brandData" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :path="`${page}${props.info?.nameCn}`" :personalId="query.personId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 城市选择 -->
		<cityFilter ref="city" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import dayjs from 'dayjs';
import { showToast, showLoadingToast } from 'vant';
import { salesAchievementStatus, salesAchievementDownload } from '@/api/sales';
import { formatNumber, formatNumberT, formatPrecentOne, generateRandomNumber, translateFont, getNamesByIdsInArray, getNameByPath, getFilterTime, isPCTrue } from '@/utils/index';
import Decimal from 'decimal.js';
import usePremissionStore from '@/store/modules/premission';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
const perStore = usePremissionStore();

let props = defineProps(['info', 'isRecommend']);
let myEcharts = echarts;
let chart; //图实例
let chartData = ref({}); //图数据
let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
// 明细
const isMxDownLoad = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'isSalesAchievementDownLoad');
});

// 汇总
const isHzDownLoad = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'isSalesAchievementSunDownload');
});

// 下载明细
const downloadExcel = async () => {
	showLoadingToast({
		message: '下载中...',
		forbidClick: true,
		duration: 0,
		class: 'ppt-pc-loading',
	});
	// 优先下载汇总
	await salesAchievementDownload({
		skuCode: query.brandId ? query.brandId.split(',') : [],
		territoryCode: query.personId ? query.personId.split(',') : [],
		hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
		startLocalDate: props.info.startTime,
		endLocalDate: props.info.endTime,
		email: userStore.userInfo.email,
		userType: isHzDownLoad.value ? 1 : 2,
		title: props.info?.nameCn,
	});
	showToast({
		message: '数据下载成功，稍后会发送到您的邮箱。',
		position: 'top',
	});
};

// 计算当季度开始月份与结束月份
const salesDate = reactive({
	startYm: '',
	endYm: '',
});

const getQuarter = (sTime) => {
	let month = 0;
	if (sTime?.length > 6) {
		let year = sTime.substring(0, 5);
		if (sTime.substring(5, 6) === '0') {
			month = sTime.substring(6, 7);
		} else {
			month = sTime.substring(5, 7);
		}
		let endM = getQuarterStartAndEnd(month);
		let startM = endM - 2;
		endM = endM < 10 ? '0' + endM : endM;
		startM = startM < 10 ? '0' + startM : startM;
		salesDate.startYm = year + startM;
		salesDate.endYm = year + endM;
	}
};

function getQuarterStartAndEnd(month) {
	if (month < 1 || month > 12) return;
	return Math.ceil(month / 3) * 3; // 计算季度的结束月份
}

// 筛选条件
const query = reactive({
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
});
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	getQuarter(formatDate(query.startDate + '01'));
	await getData();
	chart.resize();
	setOption();
	document.querySelector('.sale-trend-table-content').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};
// 城市
const city = ref(null);
const openCity = () => {
	city.value.show = true;
};
const cityConfirm = async (params) => {
	console.log(params);
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	brand.value.show = true;
};

let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgZ.value.show = true;
};
const hospital = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, getCity, getHospital, initFilters } = useCascader(query, { city, hospital, brandData });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
	cascaderChange();
};

// 医院筛选
const openHospital = () => {
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};

//初始化
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#sales-achievement-status-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
//设置
let setOption = () => {
	let option = {
		series: [
			{
				type: 'gauge',
				startAngle: 180,
				endAngle: 0,
				min: 0,
				max: 100,
				splitNumber: 1,
				center: [translateFont(80), translateFont(90)],
				radius: translateFont(66),
				itemStyle: {
					color: '#0074f9',
				},
				progress: {
					show: true,
					roundCap: false,
					width: translateFont(18),
				},
				pointer: {
					show: true,
					icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
					length: '60%',
					width: translateFont(8),
					offsetCenter: [0, '5%'],
				},
				axisLine: {
					roundCap: false,
					lineStyle: {
						width: translateFont(18),
						color: [[1, '#E5EDF9']],
					},
				},
				axisTick: {
					show: false,
				},
				splitLine: {
					length: 0,
				},
				axisLabel: {
					show: false,
				},
				detail: {
					show: false,
				},
				data: [
					{
						value: chartData.value.month.ach,
					},
				],
			},
			{
				type: 'gauge',
				startAngle: 180,
				endAngle: 0,
				min: 0,
				max: 100,
				splitNumber: 1,
				center: [translateFont(250), translateFont(90)],
				radius: translateFont(66),
				itemStyle: {
					color: '#00EFFF',
				},
				progress: {
					show: true,
					roundCap: false,
					width: translateFont(18),
				},
				pointer: {
					show: true,
					icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
					length: '60%',
					width: translateFont(8),
					offsetCenter: [0, '5%'],
				},
				axisLine: {
					roundCap: false,
					lineStyle: {
						width: translateFont(18),
						color: [[1, '#E5EDF9']],
					},
				},
				axisTick: {
					show: false,
				},
				splitLine: {
					length: 0,
				},
				axisLabel: {
					show: false,
				},
				title: {
					show: false,
				},
				detail: {
					show: false,
				},
				data: [
					{
						value: chartData.value.quarter.ach,
					},
				],
			},
		],
	};

	chart.setOption(option);
};
//获取数据
let comStatus = ref('loading');
let getData = async () => {
	try {
		comStatus.value = 'loading';
		let res = await salesAchievementStatus({
			skuCode: query.brandId ? query.brandId.split(',') : [],
			territoryCode: query.personId ? query.personId.split(',') : [],
			hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
			nowLocalDateTime: formatDate(query.startDate + '01'),
			endLocalDate: dayjs(formatDate(query.startDate + '01'))
				.endOf('month')
				.format('YYYY-MM-DD'),
			province: query.province || [],
			city: query.city || [],
		});
		if (res.result.isExceededAuthority) return (comStatus.value = 'isExceededAuthority');
		chartData.value = res.result;
		comStatus.value = 'normal';
		return Promise.resolve();
	} catch (error) {
		return Promise.reject(error);
	}
};

function formatDate(num) {
	const str = num; // 将数字转换为字符串
	if (str.length === 8) {
		return str.slice(0, 4) + '-' + str.slice(4, 6) + '-' + str.slice(6, 8); // 取前四位，中间两位，最后两位，拼接成时间格式
	}
	return null; // 如果长度不为8，返回null或其他错误处理
}

const emit = defineEmits(['focus']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let echartsId = generateRandomNumber();
let positionSeason = computed(() => {
	if (chartData.value.quarter) {
		return new Decimal(chartData.value.quarter?.ach).toDecimalPlaces(0) >= 50 ? 'right' : 'left';
	}
	return 'left';
});
onMounted(async () => {
	initFilter();
	//初始化级联
	if (query.personId) {
		initFilters();
	}
	getQuarter(formatDate(query.startDate + '01'));
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
query.startDate = getFilterTime(props.info.startTime);
query.endDate = getFilterTime(props.info.endTime);

defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/salesAchievementStatus.scss';
.sales-achievement-status {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	&-tip {
		padding: 5px 5px 0;
		font-size: 12px;
		color: #7c88a6;
	}
	.card-echart {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		overflow: hidden;
		padding-top: 7px;
		position: relative;
		.ym-month {
			position: absolute;
			color: var(--pv-default-color);
			left: 25%;
			transform: translateX(-50%);
			top: 10px;
			// width: 100px;
			text-align: center;
			font-size: 12px;
		}
		.ym-quarter {
			position: absolute;
			color: var(--pv-default-color);
			right: 25%;
			transform: translateX(50%);
			top: 10px;
			// width: 120px;
			text-align: center;
			font-size: 12px;
		}
		.info1 {
			position: absolute;
			left: 45px;
			top: 43px;
			text-align: center;
			line-height: 18px;
			width: 70px;
			div:nth-child(1) {
				font-size: 10px;
				color: #6b778c;
			}
			div:nth-child(2) {
				font-size: 13px;
				font-weight: bold;
			}
		}
		.info2 {
			position: absolute;
			left: 215px;
			top: 43px;
			text-align: center;
			line-height: 18px;
			width: 70px;
			div:nth-child(1) {
				font-size: 10px;
				color: #6b778c;
			}
			div:nth-child(2) {
				font-size: 13px;
				font-weight: bold;
			}
		}
		.value1 {
			font-size: 10px;
			position: absolute;
			width: 140px;
			display: flex;
			justify-content: space-between;
			left: 20px;
		}
		.value2 {
			font-size: 10px;
			position: absolute;
			width: 137px;
			display: flex;
			justify-content: space-between;
			left: 189px;
		}
		&-info {
			font-size: 12px;
			position: relative;
			span:nth-child(1) {
				position: absolute;
				left: 23px;
			}
			span:nth-child(2) {
				position: absolute;
				left: 50px;
			}
			span:nth-child(3) {
				position: absolute;
				left: 268px;
				font-weight: bold;
				width: 55px;
				word-break: break-all;
			}
			.month {
				position: absolute;
				left: 50%;
				top: -75px;
				transform: translateX(-50%);
				text-align: center;
				div:nth-child(1) {
					font-size: 30px;
					font-weight: bold;
				}
				div:nth-child(2) {
					font-size: 12px;
					color: var(--pv-no-active-color);
					top: -30px;
				}
			}
			.season {
				font-size: 12px;
				position: absolute;
				top: -140px;
				text-align: center;
				div:nth-child(1) {
					color: var(--pv-no-active-color);
				}
				&.left {
					left: 20px;
				}
				&.right {
					right: 20px;
				}
			}
		}
		&-detail {
			margin: 18px 11px 8px;
			display: flex;
			gap: 8px;
			&-item {
				flex: 1;
				border: 1px dashed #dfe1e6;
				padding: 3px 6px;
				border-radius: 5px;
				.title {
					font-weight: bold;
				}
				.other {
					color: var(--pv-no-active-color);
					margin-top: 5px;
					font-size: 12px;
					span {
						color: var(--pv-default-color);
					}
					.icon {
						font-size: 15px;
					}
				}
			}
		}
	}
	&-echarts {
		width: 100%;
		height: 90px;
	}
}
</style>
