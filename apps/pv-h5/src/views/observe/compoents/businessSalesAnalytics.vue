<template>
	<div class="branch-sales-overview">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span>
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
			<div class="q-box">
				<!-- 时间 -->
				<div @click="openTime" class="q-width-49 q-fileter-item">
					<span class="item-title ell">{{ query.startDate + ' - ' + query.endDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<div @click="openCity" class="q-width-49 q-fileter-item">
					<span class="item-title ell">{{ query.countyName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div class="q-box">
				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.brandName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div @click="openOrg" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部医院 -->
				<div @click="openHospital" class="q-width-32 q-fileter-item">
					<span class="item-title ell">{{ query.hospitalName === '全部医院' ? '全部终端' : query.hospitalName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
		</div>

		<div class="branch-sales-overview-filter">
			<loading v-show="comStatus === 'loading'"></loading>
			<div v-show="comStatus === 'normal'" class="branch-sales-overview-filter-echarts-card">
				<div class="branch-sales-overview-filter-echarts" :id="`branch-sales-overview-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :defaultHosCheckedAll="defaultHosCheckedAll" :path="`${page}${props.info?.nameCn}`" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import { getcubejsData } from '@/api/sales';
import * as echarts from 'echarts';
import { generateRandomNumber, getYearMonthBefore, getFilterTime, translateFont, spaces, getNameByPath, isPCTrue } from '@/utils/index';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';

let filterStore = usefilterStore();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let props = defineProps(['info', 'defaultValue']);
function formatNumbeW(number) {
	if (number == null) return 0;
	const isNegative = number < 0;
	const absoluteValue = Math.abs(number);
	if (absoluteValue < 10000) {
		const formatted = new Decimal(absoluteValue).toDecimalPlaces(2).toNumber().toLocaleString();
		return isNegative ? `-${formatted}` : formatted;
	} else {
		const formatted = `${new Decimal(absoluteValue).dividedBy(10000).toDecimalPlaces(2).toNumber().toLocaleString()}W`;
		return isNegative ? `-${formatted}` : formatted;
	}
}
function saleTrendFilter(item) {
	if (item.seriesName === '指标' || item.seriesName === '销售' || item.seriesName === '纯销' || item.seriesName === '同期') {
		return formatNumbeW(item.value);
	}
	if (item.seriesName === '达成' || item.seriesName === '同比增长' || item.seriesName === '占比' || item.seriesName === '同比' || item.seriesName === '达成率' || item.seriesName === '完成率' || item.seriesName === '同比') {
		return new Decimal(item.value).toDecimalPlaces(0) + '%';
	}
}
const defaultPerson = ref('');
const defaultHosCheckedAll = ref('');
const defaultProvinceCity = ref({});
const defaultSku = ref('');
const defaultHos = ref('');
// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
	city: '',
	province: '',
});
const queryField = computed(() => {
	return {
		startDate: query.startDate,
		endDate: query.endDate,
		brandId: query.brandId,
		personId: query.personId,
		hospitalId: query.hospitalId,
		city: query.city,
		province: query.province,
	};
});
watch(queryField, (n) => {
	console.log(n, '////');
});
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};
// 城市
const city = ref(null);
const openCity = () => {
	city.value.show = true;
};
const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
	brand.value.show = true;
};

let brandConfirm = async (params) => {
	console.log(params);

	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = '';
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgZ.value.show = true;
};

const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
// 医院筛选
const hospital = ref(null);
const openHospital = () => {
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};

const defaultQuery = {
	query: {
		timezone: 'Asia/Shanghai',
		dimensions: ['business_sales_analysis.current_territory_code'],
		measures: ['business_sales_analysis.sales_v', 'business_sales_analysis.sales_v_ly', 'business_sales_analysis.sales_v_achievement_rate', 'business_sales_analysis.sales_v_yoy_rate'],
		filters: [
			{
				member: 'business_sales_analysis.parent_territory_code',
				operator: 'equals',
				values: ['ADMIN'],
			},
		],
	},
};
let myEcharts = echarts;
let chart;
let echartsId = generateRandomNumber();
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#branch-sales-overview-echarts-${echartsId}`), { devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio });
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let x = tableList.value.map((ele) => ele['business_sales_analysis.current_territory_code']);
	let cx = tableList.value.map((ele) => ele['business_sales_analysis.sales_v']);
	let tq = tableList.value.map((ele) => ele['business_sales_analysis.sales_v_ly']);
	let wcl = tableList.value.map((ele) => ele['business_sales_analysis.sales_v_achievement_rate']);
	let tb = tableList.value.map((ele) => ele['business_sales_analysis.sales_v_yoy_rate']);
	let option = {
		grid: {
			top: translateFont(40),
			left: translateFont(58),
			right: translateFont(40),
			bottom: translateFont(30),
		},
		legend: {
			data: ['纯销', '同期', '完成率', '同比'],
			left: 'center',
			top: translateFont(13),
			itemGap: translateFont(15),
			itemWidth: translateFont(8),
			itemHeight: translateFont(4),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(9),
			},
		},
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = '';
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${saleTrendFilter(item)}</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			axisPointer: {
				type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
				},
			},
			extraCssText: 'z-index:9',
		},
		xAxis: [
			{
				type: 'category',
				data: x,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: spaces(x.length), // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return formatNumbeW(value);
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: true, // 隐藏 Y 轴横线
					lineStyle: {
						color: 'rgba(223,225,230,.5)',
					},
				},
				splitNumber: 4,
			},
			{
				type: 'value',
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
					fontSize: translateFont(9),
					formatter: (value) => {
						return new Decimal(value).toDecimalPlaces(0) + '%';
					},
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				splitLine: {
					show: false, // 隐藏 Y 轴横线
				},
			},
		],
		color: ['#0052CC', '#01A2BF', '#E95600', '#01A2BF'],
		series: [
			{
				name: '纯销',
				type: 'bar',
				data: cx,
				barWidth: translateFont(6),
				z: 2,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '同期',
				type: 'bar',
				data: tq,
				barWidth: translateFont(6),
				z: 4,
				emphasis: { disabled: true },
				yAxisIndex: 0,
			},
			{
				name: '完成率',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: wcl,
				z: 4,
			},
			{
				name: '同比',
				type: 'line',
				yAxisIndex: 1,
				lineStyle: {
					width: translateFont(1),
				},
				symbolSize: translateFont(5),
				data: tb,
				z: 4,
			},
		],
	};
	chart.setOption(option);
};
let comStatus = ref('loading');
const tableList = ref([]);
const getData = async () => {
	try {
		comStatus.value = 'loading';
		let res = await getcubejsData(defaultQuery);
		tableList.value = res.data;
		console.log(tableList.value);
		comStatus.value = res.data.length < 1 ? 'empty' : 'normal';
	} catch (error) {
		console.log(error);
	}
};
const getProduct = async () => {
	let res = await getcubejsData({
		query: {
			timezone: 'Europe/Berlin',
			dimensions: ['pv_ads_product_filter.bu', 'pv_ads_product_filter.bu_cn', 'pv_ads_product_filter.franchise_code', 'pv_ads_product_filter.franchise_cn', 'pv_ads_product_filter.brand_code', 'pv_ads_product_filter.brand_cn', 'pv_ads_product_filter.sku_code', 'pv_ads_product_filter.sku_cn'],
		},
	});
	console.log(res);
};
onMounted(async () => {
	initFilter();
	await getData();
	getProduct();
	initChart();
});
</script>
<style lang="scss" scoped>
.branch-sales-overview {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	&-filter {
		border-radius: 5px;
		margin-top: 8px;
		background-color: var(--pv-bgc);
		&-echarts {
			height: 220px;
			&-card {
				background-color: var(--pv-bgc);
			}
		}
	}
}
</style>
