<template>
	<div>
		<vxe-table show-overflow :show-footer="show" max-height="400" :sort-config="{ trigger: 'cell' }" :footer-method="footerMethod" :data="props.detailList">
			<vxe-column field="hospitalType" title="医院类型" align="center" width="70px" fixed="left"></vxe-column>
			<vxe-column field="totalCount" title="总数" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="admissionCount" title="准入" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="admissionRate" title="准入(%)" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="formalMedicine" title="正式进药" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="temporaryProcurement" title="临时采购" align="right" sortable width="70px"></vxe-column>
			<vxe-column field="inHospitalPharmacy" title="院内决策药房" align="right" sortable width="100px"></vxe-column>
			<vxe-column field="notAdmitted" title="未准入" align="right" sortable width="70px"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
const props = defineProps(['detailList', 'lastResult']);
const footerData = ref([]);
const show = ref(false);
watch(
	() => props.lastResult,
	(val) => {
		if (val) {
			footerData.value = [];
			let tempData = [val.hospitalType, val.totalCount, val.admissionCount, val.admissionRate, val.formalMedicine, val.temporaryProcurement, val.inHospitalPharmacy, val.notAdmitted];
			footerData.value.push(tempData);
			show.value = true;
		}
	},
	{ immediate: true }
);

const footerMethod = () => {
	// 返回一个二维数组的表尾合计
	return footerData.value;
};
</script>
<style lang="scss" scoped></style>
