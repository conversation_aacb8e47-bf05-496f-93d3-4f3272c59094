<template>
	<div class="admission-situation" id="ap-admission-situation-hs">
		<div class="top">
			<div class="card-title">
				<span>{{ props.info?.nameCn }}</span>
				<span>
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
			<!-- 通用筛选器 -->
			<globalFilter
				:filters="[
					{ name: 'time', size: '49', queryName: 'dateRange', single: true },
					{ name: 'org', size: '49', queryName: 'personName' },
					{ name: 'brand', size: '32', queryName: 'brandName' },
					{ name: 'city', size: '32', queryName: 'countyName' },
					{ name: 'hospital', size: '32', queryName: 'hospitalName' },
				]"
				:query="query"
				:loading="filterLoading"
				@openTime="openTime"
				@openOrg="openOrg"
				@openBrand="openBrand"
				@openCity="openCity"
				@openHospital="openHospital"
			></globalFilter>
		</div>
		<loading v-show="comStatus === 'loading'"></loading>

		<div v-show="comStatus === 'normal'" class="admission-situation-card">
			<div class="admission-situation-echarts" :id="`admission-situation-echarts-${echartsId}`"></div>
		</div>
		<empty-com v-show="comStatus === 'empty'"></empty-com>
		<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>

		<div class="admission-table">
			<div class="admission-table-title" @click="zk = !zk">
				<span>点击查看明细</span>
				<svg-icon class="icon" :class="{ down: zk }" icon-class="down2"></svg-icon>
			</div>
			<transition @beforeEnter="handleBeforeEnter" @enter="handleEnter" @leave="handleLeave">
				<div v-show="zk" class="admission-table-detail">
					<admissionHospitalsTable :detailList="detailList" :lastResult="lastResult"></admissionHospitalsTable>
				</div>
			</transition>
		</div>
		<!-- 品牌 -->
		<skuFilter ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :defaultHosCheckedAll="defaultHosCheckedAll" :path="`${page}${props.info?.nameCn}`" :personalId="query.personId" :skuCode="query.brandId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { generateRandomNumber, translateFont, formatNumber, getFilterTime, getNameByPath, isPCTrue } from '@/utils/index';
import { accessStatusLevel } from '@/api/sales';
import usefilterStore from '@/store/modules/filter';
import admissionHospitalsTable from './admissionHospitalsTable';

let props = defineProps(['info', 'defaultValue', 'summary', 'inChat']);

let filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
});
// 默认选中的人员
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionHospitals',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-日期`, `${_startDate}-${_endDate}`);
};

// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionHospitals',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, initFilters, filterLoading } = useCascader(query, { city, hospital, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	await getData();
	chart.resize();
	setOption();
	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部产品';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	await getData();
	chart.resize();
	setOption();
	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 城市

const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionHospitals',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();
	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
// 医院筛选
const openHospital = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'admissionHospitals',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	await getData();
	chart.resize();
	setOption();
	document.querySelector('.admission-table-detail').style.height = 'auto';
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};

let myEcharts = echarts;
let chart;
let chartLegend = ref([]);
let chartData = ref([]);
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#admission-situation-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let names = chartData.value.categories?.map((i) => i + '医院'); //产品
	const series = chartLegend.value.map((legend) => {
		const data = chartData.value?.series?.map((item) => {
			const statusData = item.statusDetail[legend];
			return statusData ? statusData : 0;
		});
		return {
			name: legend,
			type: 'bar',
			data: data, // data 是一个包含所有值的数组
			stack: 'total',
			barWidth: translateFont(10), // 假设 translateFont 是已定义的函数
			emphasis: { disabled: true },
		};
	});
	let option = {
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				console.log(value);
				let str = `<div class="product" style="font-size:9px;color:#172B4D;">${value[0].axisValue}</div><div style="color:#172B4D;"> <span style="font-weight:bold">准入</span><span>${chartData.value.series.find((ele) => value[0].axisValue.includes(ele.level)).admissionCount}家/ <span>${
					chartData.value.series.find((ele) => value[0].axisValue.includes(ele.level)).admissionRate
				}</span></div>`;
				for (let item of value.slice(0, -1)) {
					str += ` <div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${formatNumber(item.value)}</span></div>`;
				}
				str += `<div style="color:#172B4D;"><span style="font-weight:bold">未准入</span><span>${chartData.value.series.find((ele) => value[0].axisValue.includes(ele.level)).statusDetail['未准入']}家/ <span>${
					chartData.value.series.find((ele) => value[0].axisValue.includes(ele.level)).notAdmittedRate
				}</span></div>`;
				return `<div class="tooltip-saletrend" style="line-height:2">${str}</div>`;
			},
			axisPointer: {
				type: 'shadow', //默认为line，line直线，cross十字准星，shadow阴影
				shadowStyle: {
					color: 'rgba(225,235,255)',
					opacity: '0.1',
				},
			},
			extraCssText: 'z-index:9',
		},
		legend: {
			data: chartLegend.value,
			left: 'center',
			top: translateFont(15),
			itemGap: translateFont(10),
			itemWidth: translateFont(12),
			itemHeight: translateFont(3),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		grid: {
			top: translateFont(50),
			left: translateFont(10),
			right: translateFont(10),
			bottom: 0,
			containLabel: true,
		},
		xAxis: {
			type: 'value',
			axisLabel: {
				show: false, // 是否显示坐标轴刻度标签。
			},
			axisLine: {
				show: false, // 是否显示坐标轴轴线
			},
			splitLine: {
				show: false,
			},
		},
		yAxis: [
			{
				type: 'category',
				data: names,
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					rich: {
						name: {
							width: translateFont(50),
							backgroundColor: 'none',
							align: 'left',
							fontSize: translateFont(9),
							color: '#6B778C',
							padding: [0, 0, 0, translateFont(9)],
						},
					},
					formatter: function (value) {
						if (value.length > 8) {
							return `{name|${value.slice(0, 8)}...}`;
						} else {
							return `{name|${value}}`;
						}
					},
				},
			},
			{
				type: 'category',
				data: admissionRateList.value.map((ele) => ele.admissionRate),
				position: 'right', // 显示在右侧
				name: '准入率%', // 添加 Y 轴名称
				nameLocation: 'end',
				nameTextStyle: {
					color: '#6B778C', // 名称颜色，与其他文本一致
					fontSize: translateFont(7), // 字体大小
					align: 'left', // 居中对齐
					verticalAlign: 'top',
					width: translateFont(150),
					height: translateFont(50),
					padding: [translateFont(7), 0, 0, translateFont(5)],
				},
				axisLine: {
					show: false, // 是否显示坐标轴轴线
				},
				axisTick: {
					show: false, // 是否显示坐标轴刻度。
				},
				axisLabel: {
					show: true, // 是否显示坐标轴刻度标签。
					interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
					inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
					rich: {
						name: {
							// width: translateFont(50),
							backgroundColor: 'none',
							align: 'left',
							fontSize: translateFont(9),
							color: '#172B4D',
						},
					},
					formatter: function (value) {
						return `{name|${value}}`;
					},
				},
			},
		],
		color: ['#0251CB', '#0379FF', '#79E1F2', '#DFE1E6'],
		series,
	};
	chart.setOption(option);
};

let zk = ref(false);
// 表格过渡动画
const handleBeforeEnter = (el) => {
	el.style.height = '0px';
};
const handleEnter = (el) => {
	el.style.height = 'auto';
	let h = el.clientHeight;
	el.style.height = '0px';
	requestAnimationFrame(() => {
		el.style.height = h + 'px';
		el.style.transition = '.5s';
	});
	proxy.$umeng('点击', `${page}${props.info.nameCn}`, '点击查看明细');
};
const handleLeave = (el) => {
	requestAnimationFrame(() => {
		el.style.height = '0px';
	});
};
const emit = defineEmits(['focus', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let echartsId = generateRandomNumber();
let admissionRateList = ref([]);
let comStatus = ref('loading');
let lastResult = ref({});
const detailList = ref([]);
let getData = async () => {
	comStatus.value = 'loading';
	let res = await accessStatusLevel({
		skuCode: query.brandId ? query.brandId : '',
		territoryCode: query.personId ? query.personId : '',
		hospCode: query.hospitalId ? query.hospitalId : '',
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		province: Array.isArray(query.province) ? query.province?.join(',') || '' : '',
		city: Array.isArray(query.city) ? query.city?.join(',') || '' : '',
	});
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}
	chartLegend.value = res.result.legend;
	chartData.value = res.result.chartData;
	detailList.value = res.result.tableData.filter((ele) => ele.hospitalType !== '合计');
	admissionRateList.value = res.result.chartData?.series;
	comStatus.value = res.result.tableData.length < 1 ? 'empty' : 'normal';
	if (comStatus.value === 'normal') {
		lastResult.value = res.result.tableData.find((ele) => ele.hospitalType === '合计');
	}
	if (props.summary) {
		emit('summaryIng', { data: { ...res.result }, params: query });
	}
};

const defaultSku = ref('');
const defaultHos = ref('');
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			brand, // 传入 ref
			orgZ, // 传入 ref
			time,
			defaultSku, // 传入 ref
			defaultHos, // 传入 ref
			defaultPerson, // 传入 ref
			defaultProvinceCity, // 传入 ref
		});
	}
	//初始化级联
	initFilters();
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/admissionHospitals.scss';
.admission-situation {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}
	&-card {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		padding-bottom: 15px;
		height: 170px;
		position: relative;
		/* 设置纵向滚动条样式 */
		&::-webkit-scrollbar {
			width: 4px; /* 设置纵向滚动条宽度 */
		}

		&::-webkit-scrollbar-thumb {
			background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
			border-radius: 5px;
		}
		.zr {
			position: absolute;
			top: 42px;
			right: 20px;
			font-size: 7px;
			color: #6b778c;
		}
		.zr-item {
			position: absolute;
			top: 58px;
			right: 20px;
			font-size: 9px;
			color: #172b4d;
			height: calc(100% - 15px - 50px - 20px);
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}
	}
	&-echarts {
		height: 100%;
	}
	.admission-table {
		background-color: #fff;
		margin-top: 8px;
		overflow: hidden;
		&-title {
			font-size: 12px;
			color: var(--pv-default-color);
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 6px;
			position: relative;
			.icon {
				font-size: 7px;
				position: absolute;
				right: 14px;
				transition: all 0.5s;
			}
			.down {
				transform: rotate(180deg);
			}
		}
		&-detail {
			margin: 0 7px;
		}
	}
}
</style>
