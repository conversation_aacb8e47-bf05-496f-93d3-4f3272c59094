<template>
	<div class="marek-share" id="ap-marek-share">
		<div class="top">
			<div class="card-title">
				<span>
					{{ props.info?.nameCn || pageTitle }}
				</span>
				<span class="operation" v-if="!hiddenStar">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
					<switchCom ref="switchComRef" :defaultValue="props.info" @currencySwitching="currencySwitching"></switchCom>
				</span>
			</div>
		</div>
		<!-- 通用筛选器 -->
		<globalFilter
			:filters="[
				{ name: 'time', size: '49', queryName: 'dateRange' },
				{ name: 'org', size: '49', queryName: 'personName' },
				{ name: 'brand', size: '32', queryName: 'brandName' },
				{ name: 'city', size: '32', queryName: 'countyName' },
				{ name: 'hospital', size: '32', queryName: 'hospitalName' },
			]"
			:query="query"
			:loading="filterLoading"
			@openTime="openTime"
			@openOrg="openOrg"
			@openBrand="openBrand"
			@openCity="openCity"
			@openHospital="openHospital"
		></globalFilter>
		<div class="content">
			<div>
				<filterCom :filterList="filterList" :currentIndex="currentIndex" @filterChange="filterChange" :downloadExcel="false" @_downExcel="downExcel"></filterCom>
				<loading v-show="comStatus === 'loading'"></loading>
				<div v-show="comStatus === 'normal'" class="marek-share-legend">
					<div class="marek-share-legend-item" :class="{ gray: !item.active }" v-for="(item, index) in legend" :key="item" :style="{ '--bar-color': color[index % color.length] }" @click="legendClick(item)">
						<span class="bar"></span>
						<span>{{ item.name }}</span>
					</div>
				</div>
				<div v-show="comStatus === 'normal'" class="marek-share-echarts" :id="`marek-share-echarts-${echartsId}`"></div>
			</div>
			<empty-com v-show="comStatus === 'empty'"></empty-com>
			<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>
		</div>
		<div class="sale-table">
			<div class="sale-table-title" @click="zk = !zk">
				<span>点击查看明细</span>
				<svg-icon class="icon" :class="{ down: zk }" icon-class="down2"></svg-icon>
			</div>
			<!-- <transition @beforeEnter="handleBeforeEnter" @enter="handleEnter" @leave="handleLeave"> -->
			<div v-show="zk" class="sale-table-content">
				<salesTrendBrandTable :tableData="tableData" :columns="columns" :lastResult="lastResult"></salesTrendBrandTable>
			</div>
			<!-- </transition> -->
		</div>
		<div v-if="showTags" class="tags">
			<div class="tags-item"># BU/RSM Report</div>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.info?.dataSyncTime" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 品牌 -->
		<skuFilter level="brand" ref="brand" :listData="brandData" :defaultCheck="defaultSku" :path="`${page}${props.info?.nameCn}-产品`" @_confirm="brandConfirm"></skuFilter>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${props.info?.nameCn}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 医院 -->
		<HospitalCheckbox ref="hospital" :defaultCheck="defaultHos" :defaultHosCheckedAll="defaultHosCheckedAll" :path="`${page}${props.info?.nameCn}`" :personalId="query.personId" :province="query.province" :city="query.city" :skuCode="query.brandId" @_confirm="hospitalConfirm"></HospitalCheckbox>
		<!-- 日期选择器 -->
		<DatePickerMultery ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerMultery>
		<!-- 城市选择 -->
		<cityFilter ref="city" :defaultValue="defaultProvinceCity" @_confirm="cityConfirm"></cityFilter>
	</div>
</template>
<script setup>
import { showToast, showLoadingToast } from 'vant';
import { getBrandSalesTrends, salesInfoDownload } from '@/api/sales';
import { generateRandomNumber, getFilterTime, translateFont, deepClone, getNameByPath, formatNumbeW, formatNumbeWT, spaces, isPCTrue } from '@/utils/index';
import * as echarts from 'echarts';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
import useUserStore from '@/store/modules/report';
let reportStore = useUserStore();
let filterStore = usefilterStore();
import salesTrendBrandTable from './salesTrendBrandTable';
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let showUpdateTime = ref(true);
let pageTitle = '各品牌趋势分析';
if (route.fullPath.includes('/report')) {
	showUpdateTime.value = false;
}

const props = defineProps(['showTags', 'info', 'hiddenStar', 'isPPT', 'pathAddress', 'defaultValue', 'summary', 'inChat']);
// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	brandId: '',
	brandName: '全部品牌',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
	countyName: '全部省市',
});
// 默认选中人员
const defaultPerson = ref('');

// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
	//初始化产品筛选器值
	if (brandData.value.length === 0) {
		brandData.value = filterStore.skuInfoTree;
	}
};

// 下载明细
const downExcel = async (emial) => {
	showLoadingToast({
		message: '下载中...',
		forbidClick: true,
		duration: 0,
		class: 'ppt-pc-loading',
	});
	for (const ele of columns.value) {
		ele.name = ele.title;
		ele.key = ele.field;
	}
	const json = {
		headerData: columns.value,
		bodyData: tableData.value,
		footerData: lastResult.value,
	};
	const query = {
		name: props.info?.nameCn || pageTitle,
		email: emial,
		tableInfo: json,
	};
	await salesInfoDownload(query);
	showToast({
		message: '数据下载成功，稍后会发送到您的邮箱。',
		position: 'top',
	});
};

// 日期筛选
const time = ref(null);
const openTime = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesTrendBrand',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info?.nameCn || pageTitle}-日期`, `${_startDate}-${_endDate}`);
};
watch(
	() => reportStore.globalFilterInfo.time,
	async (n) => {
		console.log(n);
		time.value.resetActive();
		query.startDate = n.startDate;
		query.endDate = n.endDate;
		// showPicker.value = false;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.dinfo?.nameCn || pageTitle}-日期`, `${query.startDate}-${query.endDate}`);
	}
);

watch(
	() => reportStore.globalFilterInfo.area,
	async (n) => {
		console.log(n);
		city.value.golbalInit(n.provinceData);
		query.countyName = n.countyName;
		query.city = n.city;
		query.province = n.province;
		await getData();
		chart.resize();
		setOption();
		proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
	}
);
const lastResult = ref({});

// 品牌筛选
const brandData = ref([]);
const brand = ref(null);
// 传入树形数组只保留传入的层数

const filterTree = (arr, level, max) => {
	level = level || 0;
	max = max || 2;
	return arr.filter((item) => {
		if (item.children) {
			item.children = filterTree(item.children, level + 1, max);
		}
		return level <= max;
	});
};
const openBrand = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesTrendBrand',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	brand.value.show = true;
};

const defaultSku = ref('');
watch(
	() => reportStore.globalFilterInfo.brand,
	async (n) => {
		brand.value.handleReset();
		brandData.value = filterStore.skuInfoTree;
		defaultSku.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		brand.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		brand.value.confirm();
	}
);
// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesTrendBrand',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	orgZ.value.show = true;
};
const hospital = ref(null);
const city = ref(null);
// 使用级联筛选器hooks
const { cascaderChange, initFilters, cascaderChangeHospital, filterLoading } = useCascader(query, { city, hospital, brandData, orgList });
const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	defaultPerson.value = ids;
	cascaderChange('org');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-人员`, query.personName);
};
let brandConfirm = async (params) => {
	console.log(params);
	if (params.skuName.length > 0) {
		query.brandName = params.skuName.join(',');
	} else {
		query.brandName = '全部品牌';
	}
	query.brandId = params.skuCode.join(',');
	defaultSku.value = params.skuCode;
	cascaderChange('brand');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-产品`, query.brandName);
};
// 城市

const openCity = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesTrendBrand',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	city.value.show = true;
};
const cityConfirm = async (params) => {
	console.log(params);
	query.countyName = [...params.province, ...params.city].join(',') || '全部省市';
	query.city = params.city;
	query.province = params.province;
	defaultProvinceCity.value = {
		province: query.province,
		city: query.city,
	};
	cascaderChange('city');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-城市`, query.countyName);
};
watch(
	() => reportStore.globalFilterInfo.people,
	async (n) => {
		orgZ.value.handleReset();
		defaultPerson.value = n.checked; //默认选中
		await nextTick(); // 确保 DOM 更新完成后再执行
		orgZ.value.handleDefault();
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		orgZ.value.confirm();
	}
);
// 医院筛选
const openHospital = () => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesTrendBrand',
			defaultValue: {
				...toRaw(query),
			},
		});
		return;
	}
	hospital.value.show = true;
};
let hospitalConfirm = async (params, name) => {
	if (params.length === 0) {
		query.hospitalName = '全部医院';
		query.hospitalId = '';
	} else {
		query.hospitalName = name;
		query.hospitalId = params.join(',');
	}
	defaultHos.value = params;
	cascaderChange('hospital');
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-医院`, query.hospitalName);
};
const defaultHos = ref('');
const defaultHosCheckedAll = ref('');

watch(
	() => reportStore.globalFilterInfo.hospital,
	async (n) => {
		console.log(n);

		hospital.value.handleReset();
		defaultHos.value = n.checked; //默认选中
		defaultHosCheckedAll.value = n.isCheckAll;
		await nextTick(); // 确保 DOM 更新完成后再执行
		hospital.value.handleDefault(n);
		await new Promise((resolve) => setTimeout(resolve)); // 等待下一个事件循环
		hospital.value.confirm();
	}
);
let myEcharts = echarts;
let chart;
let color = ['#FFD400', '#FF9E5A', '#8A8A8A', '#FF001C', '#CCCCCC', '#86C669', '#3E86B1', '#6FB5E3', '#0052CC', '#277C00'];
const legend = ref([]);
const result = ref(null);
const lineList = ref([]);
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#marek-share-echarts-${echartsId}`), null, {
		devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio,
	});
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
let setOption = () => {
	let option = {
		legend: {
			show: false,
			data: legend.value,
			top: translateFont(5),
			left: translateFont(10),
			type: 'scroll',
			pageIconColor: '#0052CC',
			itemGap: translateFont(15),
			itemWidth: translateFont(15),
			itemHeight: translateFont(4),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
		},
		color,
		tooltip: {
			trigger: 'axis', // 'item', 'axis', 'none'
			position: 'top',
			confine: true,
			borderWidth: 0,
			backgroundColor: 'rgba(255,255,255,.9)',
			padding: 0,
			formatter: (value) => {
				let str = '';
				for (let item of value) {
					str += `<div><span class="dot" style="background-color:${item.color}"></span><span class="key">${item.seriesName}</span><span class="value">${formatNumbeWT(item.value)}K</span></div>`;
				}
				return `<div class="tooltip-saletrend">${str}</div>`;
			},
			extraCssText: 'z-index:9',
		},
		grid: {
			left: translateFont(10),
			right: translateFont(20),
			bottom: translateFont(10),
			top: translateFont(15),
			containLabel: true,
		},
		xAxis: {
			type: 'category',
			show: true,
			data: result.value.axisMonth,
			axisLabel: {
				show: true, // 是否显示坐标轴刻度标签。
				interval: spaces(result.value.axisMonth?.length), // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
				inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
				color: ' #6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
				fontSize: translateFont(10),
			},
			axisLine: {
				show: true, // 是否显示坐标轴轴线
				lineStyle: {
					color: '#fff',
					width: translateFont(1),
					type: 'solid',
				},
			},
			axisTick: {
				show: true,
			},
		},
		yAxis: {
			type: 'value',
			axisLabel: {
				show: true, // 是否显示坐标轴刻度标签。
				interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效。设置成 0 强制显示所有标签，如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
				inside: false, // 默认值false。true 表示坐标轴刻度标签朝内，false 表示坐标轴刻度标签朝外
				color: '#6B778C', // 刻度标签文字的颜色。不设置就默认取 axisLine.lineStyle.color，即与轴线颜色一样
				fontSize: translateFont(10),
				formatter: function (params) {
					return `${formatNumbeW(params)}`;
				},
			},
			splitLine: {
				show: true, // 隐藏 Y 轴横线
				lineStyle: {
					color: 'rgba(223,225,230,.5)',
				},
			},
			splitNumber: 4,
		},
		series: lineList.value,
	};
	chart.setOption(option, true);
};
let comStatus = ref('loading');
let zk = ref(false);
const tableData = ref([]);
const columns = ref([]);
let getData = async () => {
	comStatus.value = 'loading';

	let res = await getBrandSalesTrends({
		skuCode: query.brandId ? query.brandId.split(',') : [],
		territoryCode: query.personId ? query.personId.split(',') : [],
		hospCode: query.hospitalId ? query.hospitalId.split(',') : [],
		type: typeFilter.value,
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
		province: query.province || [],
		city: query.city || [],
		pathAddress: props.pathAddress || '',
		salesOrAmount: switchComRef.value.type === '数量' ? '1' : '0',
	});
	query.salesOrAmount = switchComRef.value.type;
	if (res.result.isExceededAuthority) {
		comStatus.value = 'isExceededAuthority';
		emit('summaryIng', { noSummary: true });
		return;
	}
	result.value = res.result;
	legend.value = res.result.product.map((ele) => {
		return { name: ele, active: true };
	});
	lineList.value = [];
	for (const item of res.result.data) {
		lineList.value.push({
			name: item.name,
			data: item.data.map((ele) => {
				return new Decimal(ele).toString();
			}),
			type: 'line',
			lineStyle: {
				width: translateFont(1),
			},
			symbolSize: translateFont(5),
		});
	}
	tableData.value = res.result.tableData.map((ele) => {
		for (let i in ele) {
			ele[i] = i !== 'product' ? formatNumbeWT(ele[i]) : ele[i];
		}
		return ele;
	});
	columns.value = res.result.tableDynamicColumns;
	// 合计数据
	for (let i in res.result.total) {
		res.result.total[i] = i !== 'product' ? formatNumbeWT(res.result.total[i]) : res.result.total[i];
	}
	lastResult.value = res.result.total;
	comStatus.value = res.result.data.length < 1 ? 'empty' : 'normal';
	reportStore.setReportInfo({
		name: '各品牌销售趋势分析报告',
		info: {
			monthText: `${query.startDate.slice(0, 4)}${query.startDate.slice(4, 6)} - ${query.endDate.slice(0, 4)}${query.endDate.slice(4, 6)}`,
			name: '各品牌销售趋势分析',
			people: query.personName,
			hospital: query.hospitalName,
			product: query.brandName,
			data: {
				tableData: tableData.value,
				lineList: lineList.value,
				result: result.value,
				legend: legend.value,
				columns: columns.value,
				message: res.result.message,
				lastResult: lastResult.value,
			},
		},
	});
	if (props.summary) {
		emit('summaryIng', { data: { ...res.result.tableData }, params: query });
	}
};
let legendClick = (item) => {
	let index = lineList.value.findIndex((ele) => ele.name === item.name);
	item.active = !item.active;
	if (!item.active) {
		console.log(lineList.value[index].data, '?');
		lineList.value[index].sourceData = deepClone(lineList.value[index].data);
		lineList.value[index].data = [];
	} else {
		lineList.value[index].data = lineList.value[index].sourceData;
	}
	setOption();
};
const emit = defineEmits(['focus', 'filterChange', 'summaryIng', 'openfilter']);
const focus = () => {
	emit('focus', {
		id: props.info.reportCd,
		name: props.info.nameCn,
		isLike: !props.info.isLike,
	});
};
let echartsId = generateRandomNumber();
let typeFilter = ref('sales');
let filterList = ref(['按销售', '按终端覆盖', '按终端新增']);
let currentIndex = ref('按销售');
let filterChange = async (item) => {
	currentIndex.value = item;
	if (item === '按终端覆盖') {
		typeFilter.value = 'TerminalCoverage';
	} else if (item === '按终端新增') {
		typeFilter.value = 'TerminalAdded';
	} else {
		typeFilter.value = 'sales';
	}
	await getData();
	chart.resize();
	setOption();
	proxy.$umeng('筛选', `${page}${props.info.nameCn}-展示方式`, item);
};
const switchComRef = ref(null);
const currencySwitching = async (type) => {
	if (props.inChat) {
		emit('openfilter', {
			comName: 'salesTrendBrand',

			defaultValue: {
				...toRaw(query),
			},
		});

		return;
	}
	// proxy.$umeng('点击', `${props.umengPath}-销量金额筛选器`, type);
	await getData();
	chart.resize();
	setOption();
};
const defaultProvinceCity = ref({});
onMounted(async () => {
	initFilter();
	//处理默认值
	if (props.defaultValue) {
		await useDefaultValue(props.defaultValue, {
			query,
			brand, // 传入 ref
			orgZ, // 传入 ref
			time,
			defaultSku, // 传入 ref
			defaultHos, // 传入 ref
			defaultPerson, // 传入 ref
			defaultProvinceCity, // 传入 ref
			switchComRef,
		});
	}
	//初始化级联
	initFilters();
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/salesTrendProduct.scss';
.marek-share {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.content {
		background-color: var(--pv-nav-bar-active);
		margin-top: 8px;
		border-radius: 5px;
	}
	&-legend {
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		padding: 4px 8px;
		max-height: 65px;
		overflow-y: auto;
		/* 设置纵向滚动条样式 */
		&::-webkit-scrollbar {
			width: 4px; /* 设置纵向滚动条宽度 */
		}

		&::-webkit-scrollbar-thumb {
			background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
			border-radius: 5px;
		}
		&-item {
			font-size: 11px;
			color: var(--pv-no-active-color);
			margin-right: 20px;
			margin-top: 4px;
			display: flex;
			align-items: center;
			.bar {
				height: 1.1px;
				width: 16px;
				background-color: var(--bar-color);
				margin-right: 8px;
				position: relative;
				&::before {
					position: absolute;
					content: '';
					width: 4px;
					height: 4px;
					border-radius: 50%;
					border: 1px solid var(--bar-color);
					background-color: #fff;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
				}
			}
		}
		.gray {
			filter: grayscale(100%);
			opacity: 0.5;
		}
	}
	&-echarts {
		width: 100%;
		height: 215px;
	}
	.tags {
		font-size: 9px;
		padding: 0 10px 8px;
		display: flex;
		&-item {
			background: #f2f5f8;
			border-radius: 0.533vw;
			color: #a9b2c2;
			margin-bottom: 1.333vw;
			margin-right: 1.333vw;
			text-align: center;
			padding: 3px;
		}
	}
}
.sale-table {
	background-color: #fff;
	margin-top: 8px;
	overflow: hidden;
	&-title {
		font-size: 12px;
		color: var(--pv-default-color);
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 6px;
		position: relative;
		.icon {
			font-size: 7px;
			position: absolute;
			right: 14px;
			transition: all 0.5s;
		}
		.down {
			transform: rotate(180deg);
		}
	}
	&-content {
		padding: 0 8px 0px;
	}
}
</style>
