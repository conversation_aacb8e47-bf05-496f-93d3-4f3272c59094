<template>
	<div class="ignore-visit-management">
		<div class="card-title">
			<span>{{ info.info.name }}</span>
		</div>
		<div class="ignore-visit-management-echarts" :id="`visit-management-echarts-${echartsId}`"></div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import Decimal from 'decimal.js';
import { generateRandomNumber, getYearMonthBefore, getCurrentYearMonth, isPCTrue } from '@/utils/index';

let props = defineProps(['info', 'hiddenStar']);
let myEcharts = echarts;
let chart;

let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#visit-management-echarts-${echartsId}`), { devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio });
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};

let setOption = () => {
	let ab = visitList.value.ab.map((ele) => {
		return new Decimal(ele.abDoctorAch === 'NaN' ? 0 : ele.abDoctorAch).toString();
	});
	let ach = visitList.value.ach.map((ele) => {
		return new Decimal(ele.visitAvh === 'NaN' ? 0 : ele.visitAvh).toString();
	});
	let option = {
		color: colors,
		grid: [
			{ top: 70, left: 10, right: 10, height: 65, show: true, borderColor: '#eee' },
			{ top: 145, left: 10, right: 10, bottom: 20, show: true, borderColor: '#eee' },
		],
		legend: {
			itemGap: 12,
			itemWidth: 9,
			itemHeight: 9,
			left: 'center',
			top: 10,
			textStyle: {
				color: '#6B778C',
				fontSize: 12,
			},
			data: [
				{
					name: 'A级客户拜访频次',
					icon: 'rect',
				},
				{
					name: 'B级客户拜访频次',
					icon: 'rect',
				},
				{
					name: 'C级客户拜访频次',
					icon: 'rect',
				},
				{
					name: 'AB客户覆盖率',
				},
				{
					name: '拜访计划执行率',
				},
			],
		},
		xAxis: [
			{
				type: 'category',
				data: ['A', 'B', 'C', 'D', 'E', 'a'],
				gridIndex: 0,
				axisLabel: { show: false },
				axisTick: { show: false },
				splitLine: {
					show: false,
				},
				axisLine: { lineStyle: { color: '#eee' } },
			},
			{ type: 'category', data: visitList.value.axisMonth, gridIndex: 1, axisLabel: { show: true, color: '#6B778C', fontSize: 9 }, axisTick: { show: false }, splitLine: { show: true, lineStyle: { color: '#eee' }, interval: 0 }, axisLine: { lineStyle: { color: '#eee' } } },
		],
		yAxis: [
			{
				type: 'value',
				gridIndex: 0,
				axisLabel: { show: false },
				axisTick: { show: false },
				splitLine: {
					show: false,
				},
			},
			{ type: 'value', gridIndex: 1, axisLabel: { show: false }, axisTick: { show: false }, splitLine: { show: false } },
		],
		series: [
			{
				name: 'A级客户拜访频次',
				type: 'bar',
				data: abcList.value[0],
				xAxisIndex: 1,
				yAxisIndex: 1,
				barWidth: 11,
				label: {
					show: true,
					position: 'top',
					distance: 1,
					color: '#172B4D',
					fontSize: 7,
				},
			},
			{
				name: 'B级客户拜访频次',
				type: 'bar',
				data: abcList.value[1],
				xAxisIndex: 1,
				yAxisIndex: 1,
				barWidth: 11,
				label: {
					show: true,
					position: 'top',
					distance: 1,
					color: '#172B4D',
					fontSize: 7,
				},
			},
			{
				name: 'C级客户拜访频次',
				type: 'bar',
				data: abcList.value[2],
				xAxisIndex: 1,
				yAxisIndex: 1,
				barWidth: 11,
				label: {
					show: true,
					position: 'top',
					distance: 1,
					color: '#172B4D',
					fontSize: 7,
				},
			},
			{
				name: 'AB客户覆盖率',
				type: 'line',
				lineStyle: {
					width: 1,
				},
				data: ab,
				xAxisIndex: 0,
				yAxisIndex: 0,
				label: {
					show: true,
					distance: 2,
					fontSize: 7,
					color: '#172B4D',
					formatter: ({ value }) => {
						return value + '%';
					},
				},
				symbolSize: 5,
			},
			{
				name: '拜访计划执行率',
				type: 'line',
				lineStyle: {
					width: 1,
				},
				data: ach,
				xAxisIndex: 0,
				yAxisIndex: 0,
				label: {
					show: true,
					distance: 2,
					fontSize: 7,
					color: '#172B4D',
					formatter: ({ value }) => {
						return value + '%';
					},
				},
				symbolSize: 5,
			},
		],
	};

	chart.setOption(option);
};

let visitList = ref([]);
let abcList = ref([]);

const emit = defineEmits(['focus']);

watch(
	() => props.info,
	(n) => {
		if (n.info.data) {
			visitList.value = n.info.data.visitList;
			abcList.value = n.info.data.abcList;
			nextTick(() => {
				chart ? setOption() : initChart();
			});
		}
	}
);
let colors = ['#0052CC', '#0379FF', '#79E1F2', '#FF991F', '#01A2BF'];
let echartsId = generateRandomNumber();

onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.ignore-visit-management {
	font-size: 10px;
	width: 100%;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background-color: var(--pv-nav-bar-active);
	border-radius: 0px 0px 4px 4px;

	.card-title {
		width: 100%;
		border-radius: 4px 4px 0 0;
		display: flex;
		align-items: center;
		padding: 6px 12px;
		background: #283b5c;
		height: 23px;
		span {
			font-size: 10px;
			color: #fff;
		}
	}
	&-echarts {
		width: 100%;
		height: calc(100% - 23px);
	}
}
</style>
