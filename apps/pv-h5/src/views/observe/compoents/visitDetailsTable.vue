<template>
	<div class="table">
		<vxe-table max-height="400" :data="props.detailList" :sort-config="{ trigger: 'cell' }" :scroll-y="{ enabled: false }" @scroll="onScroll" :loading-config="{ icon: 'vxe-icon-indicator roll', text: '加载中...' }" :loading="loading">
			<vxe-column field="visitTime" title="拜访日期" align="left" width="85px" fixed="left"></vxe-column>
			<vxe-column field="hospital" title="医院名称" align="left" sortable width="150px"></vxe-column>
			<vxe-column field="doctor" title="客户" align="left" sortable width="70px"></vxe-column>
			<vxe-column field="username" title="代表" align="left" sortable width="70px"></vxe-column>
			<vxe-column field="visitMethod" title="拜访方式" align="left" sortable width="70px"></vxe-column>
			<vxe-column field="doctorFeedback" title="医生反馈" align="left" width="100px"></vxe-column>
			<vxe-column field="visitTarget" title="拜访目标" align="left" width="120px"></vxe-column>
			<vxe-column field="visitContent" title="传递内容" align="left" width="120px"></vxe-column>
			<vxe-column field="productKeyInformation" title="产品关键信息" align="left" width="200px"></vxe-column>
		</vxe-table>
	</div>
</template>
<script setup>
import { translateFont } from '@/utils/index';

const props = defineProps(['detailList', 'loading']);
const emits = defineEmits(['pullData']);
let onScroll = (e) => {
	if (e.isY && e.scrollHeight - e.scrollTop - translateFont(4) <= e.bodyHeight) {
		console.log('触底了');
		emits('pullData');
	}
};
</script>
<style lang="scss" scoped>
.table {
	padding: 10px;
	&:deep(.vxe-header--column.col--left) {
		.vxe-cell {
			justify-content: flex-start !important;
			padding: 8px !important;
		}
	}

	&:deep(.vxe-body--column.col--left) {
		.vxe-cell {
			padding-left: 8px !important;
			padding-right: 8px !important;
		}
	}
}

@media screen and (min-width: 600px) {
	.table {
		padding: 10px !important;
		&:deep(.vxe-header--column.col--left) {
			.vxe-cell {
				padding: 8px !important;
			}
		}

		&:deep(.vxe-body--column.col--left) {
			.vxe-cell {
				padding-left: 8px !important;
				padding-right: 8px !important;
			}
		}
	}
}
</style>
