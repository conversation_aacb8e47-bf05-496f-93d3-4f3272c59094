<template>
	<div id="ap-visit-summary" class="visit-summary">
		<div class="top">
			<div class="card-title">
				<span>{{ pageTitle }}</span>
				<span v-if="!hiddenStar">
					<img class="icon" v-if="!info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collection.png" alt="" @click="focus" />
					<img class="icon" v-if="info.isLike && !info.reportCd.includes('-')" src="@/assets/img/collectioned.png" alt="" @click="focus" />
				</span>
			</div>
		</div>
		<div>
			<div class="q-box">
				<!-- 时间 -->
				<div @click="openTime" class="q-fileter-item" :class="isNotShowFilterProduct ? 'q-width-49' : 'q-width-100'">
					<span class="item-title ell">{{ query.startDate }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div v-if="isNotShowFilterProduct" @click="openOrg" class="q-width-49 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
			<div v-if="!isNotShowFilterProduct" class="q-box">
				<!-- 全部产品 -->
				<div @click="openBrand" class="q-width-49 q-fileter-item">
					<span class="item-title ell">{{ query.productName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
				<!-- 全部人员 -->
				<div @click="openOrg" class="q-width-49 q-fileter-item">
					<span class="item-title ell">{{ query.personName }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>
		</div>
		<loading v-show="comStatus === 'loading'"></loading>

		<div v-show="comStatus === 'normal'" class="visit-summary-echarts-card">
			<div class="item" v-for="(item, index) in visitList" :key="index">
				<div class="item-name">{{ item.doctorLevel }}级客户</div>
				<div class="item-fg">
					<span
						>覆盖：<span>{{ item.visitDoctor }} 人</span></span
					>
					<span class="item-fg-right"
						>平均每位客户被拜访<span>{{ item.visitAvg }}</span
						>次</span
					>
				</div>
				<div class="item-detail">
					<span v-for="(iten, idx) in item.list" :key="idx" :style="{ backgroundColor: colors[idx] }"> {{ iten.visitMethod + ': ' + iten.visitNum }}</span>
				</div>
				<div class="item-tip">
					实际拜访<span>{{ item.visitNum }}</span
					>次，计划拜访<span>{{ item.targetNum }}</span
					>次，计划执行力<span>{{ new Decimal(item.visitAch).toDecimalPlaces(0) + '%' }}</span>
				</div>
				<div class="item-echarts" :id="`item-echarts${echartsIds[index]}`"></div>
			</div>
		</div>
		<empty-com v-show="comStatus === 'empty'"></empty-com>
		<isExceededAuthority v-show="comStatus === 'isExceededAuthority'"></isExceededAuthority>

		<!-- 数据更新至 -->
		<dataUpdateBy v-if="showUpdateTime" :updateTime="props.info?.dataSyncTime" :isYMD="true" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		<!-- 品牌 -->
		<OneLevelRadio ref="brand" :list="brandData" :path="`${page}${pageTitle}-产品`" :defaultCheck="query.productId" @_confirm="brandConfirm"></OneLevelRadio>
		<!-- 人员架构 -->
		<OrgTreeList ref="orgZ" :listData="orgList" :defaultCheck="defaultPerson" :path="`${page}${pageTitle}`" @_confirm="orgConfirm"></OrgTreeList>
		<!-- 日期选择器 -->
		<DatePickerSingle ref="time" @_confirm="dateConfirm" :_startDate="query.startDate" :_endDate="query.endDate"></DatePickerSingle>
	</div>
</template>
<script setup>
import Decimal from 'decimal.js';
import * as echarts from 'echarts';
import { visitSummaryInfo } from '@/api/report';
import { generateRandomNumber, getFilterTime, getNameByPath, isPCTrue } from '@/utils/index';
import useReport from '@/store/modules/report.js';
let useReportStore = useReport();
import usefilterStore from '@/store/modules/filter';
let filterStore = usefilterStore();
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const relam = enterStore.enterInfo.id;
let props = defineProps(['info', 'hiddenStar']);
const { proxy } = getCurrentInstance();
const route = useRoute();
let page = getNameByPath(route.fullPath);
let showUpdateTime = ref(true);
let pageTitle = props.info?.nameCn || '拜访汇总分析';
if (route.fullPath.includes('/report')) {
	showUpdateTime.value = false;
}

// 元素
const isNotShowFilterProduct = computed(() => {
	return perStore.systemBtnList.some((ele) => ele.path === 'notShowFilterProduct');
});

const brandData = ref([]);
const productList = relam === 'muqiao' ? filterStore.productMQInfo : filterStore.productInfo;
for (const item of productList) {
	brandData.value.push({ id: item.productCode, name: item.productCn });
}
// 筛选条件
const query = reactive({
	startDate: useReportStore.bfhzTime.length > 0 ? getFilterTime(useReportStore.bfhzTime[0]) : getFilterTime(props.info?.startTime),
	endDate: useReportStore.bfhzTime.length > 0 ? getFilterTime(useReportStore.bfhzTime[1]) : getFilterTime(props.info?.endTime),
	productId: brandData.value.length > 0 ? brandData.value[0].id : '',
	productName: brandData.value.length > 0 ? brandData.value[0].name : '',
	personId: '',
	personName: '全部人员',
});
const defaultPerson = ref('');
// 有哪些筛选器 备用
const filterImp = ref([]);
// 初始化筛选条件和筛选数据
const initFilter = async () => {
	if (props.info.filterConfig) {
		const filterConfig = JSON.parse(props.info.filterConfig);
		filterImp.value = filterConfig.filterList;
		// 处理人员筛选
		if (filterImp.value.indexOf('org') > -1) {
			const result = useOrg(filterConfig.filterInfo);
			orgList.value = result.orgList;
			defaultPerson.value = result.defaultPerson;
			query.personId = result.personId;
			query.personName = result.personName;
		} else {
			orgList.value = filterStore.treeInfo;
		}
	} else {
		// 组织人员附属初始值
		orgList.value = filterStore.treeInfo;
	}
};
// 日期筛选
const time = ref(null);
const openTime = () => {
	time.value.show = true;
};
const dateConfirm = async ({ _startDate, _endDate }) => {
	query.startDate = _startDate;
	query.endDate = _endDate;
	await getData();
	initChart();
	proxy.$umeng('筛选', `${page}${pageTitle}-日期`, `${_startDate}-${_endDate}`);
};

// 品牌筛选
const brand = ref(null);
const openBrand = () => {
	brand.value.show = true;
};

let brandConfirm = async ({ id, name }) => {
	query.productName = name;
	query.productId = id;
	await getData();
	initChart();
	proxy.$umeng('筛选', `${page}${pageTitle}-产品`, name);
};

// 组织列表
const orgList = ref([]);
const orgZ = ref(null);
const openOrg = () => {
	orgZ.value.show = true;
};

const orgConfirm = async ({ userId: ids, userName: names }) => {
	defaultPerson.value = '';
	query.personId = ids.length > 0 ? ids.join(',') : '';
	query.personName = names.length > 0 ? names.join(',') : '全部人员';
	await getData();
	initChart();
	proxy.$umeng('筛选', `${page}${pageTitle}-人员`, query.personName);
};

let colors = ['#7568BB', '#FFC403', '#79E1F2', '#6B778C', '#7568BB', '#FFC403', '#79E1F2', '#F4F5F7'];
let echartsIds = ref([]);
let coverList = ref([]);
let myEcharts = echarts;
let charts = [];

let initChart = () => {
	for (let item of echartsIds.value) {
		charts.push(myEcharts.init(document.querySelector(`#item-echarts${item}`), { devicePixelRatio: isPCTrue() ? window.devicePixelRatio * 3 : window.devicePixelRatio }));
	}
	setChartData();
};

let setChartData = () => {
	for (let [index, chart] of charts.entries()) {
		chart.resize();
		setOption(index, chart);
		window.addEventListener('resize', function () {
			chart.resize();
		});
	}
};

let setOption = (index, chart) => {
	let option = {
		series: [
			{
				type: 'gauge',
				center: ['50%', '100%'],
				startAngle: 180,
				endAngle: 0,
				min: 0,
				max: 100,
				radius: '200%',
				itemStyle: {
					color: '#0052CC',
				},
				progress: {
					show: true,
					roundCap: false,
					width: 16,
				},
				axisLine: {
					roundCap: false,
					lineStyle: {
						width: 16,
					},
				},
				axisTick: {
					show: false,
				},
				pointer: {
					show: false,
				},
				splitLine: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				title: {
					show: false,
				},
				detail: {
					show: true,
					offsetCenter: [0, -7],
					valueAnimation: true,
					formatter: function (value) {
						return '{value|' + value.toFixed(0) + '%' + '}';
					},
					rich: {
						value: {
							fontSize: 12,
							color: '#172B4D',
						},
					},
				},
				data: [
					{
						value: coverList.value[index],
					},
				],
			},
		],
	};
	chart.setOption(option);
};

let visitList = ref([]);
let comStatus = ref('loading');

let getData = async () => {
	comStatus.value = 'loading';
	const json = {
		territoryCode: query.personId ? query.personId.split(',') : [],
		startLocalDate: `${query.startDate.slice(0, 4)}-${query.startDate.slice(4, 6)}-01`,
		endLocalDate: `${query.endDate.slice(0, 4)}-${query.endDate.slice(4, 6)}-01`,
	};
	if (!isNotShowFilterProduct.value) {
		json.productCode = query.productId ? query.productId.split(',') : [];
	}
	let res = await visitSummaryInfo(json);
	if (res.result.isExceededAuthority) return (comStatus.value = 'isExceededAuthority');
	visitList.value = res.result.data;
	// 筛选重新请求接口时，销毁chart、重置数据
	for (const item of charts) {
		item.dispose();
	}
	echartsIds.value = [];
	coverList.value = [];
	charts = [];
	for (const item of visitList.value) {
		echartsIds.value.push(generateRandomNumber());
		coverList.value.push(item.visitCoverAch);
	}
	comStatus.value = res.result.data.length < 1 ? 'empty' : 'normal';

	let year = query.endDate.substring(0, 4); // 截取年份部分
	let month = query.endDate.substring(4); // 截取月份部分
	let startLocalDate = year + '年' + month + '月'; // 拼接年份和月份信息
	let name = `${startLocalDate}${pageTitle}`;
	emit('filterChange', {
		name: 'processManagementAndAnalysisPPT1',
		info: { date: { startLocalDate }, name, people: query.personName, data: { visitList: visitList.value, message: res.result.message } },
	});
};

const emit = defineEmits(['focus', 'filterChange']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};

onMounted(async () => {
	initFilter();
	await getData();
	initChart();
});
onUnmounted(() => {
	for (let item of charts) {
		item && item.dispose();
	}
});
defineExpose({ query });
</script>
<style lang="scss" scoped>
@import '../../../style/pc/observe/visitSummary.scss';
.visit-summary {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}

	&-echarts {
		&-card {
			background-color: var(--pv-bgc);
			margin-top: 8px;
			border-radius: 5px;
			padding: 0 15px 15px;
			.item {
				padding-top: 23px;
				padding-bottom: 8px;
				border-bottom: 1px dashed #dfe1e6;
				position: relative;
				&-name {
					font-size: 15px;
					font-weight: bold;
					color: var(--pv-default-color);
				}
				&-fg {
					margin-top: 10px;
					font-size: 12px;
					color: var(--pv-no-active-color);
					display: flex;
					align-items: center;
					justify-content: space-between;
					& > span {
						span {
							color: var(--pv-default-color);
						}
					}
					&-right {
						font-size: 9px;
						span {
							color: var(--pv-tabbar-active) !important;
						}
					}
				}
				&-detail {
					display: flex;
					gap: 4px;
					margin-top: 6px;
					span {
						flex: 1;
						border-radius: 2px;
						padding: 3px 0px;
						font-size: 9px;
						color: #fff;
						text-align: center;
					}
				}
				&-tip {
					margin-top: 8px;
					color: var(--pv-default-color);
					font-size: 12px;
					text-align: right;
					span {
						color: var(--pv-tabbar-active);
					}
				}
				&-echarts {
					position: absolute;
					top: 15px;
					right: 0;
					width: 70px;
					height: 35px;
					&::after {
						content: '覆盖率';
						font-size: 9px;
						color: var(--pv-no-active-color);
						position: absolute;
						top: 12px;
						left: -28px;
					}
				}
			}
			.item:last-child {
				padding-bottom: 0;
				border: none;
			}
		}
	}
}
</style>
