<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon color="#172b4d" :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">
				<span>新建标签</span>
			</div>
			<!-- 右侧icon -->
			<div @click="debounceClick(1, 2, 3)" class="mc-header-icon">确定</div>
		</div>

		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<div class="addTag">
				<p>标签名称：</p>
				<input v-model="tagName" placeholder="请输入标签名称" @blur="blurFn" />
				<p>成员： {{ checkData.length }}</p>
				<van-cell title="添加医生" @click="goPage">
					<van-icon name="arrow" />
				</van-cell>
			</div>
		</div>
	</div>
</template>

<script setup>
import { showToast } from 'vant';
import { setTag, personalTag } from '@/api/doctor';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { debounceSenior } from '@/utils/index.js';
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const tagName = ref(localStorage.getItem('tagName') || '');
const personalNum = ref(null); // 表示有无重叠的标签名
const checkData = ref(JSON.parse(localStorage.getItem('checkList')) || []);
const personalList = ref([]);
const goBack = () => {
	proxy.$umeng('点击', `医生360-新建标签`, `返回标签列表`);
	router.go(-1);
	localStorage.removeItem('change');
	localStorage.removeItem('checkList');
	localStorage.removeItem('tagName');
};
const onClickRight = (a, b, c) => {
	console.log(a, b, c);
	proxy.$umeng('点击', `医生360-新建标签`, `确定新建`);
	if (tagName.value !== '') {
		setTag(checkData.value, { termValue: tagName.value })
			.then((res) => {
				if (res.code === 200) {
					localStorage.removeItem('checkList');
					tagName.value = '';
					router.push({ name: 'labelList' });
					localStorage.removeItem('change');
				}
			})
			.catch((err) => {
				console.log(err);
			});
	} else {
		showToast('请正确填写标签名称');
	}
};
const debounceClick = debounceSenior(onClickRight, 1000);
const goPage = () => {
	proxy.$umeng('点击', `医生360-新建标签`, `选择医生`);
	localStorage.setItem('tagName', tagName.value);
	router.push({ path: 'doctorAll' });
};
const personalListFn = () => {
	personalTag().then((res) => {
		personalList.value = res.result;
		personalNum.value = personalList.value.findIndex((item) => item.termValue === tagName.value);
	});
};
const blurFn = () => {
	const num = personalList.value.findIndex((item) => {
		return item === tagName.value;
	});
	if (num > -1) {
		tagName.value = '';
		showToast('该标签名称已存在');
	}
};
onMounted(() => {
	personalListFn();
});
</script>

<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	color: #172b4d;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		background-color: #ffffff;
		border-bottom: 1px solid #dfe1e6;
		.mc-header-icon {
			width: 30px;
			height: 30px;
			color: #0052cc;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.mc-header-name {
			span {
				font-size: 16px;
				color: #172b4d;
				font-weight: 600;
			}
		}
	}
	.mc-content {
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		.addTag {
			/* padding: 10px; */
			p {
				color: #172b4d;
				margin: 12px 15px;
				font-size: 15px;
			}

			input {
				border: 0;
				padding: 5px 20px;
				width: 100%;
				height: 45px;
				background-color: #f4f5f7;
			}

			&:deep(.van-cell) {
				background-color: #f4f5f7;
				color: #172b4d;
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 45px);
	}

	.mc-content__app {
		height: calc(100vh - 45px);
	}
}
</style>
