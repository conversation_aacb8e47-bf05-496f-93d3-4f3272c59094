<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="onClickLeft" class="mc-header-icon">
				<van-icon color="#172b4d" :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">
				<span>选择目标医生</span>
			</div>
			<!-- 右侧icon -->
			<div @click="onClickRight" class="mc-header-icon">确定</div>
		</div>
		<!-- 搜索 -->
		<Search :text="'输入医院、医生'" @changeFn="changeFn" @reset="resetFn"></Search>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<van-list class="list" v-model:loading="loading" :immediate-check="false" :finished="finished" finished-text="没有更多了" @load="onLoad">
				<docCard v-for="item in doctorList" :key="item.doctorId" :data="item" :showCheck="true" :isShowOperation="false" @change-check="changeCheck"></docCard>
			</van-list>
		</div>
	</div>
</template>

<script>
import { ref, getCurrentInstance, onMounted, reactive } from 'vue';
import { doctorLabelList, addDocTag, allDoctor } from '@/api/doctor';
import { useRoute, useRouter } from 'vue-router';
import docCard from './components/docCard.vue';
import Search from './components/docSearch.vue';
export default {
	components: {
		docCard,
		Search,
	},
	setup() {
		const { proxy } = getCurrentInstance();
		const route = useRoute();
		const router = useRouter();
		const doctorList = ref([]);
		const loading = ref(false);
		const finished = ref(false);
		const checkId = ref([]);
		const checkData = ref(JSON.parse(localStorage.getItem('checkList')) || []); // 医生回显
		const change = ref(localStorage.getItem('change'));
		const state = reactive({
			mobileTagType: route.query.type,
		});
		const query = ref({
			page: 0,
			size: 10,
			sort: '',
			keyWord: '',
			mobileTagType: route.query.type,
		});
		const query1 = ref({
			page: 0,
			size: 10,
			sort: '',
			keyWord: '',
		});
		function onClickLeft() {
			history.back();
		}
		function getDocList() {
			loading.value = true;
			doctorLabelList(query.value, { tagTermId: route.query.id })
				.then((res) => {
					loading.value = false;
					doctorList.value = doctorList.value.concat(res.result.content);
					if (doctorList.value.length === res.result.totalElements || res.result.content.length === 0) {
						finished.value = true;
					}
					doctorList.value.forEach((item) => {
						if (checkId.value.indexOf(item.doctorId) > -1) {
							item.check = true;
							// console.log(item)
						}
					});
				})
				.catch((err) => {
					loading.value = false;
					finished.value = true;
				});
		}
		function onLoad() {
			loading.value = true;
			if (localStorage.change === '0') {
				if (doctorList.value.length > 0) {
					query.value.page++;
					getDocList();
				} else {
					getDocList();
				}
			} else {
				if (doctorList.value.length > 0) {
					query1.value.page++;
					docData();
				} else {
					docData();
				}
			}
		}
		function changeCheck(val, data) {
			if (val) {
				checkId.value.push(data.doctorId);
				checkData.value.push(data.doctorId);
			} else {
				const result = checkId.value.findIndex((item) => item === data.doctorId);
				checkId.value.splice(result, 1);
				checkData.value.splice(result, 1);
			}
		}
		function onClickRight() {
			if (localStorage.change === '0') {
				const query = {
					addedValue: '123',
					mobileTagType: state.mobileTagType,
				};
				addDocTag(route.query.id, query, checkId.value).then((res) => {
					if (res.code === 200) {
						router.back(-1);
					}
				});
			} else {
				localStorage.setItem('checkList', JSON.stringify(checkData.value));
				router.back();
			}
		}
		function changeFn(text) {
			proxy.$umeng('搜索', `医生360-选择目标医生`, text);
			doctorList.value = [];
			query.value.keyWord = text;
			query1.value.keyWord = text;
			if (localStorage.change === '0') {
				getDocList();
			} else {
				docData();
			}
		}
		function resetFn() {
			proxy.$umeng('搜索', `医生360-选择目标医生`, '重置');
			doctorList.value = [];
			query.value.keyWord = '';
			query1.value.keyWord = '';
			finished.value = false;
			if (localStorage.change === '0') {
				getDocList();
			} else {
				docData();
			}
		}
		function docData() {
			loading.value = true;
			allDoctor(query1.value).then((res) => {
				loading.value = false;
				doctorList.value = doctorList.value.concat(res.result.content);
				doctorList.value.forEach((item) => {
					const n = checkData.value.findIndex((i) => {
						return i === item.doctorId;
					});
					if (n > -1) {
						item.check = true;
					}
				});
				if (doctorList.value.length === res.result.totalElements) {
					finished.value = true;
				}
			});
		}
		onMounted(() => {
			if (localStorage.change === '0') {
				getDocList();
			}
		});
		return {
			onClickLeft,
			query1,
			getDocList,
			onLoad,
			changeCheck,
			onClickRight,
			changeFn,
			resetFn,
			doctorList,
			loading,
			finished,
			checkId,
			query,
			proxy,
			docData,
			route,
			router,
		};
	},
	beforeRouteEnter(to, from, next) {
		if (from.name === 'addPersonalTag') {
			next((e) => {
				localStorage.setItem('change', '1');
				console.log(localStorage.change);
				e.docData();
			});
		} else {
			next((e) => {
				localStorage.setItem('change', '0');
				console.log(localStorage.change);
			});
		}
	},
};
</script>

<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	color: #172b4d;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		background-color: #ffffff;
		border-bottom: 1px solid #dfe1e6;
		.mc-header-icon {
			width: 30px;
			height: 30px;
			color: #0052cc;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.mc-header-name {
			span {
				font-size: 16px;
				color: #172b4d;
				font-weight: 600;
			}
		}
	}
	.mc-content {
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
	.mc-content__h5 {
		height: calc(100dvh - 96px);
	}

	.mc-content__app {
		height: calc(100vh - 96px);
	}
}
</style>
