<template>
	<nav-bar title="标签管理" left-text="" right-text="保存" left-arrow @click-left="onClickLeft" @click-right="onClickRight" />
	<div class="tagManage">
		<p>个人标签</p>
		<div class="personLabel">
			<van-badge v-for="item in selfTag" :key="item.id">
				<div class="child">{{ item.termValue }}</div>
				<template #content>
					<van-icon name="cross" class="badge-icon" @click="delectSelfTag(item.id, item)" />
				</template>
			</van-badge>
			<van-button icon="plus" type="primary" id="addPersonTag" @click="openPopup()">新建</van-button>
		</div>
		<p>系统标签</p>
		<van-loading color="#0052cc" vertical v-if="loading">加载中</van-loading>
		<div class="systemLabel" v-for="list in tagList" :key="list.id">
			<div class="title">
				<p>
					<svg-icon v-if="list.groupExplain" icon-class="fa-info-circle" @click="showExplain(list)"></svg-icon>
					{{ list.groupName }}
				</p>
				<span>（{{ list.groupType === 'MULTIPLE_CHOICE' ? '多选' : list.groupType === 'SINGLE_CHOICE' ? '单选' : '有阶段属性的' }}）</span>
			</div>
			<div class="content">
				<span @click="clickTag(list, item.id, index)" v-for="(item, index) in list.tagTermList" :key="item.id" :class="{ on: clickList.indexOf(item.id) > -1 }">{{ item.termValue }}</span>
			</div>
		</div>
	</div>
	<!-- 输入标签名称 -->
	<!-- eslint-disable-next-line vue/no-multiple-template-root -->
	<config-provider :theme-vars="themeVars">
		<van-popup v-model:show="addTagShow" round :style="{ width: '80%' }">
			<div class="showipone">
				<van-row class="tishi">
					<strong>添加个人标签</strong>
					<svg-icon icon-class="antOutline-close" @click="addTagShow = false"></svg-icon>
				</van-row>
				<van-field v-model="tagName" placeholder="标签名称" :error-message="errormessage" />
				<van-row class="bn">
					<van-button type="default" class="upbutton" @click="addTagShow = false">取消</van-button>
					<van-button type="primary" @click="addTagName()">确定</van-button>
				</van-row>
			</div>
		</van-popup>
		<van-popup v-model:show="explainShow" round :style="{ width: '80%' }">
			<div class="showipone">
				<van-row class="tishi">
					<strong>{{ Explain.groupName }}</strong>
					<svg-icon icon-class="antOutline-close" @click="explainShow = false"></svg-icon>
				</van-row>
				<div class="explain">{{ Explain.groupExplain }}</div>
				<van-row class="bn">
					<van-button type="primary" @click="explainShow = false">确定</van-button>
				</van-row>
			</div>
		</van-popup>
	</config-provider>
</template>

<script>
import { showFailToast, showLoadingToast, closeToast, NavBar, ConfigProvider } from 'vant';
import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
import { doctorPerTagInterface, doctorChooseTagInterface, upDoctorTag, upDoctorSelfTagInterface, getRelationInterface, doctorSelfTagInterface } from '@/api/doctor';
export default {
	components: {
		'config-provider': ConfigProvider,
		'nav-bar': NavBar,
	},
	setup() {
		const { proxy } = getCurrentInstance();
		const state = reactive({
			loading: true,
			tagList: [],
			clickList: [],
			selfTag: [],
			upquery: {},
			addTagShow: false,
			errormessage: '',
			tagName: '',
			themeVars: {
				popupRoundBorderRadius: '4px',
			},
			upSelfList: {
				tagTermIdList: [],
				termValueList: [],
			},
			Explain: {
				groupName: '',
				groupExplain: '',
			},
			explainShow: false,
		});
		// 页面删除个人标签
		const delectSelfTag = (id, val) => {
			proxy.$umeng('点击', '医生360-标签管理', `删除个人标签${val.termValue}`);
			state.selfTag = state.selfTag.filter((item) => {
				return item.id !== id;
			});
		};
		// 根据医生id查找医生有权限更改的标签
		const doctorPerTag = () => {
			const query = {
				tagType: 'CUSTOMER',
			};
			doctorPerTagInterface(proxy.$route.query.doctorId, query).then((res) => {
				if (res.code === 200) {
					res.result?.forEach((ele) => {
						if (ele.groupType === 'PHASE_PROPERTIES') {
							ele.tagTermList?.forEach((item, index) => {
								if (state.clickList.indexOf(item.id) > -1) {
									ele.checked = true;
									ele.checkIndex = index;
								}
							});
						}
					});
					state.tagList = res.result;
					getRelation();
					state.loading = false;
				}
			});
		};
		// 查询医生已打系统标签
		const doctorChooseTag = () => {
			const query = {
				tagType: 'CUSTOMER',
				// isAll: false
			};
			doctorChooseTagInterface(proxy.$route.query.doctorId, query).then((res) => {
				if (res.code === 200) {
					res.result.forEach((item) => {
						item.tagTermList.forEach((list) => {
							state.clickList.push(list.id);
						});
					});
					doctorPerTag();
				}
			});
		};
		// 查询医生已打个人标签
		const doctorSelfTag = () => {
			doctorSelfTagInterface(proxy.$route.query.doctorId).then((res) => {
				if (res.code === 200) {
					state.selfTag = res.result;
				}
			});
		};
		// 点击标签事件
		const clickTag = (list, id, index) => {
			// 单选
			if (list.groupType === 'PHASE_PROPERTIES') {
				// 有阶段属性有选中值，且不可回退，且当前点击小于选中值时，无反应
				if (list.checked && !list.isFallBack && index < list.checkIndex) {
					return;
				} else {
					// 否则正常单选
					list.tagTermList.forEach((element) => {
						if (state.clickList.indexOf(element.id) > -1 && element.id !== id) {
							state.clickList.splice(state.clickList.indexOf(element.id), 1);
						}
					});
				}
			}
			if (list.groupType === 'SINGLE_CHOICE') {
				// 单选，取消标签组中其他选中的标签值
				list.tagTermList.forEach((element) => {
					if (state.clickList.indexOf(element.id) > -1 && element.id !== id) {
						state.clickList.splice(state.clickList.indexOf(element.id), 1);
					}
				});
			}
			if (state.clickList.indexOf(id) > -1) {
				// 有阶段属性标签不可取消
				if (list.groupType !== 'PHASE_PROPERTIES') {
					state.clickList.splice(state.clickList.indexOf(id), 1);
					getRelation();
				}
			} else {
				state.clickList.push(id);
				getRelation();
			}
			// getRelation
		};
		// 获取关联组的关联标签
		const getRelation = () => {
			if (state.clickList.length < 1) {
				return;
			}
			const query = reactive({
				excludeGroupIdList: [],
				choiceTermIdList: state.clickList,
			});
			state.tagList?.forEach((item) => {
				if (item.isRelation) {
					query.excludeGroupIdList.push(item.id);
				}
			}),
				getRelationInterface(query).then((res) => {
					if (res.code === 200) {
						res.result.tagGroupVOList?.forEach((ele) => {
							if (ele.groupType === 'PHASE_PROPERTIES') {
								ele.tagTermList?.forEach((item, index) => {
									if (state.clickList.indexOf(item.id) > -1) {
										ele.checked = true;
										ele.checkIndex = index;
									}
								});
							}
							for (let i = 0; i < state.tagList.length; i++) {
								if (state.tagList[i].position <= ele.position && state.tagList[i + 1].position > ele.position && state.tagList[i].id !== ele.id) {
									state.tagList.splice(i + 1, 0, ele);
									break;
								}
							}
						});
						res.result.excludeGroupIdList?.forEach((item) => {
							for (let i = 0; i < state.tagList.length; i++) {
								if (state.tagList[i].id === item) {
									state.tagList[i].tagTermList.forEach((element) => {
										if (state.clickList.indexOf(element.id) > -1) {
											state.clickList.splice(state.clickList.indexOf(element.id), 1);
										}
									});
									state.tagList.splice(i, 1);
									break;
								}
							}
						});
						// upDoctorSelfTag()
					}
				});
		};
		// 保存医生添加标签
		const onClickRight = () => {
			showLoadingToast({
				duration: 0,
				forbidClick: true,
				message: '保存中...',
			});
			state.clickList.forEach((id) => {
				state.upquery[id] = '';
			});
			const tagType = {
				tagType: 'CUSTOMER',
			};
			upDoctorTag(proxy.$route.query.doctorId, state.upquery, tagType).then((res) => {
				if (res.code === 200) {
					upDoctorSelfTag();
				} else {
					showFailToast('保存失败');
				}
			});
		};
		// 修改个人标签
		const upDoctorSelfTag = () => {
			state.upSelfList.termValueList = [];
			state.upSelfList.tagTermIdList = [];
			state.selfTag.forEach((item) => {
				if (item.add) {
					state.upSelfList.termValueList.push(item.id);
				} else {
					state.upSelfList.tagTermIdList.push(item.id);
				}
			});
			upDoctorSelfTagInterface(proxy.$route.query.doctorId, state.upSelfList).then((res) => {
				closeToast();
				if (res.code !== 200) {
					showFailToast('保存失败');
				}
				proxy.$router.push({
					name: 'CustomerLabel',
					query: proxy.$route.query,
				});
			});
		};
		// 打开添加个人标签弹框
		const openPopup = () => {
			proxy.$umeng('点击', '医生360-标签管理', `新建个人标签`);
			state.errormessage = '';
			state.tagName = '';
			state.addTagShow = true;
		};
		// 添加个人标签
		const addTagName = () => {
			proxy.$umeng('点击', '医生360-标签管理', `添加个人标签`);
			if (state.tagName) {
				const tagNum = state.selfTag.filter((item) => {
					return state.tagName === item.termValue;
				});
				if (tagNum.length > 0) {
					state.errormessage = '标签名已存在';
				} else {
					state.errormessage = '';
					const list = {
						id: state.tagName,
						termValue: state.tagName,
						add: true,
					};
					state.selfTag.push(list);
					state.addTagShow = false;
				}
			} else {
				state.errormessage = '请输入标签名称';
			}
		};
		// 打开标签说明弹框
		const showExplain = (list) => {
			proxy.$umeng('点击', '医生360-标签管理', `系统标签${list.groupName}的说明弹框`);
			state.Explain.groupName = '';
			state.Explain.groupExplain = '';
			state.Explain.groupName = list.groupName;
			state.Explain.groupExplain = list.groupExplain;
			state.explainShow = true;
		};
		// 返回
		const onClickLeft = () => {
			proxy.$router.push({
				name: 'CustomerLabel',
				query: proxy.$route.query,
			});
		};
		onMounted(() => {
			doctorSelfTag();
			doctorChooseTag();
		});
		return {
			...toRefs(state),
			clickTag,
			onClickRight,
			onClickLeft,
			openPopup,
			addTagName,
			delectSelfTag,
			showExplain,
		};
	},
};
</script>

<style lang="scss" scoped>
:deep(.van-cell) {
	padding-left: 0px !important;
}
:deep(.van-nav-bar__text) {
	color: #0052cc !important;
}
:deep(.van-nav-bar__content) {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px;
	.van-nav-bar__title {
		font-size: 16px;
	}
}
.van-nav-bar {
	:deep(.van-icon) {
		color: #172b4d !important;
	}
}

.tagManage {
	:deep(.van-button) {
		font-size: 12px;
		padding: 5px 8px;
		height: inherit;
		border: 0;
		vertical-align: middle;
		background-color: #0052cc;
		color: #fff;
	}
	p {
		padding: 10px;
		color: #666;
	}
	.personLabel {
		padding: 10px 10px 15px 10px;
		background-color: #fff;
		.child {
			display: inline-block;
			font-size: 12px;
			margin-right: 8px;
			margin-bottom: 10px;
			padding: 5px 8px;
			border-radius: 2px;
			color: #ffffff;
			background-color: #0052cc;
		}
	}

	.systemLabel {
		padding: 0 10px;
		background-color: #fff;
		.title {
			display: flex;
			margin: 3px 0;
			align-items: center;
			p {
				color: #1b2b4b;
				font-weight: 600;
				font-size: 13px;
				padding-left: 0px;
			}
			span {
				font-size: 10px;
				color: #a9a9a9;
			}
		}

		.content {
			span {
				display: inline-block;
				background-color: #f3f1f1;
				color: #6c6c6c;
				padding: 4px 7px;
				margin: 5px 8px 5px 0;
				margin-bottom: 10px;
				font-size: 12px;
			}
			.on {
				background-color: #deebff;
				color: #0052cc;
			}
		}
	}
}

.active {
	background-color: #e7effd !important;
	color: #0052cc !important;
}

::v-deep(.van-badge--top-right) {
	right: 5px;
}

::v-deep(.van-badge) {
	padding: 0;
	line-height: normal;
	line-height: 10px;
	font-size: 10px;
	height: 14px;
	width: 14px;
	background-color: #9b9b9b;
}
:deep(.van-badge--top-right) {
	right: 12px;
}
:deep(.van-nav-bar__content) {
	box-shadow: 0px 2px 6px 0px rgb(217 215 215);
}
:deep(.van-popup--center.van-popup--round) {
	border-radius: 4px !important;
}
.showipone {
	padding: 20px;
	font-size: 14px;
	box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.3);
	.tishi {
		align-items: center;
		justify-content: space-between;
		display: flex;
		color: #172b4d;
		font-size: 18px;
		text-align: left;
		font-family: PingFangSC-regular;
	}
	:deep(.van-button) {
		height: 32px;
		margin-left: 8px;
	}
	:deep(.van-button--primary) {
		height: 32px;
		margin-left: 8px;
		background-color: #0052cc;
		border-color: #0052cc;
		color: #fff;
	}
	.upbutton {
		background-color: rgba(237, 237, 237, 100);
	}
	.bn {
		margin-top: 25px;
		justify-content: flex-end;
	}
	.explain {
		color: #172b4d;
		font-size: 14px;
		text-align: left;
		font-family: PingFangSC-regular;
		margin-top: 13px;
	}
}
</style>
