<template>
	<div class="box">
		<van-tabs title-active-color="#0052CC" title-inactive-color="#172B4D" v-model:active="active" @click-tab="onClickTab(val)">
			<van-tab title="概览">
				<overview></overview>
			</van-tab>
			<van-tab title="学术">
				<academic></academic>
			</van-tab>
			<van-tab title="科研">
				<scientific></scientific>
			</van-tab>
			<van-tab title="动态">
				<dynamic></dynamic>
			</van-tab>
			<van-tab title="合作">
				<cooperation></cooperation>
			</van-tab>
		</van-tabs>
	</div>
</template>
<script>
import { onMounted, reactive, toRefs } from 'vue';
import overview from './components/overview.vue';
import academic from './components/academic.vue';
import scientific from './components/scientific.vue';
import dynamic from './components/dynamic.vue';
import cooperation from './components/cooperation.vue';
import { onBeforeRouteLeave } from 'vue-router';
export default {
	name: 'Index',
	components: {
		overview,
		academic,
		scientific,
		dynamic,
		cooperation,
	},
	setup(props, context) {
		const state = reactive({
			active: 0,
		});
		onBeforeRouteLeave(() => {
			sessionStorage.removeItem('infoTab');
		});
		onMounted(() => {
			if (sessionStorage.getItem('infoTab')) {
				state.active = Number(sessionStorage.getItem('infoTab'));
			}
		});
		const onClickTab = (val) => {
			sessionStorage.setItem('infoTab', state.active);
		};
		return { ...toRefs(state), onClickTab };
	},
};
</script>
<style lang="scss" scoped>
.van-tabs {
	::v-deep(.van-tab) {
		font-size: 16px;
		font-weight: 600;
	}
	::v-deep(.van-tabs__wrap) {
		border: none;
		box-shadow: 1px 2px 3px 0px rgba(51, 98, 236, 0.1);
		border-radius: 0px 0px 6px 6px;
	}
	::v-deep(.van-tabs__line) {
		width: 12px;
		background: #3875c6;
		height: 3px;
		margin-bottom: 2px;
	}
}
.box {
	background-color: #ffffff;
}
</style>
