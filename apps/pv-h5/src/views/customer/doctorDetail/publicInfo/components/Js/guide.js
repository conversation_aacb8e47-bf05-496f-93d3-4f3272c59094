import { getCurrentInstance, reactive, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { guideInterface } from '@/api/doctor';
/**
 * @method
 * @param doctorId
 * @returns
 * @desc 临床指南信息相关
 */
export function guide(page, limit) {
	const { proxy } = getCurrentInstance();
	const route = useRoute();
	const item1 = ref(['1']);
	const state2 = reactive({
		page: 0,
		limit: 10,
		guideLabel: [],
		guideInfo: [],
	});
	const guideArticle = (startDate, endDate, diseaseLabel, planLabel, guideSourceArr) => {
		if (diseaseLabel) {
			if (diseaseLabel[0] === '全部') {
				diseaseLabel = '';
			}
		}
		if (planLabel) {
			if (planLabel[0] === '全部') {
				planLabel = '';
			}
		}
		const query = {
			doctorId: route.query.doctorId,
			page: state2.page,
			size: state2.limit,
			startDate: startDate,
			endDate: endDate,
			illness: diseaseLabel ? diseaseLabel.toString() : '',
			treatment: planLabel ? planLabel.toString() : '',
			source: guideSourceArr ? guideSourceArr.toString() : '',
		};
		guideInterface(query).then((res) => {
			console.log(res);
			state2.guideLabel = res.result.label;
			state2.guideInfo = res.result.data;
		});
	};
	onMounted(guideArticle);
	return {
		item1,
		state2,
		guideArticle,
	};
}
