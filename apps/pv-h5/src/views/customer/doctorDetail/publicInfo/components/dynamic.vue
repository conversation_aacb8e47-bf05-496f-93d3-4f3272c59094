<template>
	<van-notice-bar wrapable :scrollable="false" left-icon="volume-o" color="#F45C5C" background="#FFF2F2" text="点击时间标签切换查看相关信息" />
	<div class="LabelFilter">
		<van-row>
			<van-col :span="3"><span>时间:</span></van-col>
			<van-col :span="21">
				<span class="lable_filter_item" :class="flagTime == index ? 'timeActive' : ''" v-for="(item, index) in timeList" :key="index" @click="timeFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
			>
		</van-row>
	</div>
	<van-loading v-if="isLoading" color="#1989fa" />
	<!-- eslint-disable-next-line vue/no-multiple-template-root -->
	<div class="HCPDynamic" v-else>
		<HCPDynamic :dynamicList="dynamicList1"></HCPDynamic>
		<div class="more" @click="moreHandler" v-if="isMore">加载更多...</div>
		<div class="nomore" v-else>没有更多了</div>
	</div>
</template>
<script>
import { reactive, toRefs, ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { GMTToStr, dateBefore } from '@/utils/index.js';
import { dynamicTabelInterface, dynamicTimeLabelInterface } from '@/api/doctor';
import HCPDynamic from './components/HCPDynamic.vue';
export default {
	name: 'Index',
	components: { HCPDynamic },
	setup(props, context) {
		const state = reactive({
			timeList: [],
			dynamicList: [],
			dynamicList1: [],
			page: 1,
			isMore: true,
			isLoading: true,
		});
		const { proxy } = getCurrentInstance();
		const route = useRoute();
		// 时间筛选
		const flagTime = ref(0);
		const timeFilter = (item, index) => {
			flagTime.value = index;
			let startDate = '';
			let endDate = '';
			if (flagTime.value === 0) {
				// 1
			} else if (flagTime.value === 1) {
				endDate = GMTToStr(new Date());
				startDate = dateBefore(endDate, 1);
			} else if (flagTime.value === 2) {
				endDate = GMTToStr(new Date());
				startDate = dateBefore(endDate, 3);
			} else {
				endDate = GMTToStr(new Date());
				startDate = dateBefore(endDate, 5);
			}
			state.page = 1;
			state.isMore = true;
			state.isLoading = true;
			state.dynamicList = [];
			state.dynamicList1 = [];
			dynamicTabel(startDate, endDate);
		};
		const dynamicTimeLabel = () => {
			const query = {
				// doctorId: route.query.doctorId,
				// type: 1
			};
			dynamicTimeLabelInterface(query, 1, route.query.doctorId).then((res) => {
				const arr = [];
				for (const i of res.result) {
					const obj = {
						label: i.name,
						num: i.year,
					};
					arr.push(obj);
				}
				state.timeList = arr;
			});
		};
		const dynamicTabel = (startDate, endDate) => {
			const query = {
				doctorId: route.query.doctorId,
				startDate: startDate,
				endDate: endDate,
				type: 1,
				page: 0,
				size: 10000,
			};
			dynamicTabelInterface(query).then((res) => {
				state.dynamicList = res.result.content;
				state.isLoading = false;
				state.dynamicList1 = JSON.parse(JSON.stringify(state.dynamicList));
				if (state.dynamicList1.length > 5) {
					state.dynamicList1 = state.dynamicList1.slice(0, 5);
				}
				if (state.dynamicList1.length === state.dynamicList.length) {
					// 全部加载
					state.isMore = false;
				}
			});
		};
		const moreHandler = () => {
			let num = 0;
			state.dynamicList.forEach((item) => {
				if (num >= state.page * 5 && num < (state.page + 1) * 5) {
					state.dynamicList1.push(item);
				}
				num++;
			});
			if (state.dynamicList1.length === state.dynamicList.length) {
				// 全部加载
				state.isMore = false;
			}
			state.page++;
		};
		onMounted(dynamicTimeLabel);
		onMounted(dynamicTabel);
		return {
			...toRefs(state),
			flagTime,
			timeFilter,
			moreHandler,
		};
	},
};
</script>
<style lang="scss" scoped>
.van-notice-bar {
	margin-top: 12px;
	font-size: 12px !important;
	font-weight: 600;
}
.van-notice-bar--wrapable {
	padding: 8px;
}
::v-deep(.van-notice-bar__content) {
	line-height: 18px;
}
.LabelFilter {
	padding: 0px 15px;
	margin-top: 10px;
	color: #172b4d;
	.lable_filter_item {
		display: inline-block;
		padding: 0 5px;
		height: 20px;
		line-height: 20px;
		border-radius: 6px;
		text-align: center;
		font-size: 12px;
		color: #0052cc;
		background-color: rgba(51, 119, 255, 0.1);
		margin-left: 5px;
		margin-bottom: 5px;
	}
	.timeActive {
		background-color: rgba(51, 119, 255, 0.3);
	}
}
.HCPDynamic {
	padding: 10px 15px;
}
.more {
	text-align: center;
	margin-bottom: 30px;
	font-size: 13px;
	color: #0052cc;
}
.nomore {
	text-align: center;
	margin-bottom: 30px;
	font-size: 13px;
}
.van-loading {
	margin-top: 15px;
	text-align: center;
}
</style>
