<template>
	<div class="HCPLive_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">{{ titleInfo }}</span>
		</div>
		<slot></slot>
		<div>
			<div class="list" v-for="(item, index) in articleList" :key="index">
				<div class="Live_title" @click="link(item)">{{ item.liveTitle }}</div>
				<div class="Live_sorth">{{ item.keywords }}</div>
				<div class="Live_time">{{ item.liveTime }}</div>
			</div>
			<div class="more" @click="getMore" v-if="articleList.length < articleTotal">加载更多...</div>
			<div class="nomore" v-else>没有更多了</div>
		</div>
	</div>
</template>
<script>
import { reactive, ref, toRefs, toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		title: {
			type: String,
			require: true,
		},
		aliveInfo: {
			require: true,
		},
		total: {
			require: true,
		},
	},
	setup(props, context) {
		const titleInfo = ref(props.title);
		const state = reactive({
			info: ['1', '2'],
		});
		const articleList = toRef(props, 'aliveInfo');
		const articleTotal = toRef(props, 'total');
		const getMore = () => {
			context.emit('getlives');
		};
		const link = (val) => {
			window.location.href = val.url;
		};
		return {
			...toRefs(state),
			titleInfo,
			articleList,
			articleTotal,
			getMore,
			link,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPLive_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.list {
		margin: 00px 0;
		padding: 0 10px;
		.Live_title {
			color: #0052cc;
			line-height: 30px;
		}
		.Live_sorth {
			line-height: 20px;
		}
		.Live_time {
			line-height: 30px;
			color: #172b4d;
		}
	}
	.more {
		text-align: center;
		color: #0052cc;
	}
	.nomore {
		text-align: center;
		color: #172b4d;
	}
}
</style>
