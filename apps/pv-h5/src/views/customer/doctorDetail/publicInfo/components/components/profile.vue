<template>
	<div class="individual_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">个人简介</span>
		</div>
		<div v-if="flag">
			<div class="content van-multi-ellipsis--l3">
				{{ doctorInfo.personalResume }}
			</div>
			<div class="open" @click="openHandler" v-show="doctorInfo.personalResume && doctorInfo.personalResume.length > 76">展开</div>
		</div>
		<div v-else>
			<div class="content" id="contend">
				{{ doctorInfo.personalResume }}
			</div>
			<div class="close" @click="closeHandler" v-show="oo">收起</div>
		</div>
	</div>
</template>
<script>
import { reactive, toRefs, toRef } from 'vue';
// import { ref } from 'vue'
export default {
	name: 'Index',
	props: {
		HcpInfo: {
			type: Object,
			require: true,
		},
	},
	setup(props, context) {
		const state = reactive({
			flag: true,
			oo: true,
		});
		const openHandler = () => {
			state.flag = false;
		};
		const closeHandler = () => {
			state.flag = true;
		};
		const doctorInfo = toRef(props, 'HcpInfo');
		// onMounted(() => {
		//   setTimeout(() => {
		//     console.log(document.getElementById('contend').offsetHeight)
		//     if (document.getElementById('contend').offsetHeight > 90) {
		//       state.flag = true
		//     } else {
		//       state.oo = false
		//     }
		//   }, 200)
		// })
		return {
			...toRefs(state),
			openHandler,
			closeHandler,
			doctorInfo,
		};
	},
};
</script>
<style lang="scss" scoped>
.individual_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.content {
		height: auto;
		line-height: 1.5;
		padding: 0 12px;
		text-indent: 2em;
		margin-top: 10px;
		color: #172b4d;
	}
	.open {
		text-align: right;
		margin-top: 5px;
		color: #0052cc;
		margin-bottom: 5px;
	}
	.close {
		text-align: right;
		margin-top: 5px;
		color: #0052cc;
		margin-bottom: 5px;
	}
}
</style>
