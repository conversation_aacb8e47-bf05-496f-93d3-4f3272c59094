import { getCurrentInstance, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { academicArticleInterface, academicTabsInterface } from '@/api/doctor';
/**
 * @method
 * @param doctorId
 * @returns
 * @desc 学术文章信息相关
 */
export function article(page, limit) {
	const { proxy } = getCurrentInstance();
	const route = useRoute();
	const state1 = reactive({
		articleInfo: [],
		articleAuthor: [],
		articleSource: [],
		timeList: [],
		illList: [],
		treatmentList: [],
		page: 0,
		limit: 10,
		isHaveMore: true,
		articleTotal: '',
	});
	// 获取学术文章信息列表
	const academicArticle = (startDate, endDate, diseaseLabel, planLabel, authorLabel, articleSourceLabel) => {
		if (diseaseLabel) {
			if (diseaseLabel[0] === '全部') {
				diseaseLabel = '';
			}
		}
		if (planLabel) {
			if (planLabel[0] === '全部') {
				planLabel = '';
			}
		}
		const query = {
			doctorId: route.query.doctorId,
			page: state1.page,
			size: state1.limit,
			startDate: startDate,
			endDate: endDate,
			illness: diseaseLabel ? diseaseLabel.toString() : '',
			treatment: planLabel ? planLabel.toString() : '',
			author: authorLabel ? authorLabel.toString() : '',
			source: articleSourceLabel ? articleSourceLabel.toString() : '',
		};
		academicArticleInterface(query).then((res) => {
			state1.articleTotal = res.result.total;
			state1.articleInfo = state1.articleInfo.concat(res.result.data);
			state1.articleAuthor = res.result.authorLabel;
			state1.articleSource = res.result.sourceLabel;
		});
	};
	// 查询学术信息标签
	const academicTabs = () => {
		const query = {
			// doctorId: route.query.doctorId
		};
		academicTabsInterface(query, route.query.doctorId).then((res) => {
			console.log(res);
			// for (const i in res.data) {
			//   if (i.indexOf('e') === -1) {
			//     // 时间
			//     state1.timeList.push(i + '(' + res.data[i] + ')')
			//   }
			// }
			let arr = res.result.filter((ele) => ele.name === '全部')[0];
			console.log(arr);
			for (let i in arr) {
				console.log(i);
				if (arr[i].label) {
					if (arr[i].label === '全部') {
						state1.timeList.unshift(arr[i].label + '(' + arr[i].num + ')');
					} else {
						state1.timeList.push(arr[i].label + '(' + arr[i].num + ')');
					}
				}
			}
			state1.illList = arr.illnessList;
			state1.treatmentList = arr.treatmentList;
		});
	};
	// 加载更多
	const getMoreArticles = () => {
		state1.page++;
		academicArticle();
	};
	onMounted(academicArticle);
	onMounted(academicTabs);
	return {
		state1,
		academicArticle,
		getMoreArticles,
	};
}
