<template>
	<div class="HCPField_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">擅长领域</span>
		</div>
		<div v-if="flag">
			<div class="content van-ellipsis" ref="content">
				{{ doctorInfo.professionalDirection }}
			</div>
			<div class="open" @click="openHandler" v-show="doctorInfo.professionalDirection && doctorInfo.professionalDirection.length > 24">展开</div>
		</div>
		<div v-else>
			<div class="content" ref="content">
				{{ doctorInfo.professionalDirection }}
			</div>
			<div class="close" @click="closeHandler">收起</div>
		</div>
	</div>
</template>
<script>
import { reactive, toRefs, toRef } from 'vue';
// import { ref } from 'vue'
export default {
	name: 'Index',
	props: {
		HcpInfo: {
			type: Object,
			require: true,
		},
	},
	setup(props, context) {
		const state = reactive({
			flag: true,
		});
		const openHandler = () => {
			state.flag = false;
		};
		const closeHandler = () => {
			state.flag = true;
		};
		const doctorInfo = toRef(props, 'HcpInfo');
		return {
			...toRefs(state),
			openHandler,
			closeHandler,
			doctorInfo,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPField_components {
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.jiejue {
		display: flex;
		align-items: center;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.content {
		line-height: 1.5;
		text-indent: 2em;
		margin-top: 10px;
		color: #172b4d;
	}
	.open {
		text-align: right;
		margin-top: 5px;
		color: #0052cc;
		margin-bottom: 5px;
	}
	.close {
		text-align: right;
		margin-top: 5px;
		color: #0052cc;
		margin-bottom: 5px;
	}
}
</style>
