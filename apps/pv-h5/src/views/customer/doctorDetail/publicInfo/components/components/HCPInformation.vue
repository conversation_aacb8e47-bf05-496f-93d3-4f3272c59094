<template>
	<div class="HCPInformation_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">{{ titleInfo }}</span>
		</div>
		<slot></slot>
		<div>
			<div class="list" v-for="(item, index) in articleList" :key="index">
				<div class="article" @click="link(item)">{{ item.title }}</div>
				<van-row>
					<van-col :span="6" class="publishDate">{{ item.publishDate }}</van-col>
					<van-col :span="6">{{ item.authorSequence }}</van-col>
					<van-col :span="12">{{ item.publishPeriodical }}</van-col>
				</van-row>
				<p>被引用：{{ item.quotedTimes }}次</p>
			</div>
			<div class="more" @click="getMore" v-if="articleList.length < articleTotal">加载更多...</div>
			<div class="nomore" v-else>没有更多了</div>
		</div>
	</div>
	<!-- <pop-up></pop-up> -->
</template>
<script>
import { ref, toRef } from 'vue';
// import popUp from '../leavePopup/leavePopup'
export default {
	name: 'Index',
	components: {
		// popUp
	},
	props: {
		title: {
			type: String,
			require: true,
		},
		articleInfo: {
			require: true,
		},
		total: {
			require: true,
		},
	},
	setup(props, context) {
		const titleInfo = ref(props.title);
		const flagTime = ref(0);
		const timeFilter = (item, index) => {
			flagTime.value = index;
		};
		const flagSource = ref(0);
		const sourceFilter = (item, index) => {
			flagSource.value = index;
		};
		const articleList = toRef(props, 'articleInfo');
		const articleTotal = toRef(props, 'total');
		const getMore = () => {
			context.emit('getArticles');
		};
		const link = (val) => {
			window.location.href = val.url;
		};
		return {
			titleInfo,
			flagTime,
			timeFilter,
			flagSource,
			sourceFilter,
			articleList,
			getMore,
			articleTotal,
			link,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPInformation_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.LabelFilter {
		color: #172b4d;
		// padding: 0px 25px;
		margin-top: 10px;
		.lable_filter_item {
			display: inline-block;
			padding: 0 5px;
			height: 20px;
			line-height: 20px;
			border-radius: 6px;
			text-align: center;
			font-size: 12px;
			color: #0052cc;
			background-color: rgba(51, 119, 255, 0.1);
			margin-left: 5px;
			margin-bottom: 5px;
		}
		.authorActive {
			background-color: rgba(51, 119, 255, 0.3);
		}
		.source {
			background-color: rgba(112, 201, 26, 0.1);
			color: rgba(108, 198, 22, 100);
		}
		.sourceActive {
			background-color: rgba(112, 201, 26, 0.3);
			color: rgba(108, 198, 22, 100);
		}
	}
	.list {
		margin: 10px 0;
		padding: 0 10px;
		.article {
			color: rgba(19, 118, 220, 100);
			margin-bottom: 3px;
		}
		// .van-col--6 {
		//   display: flex;
		//   align-items: flex-end;
		// }
		.publishDate {
			position: relative;
			top: 3px;
		}
	}
	.more {
		text-align: center;
		color: #0052cc;
	}
	.nomore {
		text-align: center;
		color: #172b4d;
	}
}
</style>
