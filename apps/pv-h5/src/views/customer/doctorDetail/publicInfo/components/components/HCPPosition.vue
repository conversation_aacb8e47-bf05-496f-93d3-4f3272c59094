<template>
	<div class="HCPPosition_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">行政职务</span>
		</div>
		<div>
			<van-row class="list" v-for="(item, index) in info" :key="index">
				<van-col :span="24">{{ item }} </van-col>
			</van-row>
		</div>
	</div>
</template>
<script>
import { toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		administrativePosition: {
			require: true,
		},
	},
	setup(props, context) {
		const info = toRef(props, 'administrativePosition');
		return {
			info,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPPosition_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.list {
		margin: 10px 0;
		padding: 0 10px;
		color: #172b4d;
	}
}
</style>
