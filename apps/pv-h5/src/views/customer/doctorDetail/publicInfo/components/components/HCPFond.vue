<template>
	<div class="HCPFond_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">{{ titleInfo }}</span>
		</div>
		<slot></slot>
		<div>
			<div class="list" v-for="(item, index) in articleList" :key="index">
				<div class="Fond_title" @click="link(item)">
					<span>{{ item.subjectCode }}</span>
					<span style="margin-left: 3vw">{{ item.projectName }}</span>
				</div>
				<div class="Fond_time">
					<span>项目类别:</span>
					<span>{{ item.projectCategory }}</span>
					<span>资助金额:</span>
					<span>{{ item.fundingNumber }}</span>
				</div>
				<div class="Fond_time">
					<span>{{ item.applicationTime }}</span>
					<span>{{ item.companyName }}</span>
					<span>{{ item.role }}</span>
				</div>
				<div class="Fond_border" v-show="index !== articleList.length - 1"></div>
			</div>
			<div class="more" @click="getMore" v-if="articleList.length < articleTotal">加载更多...</div>
			<div class="nomore" v-else>没有更多了</div>
		</div>
	</div>
</template>
<script>
import { ref, toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		title: {
			type: String,
			require: true,
		},
		fundInfo: {
			require: true,
		},
		total: {
			require: true,
		},
	},
	setup(props, context) {
		const titleInfo = ref(props.title);
		const articleList = toRef(props, 'fundInfo');
		const articleTotal = toRef(props, 'total');
		const getMore = () => {
			context.emit('getfunds');
		};
		const link = (val) => {
			window.location.href = val.naturefundUrl;
		};
		return {
			titleInfo,
			articleList,
			articleTotal,
			getMore,
			link,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPFond_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.list {
		margin: 5px 0;
		padding: 0 10px;
		.Fond_title {
			color: #0052cc;
			line-height: 25px;
		}
		.Fond_time {
			line-height: 30px;
			font-size: 13px;
			span {
				margin-right: 10px;
			}
		}
		.Fond_border {
			margin: 10px 0;
			border: 1px dashed rgba(51, 98, 236, 0.15);
		}
	}
	.more {
		text-align: center;
		color: #0052cc;
	}
	.nomore {
		text-align: center;
		color: #172b4d;
	}
}
</style>
