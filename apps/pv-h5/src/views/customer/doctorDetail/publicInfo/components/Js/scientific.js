import { getCurrentInstance, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { projectPatentInterface, projectResearchInterface, projectFundInterface } from '@/api/doctor';
/**
 * @method
 * @param doctorId
 * @returns
 * @desc 科研相关
 */
export function scientific(page, limit) {
	const { proxy } = getCurrentInstance();
	const route = useRoute();
	const scient = reactive({
		page: 0,
		page1: 0,
		page2: 0,
		patentInfo: [],
		patentTotal: '',
		researchInfo: [],
		researchTotal: '',
		researchRoleLabel: [],
		researchStageLabel: [],
		researchTypeLabel: [],
		fundInfo: [],
		fundTotal: '',
		fundTypeLabel: [],
	});
	/**
	 * @method
	 * @param  query
	 * @returns
	 * @desc 临床课题研究情况
	 */
	const projectResearch = (startDate, endDate, role, phase, fundType) => {
		const query = {
			doctorId: route.query.doctorId,
			page: scient.page,
			size: 10,
			startDate: startDate,
			endDate: endDate,
			role: role ? role.toString() : '',
			phase: phase ? phase.toString() : '',
			fundType: fundType ? fundType.toString() : '',
		};
		projectResearchInterface(query).then((res) => {
			console.log(res);
			scient.researchInfo = scient.researchInfo.concat(res.result.data);
			scient.researchTotal = res.result.total;
			scient.researchRoleLabel = res.result.roleLabel;
			scient.researchStageLabel = res.result.stageLabel;
			scient.researchTypeLabel = res.result.typeLabel;
		});
	};
	const getResearches = () => {
		scient.page++;
		projectResearch();
	};
	/**
	 * @method
	 * @param  query
	 * @returns
	 * @desc 国家自然科学基金项目情况
	 */
	const projectFund = (startDate, endDate, flagFondTypeArr) => {
		const query = {
			doctorId: route.query.doctorId,
			page: scient.page1,
			size: 10,
			startDate: startDate,
			endDate: endDate,
			fundType: flagFondTypeArr ? flagFondTypeArr.toString() : '',
		};
		projectFundInterface(query).then((res) => {
			scient.fundInfo = scient.fundInfo.concat(res.result.data);
			scient.fundTotal = res.result.total;
			scient.fundTypeLabel = res.result.fundLabel;
		});
	};
	const getfunds = () => {
		scient.page1++;
		projectFund();
	};
	/**
	 * @method
	 * @param  query
	 * @returns
	 * @desc 发明专利项目情况
	 */
	const projectPatent = (startDate, endDate) => {
		const query = {
			doctorId: route.query.doctorId,
			page: scient.page2,
			size: 10,
			startDate: startDate,
			endDate: endDate,
		};
		projectPatentInterface(query).then((res) => {
			scient.patentInfo = scient.patentInfo.concat(res.result.doctorPatents);
			scient.patentTotal = res.result.total;
		});
	};
	const getPatents = () => {
		scient.page2++;
		projectPatent();
	};
	onMounted(projectResearch);
	onMounted(projectFund);
	onMounted(projectPatent);
	return {
		scient,
		projectResearch,
		getResearches,
		projectFund,
		getfunds,
		getPatents,
		projectPatent,
	};
}
