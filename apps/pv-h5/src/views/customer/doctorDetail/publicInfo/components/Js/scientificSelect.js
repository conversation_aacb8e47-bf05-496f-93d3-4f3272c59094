import { ref } from 'vue';
/**
 * @method
 * @param doctorId
 * @returnspasssword1qaz2wsx
 * @desc 科研筛选相关相关
 */
export function scientficSelect(page, limit) {
	// 时间筛选
	const flagTime = ref(0);
	const timeFilter = (item, index) => {
		flagTime.value = index;
	};
	// 临床课题研究情况角色筛选
	const flagRoleArr = ref([]);
	const roleFilter = (item, index) => {
		if (flagRoleArr.value.includes(item.label)) {
			// 已经选中 需要删除
			flagRoleArr.value = flagRoleArr.value.filter((val) => {
				return val !== item.label;
			});
		} else {
			// 选中
			flagRoleArr.value.push(item.label);
		}
	};
	const flagPhaseArr = ref([]);
	const phaseFilter = (item, index) => {
		if (flagPhaseArr.value.includes(item.label)) {
			// 已经选中 需要删除
			flagPhaseArr.value = flagPhaseArr.value.filter((val) => {
				return val !== item.label;
			});
		} else {
			// 选中
			flagPhaseArr.value.push(item.label);
		}
	};
	const flagTypeArr = ref([]);
	const typeFilter = (item, index) => {
		if (flagTypeArr.value.includes(item.label)) {
			// 已经选中 需要删除
			flagTypeArr.value = flagTypeArr.value.filter((val) => {
				return val !== item.label;
			});
		} else {
			// 选中
			flagTypeArr.value.push(item.label);
		}
	};
	// 基金类型筛选
	const flagFondTypeArr = ref([]);
	const fondTypeFilter = (item, index) => {
		if (flagFondTypeArr.value.includes(item.label)) {
			// 已经选中 需要删除
			flagFondTypeArr.value = flagFondTypeArr.value.filter((val) => {
				return val !== item.label;
			});
		} else {
			// 选中
			flagFondTypeArr.value.push(item.label);
		}
	};
	return {
		flagTime,
		timeFilter,
		flagRoleArr,
		roleFilter,
		flagPhaseArr,
		phaseFilter,
		flagTypeArr,
		typeFilter,
		flagFondTypeArr,
		fondTypeFilter,
	};
}
