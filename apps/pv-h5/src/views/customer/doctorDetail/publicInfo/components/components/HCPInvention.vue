<template>
	<div class="HCPInvention_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">{{ titleInfo }}</span>
		</div>
		<slot></slot>
		<div>
			<div class="list" v-for="(item, index) in articleList" :key="index">
				<div class="Invention_title">
					<span>{{ item.patentId }}</span>
					<span>{{ item.patentName }}</span>
				</div>
				<div>{{ item.date }}</div>
				<div class="Invention_content" v-if="item.isOpen">{{ item.digest }}</div>
				<div class="Invention_content van-multi-ellipsis--l3" v-else>
					{{ item.digest }}
				</div>
				<div class="open" @click="openHandler(item)" v-if="!item.isOpen">展开</div>
				<div class="open" @click="closeHandler(item)" v-else>收起</div>
				<div class="Invention_border"></div>
				<!-- <div class="Invention_border" v-show="index !== info.length - 1"></div> -->
			</div>
		</div>
		<div class="more" @click="getMore" v-if="articleList.length < articleTotal">加载更多...</div>
		<div class="nomore" v-else>没有更多了</div>
	</div>
</template>
<script>
import { ref, toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		title: {
			type: String,
			require: true,
		},
		patentInfo: {
			require: true,
		},
		total: {
			require: true,
		},
	},
	setup(props, context) {
		const titleInfo = ref(props.title);
		const openHandler = (item) => {
			item.isOpen = true;
		};
		const closeHandler = (item) => {
			item.isOpen = false;
		};
		const articleList = toRef(props, 'patentInfo');
		const articleTotal = toRef(props, 'total');
		const getMore = () => {
			context.emit('getPatents');
		};
		return {
			titleInfo,
			openHandler,
			closeHandler,
			articleList,
			articleTotal,
			getMore,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPInvention_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.Invention_content {
		line-height: 25px;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.list {
		margin: 10px 0;
		padding: 0 10px;
		.Invention_title {
			// color: #1376dc;
			line-height: 25px;
		}
		.Invention_time {
			line-height: 30px;
			font-size: 13px;
			span {
				margin-right: 10px;
			}
		}
		.Invention_border {
			margin: 10px 0;
			border: 1px dashed rgba(51, 98, 236, 0.15);
		}
	}
	.open {
		color: #0052cc;
		text-align: right;
		margin-top: 5px;
	}
}
.more {
	text-align: center;
	margin-bottom: 10px;
	color: #0052cc;
}
.nomore {
	text-align: center;
	color: #172b4d;
}
</style>
