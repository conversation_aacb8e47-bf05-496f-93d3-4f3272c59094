<template>
	<div class="basicView">
		<basic-view :HcpInfo="doctorInfo"></basic-view>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<profile :HcpInfo="doctorInfo"></profile>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPField :HcpInfo="doctorInfo"></HCPField>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPPractice :HcpInfo="doctorInfo"></HCPPractice>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPEducation :HcpInfo="educationInfo"></HCPEducation>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPExperience :HcpInfo="experienceInfo"></HCPExperience>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPPosition :administrativePosition="administrativePosition"></HCPPosition>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPAcademic :mainAcademicTitle="mainAcademicTitle"></HCPAcademic>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPAssociation :association="association"></HCPAssociation>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPJournal :academicJournal="academicJournal"></HCPJournal>
	</div>
	<!-- 撑起底部高度 -->
	<!-- eslint-disable-next-line vue/no-multiple-template-root -->
	<div class="bottom"></div>
</template>
<script>
import { computed, watch, ref, reactive, toRefs } from 'vue';
import usefilterStore from '@/store/modules/filter';
const filterStore = usefilterStore();
import basicView from './components/basicView.vue';
import profile from './components/profile.vue';
import HCPField from './components/HCPField.vue';
import HCPPractice from './components/HCPPractice.vue';
import HCPEducation from './components/HCPEducation.vue';
import HCPExperience from './components/HCPExperience.vue';
import HCPPosition from './components/HCPPosition.vue';
import HCPAcademic from './components/HCPAcademic.vue';
import HCPAssociation from './components/HCPAssociation.vue';
import HCPJournal from './components/HCPJournal.vue';
// import { useRouter, useRoute } from 'vue-router'
export default {
	name: 'Index',
	components: {
		basicView: basicView,
		profile: profile,
		HCPField: HCPField,
		HCPPractice: HCPPractice,
		HCPEducation: HCPEducation,
		HCPExperience: HCPExperience,
		HCPPosition: HCPPosition,
		HCPAcademic: HCPAcademic,
		HCPAssociation: HCPAssociation,
		HCPJournal: HCPJournal,
	},
	setup(props, context) {
		// provide('HcpInfo', doctorInfo)
		const state = reactive({
			administrativePosition: '',
			mainAcademicTitle: '',
			association: '',
			academicJournal: '',
		});
		const educationInfo = ref('');
		const experienceInfo = ref('');
		const doctorInfo = ref(filterStore.doctorInfo);
		watch(
			doctorInfo,
			(newValue, oldValue) => {
				educationInfo.value = doctorInfo.value.educationInfo ? doctorInfo.value.educationInfo.replace(/-#/g, '').replace(/#-/g, '').replace(/#/g, ' ').split(';') : [];
				experienceInfo.value = doctorInfo.value.workInfo ? doctorInfo.value.workInfo.replace(/-#/g, '').replace(/#-/g, '').replace(/#/g, ' ').split(';') : [];
				state.administrativePosition = doctorInfo.value.administrativePosition ? doctorInfo.value.administrativePosition.split('/') : [];
				// 学术头衔数据处理
				state.mainAcademicTitle = doctorInfo.value.mainAcademicTitle ? doctorInfo.value.mainAcademicTitle.split('/') : '';
				// 协会学会及任职数据处理
				if (doctorInfo.value.belongAssociation && doctorInfo.value.otherAssociation) {
					state.association = doctorInfo.value.belongAssociation + ';' + doctorInfo.value.otherAssociation;
				} else if (doctorInfo.value.belongAssociation) {
					state.association = doctorInfo.value.belongAssociation;
				} else if (doctorInfo.value.otherAssociation) {
					state.association = doctorInfo.value.otherAssociation;
				}
				state.association = state.association.split(';');
				// 学术期刊及任职数据处理
				if (doctorInfo.value.academicJournal) {
					state.academicJournal = doctorInfo.value.academicJournal.split(';');
				}
			},
			{ immediate: true }
		);
		return {
			...toRefs(state),
			doctorInfo,
			educationInfo,
			experienceInfo,
		};
	},
};
</script>
<style lang="scss" scoped>
.basicView {
	padding: 15px;
	padding-bottom: 10px;
}
.border {
	padding: 0 15px;
	.line {
		border-bottom: 1px solid #dfe1e6;
		opacity: 0.8;
	}
}
.bottom {
	height: 30px;
}
</style>
