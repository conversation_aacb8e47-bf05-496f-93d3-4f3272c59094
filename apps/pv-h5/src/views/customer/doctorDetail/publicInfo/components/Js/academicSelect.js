import { ref } from 'vue';
/**
 * @method
 * @param doctorId
 * @returnspasssword1qaz2wsx
 * @desc 学术文章信息相关
 */
export function academicSelect(page, limit) {
	// 时间筛选
	const flagTime = ref(0);
	const timeFilter = (item, index) => {
		flagTime.value = index;
	};
	// 疾病筛选
	const flagDiseaseArr = ref(['全部']);
	const diseaseFilter = (item, index) => {
		if (item.label === '全部') {
			flagDiseaseArr.value = ['全部'];
		} else {
			if (flagDiseaseArr.value.includes(item.label)) {
				// 已经选中 需要删除
				flagDiseaseArr.value = flagDiseaseArr.value.filter((val) => {
					return val !== item.label && val !== '全部';
				});
			} else {
				// 选中
				flagDiseaseArr.value.push(item.label);
				flagDiseaseArr.value = flagDiseaseArr.value.filter((val) => {
					return val !== '全部';
				});
			}
		}
	};
	// 诊疗方案筛选
	const flagPlanArr = ref(['全部']);
	const planFilter = (item, index) => {
		if (item.label === '全部') {
			flagPlanArr.value = ['全部'];
		} else {
			if (flagPlanArr.value.includes(item.label)) {
				// 已经选中 需要删除
				flagPlanArr.value = flagPlanArr.value.filter((val) => {
					return val !== item.label && val !== '全部';
				});
			} else {
				// 选中
				flagPlanArr.value.push(item.label);
				flagPlanArr.value = flagPlanArr.value.filter((val) => {
					return val !== '全部';
				});
			}
		}
	};
	// 学术文章信息作者筛选
	const flagArticleArr = ref([]);
	const articleFilter = (item, index) => {
		if (flagArticleArr.value.includes(item.label)) {
			// 已经选中 需要删除
			flagArticleArr.value = flagArticleArr.value.filter((val) => {
				return val !== item.label;
			});
		} else {
			// 选中
			flagArticleArr.value.push(item.label);
		}
	};
	// 学术文章信息来源筛选
	const flagSourceArr = ref([]);
	const sourceFilter = (item, index) => {
		if (flagSourceArr.value.includes(item.label)) {
			// 已经选中 需要删除
			flagSourceArr.value = flagSourceArr.value.filter((val) => {
				return val !== item.label;
			});
		} else {
			// 选中
			flagSourceArr.value.push(item.label);
		}
	};

	// 临床指南来源筛选
	const guideSourceArr = ref([]);
	const sourceInfoFilter = (item, index) => {
		if (guideSourceArr.value.includes(item.label)) {
			// 已经选中 需要删除
			guideSourceArr.value = guideSourceArr.value.filter((val) => {
				return val !== item.label;
			});
		} else {
			// 选中
			guideSourceArr.value.push(item.label);
		}
	};
	// 学术会议信息角色筛选
	const flagRoleArr = ref([]);
	const roleFilter = (item, index) => {
		if (flagRoleArr.value.includes(item.label)) {
			// 已经选中 需要删除
			flagRoleArr.value = flagRoleArr.value.filter((val) => {
				return val !== item.label;
			});
		} else {
			// 选中
			flagRoleArr.value.push(item.label);
		}
	};
	// 学术会议信息来源筛选
	const flagSourceMeetArr = ref([]);
	const sourceMeetFilter = (item, index) => {
		if (flagSourceMeetArr.value.includes(item.label)) {
			// 已经选中 需要删除
			flagSourceMeetArr.value = flagSourceMeetArr.value.filter((val) => {
				return val !== item.label;
			});
		} else {
			// 选中
			flagSourceMeetArr.value.push(item.label);
		}
	};
	// 学术直播来源筛选
	const flagSourceLiveArr = ref([]);
	const sourceLiveFilter = (item, index) => {
		if (flagSourceLiveArr.value.includes(item.label)) {
			// 已经选中 需要删除
			flagSourceLiveArr.value = flagSourceLiveArr.value.filter((val) => {
				return val !== item.label;
			});
		} else {
			// 选中
			flagSourceLiveArr.value.push(item.label);
		}
	};
	return {
		flagTime,
		timeFilter,
		diseaseFilter,
		planFilter,
		articleFilter,
		sourceFilter,
		flagDiseaseArr,
		flagPlanArr,
		flagArticleArr,
		flagSourceArr,
		guideSourceArr,
		flagRoleArr,
		sourceInfoFilter,
		roleFilter,
		flagSourceMeetArr,
		sourceMeetFilter,
		flagSourceLiveArr,
		sourceLiveFilter,
	};
}
