<template>
	<div class="basic_view_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">基本概览</span>
		</div>
		<div class="box">
			<van-row>
				<van-col :span="12">
					<span>医生性别:</span>
					<span class="info">{{ doctorInfo.doctorSex }}</span>
				</van-col>
				<van-col :span="12">
					<span>医生籍贯:</span>
					<span class="info">{{ doctorInfo.nativePlace }}</span>
				</van-col>
			</van-row>
			<van-row>
				<van-col :span="12">
					<span>年龄段:</span>
					<span class="info">{{ doctorInfo.ageRange }}</span>
				</van-col>
				<van-col :span="12">
					<span>所在省份:</span>
					<span class="info">{{ doctorInfo.province }}</span>
				</van-col>
			</van-row>
			<van-row>
				<van-col :span="12">
					<span>从业时间:</span>
					<span class="info">{{ doctorInfo.workingTime }}</span>
				</van-col>
				<van-col :span="12">
					<span>所在城市:</span>
					<span class="info">{{ doctorInfo.city }}</span>
				</van-col>
			</van-row>
			<van-row>
				<van-col :span="12">
					<span>医院等级:</span>
					<span class="info">{{ doctorInfo.hospitalLevel }}</span>
				</van-col>
				<van-col :span="12">
					<span>最高学历:</span>
					<span class="info">{{ doctorInfo.degreeTitle ? doctorInfo.degreeTitle.split('&')[0] : '' }}</span>
				</van-col>
			</van-row>
			<van-row>
				<van-col :span="12">
					<span>线上执业平台:</span>
					<span class="info">{{ doctorInfo.practiceChannel }}</span>
				</van-col>
				<van-col :span="12">
					<span>所属领域:</span>
					<span class="info">{{ doctorInfo.indication }}</span>
				</van-col>
			</van-row>
			<van-row>
				<van-col :span="12">
					<span>医院床位数:</span>
					<span class="info">{{ doctorInfo.hospitalBeds }}</span>
				</van-col>
				<van-col :span="12">
					<span>医院年门诊量:</span>
					<span class="info">{{ doctorInfo.hospitalYearOutpatient }}</span>
				</van-col>
			</van-row>
		</div>
	</div>
</template>
<script>
import { toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		HcpInfo: {
			type: Object,
			require: true,
		},
	},
	setup(props, context) {
		const doctorInfo = toRef(props, 'HcpInfo');
		return {
			doctorInfo,
		};
	},
};
</script>
<style lang="scss" scoped>
.basic_view_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.box {
		padding: 10px;
		.van-row {
			margin-bottom: 7px;
			color: #172b4d;
		}
	}
}
.info {
	margin-left: 5px;
	background-color: transparent;
}
</style>
