<template>
	<van-notice-bar wrapable :scrollable="false" left-icon="volume-o" color="#F45009" background="#FFF2F2" text="点击时间、疾病、诊疗方案、来源、作者标签切换查看相关信息" />
	<div class="LabelFilter">
		<van-row>
			<van-col :span="3"><span>时间:</span></van-col>
			<van-col :span="21">
				<span class="lable_filter_item" :class="flagTime == index ? 'timeActive' : ''" v-for="(item, index) in timeList" :key="index" @click="timeFilter(item, index)">{{ item }}</span></van-col
			>
		</van-row>
	</div>
	<div class="LabelFilter">
		<van-row>
			<van-col :span="3"><span>疾病:</span></van-col>
			<van-col :span="21">
				<span class="lable_filter_item" :class="flagDiseaseArr.includes(item.label) ? 'diseaseActive' : 'disease'" v-for="(item, index) in illList.slice(0, 10)" :key="index" @click="diseaseFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
			>
		</van-row>
	</div>
	<div class="LabelFilter">
		<van-row>
			<van-col :span="5"><span>诊疗方案:</span></van-col>
			<van-col :span="19">
				<span class="lable_filter_item" :class="flagPlanArr.includes(item.label) ? 'diseaseActive' : 'disease'" v-for="(item, index) in treatmentList.slice(0, 10)" :key="index" @click="planFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
			>
		</van-row>
	</div>
	<div class="basicView">
		<HCPInformation title="学术文章信息" :articleInfo="articleInfo" @getArticles="getMoreArticles" :total="articleTotal">
			<div class="LabelFilter_slot">
				<van-row>
					<van-col :span="3"><span>作者:</span></van-col>
					<van-col :span="21">
						<span class="lable_filter_item" :class="flagArticleArr.includes(item.label) ? 'authorActive' : ''" v-for="(item, index) in articleAuthor" :key="index" @click="articleFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
					>
				</van-row>
				<van-row>
					<van-col :span="3"><span>来源:</span></van-col>
					<van-col :span="21">
						<span class="lable_filter_item" :class="flagSourceArr.includes(item.label) ? 'sourceActive' : 'source'" v-for="(item, index) in articleSource" :key="index" @click="sourceFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
					>
				</van-row>
			</div>
		</HCPInformation>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPGuide title="临床指南信息" :guideInfo="guideInfo">
			<div class="LabelFilter_slot">
				<van-row>
					<van-col :span="3"><span>来源:</span></van-col>
					<van-col :span="21">
						<span class="lable_filter_item" :class="guideSourceArr.includes(item.label) ? 'sourceActive' : 'source'" v-for="(item, index) in guideLabel" :key="index" @click="sourceInfoFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
					>
				</van-row>
			</div>
		</HCPGuide>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPMeeting title="学术会议信息" :guideInfo="meetingInfo" @getMeetings="getMeetings" :total="meetingTotal">
			<div class="LabelFilter_slot">
				<van-row>
					<van-col :span="3"><span>角色:</span></van-col>
					<van-col :span="21">
						<span class="lable_filter_item" :class="flagRoleArr.includes(item.label) ? 'authorActive' : ''" v-for="(item, index) in roleList" :key="index" @click="roleFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
					>
				</van-row>
				<van-row>
					<van-col :span="3"><span>来源:</span></van-col>
					<van-col :span="21">
						<span class="lable_filter_item" :class="flagSourceMeetArr.includes(item.label) ? 'sourceActive' : 'source'" v-for="(item, index) in meetingSourceList" :key="index" @click="sourceMeetFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
					>
				</van-row>
			</div>
		</HCPMeeting>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPLive title="学术直播信息" :aliveInfo="aliveInfo" :total="aliveTotal" @getlives="getlives">
			<div class="LabelFilter_slot">
				<van-row>
					<van-col :span="3"><span>来源:</span></van-col>
					<van-col :span="21">
						<span class="lable_filter_item" :class="flagSourceLiveArr.includes(item.label) ? 'sourceActive' : 'source'" v-for="(item, index) in aliveSourceList" :key="index" @click="sourceLiveFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
					>
				</van-row>
			</div>
		</HCPLive>
	</div>
	<!-- 撑起底部高度 -->
	<!-- eslint-disable-next-line vue/no-multiple-template-root -->
	<div class="bottom"></div>
</template>
<script>
import { reactive, toRefs, watch } from 'vue';
import { article } from './Js/article'; // 学术文章信息相关功能
import { guide } from './Js/guide'; // 学术文章信息相关功能
import { meeting } from './Js/meeting'; // 学术会议信息相关功能
import { live } from './Js/live'; // 学术直播信息相关功能
import { academicSelect } from './Js/academicSelect'; // 学术筛选功能
import { GMTToStr, dateBefore } from '@/utils/index.js';
import HCPInformation from './components/HCPInformation.vue';
import HCPGuide from './components/HCPGuide.vue';
import HCPMeeting from './components/HCPMeeting.vue';
import HCPLive from './components/HCPLive.vue';
export default {
	name: 'Index',
	components: {
		HCPInformation,
		HCPGuide,
		HCPMeeting,
		HCPLive,
	},
	setup(props, context) {
		const { state1, getMoreArticles, academicArticle } = article();
		const { flagTime, timeFilter, diseaseFilter, planFilter, articleFilter, sourceFilter, flagDiseaseArr, flagPlanArr, flagArticleArr, flagSourceArr, guideSourceArr, sourceInfoFilter, flagRoleArr, roleFilter, flagSourceMeetArr, sourceMeetFilter, flagSourceLiveArr, sourceLiveFilter } =
			academicSelect();
		const { item1, state2, guideArticle } = guide();
		const { state3, getMeetings, meetingArticle } = meeting();
		const { state4, getlives, liveArticle } = live();
		const state = reactive({
			startDate: '',
			endDate: '',
		});
		const func = () => {
			if (flagTime.value === 0) {
				// 全部
				state.endDate = '';
				state.startDate = '';
			} else if (flagTime.value === 1) {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateBefore(state.endDate, 1);
			} else if (flagTime.value === 2) {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateBefore(state.endDate, 3);
			} else {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateBefore(state.endDate, 5);
			}
		};
		// 侦听公共筛选值的变化
		watch(
			[flagTime, flagDiseaseArr, flagPlanArr],
			(newValue, oldValue) => {
				state1.articleInfo = [];
				state1.page = 0;
				state2.guideInfo = [];
				state2.page = 0;
				state3.meetingInfo = [];
				state3.page = 0;
				state4.aliveInfo = [];
				state4.page = 0;
				func();
				// 学术文章信息筛选
				academicArticle(state.startDate, state.endDate, flagDiseaseArr.value, flagPlanArr.value);
				// 临床指南信息筛选
				guideArticle(state.startDate, state.endDate, flagDiseaseArr.value, flagPlanArr.value);
				// 学术会议信息筛选
				meetingArticle(state.startDate, state.endDate, flagDiseaseArr.value, flagPlanArr.value);
				// 学术直播信息筛选
				liveArticle(state.startDate, state.endDate, flagDiseaseArr.value, flagPlanArr.value);
			},
			{
				deep: true,
			}
		);
		// 侦听学术文章信息作者来源筛选
		watch(
			[flagArticleArr, flagSourceArr],
			() => {
				state1.articleInfo = [];
				state1.page = 0;
				func();
				academicArticle(state.startDate, state.endDate, flagDiseaseArr.value, flagPlanArr.value, flagArticleArr.value, flagSourceArr.value);
			},
			{
				deep: true,
			}
		);
		// 侦听临床信息指南来源筛选
		watch(
			[guideSourceArr],
			() => {
				state2.guideInfo = [];
				state2.page = 0;
				func();
				guideArticle(state.startDate, state.endDate, flagDiseaseArr.value, flagPlanArr.value, guideSourceArr.value);
			},
			{
				deep: true,
			}
		);
		// 侦听学术会议信息角色和来源来源筛选
		watch(
			[flagRoleArr, flagSourceMeetArr],
			() => {
				state3.meetingInfo = [];
				state3.page = 0;
				func();
				meetingArticle(state.startDate, state.endDate, flagDiseaseArr.value, flagPlanArr.value, flagRoleArr.value, flagSourceMeetArr.value);
			},
			{
				deep: true,
			}
		);
		// 侦听学术直播信息来源筛选
		watch(
			[flagSourceLiveArr],
			() => {
				state4.aliveInfo = [];
				state4.page = 0;
				func();
				liveArticle(state.startDate, state.endDate, flagDiseaseArr.value, flagPlanArr.value, flagSourceLiveArr.value);
			},
			{
				deep: true,
			}
		);
		return {
			...toRefs(state1),
			...toRefs(state2),
			...toRefs(state3),
			...toRefs(state4),
			flagTime,
			timeFilter,
			diseaseFilter,
			planFilter,
			articleFilter,
			sourceFilter,
			sourceInfoFilter,
			roleFilter,
			sourceMeetFilter,
			sourceLiveFilter,
			getMoreArticles,
			flagDiseaseArr,
			flagPlanArr,
			flagArticleArr,
			flagSourceArr,
			item1,
			guideSourceArr,
			getMeetings,
			flagRoleArr,
			flagSourceMeetArr,
			getlives,
			flagSourceLiveArr,
		};
	},
};
</script>
<style lang="scss" scoped>
.van-notice-bar {
	margin-top: 12px;
	font-size: 12px !important;
	font-weight: 600;
}
.van-notice-bar--wrapable {
	padding: 8px;
}
::v-deep(.van-notice-bar__content) {
	line-height: 18px;
}
.LabelFilter_slot {
	.lable_filter_item {
		display: inline-block;
		padding: 0 5px;
		height: 20px;
		line-height: 20px;
		border-radius: 6px;
		text-align: center;
		font-size: 12px;
		color: #0052cc;
		background-color: rgba(51, 119, 255, 0.1);
		margin-left: 5px;
		margin-bottom: 5px;
	}
	margin-top: 10px;
	.authorActive {
		background-color: rgba(51, 119, 255, 0.3);
	}
	.source {
		background-color: rgba(112, 201, 26, 0.1);
		color: rgba(108, 198, 22, 100);
	}
	.sourceActive {
		background-color: rgba(112, 201, 26, 0.3);
		color: rgba(108, 198, 22, 100);
	}
}
.LabelFilter {
	color: #172b4d;
	padding: 0px 12px;
	margin-top: 10px;
	.lable_filter_item {
		display: inline-block;
		padding: 0 5px;
		height: 20px;
		line-height: 20px;
		border-radius: 6px;
		text-align: center;
		font-size: 12px;
		color: #0052cc;
		background-color: rgba(51, 119, 255, 0.1);
		margin-left: 5px;
		margin-bottom: 5px;
	}
	.timeActive {
		background-color: rgba(51, 119, 255, 0.3);
	}
	.disease {
		background-color: rgba(254, 237, 230, 100);
		color: rgba(244, 80, 9, 100);
	}
	.diseaseActive {
		background-color: rgba(252, 202, 181, 100);
		color: rgba(244, 80, 9, 100);
	}
}
.basicView {
	padding: 15px;
	padding-bottom: 0;
}
.border {
	margin-top: 16px;
	padding: 0 15px;
	.line {
		border-bottom: 1px solid #dfe1e6;
		opacity: 0.8;
	}
}
.bottom {
	height: 20px;
}
</style>
