<template>
	<div class="cooperation">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">医生合作概览</span>
		</div>
	</div>
	<van-notice-bar wrapable :scrollable="false" left-icon="volume-o" color="#F45C5C" background="#FFF2F2" text="根据医生合作亲密度排序展示医生合作人及合作类型，点击类型标签切换" />
	<div class="search-content">
		<div class="input">
			<input type="text" v-model="searchName" />
			<div class="icon" @click="searchHandler"><van-icon name="search" /></div>
		</div>
		<div class="reset" @click="resetHandler">取消</div>
	</div>
	<div class="LabelFilter">
		<van-row>
			<van-col :span="3"><span>类型:</span></van-col>
			<van-col :span="21">
				<span class="lable_filter_item" :class="flagTypeArr.includes(item.label) ? 'timeActive' : ''" v-for="(item, index) in typeList" :key="index" @click="typeFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
			>
		</van-row>
	</div>
	<div class="total">
		医生合作总数:<span class="num">{{ total }}</span
		>人
	</div>
	<div class="HCPCooperation">
		<HCPCooperation :list="list" v-show="list.length > 0"></HCPCooperation>
	</div>
	<div class="more" @click="moreHandler" v-if="isMore">加载更多...</div>
	<!-- eslint-disable-next-line vue/no-multiple-template-root -->
	<div class="nomore" v-else>没有更多了</div>
</template>
<script>
import { reactive, toRefs, ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { teamWorkTagListInterface, networkInterface } from '@/api/doctor';
export default {
	name: 'Index',
	setup(props, context) {
		const { proxy } = getCurrentInstance();
		const route = useRoute();
		const state = reactive({
			total: '',
			typeList: [],
			list: [],
			page: 1,
			limit: 10,
			relationTag: '',
			searchName: '',
			isMore: true,
		});
		const flagTypeArr = ref(['全部']);
		const typeFilter = (item, index) => {
			if (item.label === '全部') {
				flagTypeArr.value = ['全部'];
			} else {
				if (flagTypeArr.value.includes(item.label)) {
					// 已经选中 需要删除
					flagTypeArr.value = flagTypeArr.value.filter((val) => {
						return val !== item.label && val !== '全部';
					});
				} else {
					// 选中
					flagTypeArr.value.push(item.label);
					flagTypeArr.value = flagTypeArr.value.filter((val) => {
						return val !== '全部';
					});
				}
			}
			if (flagTypeArr.value) {
				if (flagTypeArr.value[0] === '全部') {
					state.relationTag = '';
				} else {
					state.relationTag = flagTypeArr.value.toString();
				}
			} else {
				state.relationTag = '';
			}
			state.page = 1;
			state.list = [];
			state.isMore = true;
			network();
		};
		const teamWorkTagList = () => {
			const query = {
				// doctorId: route.query.doctorId
			};
			teamWorkTagListInterface(query, route.query.doctorId).then((res) => {
				state.typeList = res.result;
			});
		};
		const network = () => {
			const query = {
				doctorId: route.query.doctorId,
				// doctorId: '32d3b960ceb8ae9e0910821f63baedef',
				page: state.page,
				size: state.limit,
				search: state.searchName,
				relationTypes: state.relationTag,
			};
			networkInterface(query, route.query.doctorId).then((res) => {
				state.total = res.result.count;
				state.list = state.list.concat(res.result.list);
				if (res.result.list === null || res.result.list.length === 0) {
					state.isMore = false;
				}
			});
		};
		const moreHandler = () => {
			state.page++;
			network();
		};
		const resetHandler = () => {
			state.page = 1;
			state.list = [];
			state.isMore = true;
			state.searchName = '';
			network();
		};
		const searchHandler = () => {
			state.page = 1;
			state.list = [];
			state.isMore = true;
			network();
		};
		onMounted(teamWorkTagList);
		onMounted(network);
		return {
			...toRefs(state),
			flagTypeArr,
			typeFilter,
			moreHandler,
			searchHandler,
			resetHandler,
		};
	},
};
</script>
<style lang="scss" scoped>
.van-notice-bar {
	margin-top: 12px;
	// min-height: 28px !important;
	font-size: 12px !important;
	font-weight: 600;
}
.van-notice-bar--wrapable {
	padding: 8px;
}
::v-deep(.van-notice-bar__content) {
	line-height: 18px;
}
.LabelFilter {
	padding: 0px 15px;
	margin-top: 10px;
	color: #172b4d;
	.lable_filter_item {
		display: inline-block;
		padding: 0 5px;
		height: 20px;
		line-height: 20px;
		border-radius: 6px;
		text-align: center;
		font-size: 12px;
		color: #0052cc;
		background-color: rgba(51, 119, 255, 0.1);
		margin-left: 5px;
		margin-bottom: 5px;
	}
	.timeActive {
		background-color: rgba(51, 119, 255, 0.3);
	}
}
.total {
	margin-top: 10px;
	margin-left: 30px;
	.num {
		color: #172b4d;
		opacity: 0.5;
		margin: 0 3px;
	}
}
.jiejue {
	display: flex;
	align-items: center;
}
.title {
	font-size: 16px;
	color: #172b4d;
	font-weight: 600;
}
.circle {
	// display: inline-block;
	width: 8px;
	height: 8px;
	background: #0052cc;
	opacity: 0.8;
	border-radius: 50%;
	margin-right: 4px;
	border: none;
}
.cooperation {
	padding: 20px 15px 0 15px;
}
.search-content {
	// padding: 10px 20px;
	margin-left: 20px;
	margin-top: 10px;
	overflow: hidden;
	height: 30px;
	.input {
		float: left;
		height: 30px;
		width: calc(100% - 80px);
		position: absolute;
		.icon {
			position: absolute;
			top: 0;
			right: 0;
			z-index: 999;
			height: 30px;
			line-height: 30px;
			width: 36px;
			background: #0052cc;
			text-align: center;
			color: #fff;
			font-weight: 600;
			font-size: 16px;
			border-radius: 0 4px 4px 0;
		}
		input {
			width: 100%;
			height: 30px;
			border: 1px solid #1376dc;
			opacity: 0.5;
			border-radius: 4px;
			padding-right: 36px;
		}
	}
	.reset {
		float: right;
		height: 30px;
		width: 60px;
		text-align: center;
		line-height: 30px;
		color: #0052cc;
	}
}
.HCPCooperation {
	padding: 10px 15px;
}
.more {
	text-align: center;
	margin-bottom: 30px;
	font-size: 13px;
	color: #0052cc;
}
.nomore {
	text-align: center;
	margin-bottom: 30px;
	font-size: 13px;
	color: #172b4d;
}
</style>
