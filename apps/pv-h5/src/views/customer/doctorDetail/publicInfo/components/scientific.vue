<template>
	<van-notice-bar wrapable :scrollable="false" left-icon="volume-o" color="#F45C5C" background="#FFF2F2" text="点击时间、疾病、诊疗方案、来源、作者标签切换查看相关信息" />
	<div class="LabelFilter">
		<van-row>
			<van-col :span="3"><span>时间:</span></van-col>
			<van-col :span="21">
				<span class="lable_filter_item" :class="flagTime == index ? 'timeActive' : ''" v-for="(item, index) in timeList" :key="index" @click="timeFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
			>
		</van-row>
	</div>
	<div class="basicView">
		<HCPClinical title="临床课题研究情况" :researchInfo="researchInfo" :total="researchTotal" @getResearches="getResearches">
			<div class="LabelFilter_slot">
				<van-row>
					<van-col :span="3"><span>角色:</span></van-col>
					<van-col :span="21">
						<span class="lable_filter_item" :class="flagRoleArr.includes(item.label) ? 'authorActive' : ''" v-for="(item, index) in researchRoleLabel" :key="index" @click="roleFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
					>
				</van-row>
				<van-row>
					<van-col :span="3"><span>阶段:</span></van-col>
					<van-col :span="21">
						<span class="lable_filter_item" :class="flagPhaseArr.includes(item.label) ? 'sourceActive' : 'source'" v-for="(item, index) in researchStageLabel" :key="index" @click="phaseFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
					>
				</van-row>
				<van-row>
					<van-col :span="3"><span>类型:</span></van-col>
					<van-col :span="21">
						<span class="lable_filter_item" :class="flagTypeArr.includes(item.label) ? 'diseaseActive' : 'disease'" v-for="(item, index) in researchTypeLabel" :key="index" @click="typeFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
					>
				</van-row>
			</div>
		</HCPClinical>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPFond title="国家自然科学基金项目情况" :fundInfo="fundInfo" :total="fundTotal" @getfunds="getfunds">
			<div class="LabelFilter_slot">
				<van-row>
					<van-col :span="3"><span>类型:</span></van-col>
					<van-col :span="21">
						<span class="lable_filter_item" :class="flagFondTypeArr.includes(item.label) ? 'sourceActive' : 'source'" v-for="(item, index) in fundTypeLabel" :key="index" @click="fondTypeFilter(item, index)">{{ item.label + '(' + item.num + ')' }}</span></van-col
					>
				</van-row>
			</div>
		</HCPFond>
	</div>
	<div class="border">
		<div class="line"></div>
	</div>
	<div class="basicView">
		<HCPInvention title="发明专利项目情况" :patentInfo="patentInfo" :total="patentTotal" @getPatents="getPatents"></HCPInvention>
	</div>
	<!-- eslint-disable-next-line vue/no-multiple-template-root -->
	<div class="bottom"></div>
</template>
<script>
import { reactive, toRefs, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { scientific } from './Js/scientific'; // 学术文章信息相关功能
import { scientficSelect } from './Js/scientificSelect'; // 科研筛选功能
import { GMTToStr, dateBefore } from '@/utils/index.js';
import { projectRange } from '@/api/doctor';
import HCPClinical from './components/HCPClinical.vue';
import HCPFond from './components/HCPFond.vue';
import HCPInvention from './components/HCPInvention.vue';
export default {
	name: 'Index',
	components: { HCPClinical, HCPFond, HCPInvention },
	setup(props, context) {
		const state = reactive({
			timeList: [],
			startDate: '',
			endDate: '',
		});
		const { proxy } = getCurrentInstance();
		const route = useRoute();
		const { scient, getResearches, projectResearch, projectFund, getfunds, getPatents, projectPatent } = scientific();
		const { flagTime, timeFilter, flagRoleArr, roleFilter, flagPhaseArr, phaseFilter, flagTypeArr, typeFilter, flagFondTypeArr, fondTypeFilter } = scientficSelect();
		/**
		 * @method
		 * @param  doctorId,type
		 * @returns
		 * @desc 科研事件筛选tab
		 */
		const scientificTabel = (startDate, endDate) => {
			const query = {
				// doctorId: route.query.doctorId,
				// type: 1
			};
			projectRange(query, route.query.doctorId).then((res) => {
				const arr = [];
				for (const i of res.result) {
					const obj = {
						label: i.name,
						num: i.year,
					};
					arr.push(obj);
				}
				state.timeList = arr;
			});
		};
		const func = () => {
			if (flagTime.value === 0) {
				// 全部
				state.endDate = '';
				state.startDate = '';
			} else if (flagTime.value === 1) {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateBefore(state.endDate, 1);
			} else if (flagTime.value === 2) {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateBefore(state.endDate, 3);
			} else {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateBefore(state.endDate, 5);
			}
		};
		// 侦听公共筛选值的变化
		watch(
			[flagTime],
			(newValue, oldValue) => {
				func();
				scient.researchInfo = [];
				scient.page = 0;
				scient.fundInfo = [];
				scient.page1 = 0;
				scient.patentInfo = [];
				scient.page2 = 0;
				projectResearch(state.startDate, state.endDate);
				projectFund(state.startDate, state.endDate);
				projectPatent(state.startDate, state.endDate);
			},
			{
				deep: true,
			}
		);
		// 侦听临床课题研究情况筛选值的变化
		watch(
			[flagRoleArr, flagPhaseArr, flagTypeArr],
			(newValue, oldValue) => {
				func();
				scient.researchInfo = [];
				scient.page = 0;
				projectResearch(state.startDate, state.endDate, flagRoleArr.value, flagPhaseArr.value, flagTypeArr.value);
			},
			{
				deep: true,
			}
		);
		// 侦听国家自然科学基金项目情况类型筛选值的变化
		watch(
			[flagFondTypeArr],
			(newValue, oldValue) => {
				func();
				scient.fundInfo = [];
				scient.page1 = 1;
				projectFund(state.startDate, state.endDate, flagFondTypeArr.value);
			},
			{
				deep: true,
			}
		);
		onMounted(scientificTabel);
		return {
			...toRefs(state),
			...toRefs(scient),
			flagTime,
			timeFilter,
			flagRoleArr,
			roleFilter,
			flagPhaseArr,
			phaseFilter,
			flagTypeArr,
			typeFilter,
			flagFondTypeArr,
			fondTypeFilter,
			getResearches,
			getfunds,
			getPatents,
		};
	},
};
</script>
<style lang="scss" scoped>
.van-notice-bar {
	margin-top: 12px;
	font-size: 12px !important;
	font-weight: 600;
}
.van-notice-bar--wrapable {
	padding: 8px;
}
::v-deep(.van-notice-bar__content) {
	line-height: 18px;
}
.LabelFilter_slot {
	.lable_filter_item {
		display: inline-block;
		padding: 0 5px;
		height: 20px;
		line-height: 20px;
		border-radius: 6px;
		text-align: center;
		font-size: 12px;
		color: #0052cc;
		background-color: rgba(51, 119, 255, 0.1);
		margin-left: 5px;
		margin-bottom: 5px;
	}
	margin-top: 10px;
	.authorActive {
		background-color: rgba(51, 119, 255, 0.3);
	}
	.source {
		background-color: rgba(112, 201, 26, 0.1);
		color: rgba(108, 198, 22, 100);
	}
	.sourceActive {
		background-color: rgba(112, 201, 26, 0.3);
		color: rgba(108, 198, 22, 100);
	}
	.disease {
		background-color: rgba(254, 237, 230, 100);
		color: rgba(244, 80, 9, 100);
	}
	.diseaseActive {
		background-color: rgba(252, 202, 181, 100);
		color: rgba(244, 80, 9, 100);
	}
}
.LabelFilter {
	padding: 0px 12px;
	margin-top: 10px;
	color: #172b4d;
	.lable_filter_item {
		display: inline-block;
		padding: 0 5px;
		height: 20px;
		line-height: 20px;
		border-radius: 6px;
		text-align: center;
		font-size: 12px;
		color: #0052cc;
		background-color: rgba(51, 119, 255, 0.1);
		margin-left: 5px;
		margin-bottom: 5px;
	}
	.timeActive {
		background-color: rgba(51, 119, 255, 0.3);
	}
	.disease {
		background-color: rgba(254, 237, 230, 100);
		color: rgba(244, 80, 9, 100);
	}
	.diseaseActive {
		background-color: rgba(252, 202, 181, 100);
		color: rgba(244, 80, 9, 100);
	}
}
.border {
	padding: 0 15px;
	margin-top: 13px;
	.line {
		border-bottom: 1px solid #dfe1e6;
		opacity: 0.8;
	}
}
.basicView {
	padding: 10px 25px;
	padding-bottom: 0;
}
.bottom {
	height: 30px;
}
</style>
