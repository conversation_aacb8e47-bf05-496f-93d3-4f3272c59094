<template>
	<div class="HCPCooperation_components">
		<div class="cooperation_box">
			<div class="box_list" v-for="(item, index) in listInfo" :key="index">
				<div @click="link(item)">
					<span class="list_name">{{ item.relationName }}</span>
					<span class="list_position">{{ item.relationHospital }}</span>
				</div>
				<div class="list_info">
					<span v-for="(i, id) in item.data" :key="id">
						{{ id }}<span v-if="i">（{{ i }}）</span>；</span
					>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { getCurrentInstance, toRef } from 'vue';
import { findByDoctorId } from '@/api/doctor';
export default {
	name: 'Index',
	props: {
		list: {
			type: Object,
			require: true,
		},
	},
	setup(props, context) {
		const listInfo = toRef(props, 'list');
		const { proxy } = getCurrentInstance();
		const link = (val) => {
			const query = {
				doctorId: val.relationId,
			};
			findByDoctorId(query).then((res) => {
				sessionStorage.removeItem('infoTab');
				const url = window.location.origin + window.location.pathname + '?doctorId=' + val.relationId + '&id=' + res.data.id;
				window.location.replace(url);
				// router.replace({
				//   name: 'publicInfo',
				//   query: {
				//     doctorId: val.relationId,
				//     id: res.data.id
				//   }
				// })
			});
		};
		return {
			listInfo,
			link,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPCooperation_components {
	.cooperation_box {
		border: 1px solid rgba(19, 118, 220, 0.5);
		.box_list {
			padding: 10px 15px;
			border-bottom: 1px dashed rgba(19, 118, 220, 0.5);
			.list_name {
				color: #0052cc;
			}
			.list_position {
				color: #0052cc;
				margin-left: 5px;
			}
			.list_info {
				margin-top: 5px;
			}
			&:last-child {
				border: none;
			}
		}
	}
}
</style>
