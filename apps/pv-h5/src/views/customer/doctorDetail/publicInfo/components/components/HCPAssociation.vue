<template>
	<div class="HCPAssociation_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">协会/学会及任职</span>
		</div>
		<div v-if="info.length > 1">
			<div class="th_title">
				<div class="journal">协会</div>
				<div class="job">职务</div>
			</div>
			<van-row class="list" v-for="(item, index) in info" :key="index">
				<div class="journal" :class="[{ odd: index % 2 == 0 }, { even: index % 2 !== 0 }, { last: index == info.length - 1 }]">
					{{ item.split('#')[0] }}
				</div>
				<div class="job" :class="[{ odd: index % 2 == 0 }, { even: index % 2 !== 0 }, { last: index == info.length - 1 }]">
					{{ item.split('#')[1] }}
				</div>
			</van-row>
		</div>
	</div>
</template>
<script>
import { toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		association: {
			require: true,
		},
	},
	setup(props, context) {
		const info = toRef(props, 'association');
		return {
			info,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPAssociation_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.th_title {
		overflow: hidden;
		margin-top: 15px;
		color: #172b4d;
	}
	.list {
		clear: both;
		color: #172b4d;
	}
	.journal {
		float: left;
		width: 70%;
		padding: 10px;
		background-color: rgba(234, 246, 254, 100);
		border-left: 2px solid #d9ecfe;
		text-align: center;
	}
	.job {
		float: left;
		width: 30%;
		padding: 10px;
		border-left: 2px solid #d9ecfe;
		border-right: 2px solid #d9ecfe;
		background-color: rgba(234, 246, 254, 100);
		text-align: center;
	}
	.odd {
		background: #fff;
	}
	.even {
		background-color: rgba(249, 252, 255, 100);
	}
	.last {
		border-bottom: 2px solid #d9ecfe;
	}
}
</style>
