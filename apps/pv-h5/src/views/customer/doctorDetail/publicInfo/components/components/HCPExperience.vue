<template>
	<div class="HCPExperience_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">工作经历</span>
		</div>
		<div>
			<van-row class="list" v-for="(item, index) in doctorInfo" :key="index">
				{{ item }}
			</van-row>
		</div>
	</div>
</template>
<script>
import { reactive, toRefs, toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		HcpInfo: {
			require: true,
		},
	},
	setup(props, context) {
		const state = reactive({
			info: ['1', '2'],
		});
		const doctorInfo = toRef(props, 'HcpInfo');
		return {
			...toRefs(state),
			doctorInfo,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPExperience_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.list {
		margin: 10px 0;
		padding: 0 10px;
		color: #172b4d;
	}
}
</style>
