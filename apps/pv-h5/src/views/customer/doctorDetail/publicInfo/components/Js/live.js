import { getCurrentInstance, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { liveInterface } from '@/api/doctor';
/**
 * @method
 * @param doctorId
 * @returns
 * @desc 学术直播信息相关
 */
export function live(page, limit) {
	const { proxy } = getCurrentInstance();
	const route = useRoute();
	const state4 = reactive({
		page: 0,
		limit: 10,
		aliveSourceList: [],
		aliveInfo: [],
		aliveTotal: '',
	});
	const liveArticle = (startDate, endDate, diseaseLabel, planLabel, flagSourceLiveArr) => {
		if (diseaseLabel) {
			if (diseaseLabel[0] === '全部') {
				diseaseLabel = '';
			}
		}
		if (planLabel) {
			if (planLabel[0] === '全部') {
				planLabel = '';
			}
		}
		const query = {
			doctorId: route.query.doctorId,
			page: state4.page,
			size: state4.limit,
			startDate: startDate,
			endDate: endDate,
			illness: diseaseLabel ? diseaseLabel.toString() : '',
			treatment: planLabel ? planLabel.toString() : '',
			source: flagSourceLiveArr ? flagSourceLiveArr.toString() : '',
		};
		liveInterface(query).then((res) => {
			state4.aliveSourceList = res.result.label;
			state4.aliveInfo = state4.aliveInfo.concat(res.result.data);
			state4.aliveTotal = res.result.total;
		});
	};
	const getlives = () => {
		state4.page++;
		liveArticle();
	};
	onMounted(liveArticle);
	return {
		state4,
		liveArticle,
		getlives,
	};
}
