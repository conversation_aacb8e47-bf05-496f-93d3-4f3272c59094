<template>
	<div class="HCPAcademic_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">学术头衔</span>
		</div>
		<div>
			<van-row class="list" v-for="(item, index) in info" :key="index">
				<van-col :span="24">{{ item }} </van-col>
			</van-row>
		</div>
		<!-- <div class="open">展开</div> -->
	</div>
</template>
<script>
import { toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		mainAcademicTitle: {
			require: true,
		},
	},
	setup(props, context) {
		const info = toRef(props, 'mainAcademicTitle');
		return {
			info,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPAcademic_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.list {
		margin: 10px 0;
		padding: 0 10px;
		color: #172b4d;
	}
	.open {
		text-align: right;
		margin-top: 5px;
		color: #0052cc;
		margin-bottom: 5px;
	}
}
</style>
