import { getCurrentInstance, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { meetingInterface } from '@/api/doctor';
/**
 * @method
 * @param doctorId
 * @returns
 * @desc 学术会议相关信息相关
 */
export function meeting(page, limit) {
	const { proxy } = getCurrentInstance();
	const route = useRoute();
	const state3 = reactive({
		page: 0,
		limit: 10,
		roleList: [], // 角色筛选列表
		meetingInfo: [],
		meetingSourceList: [],
		meetingTotal: '',
	});
	const meetingArticle = (startDate, endDate, diseaseLabel, planLabel, flagRoleArr, flagSourceMeetArr) => {
		if (diseaseLabel) {
			if (diseaseLabel[0] === '全部') {
				diseaseLabel = '';
			}
		}
		if (planLabel) {
			if (planLabel[0] === '全部') {
				planLabel = '';
			}
		}
		const query = {
			doctorId: route.query.doctorId,
			page: state3.page,
			size: state3.limit,
			startDate: startDate,
			endDate: endDate,
			illness: diseaseLabel ? diseaseLabel.toString() : '',
			treatment: planLabel ? planLabel.toString() : '',
			source: flagSourceMeetArr ? flagSourceMeetArr.toString() : '',
			role: flagRoleArr ? flagRoleArr.toString() : '',
		};
		meetingInterface(query).then((res) => {
			state3.meetingTotal = res.result?.total;
			state3.roleList = res.result?.role;
			state3.meetingSourceList = res.result?.source;
			state3.meetingInfo = res.result && res.result.table ? state3.meetingInfo.concat(res.result.table) : [];
		});
	};
	const getMeetings = () => {
		state3.page++;
		meetingArticle();
	};
	onMounted(meetingArticle);
	return {
		state3,
		meetingArticle,
		getMeetings,
	};
}
