<template>
	<div class="HCPPractice_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">多点执业</span>
		</div>
		<van-row class="list" v-for="(item, index) in doctorInfo.servingHospital" :key="index">
			<span class="hospital">{{ item.hospital }}</span>
			<span>{{ item.department.indexOf('/') !== -1 ? item.department.split('/')[1] : item.department }}</span>
		</van-row>
	</div>
</template>
<script>
import { toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		HcpInfo: {
			type: Object,
			require: true,
		},
	},
	setup(props, context) {
		const doctorInfo = toRef(props, 'HcpInfo');
		return {
			doctorInfo,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPPractice_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.list {
		margin: 10px 0;
		padding: 0 10px;
		color: #172b4d;
		.hospital {
			margin-right: 12px;
		}
	}
	.level {
		display: inline-block;
		border-radius: 6px;
		width: 60px;
		height: 20px;
		line-height: 20px;
		text-align: center;
		border: 1px solid rgba(5, 83, 247, 80);
		color: #0052cc;
		font-size: 12px;
	}
}
</style>
