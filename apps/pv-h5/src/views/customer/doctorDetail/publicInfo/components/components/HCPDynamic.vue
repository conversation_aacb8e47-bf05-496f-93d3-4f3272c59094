<template>
	<div class="HCPDynamic_components">
		<div class="dynamic_box">
			<img src="@/assets/img/clock.png" alt="" v-show="dynamicList.length > 0" />
			<div v-for="(item, index) in dynamicList" :key="index">
				<div>
					<span class="dynamic_circle"></span>
					<span class="dynamic_time">{{ item.activityTime }}</span>
				</div>
				<div class="dynamic_content">
					<div>
						<van-row>
							<van-col :span="6"><span class="content_title">活动标题</span></van-col>
							<van-col :span="18">
								<span class="content_info" @click="link(item)">{{ item.activityTitle }}</span>
							</van-col>
						</van-row>
					</div>
					<div>
						<van-row>
							<van-col :span="6"><span class="content_role">角色</span></van-col>
							<van-col :span="18">
								<span class="role">{{ item.role }}</span>
							</van-col>
						</van-row>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		dynamicList: {
			type: Object,
			require: true,
		},
	},
	setup(props, context) {
		const dynamicInfo = toRef(props, 'dynamicList');
		const link = (val) => {
			window.location.href = val.url;
		};
		return {
			dynamicInfo,
			link,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPDynamic_components {
	padding: 10px;
	.dynamic_box {
		border-left: 2px dashed rgba(51, 119, 255, 0.45);
		position: relative;
		img {
			width: 16px;
			position: absolute;
			left: -9px;
			top: -16px;
		}
		.dynamic_circle {
			display: inline-block;
			width: 8px;
			height: 8px;
			border-radius: 50%;
			background: #0052cc;
			position: relative;
			left: -5px;
			top: -1px;
			margin-top: 15px;
		}
		.dynamic_time {
			margin-left: 5px;
			font-weight: 600;
			color: #172b4d;
		}
		.dynamic_content {
			color: #172b4d;
			height: 120px;
			margin-left: 10px;
			margin-top: 10px;
			background: rgba(234, 242, 249, 100);
			padding: 20px 10px;
			box-shadow: 0px 4px 6px 0px rgba(237, 238, 251, 100);
			border-radius: 12px;
			.content_title {
				font-weight: 600;
				display: inline-block;
				width: 80px;
			}
			.content_info {
				color: #0052cc;
			}
			.content_role {
				font-weight: 600;
				display: inline-block;
				width: 80px;
				margin-top: 30px;
			}
			.role {
				display: inline-block;
				width: 80px;
				margin-top: 30px;
			}
		}
	}
}
</style>
