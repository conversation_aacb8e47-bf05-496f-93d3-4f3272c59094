<template>
	<div class="HCPInformation_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">{{ titleInfo }}</span>
		</div>
		<slot></slot>
		<div>
			<div class="list" v-for="(item, index) in articleList" :key="index">
				<!-- <div class="article">{{ item.guideTitle }}</div>
        <van-row>
          <van-col :span="3" class="publishDate">{{ item.publishTime }}</van-col>
          <van-col :span="21">{{ item.guideFormulateInstitutions }}</van-col>
        </van-row>
        <p>被引用：{{ item.quotedTimes }}次</p> -->
				<span class="article" @click="link(item)">{{ item.recentAcademicMeeting }}</span>
				<span>({{ item.meetingData }} </span>
				<span class="adress"> {{ item.meetingAddress }})</span>
			</div>
			<div class="more" @click="getMore" v-if="articleList.length < articleTotal">加载更多...</div>
			<div class="nomore" v-else>没有更多了</div>
		</div>
	</div>
</template>
<script>
import { ref, toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		title: {
			type: String,
			require: true,
		},
		guideInfo: {
			require: true,
		},
		total: {
			require: true,
		},
	},
	setup(props, context) {
		const titleInfo = ref(props.title);
		// const flagTime = ref(0)
		// const timeFilter = (item, index) => {
		//   flagTime.value = index
		// }
		// const flagSource = ref(0)
		// const sourceFilter = (item, index) => {
		//   flagSource.value = index
		// }
		const articleList = toRef(props, 'guideInfo');
		const articleTotal = toRef(props, 'total');
		const getMore = () => {
			context.emit('getMeetings');
		};
		const link = (val) => {
			window.location.href = val.meetingUrl;
		};
		return {
			titleInfo,
			// flagTime,
			// timeFilter,
			// flagSource,
			// sourceFilter,
			articleList,
			getMore,
			articleTotal,
			link,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPInformation_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		// display: inline-block;
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.list {
		margin: 10px 0;
		padding: 0 10px;
		.article {
			color: rgba(19, 118, 220, 100);
			margin-bottom: 3px;
		}
		.adress {
			margin-left: 5px;
		}
	}
	.more {
		text-align: center;
		color: #0052cc;
	}
	.nomore {
		text-align: center;
		color: #172b4d;
	}
}
</style>
