<template>
	<div class="father">
		<!-- 维护手机号 -->
		<van-dialog v-model:show="isShow" :title="text" :show-cancel-button="false" :showConfirmButton="false">
			<!-- 允许输入正整数，调起纯数字键盘 -->
			<van-field v-if="text === '手机号'" ref="field" v-model="phone" type="digit" />
			<van-field v-else ref="field1" v-model="email" />

			<div class="footer">
				<div class="cancel" @click="isShow = false">取消</div>
				<div class="confirm" @click="confirm">确定</div>
			</div>
		</van-dialog>
	</div>
</template>
<script setup>
import { ref, watch, nextTick, onMounted } from 'vue';
import { showToast } from 'vant';
const props = defineProps({
	text: { require: true },
	phone: { require: false },
	email: { require: false },
});
const isShow = ref(false);
// eslint-disable-next-line vue/no-dupe-keys
const phone = ref('');
// eslint-disable-next-line vue/no-dupe-keys
const email = ref('');
const field = ref(null);
const field1 = ref(null);

const regex = /^1[3-9]\d{9}$/;
const regex1 = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;

const emit = defineEmits(['confirm']);
const confirm = () => {
	if (props.text === '手机号') {
		if (regex.test(phone.value)) {
			isShow.value = false;
			emit('confirm', phone.value);
			return;
		}
		showToast({ message: '手机号格式有误', position: 'top' });
		field.value.focus();
	} else {
		if (regex1.test(email.value)) {
			isShow.value = false;
			emit('confirm', email.value);
			return;
		}
		showToast({ message: '邮箱格式有误', position: 'top' });
		field1.value.focus();
	}
};
onMounted(() => {
	if (props.text === '手机号') {
		phone.value = props.phone;
	} else {
		email.value = props.email;
	}
});
watch(isShow, async (newValue) => {
	if (props.text === '手机号') {
		await nextTick();
		setTimeout(() => {
			if (newValue) {
				field.value.focus();
			}
		}, 0);
	} else {
		await nextTick();
		setTimeout(() => {
			if (newValue) {
				field1.value.focus();
			}
		}, 0);
	}
});
defineExpose({
	isShow,
});
</script>
<style scoped lang="scss">
::v-deep(.van-overlay) {
	background-color: rgba(0, 0, 0, 50%);
}
::v-deep(.van-dialog) {
	width: 86%;
	border-radius: 4px !important;
	padding: 15px 17px 15px 23px;
	.van-dialog__header {
		padding: 0px 0px;
		text-align: left;
		font-size: 18px;
		color: rgba(16, 16, 16, 100%);
	}
	.van-cell:after {
		display: none;
	}
	.van-cell {
		margin-top: 16px;
		padding: 0;
		.van-field__control {
			color: #172b4d;
		}
	}
	.footer {
		margin-top: 55px;
		text-align: center;
		line-height: 32px;
		display: flex;
		font-size: 14px;
		justify-content: flex-end;
		.cancel,
		.confirm {
			width: 56px;
			height: 32px;
			color: #172b4d;
			background-color: #ededed;
			border-radius: 3px;
		}
		.confirm {
			margin-left: 8px;
			background-color: #0052cc !important;
			color: #fff;
		}
	}
}
</style>
