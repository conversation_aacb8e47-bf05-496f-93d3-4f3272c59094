<template>
	<div>
		<div class="maintain">
			<van-dialog v-model:show="maintainShow" :show-cancel-button="false" :showConfirmButton="false">
				<template #title>
					<van-icon name="question" color="#faad15" size="5.314vw" />
					<span>您尚未维护此医生手机号</span>
				</template>
				<div class="footer">
					<div class="cancel" @click="maintainShow = false">取消</div>
					<div class="confirm" @click="confirm">维护</div>
				</div>
			</van-dialog>
		</div>
		<div class="phone">
			<Dialogone ref="phoneDialog" :text="'手机号'" :phone="phone" @confirm="confirmPhone"></Dialogone>
		</div>
	</div>
</template>
<script setup>
import Dialogone from './dialog';
import { ref } from 'vue';
const props = defineProps(['phone']);
const emit = defineEmits(['maintainPhone']);
const phoneDialog = ref(null);
const maintainShow = ref(false);
const confirm = () => {
	maintainShow.value = false;
	phoneDialog.value.isShow = true;
};
const confirmPhone = (v) => {
	emit('maintainPhone', v);
};
defineExpose({ maintainShow });
</script>
<style scoped lang="scss">
::v-deep(.van-overlay) {
	background-color: rgba(0, 0, 0, 50%);
}
.maintain ::v-deep(.van-dialog) {
	width: 86%;
	border-radius: 4px !important;
	padding: 15px 17px 15px 23px;
	.van-dialog__header {
		padding: 0px 0px;
		text-align: left;
		font-size: 16px;
		color: rgba(16, 16, 16, 100%);
		display: flex;
		align-items: center;
		.van-icon {
			margin-right: 12px;
		}
	}
	.van-cell:after {
		display: none;
	}
	.van-cell {
		margin-top: 16px;
		padding: 0;
		.van-field__control {
			color: #9f9f9f;
		}
	}
	.footer {
		margin-top: 55px;
		text-align: center;
		line-height: 32px;
		display: flex;
		font-size: 14px;
		justify-content: flex-end;
		.cancel,
		.confirm {
			width: 56px;
			height: 32px;
			color: #172b4d;
			background-color: #ededed;
			border-radius: 3px;
		}
		.confirm {
			margin-left: 8px;
			background-color: #0052cc;
			color: #fff;
		}
	}
}
</style>
