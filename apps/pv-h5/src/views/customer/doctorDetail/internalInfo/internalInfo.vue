<template>
	<div class="inter-info">
		<div class="zong">
			<div class="concat">
				<div class="title">
					<span class="blue"></span>
					<span class="title-value">联系渠道</span>
				</div>
				<div class="item">
					<div class="phone">
						<span class="fixed-width">
							<span>医生手机号：</span>
							<span>{{ state.phone }}</span>
						</span>
						<span class="defend" @click="phoneDialog.isShow = true">维护</span>
					</div>
					<div class="email">
						<span class="fixed-width">
							<span>医生邮箱：</span>
							<span>{{ state.email }}</span>
						</span>
						<span class="defend" @click="emailDialog.isShow = true">维护</span>
					</div>
					<div class="wecom">
						<span class="fixed-width">
							<span>企业微信：</span>
							<span v-if="!state.weCom">未招募</span>
							<span v-else>
								<p>微信昵称：{{ state.weCom.nickName }}</p>
								<p>添加微信的代表：{{ state.weCom.sales.map((ele) => ele.username).join('、') }}</p>
							</span>
						</span>
						<span v-if="!state.weCom" class="defend" @click="toBound">绑定外部联系人</span>
						<span v-if="state.weCom && jbShow" class="defend" :style="{ color: !state.jbClick ? 'rgb(163 152 152)' : '' }" @click="dialogShow">解绑</span>
					</div>
				</div>
			</div>
			<div class="code">
				<div class="title">
					<span class="blue"></span>
					<span class="title-value">客户编码</span>
				</div>
				<div class="item">
					<div class="khsf">
						<span class="fixed-width-sm">
							<span>客户身份：</span>
							<span>{{ doctorInfo.doctorType }}</span>
						</span>
					</div>
					<div class="khsf">
						<span class="fixed-width-sm">
							<span>客户编码：</span>
							<span>{{ doctorInfo.companyDoctorId }}</span>
						</span>
					</div>
					<div class="khsf">
						<span class="fixed-width-sm">
							<span>医院编码：</span>
							<span>{{ doctorInfo.companyHospitalId }}</span>
						</span>
					</div>
				</div>
			</div>
			<div class="product">
				<div class="title">
					<span class="blue"></span>
					<span class="title-value">产品分级</span>
				</div>
				<div class="table table-muqiao">
					<table>
						<thead>
							<tr>
								<td>医院</td>
								<td>{{ qyId === 'muqiao' ? '客户分级' : '客户潜力分级' }}</td>
								<td>产品</td>
							</tr>
						</thead>
						<tbody>
							<tr v-for="(item, index) in state.product" :key="index">
								<td>{{ item.hospitalName || item.hospital }}</td>
								<td>{{ item.rating }}</td>
								<td>{{ item.productName }}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="colleage">
				<div class="title">
					<span class="blue"></span>
					<span class="title-value">负责该医生的相关同事</span>
				</div>
				<div class="cardd">
					<template v-if="qyId === 'muqiao'">
						<div style="width: 100%" class="card-item" v-for="(item, index) in state.colleageList" :key="index">
							<div class="box">
								<span class="name">{{ item.name }}</span>
								<span class="dep">{{ item.id }}</span>
							</div>
						</div>
					</template>
					<template v-else>
						<div class="card-item" v-for="(item, index) in state.colleageList" :key="index" :style="{ 'padding-left': index % 2 === 0 ? '0px' : '6.28vw' }">
							<div class="box">
								<span class="name">{{ item.name }}</span>
								<span class="dep">{{ item.id.split('@').length > 1 ? item.id.split('@')[0] : item.id }}</span>
							</div>
							<div v-if="index % 2 === 0" class="gang"></div>
						</div>
					</template>
				</div>
			</div>
		</div>
		<!-- 确定 -->
		<van-dialog v-model:show="isShow" :show-cancel-button="false" :showConfirmButton="false">
			<template #title>
				<span class="gou"></span>
				<span>提示</span>
			</template>
			<div class="text">操作立即生效，确认解绑？</div>
			<div class="footer">
				<div class="cancel" @click="isShow = false">取消</div>
				<div class="confirm" @click="unbindingConfirm">确定</div>
			</div>
		</van-dialog>

		<Dialogone ref="phoneDialog" :text="'手机号'" :phone="state.phone" @confirm="confirm"></Dialogone>
		<Dialogone ref="emailDialog" :text="'邮箱'" :email="state.email" @confirm="confirmEmail"></Dialogone>
		<Maintain ref="maintain" :phone="state.phone" @maintainPhone="maintainPhone"></Maintain>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Dialogone from './components/dialog';
import Maintain from './components/maintain';
import { showToast, showLoadingToast, closeToast } from 'vant';
import { postdoctorinfo, refreshExternal, refreshChannel, unbindingInterface, getcontent, findProduct, findRelatedColleagues } from '@/api/doctor';
import { debounce } from '@/utils/index';
const { proxy } = getCurrentInstance();
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
import usefilterStore from '@/store/modules/filter';
const filterStore = usefilterStore();
const route = useRoute();
const router = useRouter();
const userId = ref(userStore.userInfo.username);
const jbShow = ref('');
const doctorInfo = ref(filterStore.doctorInfo);
const relam = ref(enterStore.enterInfo.isProxy);
const qyId = ref(userStore.userInfo.enterpriseVO.id);
const isShow = ref(false);

const state = reactive({
	colleageList: [],
	phone: '',
	email: '',
	weCom: '',
	product: [],
	jbClick: false,
});
const phoneDialog = ref(null);
const emailDialog = ref(null);
const maintain = ref(null);
//维护手机号
const confirm = (value, source = '') => {
	proxy.$umeng('维护' + doctorInfo.value.doctorName + '医生的手机号为', '医生360联系渠道', value);
	const data = {
		doctorId: route.query.doctorId,
		channelCode: 'MOBILE',
		channelValue: value,
	};
	postdoctorinfo(data)
		.then((res) => {
			console.log(res);
			if (res.code === 200) {
				state.phone = value;
				if (source === 'Maintain') {
					router.push({ name: 'wxworkInfo', params: { id: route.query.doctorId } });
				}
			} else {
				showToast('手机号已重复，请联系管理员！');
			}
		})
		.catch(() => {
			showToast('手机号已重复，请联系管理员！');
		});
};
//维护邮箱
const confirmEmail = (value) => {
	proxy.$umeng('维护' + doctorInfo.value.doctorName + '医生的邮箱为', '医生360联系渠道', value);
	const data = {
		doctorId: route.query.doctorId,
		channelCode: 'MAILBOX',
		channelValue: value,
	};
	postdoctorinfo(data)
		.then((res) => {
			if (res.code === 200) {
				state.email = value;
			} else {
				showToast('邮箱维护失败，请联系管理员！');
			}
		})
		.catch(() => {
			showToast('邮箱维护失败，请联系管理员！');
		});
};
const maintainPhone = (value) => {
	confirm(value, 'Maintain');
};
//绑定外部联系人
const toBound = () => {
	proxy.$umeng('点击', '医生360联系渠道', `${doctorInfo.value.doctorName}绑定外部联系人`);
	if (!state.phone) {
		maintain.value.maintainShow = true;
		return;
	}
	router.push({ name: 'wxworkInfo', params: { id: route.query.doctorId } });
};
//同步外部联系人列表
const a = () => {
	return new Promise((resolve, reject) => {
		refreshExternal(userId.value, relam.value).then((res) => {
			resolve();
		});
	});
};
//同步代表绑定信息
const b = async () => {
	return new Promise((resolve, reject) => {
		refreshChannel(route.query.doctorId).then((res) => {
			resolve();
		});
	});
};
const unbindingConfirm = () => {
	showLoadingToast({
		message: '解绑中...',
		forbidClick: true,
		duration: 0,
	});
	debounce(unbinding, 1000);
};
//解绑
const unbinding = () => {
	proxy.$umeng('点击', '医生360联系渠道-解绑', `${doctorInfo.value.doctorName}确定绑定${state.lxr}`);
	unbindingInterface({ doctorId: doctorInfo.value.doctorId }, relam.value).then((res) => {
		state.weCom = '';
		showToast('解绑成功');
		isShow.value = false;
	});
};

const dialogShow = () => {
	if (!state.jbClick) return;
	isShow.value = true;
};
onMounted(async () => {
	// 非天普租户 通过console 菜单控制
	jbShow.value = perStore.systemBtnList.some((item) => item.path === 'canUnbind');
	//查询渠道
	getcontent(route.query.doctorId).then((res) => {
		console.log(res);
		if (res.code === 200) {
			state.phone = res.result[0].doctorChannelConfig.filter((ele) => ele.channelCode === 'MOBILE')[0]?.channelValue;
			state.email = res.result[0].doctorChannelConfig.filter((ele) => ele.channelCode === 'MAILBOX')[0]?.channelValue;
			state.weCom = JSON.parse(res.result[0].doctorChannelConfig.filter((ele) => ele.channelCode === 'WECOM')[0]?.channelValue);
		}
	});
	//查询产品分级
	findProduct(
		route.query.doctorId
		// 'C00049125'
	).then((res) => {
		if (res.code === 200) {
			state.product = res.result;
		}
	});
	//查询相关同事
	findRelatedColleagues(route.query.doctorId).then((res) => {
		console.log(res);
		if (res.code === 200) {
			state.colleageList = res.result;
		}
	});
	await a();
	if (!relam.value) {
		await b();
	}
	state.jbClick = true;
	//查询渠道
	getcontent(route.query.doctorId).then((res) => {
		console.log(res);
		if (res.code === 200) {
			state.phone = res.result[0].doctorChannelConfig.filter((ele) => ele.channelCode === 'MOBILE')[0].channelValue;
			state.email = res.result[0].doctorChannelConfig.filter((ele) => ele.channelCode === 'MAILBOX')[0].channelValue;
			state.weCom = JSON.parse(res.result[0].doctorChannelConfig.filter((ele) => ele.channelCode === 'WECOM')[0].channelValue);
		}
	});
});
</script>

<style lang="scss" scoped>
.inter-info {
	background-color: #fff;
}
.zong {
	padding: 12px;
	.title {
		color: #172b4d;
		display: flex;
		align-items: center;
		font-size: 16px;
		text-align: left;
		font-weight: 600;
		font-family: PingFangSC-medium;
		margin-bottom: 12px;
		padding-left: 3px;
		.blue {
			display: inline-block;
			width: 8px;
			height: 8px;
			border-radius: 50%;
			background-color: #0052cc;
			margin-right: 12px;
		}
	}
	.defend {
		color: #0052cc;
	}
	.concat,
	.code {
		margin-bottom: 20px;
		.item {
			padding: 0 23px;
			div {
				font-size: 14px;
				font-family: PingFangSC-regular;
				color: #172b4d;
				margin-bottom: 12px;
				display: flex;
				justify-content: space-between;
				.fixed-width {
					display: flex;
					flex: 1;
					span:nth-child(1) {
						width: 85px;
						white-space: nowrap;
					}
					span:nth-child(2) {
						flex: 1;
						word-break: break-all;
					}
				}
				.fixed-width-sm {
					display: flex;
					span:nth-child(1) {
						// width: 70px;
					}
					span:nth-child(2) {
						flex: 1;
					}
				}
			}
		}
	}
	.product {
		margin-bottom: 20px;
		.table {
			margin-left: 24px;
			table {
				font-size: 14px;
				border: 1px solid #dfe1e6;
				width: 100%;
				text-align: center;
				tr {
					td:nth-child(1) {
						border-right: 1px solid #dfe1e6;
					}
				}
				td {
					height: 35px;
				}
				thead {
					color: #0052cc;
					background-color: #e5efff;
					td {
						width: 50%;
					}
				}
				tbody {
					tr:nth-child(2n) {
						background-color: rgba(201, 225, 253, 10%);
					}
				}
			}
		}

		.table-muqiao {
			thead {
				td {
					width: 33.333% !important;
				}
			}
		}
	}
	.colleage {
		.cardd {
			margin: 0 10px;
			padding: 21px 31px;
			display: flex;
			flex-wrap: wrap;
			.card-item {
				width: 50%;
				margin-bottom: 13px;
				font-size: 14px;
				display: flex;
				align-items: center;
				.box {
					width: 100%;
					display: flex;
					justify-content: space-between;
				}
				.name {
					color: #172b4d;
				}
				.dep {
					color: #00b8d9;
				}
				.gang {
					border-right: 1px solid rgba(51, 119, 255, 20%);
					width: 1px;
					height: 12px;
					margin: 0 0px 0px 31px;
				}
			}
		}
	}
}
::v-deep(.van-dialog) {
	width: 86%;
	border-radius: 4px !important;
	padding: 31px 17px 23px 32px;
	.van-dialog__header {
		padding: 0px 0px;
		text-align: left;
		font-size: 16px;
		color: rgba(16, 16, 16, 100%);
		font-weight: bold;
		display: flex;
		align-items: center;
		.gou {
			display: inline-block;
			width: 22px;
			height: 22px;
			background-color: #ff991f;
			border-radius: 50%;
			margin-right: 16px;
			position: relative;
		}
		.gou::before {
			content: '';
			display: inline-block;
			width: 8px;
			height: 3px;
			border-top: 1px solid #fff;
			border-right: 1px solid #fff;
			transform: rotate(-225deg) translate(-50%, -50%);
			position: absolute;
			top: 45%;
			left: 10%;
		}
	}
	.van-cell:after {
		display: none;
	}
	.van-cell {
		margin-top: 16px;
		padding: 0;
		.van-field__control {
			color: #172b4d;
		}
	}
	.text {
		font-size: 14px;
		color: rgba(95, 90, 89, 100%);
		padding: 12px 38px 0;
	}
	.footer {
		margin-top: 36px;
		text-align: center;
		line-height: 32px;
		display: flex;
		font-size: 14px;
		justify-content: flex-end;
		.cancel,
		.confirm {
			width: 56px;
			height: 32px;
			color: #172b4d;
			background-color: #ededed;
			border-radius: 3px;
		}
		.confirm {
			margin-left: 8px;
			background-color: #0052cc;
			color: #fff;
		}
	}
}
</style>
