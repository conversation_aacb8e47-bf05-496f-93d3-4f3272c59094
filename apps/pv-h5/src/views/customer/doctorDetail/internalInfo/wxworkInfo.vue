<template>
	<div class="wxworkInfo">
		<van-nav-bar title="绑定信息" left-arrow @click-left="onClickLeft">
			<template #right>
				<img src="@/assets/img/refresh.png" alt="" @click="refresh" />
			</template>
		</van-nav-bar>
		<van-tabs color="#0052CC" title-active-color="#0052CC" title-inactive-color="#172B4D" v-model:active="selected" @click-tab="clickNav">
			<van-tab v-for="(item, index) in navList" :key="index" :name="item.id" :title="item.label">
				<div v-if="loading" class="tc">
					<van-loading color="#26a2ff" />
				</div>
				<!-- 数据 -->
				<div v-else v-for="(item, index) in state.dataList" :key="index" @click="toBinding(item)">
					<!-- <van-index-anchor :index="item.index"> </van-index-anchor> -->
					<!-- 联系人信息 -->
					<div class="messgList">
						<div class="iamge">
							<!-- 图像 -->
							<div class="msgImg">
								<img width="100%" :src="item.externalAvatar" />
							</div>
						</div>
						<div style="position: relative">{{ item.externalName }}</div>
					</div>
				</div>
				<div class="nodata" v-if="!loading && state.dataList.length === 0">暂无数据</div>
			</van-tab>
		</van-tabs>

		<!-- 确定 -->
		<van-dialog v-model:show="isShow" :show-cancel-button="false" :showConfirmButton="false">
			<template #title>
				<span class="gou"></span>
				<span>提示</span>
			</template>
			<div class="text">您确定要绑定吗？</div>
			<div class="footer">
				<div class="cancel" @click="isShow = false">取消</div>
				<div class="confirm" @click="confirm">确定</div>
			</div>
		</van-dialog>
	</div>
</template>
<script setup>
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue';
import { externalList, bindExternal, refreshExternal } from '@/api/doctor';
import { useRoute, useRouter } from 'vue-router';
import { showToast } from 'vant';
import { showLoadingToast, closeToast } from 'vant';
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
import usefilterStore from '@/store/modules/filter';
const filterStore = usefilterStore();
import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
const relam = ref(enterStore.enterInfo.isProxy);
const selected = ref(0);
let loading = ref(false);
const isShow = ref(false);

const navList = reactive([
	{ id: '0', label: '未绑定' },
	{ id: '1', label: '已绑定' },
]);
const state = reactive({
	dataList: [],
	indexList0: [], // 索引表
	indexList1: [], // 索引表
	data0: [],
	data1: [],
	lxr: {},
});

const onClickLeft = () => {
	proxy.$umeng('点击', '医生360联系渠道-绑定信息', `回到医生360页面`);
	history.back();
};
const clickNav = ({ name }) => {
	if (name === '0') {
		proxy.$umeng('点击', '医生360联系渠道-绑定信息', `未绑定`);
	} else {
		proxy.$umeng('点击', '医生360联系渠道-绑定信息', `已绑定`);
	}
	state.dataList = state['data' + name];
};
const { value } = computed(() => {
	return userStore.userInfo.username;
});
const doctorInfo = ref(filterStore.doctorInfo);
const getList = (isBind) => {
	loading.value = true;
	externalList(
		value,
		// '13020831359',
		isBind,
		relam.value
	).then((res) => {
		console.log(res);
		if (res.code === 200) {
			state['data' + isBind] = res.result;
			clickNav({ name: selected.value });
			loading.value = false;
		}
	});
};
const toBinding = (i) => {
	proxy.$umeng('点击', '医生360联系渠道-绑定信息', `${i.externalName}`);
	if (selected.value === '0') {
		isShow.value = true;
	}
	state.lxr = i;
};
const confirm = () => {
	proxy.$umeng('点击', '医生360联系渠道-绑定信息', `${doctorInfo.value.doctorName}确定绑定${state.lxr}`);
	showLoadingToast({
		message: '绑定中...',
		forbidClick: true,
		duration: 0,
	});
	bind();
};
const bind = () => {
	let json = {
		externalUserId: state.lxr.externalUserId,
		avatar: state.lxr.externalAvatar,
		externalName: state.lxr.externalName,
		unionId: state.lxr.unionId,
		doctorId: route.params.id,
	};
	bindExternal(json, relam.value)
		.then((res) => {
			console.log(res);
			if (res.code === 200) {
				showToast({
					message: '绑定成功',
					position: 'top',
				});
				isShow.value = false;
				router.go(-1);
			}
		})
		.catch((err) => {
			isShow.value = false;
			closeToast();
		});
};
const refresh = () => {
	proxy.$umeng('点击', '医生360联系渠道-绑定信息', `刷新`);
	showLoadingToast({
		message: '更新中...',
		forbidClick: true,
		duration: 0,
	});
	refreshExternal(value, relam.value)
		.then((res) => {
			getList('0');
			getList('1');
		})
		.finally(() => {
			closeToast();
		});
};
onMounted(() => {
	getList('0');
	getList('1');
});
</script>
<style lang="scss" scoped>
.wxworkInfo {
	::v-deep(.van-nav-bar__content) {
		height: 40px;
		.van-icon {
			color: #000;
		}
		.van-nav-bar__title {
			color: rgba(16, 16, 16, 100%);
			font-size: 16px;
		}
	}
	::v-deep(.van-tabs) {
		.van-tabs__wrap {
			height: 45px;
			margin-bottom: 10px;
		}
		.van-tab__text {
			font-size: 14px;
			position: relative;
			top: -5px;
		}
		.van-tab--active {
			font-weight: normal;
		}
		.van-tabs__line {
			width: 195px;
			bottom: 7px;
			height: 2px;
		}
		.van-tabs__nav--line {
			padding-bottom: 0;
			margin-top: 3px;
		}
	}
	.messgList {
		background: #fff;
		padding: 11.04px 11.04px 5.52px;
		border-bottom: 1px solid #e8e8e8;
		position: relative;
		display: flex;
		align-items: center;
	}
	.msgImg {
		display: inline-block;
		width: 44.16px;
		height: 44.16px;
		border-radius: 3px;
		overflow: hidden;
	}
	img {
		width: 100%;
	}
	.messgList .iamge {
		width: 20%;
		overflow: hidden;
		text-align: center;
	}
	.messgList .mesgRight {
		width: 80%;
	}
	::v-deep(.van-overlay) {
		background-color: rgba(0, 0, 0, 50%);
	}
	::v-deep(.van-dialog) {
		width: 86%;
		border-radius: 4px !important;
		padding: 31px 17px 23px 32px;
		.van-dialog__header {
			padding: 0px 0px;
			text-align: left;
			font-size: 16px;
			color: rgba(16, 16, 16, 100%);
			font-weight: bold;
			display: flex;
			align-items: center;
			.gou {
				display: inline-block;
				width: 22px;
				height: 22px;
				background-color: rgba(82, 196, 26, 100%);
				border-radius: 50%;
				margin-right: 16px;
				position: relative;
			}
			.gou::before {
				content: '';
				display: inline-block;
				width: 8px;
				height: 3px;
				border-top: 1px solid #fff;
				border-right: 1px solid #fff;
				transform: rotate(-225deg) translate(-50%, -50%);
				position: absolute;
				top: 45%;
				left: 10%;
			}
		}
		.van-cell:after {
			display: none;
		}
		.van-cell {
			margin-top: 16px;
			padding: 0;
			.van-field__control {
				color: #9f9f9f;
			}
		}
		.text {
			font-size: 14px;
			color: rgba(95, 90, 89, 100%);
			padding: 12px 38px 0;
		}
		.footer {
			margin-top: 36px;
			text-align: center;
			line-height: 32px;
			display: flex;
			font-size: 14px;
			justify-content: flex-end;
			.cancel,
			.confirm {
				width: 56px;
				height: 32px;
				color: #838080;
				background-color: #ededed;
				border-radius: 3px;
			}
			.confirm {
				margin-left: 8px;
				background-color: #3377ff;
				color: #fff;
			}
		}
	}
}
.nodata {
	padding: 20px 40px;
	text-align: center;
}
</style>
