import * as echarts from 'echarts';
/**
 * @method
 * @param id 绑定元素的id
 * @returns  运营商名称
 * @desc 月累计折线图
 */
export function lineCharts() {
	let myChart;
	const month_line_charts = (id, dataX, dataY, color) => {
		// 引用全局变量
		if (myChart != null && myChart !== '' && myChart !== undefined) {
			myChart.dispose(); // 销毁
		}
		myChart = echarts.init(document.getElementById(id));
		// 绘制图表
		myChart.setOption({
			color: [color],
			grid: {
				// 在容器中上下左右的偏移
				left: '13%',
				top: '10%',
				right: '0%',
				bottom: '15%',
			},
			xAxis: {
				boundaryGap: false,
				type: 'category',
				nameLocation: 'middle',
				// x轴线配置
				axisLine: {
					show: true,
					lineStyle: {
						color: '#CBCDCF',
						width: 1,
						type: 'solid',
					},
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					color: '#000', // 坐标值得具体的颜色
					fontSize: '10px',
					align: 'right',
					// interval: 0 // x轴坐标全部展示 不自动隐藏
				},
				data: dataX,
			},
			yAxis: {
				type: 'value', // category为刻度值
				// y轴线配置
				axisLine: {
					show: true,
					lineStyle: {
						// 坐标线的具体颜色
						color: '#CBCDCF',
						width: 1,
						type: 'solid',
					},
				},
				axisLabel: {
					color: '#000', // 坐标值得具体的颜色
				},
				splitLine: { show: false }, // 去除网格线 // 不显示横线
			},
			series: [
				{
					data: dataY,
					type: 'line',
					smooth: true, // 是否开启平滑过渡
					symbol: 'none', // 折线图不显示圆点
				},
			],
		});
	};
	return {
		month_line_charts,
	};
}
