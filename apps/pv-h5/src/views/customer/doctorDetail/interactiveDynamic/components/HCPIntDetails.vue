<template>
	<div class="HCPDynamic_components">
		<div class="dynamic_box">
			<img src="@/assets/img/clock.png" v-show="articleList.length > 0" alt="" />
			<div style="border-left: 2px dashed rgba(51, 119, 255, 0.45)" v-for="(item, index) in articleList" :key="index">
				<div>
					<span class="dynamic_circle"></span>
					<span class="dynamic_time">{{ item.actionStartTime?.replace('T', ' ') }}</span>
					<span class="dynamic_type">{{ item.actionType }}</span>
				</div>
				<div class="dynamic_content">
					<div class="box">
						<div class="box-title">{{ item.subject }}</div>
						<div class="box-type" v-if="item.duration">
							<div class="box-type-circle"></div>
							<span>在线时长:{{ item.duration }}</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="more" @click="getMore" v-if="articleList.length < articleTotal">加载更多...</div>
		<div class="nomore" v-else>没有更多了</div>
	</div>
</template>
<script>
import { reactive, toRefs, toRef } from 'vue';
export default {
	name: 'Index',
	props: {
		detailInfo: {
			require: true,
		},
		total: {
			require: true,
		},
	},
	setup(props, context) {
		const state = reactive({
			list: [1, 4, 5],
		});
		const articleList = toRef(props, 'detailInfo');
		const articleTotal = toRef(props, 'total');
		const getMore = () => {
			context.emit('getDetails');
		};
		return {
			...toRefs(state),
			articleList,
			articleTotal,
			getMore,
		};
	},
};
</script>
<style lang="scss" scoped>
.HCPDynamic_components {
	padding: 10px;
	.dynamic_box {
		// border-left: 2px dashed rgba(51, 119, 255, 0.45);
		position: relative;
		img {
			width: 16px;
			position: absolute;
			left: -7px;
			top: -16px;
		}
		.dynamic_circle {
			display: inline-block;
			width: 8px;
			height: 8px;
			border-radius: 50%;
			background-color: #0052cc;
			position: relative;
			left: -5px;
			top: -1px;
			margin-top: 15px;
		}
		.dynamic_time {
			margin-left: 11px;
			color: rgba(136, 133, 133, 1);
			font-size: 12px;
		}
		.dynamic_content {
			padding: 15px 15px;
			.box {
				background-color: rgba(239, 245, 255, 1);
				padding: 10px 15px;
				&-title {
					color: rgba(16, 16, 16, 1);
					font-size: 14px;
				}
				&-type {
					display: flex;
					align-items: center;
					margin-top: 11px;
					&-circle {
						width: 5px;
						height: 5px;
						border-radius: 50%;
						background-color: rgba(94, 213, 145, 1);
						margin-right: 12px;
					}
					span {
						color: rgba(81, 81, 81, 1);
						font-size: 14px;
					}
					span:nth-of-type(1) {
						margin-right: 31px;
					}
				}
			}
		}
	}
}
.more {
	text-align: center;
	color: #0052cc;
}
.nomore {
	color: #172b4d;
	text-align: center;
}
.dynamic_type {
	border-radius: 12px;
	background-color: rgba(199, 241, 244, 0.4);
	text-align: center;
	padding: 2px 7px;
	color: rgba(54, 207, 211, 1);
	font-size: 12px;
	margin-left: 6px;
}
</style>
