<template>
	<div class="box">
		<van-notice-bar wrapable :scrollable="false" left-icon="volume-o" color="#F45009" background="#FFF2F2" text="以下数据截止至昨天24:00" />
	</div>
	<div class="LabelFilter">
		<van-row>
			<van-col :span="3"><span>日期:</span></van-col>
			<van-col :span="21">
				<span class="lable_filter_item" :class="flagTime == index ? 'timeActive' : ''" v-for="(item, index) in item" :key="index" @click="timeFilter(item, index)">{{ item }}</span></van-col
			>
		</van-row>
		<van-row>
			<van-col :span="3"><span>类型:</span></van-col>
			<van-col :span="21">
				<span class="lable_filter_item" :class="flagType == index ? 'typeActive' : 'type'" v-for="(item, index) in item1" :key="index" @click="typeFilter(item, index)">{{ item }}</span></van-col
			>
		</van-row>
	</div>
	<!-- eslint-disable-next-line vue/no-multiple-template-root -->
	<div class="HCPIntDetails">
		<HCPIntDetails :detailInfo="detailInfo" :total="detailTotal" @getDetails="getDetails"></HCPIntDetails>
	</div>
</template>
<script>
import { reactive, toRefs, ref, getCurrentInstance, onMounted, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { GMTToStr, dateBefore, dateMonthBefore } from '@/utils/index.js';
import usefilterStore from '@/store/modules/filter';
import { detailInterface, getDataType } from '@/api/doctor';
import HCPIntDetails from './HCPIntDetails.vue';
export default {
	name: 'Index',
	components: {
		HCPIntDetails,
	},
	setup(props, context) {
		const filterStore = usefilterStore();

		const { proxy } = getCurrentInstance();
		const route = useRoute();
		const doctorInfo = ref(filterStore.doctorInfo);
		const state = reactive({
			item: ['全部', '当月', '近三个月', '近半年', '近一年'],
			item1: ['全部'],
			detailInfo: [],
			detailTotal: '',
			page: 0,
			startDate: '',
			endDate: '',
			typeFilter: '年',
			typeLabel: '',
		});
		// 时间筛选
		const flagTime = ref(0);
		const timeFilter = (item, index) => {
			proxy.$umeng('点击', '医生360互动一览', `明细-${doctorInfo.value.doctorName}日期筛选${item}`);
			flagTime.value = index;
		};
		// 类型筛选
		const flagType = ref(0);
		const typeFilter = (item, index) => {
			proxy.$umeng('点击', '医生360互动一览', `明细-${doctorInfo.value.doctorName}类型筛选${item}`);
			flagType.value = index;
		};
		const func = () => {
			if (flagTime.value === 0) {
				// 全部
				state.endDate = '';
				state.startDate = '';
				state.typeFilter = '年';
			} else if (flagTime.value === 1) {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateMonthBefore(state.endDate, 1);
				state.typeFilter = '日';
			} else if (flagTime.value === 2) {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateMonthBefore(state.endDate, 3);
				state.typeFilter = '月';
			} else if (flagTime.value === 3) {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateMonthBefore(state.endDate, 6);
				state.typeFilter = '月';
			} else {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateBefore(state.endDate, 1);
				state.typeFilter = '月';
			}
		};
		watch(
			[flagTime, flagType],
			(newValue, oldValue) => {
				func();
				state.detailInfo = [];
				state.page = 0;
				flagType.value === 0 ? (state.typeLabel = '') : (state.typeLabel = state.item1[flagType.value]);
				detail();
			},
			{
				deep: true,
			}
		);
		const detail = () => {
			const query = {
				doctorId: route.query.doctorId,
				size: 10,
				page: state.page,
				dataType: state.typeLabel,
				startDate: state.startDate,
				endDate: state.endDate,
			};

			detailInterface(query).then((res) => {
				state.detailInfo = state.detailInfo.concat(res.result.content);
				state.detailTotal = res.result.totalElements;
			});
		};
		const getDetails = () => {
			state.page++;
			detail();
		};
		onMounted(detail);
		onMounted(async () => {
			document.body.scrollTop = document.documentElement.scrollTop = 0;
			//获取类型
			let res = await getDataType();
			console.log(res);
			res.result ? (state.item1 = state.item1.concat(res.result)) : '';
		});
		return {
			...toRefs(state),
			flagTime,
			timeFilter,
			flagType,
			typeFilter,
			getDetails,
		};
	},
};
</script>
<style lang="scss" scoped>
.van-notice-bar {
	margin-top: 12px;
	font-size: 13px !important;
	// font-weight: 600;
}
.van-notice-bar--wrapable {
	padding: 8px;
}
::v-deep(.van-notice-bar__content) {
	line-height: 18px;
}
.LabelFilter {
	padding: 0 15px;
	margin-top: 10px;
	color: #172b4d;
	.van-row {
		margin-top: 5px;
	}
	.lable_filter_item {
		display: inline-block;
		padding: 0 5px;
		height: 20px;
		line-height: 20px;
		border-radius: 6px;
		text-align: center;
		font-size: 12px;
		color: #0052cc;
		background-color: rgba(51, 119, 255, 0.1);
		margin-left: 5px;
		margin-bottom: 5px;
	}
	.timeActive {
		color: #fff;
		background-color: rgba(66, 119, 255, 100);
	}
	.typeActive {
		color: #fff;
		background-color: rgba(66, 119, 255, 100);
	}
	.type {
		color: #0052cc;
		background-color: rgba(51, 119, 255, 0.1);
	}
}
.HCPIntDetails {
	padding: 20px 20px;
}
</style>
