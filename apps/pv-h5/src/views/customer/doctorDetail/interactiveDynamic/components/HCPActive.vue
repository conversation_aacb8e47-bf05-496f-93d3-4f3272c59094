<template>
	<div class="HCPActive_components">
		<div class="HcpActive">
			<div class="jiejue"><span class="circle"></span> <span class="title">医生活跃度</span></div>
			<van-notice-bar wrapable :scrollable="false" left-icon="volume-o" color="#F45009" background="#FFF2F2" text="点击标签颜色，查看或关闭相关活跃度" />
		</div>
		<div class="filter">
			<span v-for="(item, index) in tabList" :key="item" @click="tabHandler(item)">
				<span class="circleInfo circle" :style="getColor(index)"></span>
				<span class="tab1">{{ item }}</span>
			</span>
		</div>
	</div>
	<!-- eslint-disable-next-line vue/no-multiple-template-root -->
	<div id="main" class="pie"></div>
</template>
<script>
import { reactive, toRefs, toRef, watch, onMounted, getCurrentInstance, computed } from 'vue';
import { getDataType } from '@/api/doctor';
import { lineCharts } from './activeCharts'; // 月累计折线图

export default {
	name: 'Index',
	props: {
		activenessInfo: {
			require: true,
		},
		typeFilter: {
			require: true,
		},
	},
	setup(props, context) {
		const { proxy } = getCurrentInstance();
		const state = reactive({
			lineColor: '#3377fe',
			colorList: ['#3377fe', '#18cdec', '#9e5bfa', '#FFAB00', '#96ffa0'],
			tabList: ['总活跃度'],
			currentTab: '总活跃度',
		});
		const tabHandler = (val) => {
			state.currentTab = val;
			state.lineColor = state.colorList[state.tabList.findIndex((ele) => ele === val) % state.colorList.length];
			func(val);
		};
		const { month_line_charts } = lineCharts();
		const activeness = toRef(props, 'activenessInfo');
		const xType = toRef(props, 'typeFilter');
		const func = (val) => {
			if (activeness.value[val] && activeness.value[val].length > 0) {
				const dataX = [];
				const dataY = [];
				activeness.value[val].forEach((item) => {
					dataX.push(item.actionDate);
					dataY.push(item.count);
				});
				if (xType.value && xType.value === '日') {
					const arr1 = [];
					if (dataX && dataX.length > 0) {
						dataX.forEach((item) => {
							const arr = item.split('-');
							const str = arr[2] + '日';
							arr1.push(str);
						});
					}
					month_line_charts('main', arr1, dataY, state.lineColor);
				} else if (xType.value && xType.value === '月') {
					const arr1 = [];
					if (dataX && dataX.length > 0) {
						dataX.forEach((item) => {
							const arr = item.split('-');
							const str = arr[1] + '月';
							arr1.push(str);
						});
					}
					month_line_charts('main', arr1, dataY, state.lineColor);
				} else {
					month_line_charts('main', dataX, dataY, state.lineColor);
				}
			} else {
				month_line_charts('main', [], [], state.lineColor);
			}
		};
		// 侦听临床信息指南来源筛选
		watch(
			[activeness],
			() => {
				func(state.currentTab);
			},
			{
				deep: true,
			}
		);
		const getColor = computed(() => {
			return (index) => {
				let color = state.colorList[index % state.colorList.length];
				return {
					borderColor: color,
					background: state.tabList[index] === state.currentTab ? color : '',
				};
			};
		});
		onMounted(async () => {
			//获取类型
			let res = await getDataType();
			res.result ? (state.tabList = state.tabList.concat(res.result)) : '';
		});
		return {
			...toRefs(state),
			tabHandler,
			getColor,
			month_line_charts,
		};
	},
};
</script>
<style scoped lang="scss">
.pie {
	height: 200px;
	width: 100%;
}
.HCPActive_components {
	.HcpActive {
		.jiejue {
			display: flex;
			align-items: center;
		}
		.title {
			font-size: 16px;
			color: #172b4d;
			font-weight: 600;
		}
		.circle {
			width: 8px;
			height: 8px;
			background: #0052cc;
			opacity: 0.8;
			border-radius: 50%;
			margin-right: 4px;
			border: none;
		}
		.van-notice-bar {
			height: 30px;
			margin-top: 5px;
			font-size: 12px !important;
			font-weight: 600;
			color: #aeaeb2 !important;
			background-color: #fbfbfb !important;
		}
	}
}
.filter {
	margin: 5px 0px;
	.circleInfo {
		display: inline-block;
		width: 6px;
		height: 6px;
		margin-right: 4px;
		position: relative;
		bottom: 2px;
		border-radius: 50%;
	}
	.circle {
		border: 1px solid;
	}
	.tab1 {
		font-size: 13px;
		margin-right: 8px;
	}
}
::v-deep(.van-notice-bar--wrapable) {
	padding-left: 0px;
	.van-notice-bar__left-icon {
		min-width: 23px;
		position: relative;
		left: -2px;
	}
}
</style>
