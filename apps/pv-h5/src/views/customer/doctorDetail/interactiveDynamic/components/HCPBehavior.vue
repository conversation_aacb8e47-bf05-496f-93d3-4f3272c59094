<template>
	<div class="HCPBehavior_components">
		<div class="jiejue">
			<span class="circle"></span>
			<span class="title">互动行为统计</span>
		</div>
		<div ref="contentRef" class="content">
			<div class="propress" v-for="(item, index) in statistical" :key="index">
				<div>
					<span class="sort">{{ index + 1 }}</span>
					<span>{{ item.dataType }}</span>
					<span class="amount">{{ item.count }}次</span>
				</div>
				<div class="propressItem" :style="`background:${item.backgroundColor}`">
					<span class="propressItemCurrent" :style="`width:${item.proportion};background:${item.currentColor}`"></span>
				</div>
			</div>
		</div>
		<div class="expand" @click="zk">
			<span> {{ expand ? '收起' : '展开' }}</span>
			<img v-show="expand" src="@/assets/img/collapse.png" alt="" />
			<img v-show="!expand" src="@/assets/img/expand-2.png" alt="" />
		</div>
	</div>
</template>
<script>
import { reactive, toRefs, toRef, watch, nextTick, ref } from 'vue';

export default {
	name: 'Index',
	props: {
		statisticalInfo: {
			require: true,
		},
	},
	setup(props, context) {
		const state = reactive({
			list: [
				{
					title: '医生线上直播',
					counter: 86,
					all: 100,
					proportion: '44%',
					currentColor: '#2D81FE',
					backgroundColor: '#EBEFFB',
				},
				{
					title: '医生在线阅读',
					counter: 28,
					all: 100,
					proportion: '74%',
					currentColor: '#36CFD3',
					backgroundColor: '#EBEFFB',
				},
				{
					title: '医生分享文章',
					counter: 28,
					all: 100,
					proportion: '84%',
					currentColor: '#838BFD',
					backgroundColor: '#EBEFFB',
				},
				{
					title: '医生发表文章',
					counter: 24,
					all: 100,
					proportion: '24%',
					currentColor: '#C59CFC',
					backgroundColor: '#EBEFFB',
				},
			],
			backgroundColor: '#fbfbfb',
			activeColor: ['#2D81FE', '#36CFD3', '#838BFD', '#C59CFC', '#FF57D9A3'],
			expand: false,
			autoHeight: '',
		});
		const statistical = toRef(props, 'statisticalInfo');
		watch(
			[statistical],
			async (newValue, oldValue) => {
				let maxCount = 0;
				statistical.value.forEach((item, index) => {
					if (item.count > maxCount) {
						maxCount = item.count;
					}
					item.backgroundColor = state.backgroundColor;
					item.currentColor = state.activeColor[index];
				});
				statistical.value.forEach((item, index) => {
					if (item.count > 0) {
						item.proportion = (item.count / maxCount).toFixed(2) * 100 + '%';
					}
				});
				//过度动画
				nextTick(() => {
					state.expand = false;
					contentRef.value.style.maxHeight = ''; //筛选切换时置空
					state.autoHeight = contentRef.value.clientHeight; //重新获取dom的高度
					contentRef.value.style.maxHeight = '170px'; //筛选后初始化高度
				});
			},
			{
				deep: true,
			}
		);
		//互动行为统计ref
		const contentRef = ref(null);
		//展开收起 过度动画
		const zk = async () => {
			state.expand = !state.expand;
			await nextTick();
			if (state.expand) {
				contentRef.value.style.maxHeight = state.autoHeight + 'px';
			} else {
				contentRef.value.style.maxHeight = '170px';
			}
		};

		return {
			...toRefs(state),
			statistical,
			zk,
			contentRef,
		};
	},
};
</script>
<style scoped lang="scss">
.HCPBehavior_components {
	.jiejue {
		display: flex;
		align-items: center;
	}
	.title {
		font-size: 16px;
		color: #172b4d;
		font-weight: 600;
	}
	.circle {
		width: 8px;
		height: 8px;
		background: #0052cc;
		opacity: 0.8;
		border-radius: 50%;
		margin-right: 4px;
		border: none;
	}
	.content {
		overflow: hidden;
		transition: all 0.4s ease-out;
	}
	.autoHeight {
		max-height: 800px;
		overflow: hidden;
	}
	.propress {
		padding: 5px 10px;
		margin-top: 10px;
		.sort {
			margin-right: 5px;
		}
		.propressItem {
			height: 8px;
			margin-top: 11px;
			border-radius: 8px;
			font-size: 0;
			.propressItemCurrent {
				display: inline-block;
				height: 8px;
				width: 50%;
				border-radius: 8px;
			}
		}
		.amount {
			float: right;
			margin-right: 10px;
		}
	}
	.expand {
		color: #0052cc;
		font-size: 12px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 12px;
		padding-bottom: 12px;
		img {
			width: 20px;
		}
	}
}
</style>
