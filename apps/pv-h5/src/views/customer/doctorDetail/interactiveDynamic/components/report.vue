<template>
	<div class="box">
		<van-notice-bar wrapable :scrollable="false" left-icon="volume-o" color="#F45009" background="#FFF2F2" text="以下数据截止至昨天24:00" />
	</div>
	<div class="LabelFilter">
		<van-row>
			<van-col :span="3"><span>日期:</span></van-col>
			<van-col :span="21">
				<span class="lable_filter_item" :class="flagTime == index ? 'timeActive' : ''" v-for="(item, index) in item" :key="index" @click="timeFilter(item, index)">{{ item }}</span></van-col
			>
		</van-row>
	</div>
	<div class="HCPActive">
		<HCPActive :activenessInfo="activenessInfo" :typeFilter="typeFilter"></HCPActive>
	</div>
	<!-- eslint-disable-next-line vue/no-multiple-template-root -->
	<div class="HCPActive">
		<HCPBehavior :statisticalInfo="statisticalInfo"></HCPBehavior>
	</div>
</template>
<script>
import { reactive, toRefs, ref, getCurrentInstance, onMounted, watch, computed } from 'vue';
import HCPActive from './HCPActive.vue';
import HCPBehavior from './HCPBehavior.vue';
import { useRoute } from 'vue-router';
import { GMTToStr, dateBefore, dateMonthBefore } from '@/utils/index.js';
import usefilterStore from '@/store/modules/filter';
import { activenessInterface, statisticalInterface, tendencyInterface, eduTendencyInterface } from '@/api/doctor';
export default {
	name: 'Index',
	components: {
		HCPActive,
		HCPBehavior,
	},
	setup(props, context) {
		const { proxy } = getCurrentInstance();
		const filterStore = usefilterStore();
		const route = useRoute();
		const doctorInfo = ref(filterStore.doctorInfo);
		const state = reactive({
			item: ['全部', '当月', '近三个月', '近半年', '近一年'],
			activenessInfo: {},
			startDate: '',
			endDate: '',
			typeFilter: '年',
			statisticalInfo: [],
			tendencyInfo: [],
			eduTendencyInfo: [],
		});
		// 时间筛选
		const flagTime = ref(0);
		const timeFilter = (item, index) => {
			proxy.$umeng('点击', '医生360互动一览', `报告-${doctorInfo.value.doctorName}日期筛选${item}`);
			flagTime.value = index;
		};
		const func = () => {
			if (flagTime.value === 0) {
				// 全部
				state.endDate = '';
				state.startDate = '';
				state.typeFilter = '年';
			} else if (flagTime.value === 1) {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateMonthBefore(state.endDate, 1);
				state.typeFilter = '日';
			} else if (flagTime.value === 2) {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateMonthBefore(state.endDate, 3);
				state.typeFilter = '月';
			} else if (flagTime.value === 3) {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateMonthBefore(state.endDate, 6);
				state.typeFilter = '月';
			} else {
				state.endDate = GMTToStr(new Date());
				state.startDate = dateBefore(state.endDate, 1);
				state.typeFilter = '月';
			}
		};
		// 侦听公共筛选值的变化
		watch(
			[flagTime],
			(newValue, oldValue) => {
				func();
				activeness();
				statistical();
				tendency();
				eduTendency();
			},
			{
				deep: true,
			}
		);
		const activeness = () => {
			const query = {
				doctorId: route.query.doctorId,
				dateType: flagTime.value === 1 ? '1' : '2',
				// dateType: 1,
				dataType: '',
				startDate: state.startDate,
				endDate: state.endDate,
			};
			activenessInterface(query).then((res) => {
				state.activenessInfo = res.result;
			});
		};
		const statistical = () => {
			const query = {
				doctorId: route.query.doctorId,
				page: 0,
				size: 10,
				// dateType: '1',
				startDate: state.startDate,
				endDate: state.endDate,
			};
			statisticalInterface(query).then((res) => {
				state.statisticalInfo = res.result;
			});
		};
		const tendency = () => {
			const query = {
				doctorId: route.query.doctorId,
				page: 0,
				size: 10,
				dataType: '',
				startDate: state.startDate,
				endDate: state.endDate,
			};
			tendencyInterface(query).then((res) => {
				state.tendencyInfo = res.result;
			});
		};
		const eduTendency = () => {
			const query = {
				doctorId: route.query.doctorId,
				page: 0,
				size: 10,
				dataType: '',
				startDate: state.startDate,
				endDate: state.endDate,
			};
			eduTendencyInterface(query).then((res) => {
				state.eduTendencyInfo = res.result;
			});
		};
		onMounted(activeness);
		onMounted(statistical);
		onMounted(tendency);
		onMounted(eduTendency);
		onMounted(() => {
			document.body.scrollTop = document.documentElement.scrollTop = 0;
		});
		return {
			...toRefs(state),
			flagTime,
			timeFilter,
		};
	},
};
</script>
<style lang="scss" scoped>
.van-notice-bar {
	margin-top: 12px;
	font-size: 13px !important;
}
.van-notice-bar--wrapable {
	padding: 8px;
}
::v-deep(.van-notice-bar__content) {
	line-height: 18px;
}
.LabelFilter {
	padding: 0px 15px;
	margin-top: 10px;
	color: #172b4d;
	.lable_filter_item {
		display: inline-block;
		padding: 0 5px;
		height: 20px;
		line-height: 20px;
		border-radius: 6px;
		text-align: center;
		font-size: 12px;
		color: #0052cc;
		background-color: rgba(51, 119, 255, 0.1);
		margin-left: 5px;
		margin-bottom: 5px;
	}
	.timeActive {
		color: #fff;
		background-color: rgba(66, 119, 255, 100);
	}
}
.HCPActive {
	padding: 15px;
}
</style>
