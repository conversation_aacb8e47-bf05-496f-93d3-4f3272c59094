<template>
	<div class="box">
		<div v-if="qyId !== 'muqiao'" class="tab">
			<div class="report" :class="isActive === index ? 'active' : ''" v-for="(item, index) in tabList" :key="index" @click="tabChangeHandler(index)">
				<div class="text">{{ item }}</div>
			</div>
		</div>
		<div v-show="isActive == 0">
			<div style="width: 100%">
				<report></report>
			</div>
		</div>
		<div v-show="isActive !== 0">
			<detail></detail>
		</div>
	</div>
</template>
<script>
import { onMounted, ref, reactive, toRefs, computed, getCurrentInstance } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';
import report from './components/report.vue';
import detail from './components/detail.vue';
import useUserStore from '@/store/modules/user';
import usefilterStore from '@/store/modules/filter';
export default {
	name: 'Index',
	components: {
		report,
		detail,
	},
	setup(props, context) {
		const userStore = useUserStore();
		const filterStore = usefilterStore();
		const qyId = ref(userStore.userInfo.enterpriseVO.name);
		const { proxy } = getCurrentInstance();
		const state = reactive({
			tabList: ['报告', '明细'],
			isActive: 0,
		});
		const doctorInfo = ref(filterStore.doctorInfo);
		onBeforeRouteLeave(() => {
			sessionStorage.removeItem('intTab');
		});
		const tabChangeHandler = (index) => {
			if (index === 0) {
				proxy.$umeng('点击', '医生360互动一览', `${doctorInfo.value.doctorName}医生的报告`);
			} else {
				proxy.$umeng('点击', '医生360互动一览', `${doctorInfo.value.doctorName}医生的明细`);
			}
			state.isActive = index;
			sessionStorage.setItem('intTab', index);
		};
		onMounted(() => {
			if (qyId.value === 'muqiao') {
				state.isActive = 1;
			} else {
				if (sessionStorage.getItem('intTab')) {
					state.isActive = Number(sessionStorage.getItem('intTab'));
				}
			}
		});
		return {
			qyId,
			...toRefs(state),
			tabChangeHandler,
		};
	},
};
</script>
<style lang="scss" scoped>
.box {
	background-color: #ffffff;
}
.tab {
	display: flex;
	font-size: 16px;
	border-radius: 0px 0px 6px 6px;
	box-shadow: 1px 2px 3px 0px rgba(51, 98, 236, 0.1);
	.report {
		height: 39px;
		line-height: 39px;
		flex: 1;
	}
	.active {
		color: #0052cc;
		background-color: transparent;
		.text {
			color: #0052cc;
		}
	}
	.active::after {
		content: '';
		width: 10px;
		display: block;
		margin: 0 auto;
		border-bottom: 3px solid #0052cc;
	}
	.detail {
		flex: 1;
		height: 39px;
		line-height: 39px;
	}
	.text {
		color: #172b4d;
		width: 50px;
		height: 33px;
		margin: 0 auto;
		text-align: center;
	}
}
</style>
