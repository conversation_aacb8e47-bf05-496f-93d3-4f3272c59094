<template>
	<div class="card_components">
		<div class="content">
			<!-- 已认证 -->
			<span v-if="doctorInfo.doctorName && (doctorInfo.verificationStatus === 1 || doctorInfo.verificationStatus === '1')" class="data_left--realName"> 已验证 </span>
			<!-- 未认证 -->
			<span v-else-if="doctorInfo.doctorName" class="data_left--realNotName"> 未验证 </span>
			<div class="photo">
				<img v-if="doctorInfo.doctorSex === '女'" src="@/assets/img/demo/gril.png" />
				<img v-else src="@/assets/img/demo/men.png" />
			</div>
			<div class="info">
				<div style="display: flex; align-items: center">
					<span class="name">{{ doctorInfo.doctorName }}</span>
					<span class="title">{{ doctorInfo.doctorTitle }}</span>
					<!-- 多点职业 -->
					<img v-if="doctorInfo.servingHospital && doctorInfo.servingHospital.length > 0" class="more_job" src="@/assets/img/more_job.png" />
				</div>
				<div class="hospital-info">
					<span>{{ doctorInfo.hospital }}</span>
					<span class="depart">{{ doctorInfo.department }}</span>
				</div>
				<!-- 多点执业信息-->
				<div v-for="(item, index) in doctorInfo.servingHospital" :key="index" class="hospital-info" :style="{ display: item.verification_status === '0' ? 'none' : 'block' }">
					<span>{{ item.hospital }}</span>
					<span class="depart">{{ item.department.split('/')[1] }}</span>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { toRef, getCurrentInstance, ref } from 'vue';
import { updateDoctorCoverage, focusDoctor } from '@/api/doctor';
import { useRoute } from 'vue-router';
import { showToast } from 'vant';

export default {
	name: 'HCPCard',
	props: {
		HcpInfo: {
			type: Object,
			require: true,
		},
	},
	setup(props, context) {
		const route = useRoute();
		const { proxy } = getCurrentInstance();
		const doctorInfo = toRef(props, 'HcpInfo');
		const changeCollect = (bol) => {
			doctorInfo.value.isFocus = bol;

			if (bol) {
				proxy.$umeng('点击', `医生详情`, `关注${doctorInfo.value.doctorName}`);
			} else {
				proxy.$umeng('点击', `医生详情`, `取消关注${doctorInfo.value.doctorName}`);
			}
			const data = {
				type: 1,
				businessId: doctorInfo.value.doctorId,
			};
			focusDoctor(data).then((res) => {
				if (res.result.focusStatus) {
					showToast('已关注');
				} else {
					showToast('已取消关注');
				}
			});
		};
		const target = async () => {
			const targetQuery = {
				ownerType: 'DOCTOR',
				id: route.query.doctorId,
				isAdd: doctorInfo.value.isTarget,
			};
			try {
				await updateDoctorCoverage(targetQuery);
				doctorInfo.value.isTarget = !doctorInfo.value.isTarget;
				if (doctorInfo.value.isTarget) return showToast('已添加');
				showToast('已取消');
			} catch (error) {
				console.log(error);
			}
		};
		return {
			doctorInfo,
			changeCollect,
			target,
		};
	},
};
</script>
<style lang="scss" scoped>
.card_components {
	position: relative;
	.data_left--realName {
		position: absolute;
		top: 65px;
		width: 35px;
		left: 25px;
		color: #ffffff;
		background-color: #0052cc;
		padding: 1px 3px;
		border-radius: 6px;
		font-size: 8px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.data_left--realNotName {
		position: absolute;
		top: 65px;
		width: 35px;
		left: 25px;
		color: #0052cc;
		border: 1px solid #0052cc;
		background-color: #ffffff;
		padding: 1px 3px;
		border-radius: 6px;
		font-size: 8px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.content {
		background: #fff;
		padding: 12px;
		display: flex;
		align-items: center;
		position: relative;
		border-bottom: 1px solid #dfe1e6;
		.check {
			position: absolute;
			top: 10px;
			right: 10px;
			.collect {
				width: 24px;
				height: 24px;
				margin-right: 0;
			}
		}
		.photo {
			// float: left;
			width: 62px;
			height: 62px;
			img {
				width: 62px;
				border-radius: 50%;
			}
		}
		.info {
			flex: 1;
			margin-left: 16px;
			margin-top: 3px;
			.name {
				font-size: 14px;
				color: #172b4d;
				font-weight: 600;
			}
			.title {
				font-size: 12px;
				margin-left: 3px;
				color: #172b4d;
			}
			.more_job {
				width: 37px;
				margin-left: 3px;
				object-fit: cover;
			}

			.hospital-info {
				font-size: 12px;
				color: #172b4d;
				margin-bottom: 3px;
				.depart {
					margin-left: 3px;
				}
			}
			.verified-status {
				margin-left: 12px;
				font-size: 12px;
				display: flex;
				align-items: center;
				.icon {
					font-size: 18px;
					margin-right: 3px;
				}
			}
		}
	}
}
</style>
