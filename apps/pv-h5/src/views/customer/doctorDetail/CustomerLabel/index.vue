<template>
	<div class="customerLabel">
		<div class="personal">
			<div class="title">
				<span class="blue"></span>
				<span class="title-value">个人标签</span>
			</div>
			<button class="addLabel" @click="goAddTag()">添加标签</button>
		</div>
		<div class="personaLabel">
			<span v-for="item in personaList" :key="item.id">{{ item.termValue }}</span>
		</div>
		<div>
			<div class="title">
				<span class="blue"></span>
				<span class="title-value">系统标签</span>
			</div>
		</div>
		<div class="systemLabel">
			<van-loading color="#0052cc" vertical v-if="loading">加载中</van-loading>
			<div v-for="list in systemList" :key="list.id">
				<p>{{ list.groupName }}：</p>
				<span v-for="item in list.tagTermList" :key="item.id">{{ item.termValue }}</span>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { doctorChooseTagInterface, doctorSelfTagInterface } from '@/api/doctor';
import { useRoute } from 'vue-router';
import usefilterStore from '@/store/modules/filter';
const filterStore = usefilterStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
const doctorInfo = ref(filterStore.doctorInfo);
const systemList = ref([]);
const personaList = ref([]);
const checkedList = ref([]);
const loading = ref(true);

function goAddTag() {
	proxy.$umeng('点击', '医生360-客户标签', `${doctorInfo.value.doctorName}添加个人标签`);
	proxy.$router.push({
		name: 'tagManage',
		query: proxy.$route.query,
	});
}
// 查询医生已打系统标签
const doctorChooseTag = () => {
	const query = {
		tagType: 'CUSTOMER',
		// isAll: false
	};
	doctorChooseTagInterface(proxy.$route.query.doctorId, query).then((res) => {
		if (res.code === 200) {
			systemList.value = res.result;
			loading.value = false;
		}
	});
};
// 查询医生已打个人标签
const doctorSelfTag = () => {
	doctorSelfTagInterface(proxy.$route.query.doctorId).then((res) => {
		if (res.code === 200) {
			personaList.value = res.result;
		}
	});
};
onMounted(() => {
	doctorChooseTag();
	doctorSelfTag();
});
</script>

<style lang="scss" scoped>
.customerLabel {
	padding: 15px 13px;
	background-color: #fff;
	.personal {
		position: relative;
	}

	.title {
		color: #172b4d;
		font-size: 16px;
		text-align: left;
		font-weight: 600;
		font-family: PingFangSC-medium;
		margin-bottom: 12px;

		.blue {
			display: inline-block;
			width: 8px;
			height: 8px;
			border-radius: 50%;
			background-color: #0052cc;
			margin-right: 8px;
		}
	}

	.addLabel {
		border-radius: 4px;
		height: 30px;
		padding: 0 10px;
		border: 0;
		position: absolute;
		background-color: #0052cc;
		top: 0;
		color: #fff;
		right: 19px;
	}

	.personaLabel {
		margin: 10px 0px 10px 15px;

		span {
			display: inline-block;
			font-size: 12px;
			margin-right: 5px;
			margin-bottom: 5px;
			padding: 5px 8px;
			min-width: 70px;
			border-radius: 2px;
			color: #00b8d9;
			background-color: #eefcfe;
			text-align: center;
		}
	}

	.systemLabel {
		margin: 10px 0px 10px 15px;

		div {
			p {
				font-size: 14px;
				color: #666666;
				margin-bottom: 5px;
			}
			span {
				display: inline-block;
				background-color: #e7effd;
				color: #0052cc;
				padding: 4px 7px;
				margin: 5px 8px 5px 0;
				margin-bottom: 10px;
				font-size: 12px;
				min-width: 70px;
				text-align: center;
			}
		}
	}
}
</style>
