<template>
	<div style="background-color: #f4f5f7">
		<HCPCard :HcpInfo="doctorInfo"></HCPCard>
		<div class="tab">
			<van-tabs color="#0052CC" @click-tab="tabChangeHandler" title-active-color="#0052CC" title-inactive-color="#172B4D" v-model:active="active">
				<van-tab v-for="(item, index) in tabList" :key="index" :title="item.zh" :name="item.en"></van-tab>
			</van-tabs>
		</div>
		<router-view v-if="show"></router-view>
		<!-- 档案更新 -->
		<div v-if="isUpdateDoctor" class="update" @click="toUpdate">
			<span>档案</span>
			<span>更新</span>
		</div>
	</div>
</template>
<script>
import { onMounted, ref, reactive, toRefs, getCurrentInstance, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import usePremissionStore from '@/store/modules/premission';
import { findByDoctorId } from '@/api/doctor';
import HCPCard from './components/HCPCard.vue';
import usefilterStore from '@/store/modules/filter';
const filterStore = usefilterStore();
export default {
	name: 'doctorInfo',
	components: { HCPCard },
	setup(props, context) {
		const perStore = usePremissionStore();
		const isUpdateDoctor = computed(() => {
			return perStore.systemBtnList.some((item) => item.path === 'isUpdateDoctor');
		});
		const router = useRouter();
		const route = useRoute();
		const { proxy } = getCurrentInstance();
		const active = ref('');
		const state = reactive({
			doctorInfo: {},
			tabList: [
				{
					en: 'internalInfo',
					zh: '内部信息',
				},
				{
					en: 'CustomerLabel',
					zh: '客户标签',
				},
				{
					en: 'publicInfo',
					zh: '公示信息',
				},
				{
					en: 'interactiveDynamic',
					zh: '互动一览',
				},
			],
			show: false,
		});
		/**
		 * @method
		 * @param tab
		 * @returns  运营商名称
		 * @desc 平台活跃度分析多折线图
		 */
		const tabChangeHandler = ({ name }) => {
			if (name === 'CustomerLabel') {
				proxy.$umeng('点击', `医生详情`, `${state.doctorInfo.doctorName}医生的客户标签`);
			} else if (name === 'interactiveDynamic') {
				proxy.$umeng('点击', `医生详情`, `${state.doctorInfo.doctorName}医生的互动一览`);
			} else if (name === 'internalInfo') {
				proxy.$umeng('点击', `医生详情`, `${state.doctorInfo.doctorName}医生的内部信息`);
			} else if (name === 'cognitiveTendencies') {
				proxy.$umeng('点击', `医生详情`, `${state.doctorInfo.doctorName}医生的认知倾向`);
			}
			router.replace({
				name: name,
				query: route.query,
			});
		};
		const findByDoctorIdFn = () => {
			return new Promise((resolve) => {
				const query = {
					doctorId: route.query.doctorId,
				};
				findByDoctorId(query).then((res) => {
					state.doctorInfo = res.result[0];
					// 所属领域处理
					if (state.doctorInfo.indication && state.doctorInfo.indication.indexOf('{') > -1) {
						let q = state.doctorInfo.indication.split(';');
						let a = '';
						for (let i = 0; i < q.length; i++) {
							if (q[i].indexOf('{') > -1) {
								a += q[i].split('"indicationname": "')[1].slice(0, -2) + ';';
							} else {
								a += q[i] + ';';
							}
						}
						let s = a.split(';');
						let y = '';
						for (let i = 0; i < s.length; i++) {
							if (s[i]) {
								y += s[i] + ';';
							}
						}
						state.doctorInfo.indication = y;
					}
					filterStore.SET_DOCTOR_INFO(state.doctorInfo);
					sessionStorage.setItem('HcpInfo', JSON.stringify(res.result[0]));
					state.show = true;
					resolve();
				});
			});
		};
		const toUpdate = () => {
			proxy.$umeng('点击', `医生详情`, `档案更新`);
			router.push({
				name: 'newDoctor',
				query: {
					sourceType: 'mobilePhone',
					...route.query,
				},
			});
		};
		onMounted(async () => {
			active.value = route.name;
			await findByDoctorIdFn();
		});
		return {
			...toRefs(state),
			tabChangeHandler,
			toUpdate,
			isUpdateDoctor,
			active,
		};
	},
};
</script>
<style lang="scss" scoped>
.tab {
	margin-top: 8px;
	border-bottom: 1px solid #dfe1e6;
	&:deep(.van-tab__text) {
		font-size: 15px;
	}
}
.update {
	position: fixed;
	right: 13px;
	bottom: 15%;
	width: 60px;
	height: 60px;
	background-color: #0052cc;
	box-shadow: 0px 4px 10px 0px #0052cc;
	color: #ffffff;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
</style>
