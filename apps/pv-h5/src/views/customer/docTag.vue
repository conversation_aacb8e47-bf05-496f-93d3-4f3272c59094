<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="onClickLeft" class="mc-header-icon">
				<van-icon color="#172b4d" :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">
				<span>{{ title }}</span>
			</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">
				<van-icon v-if="route.query.type === 'PERSONAL'" @click="editTag" style="margin-right: 10px" name="edit" size="18" />
				<van-icon v-if="route.query.type === 'PERSONAL'" @click="delTag" style="margin-right: 12px" name="delete-o" size="18" />
			</div>
		</div>

		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<div class="list">
				<div class="text">
					<span>成员：{{ num }}</span>
					<span v-if="showCheck" @click="saveFn">保存</span>
				</div>
				<div class="cardList">
					<docCard v-for="item in doctorList" :key="item.doctorId" :data="item" :showCheck="showCheck" :isShowOperation="false" @changeCheck="changeCheck"></docCard>
				</div>
			</div>
		</div>

		<div class="box">
			<van-button size="small" color="#0052cc" type="primary" @click="addDoctor">添加医生</van-button>
			<van-button size="small" color="#0052cc" type="primary" @click="removeDoctor">删除医生</van-button>
		</div>

		<!-- 修改标签弹框 -->
		<van-dialog style="width: 85%" v-model:show="showTag" @confirm="updateTag" show-cancel-button>
			<van-cell-group style="margin: 30px 0 20px 5%; width: 90%; border: solid 1px #dfe1e6" inset>
				<van-field v-model="tagName" placeholder="请输入标签的新名称" />
			</van-cell-group>
		</van-dialog>
	</div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { updatePersonTag, deletePersonTag, tagDoctorList, removeDocTag } from '@/api/doctor';
import docCard from './components/docCard.vue';
import { showConfirmDialog, showToast } from 'vant';
import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
const qyId = ref(userStore.userInfo.enterpriseVO.name);
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const doctorList = ref([]);
const showCheck = ref(false);
const num = ref(0);
const title = ref('');
const state = reactive({
	mobileTagType: '',
});

// 删除标签
const delTag = () => {
	showConfirmDialog({
		title: '提示',
		message: '删除该标签后,医生也将被清除，是否确认删除？',
	})
		.then(() => {
			// on confirm
			deletePersonTag(data.value.tagTermId).then(() => {
				showToast({
					position: 'top',
					message: '删除成功',
				});
				router.go(-1);
			});
		})
		.catch(() => {
			// on cancel
		});
};

// 修改标签
const showTag = ref(false);
const tagName = ref('');
const editTag = () => {
	showTag.value = true;
};
const updateTag = () => {
	if (tagName.value !== '') {
		showTag.value = false;
		updatePersonTag({ id: data.value.tagTermId, termValue: tagName.value, tagType: 'CUSTOMER' }).then(() => {
			title.value = tagName.value;
			showToast({
				position: 'top',
				message: '修改成功',
			});
		});
	} else {
		showToast({
			position: 'top',
			message: '请输入标签名称',
		});
	}
};
const data = ref({
	tagTermId: route.query.id,
	arr: [],
	mobileTagType: route.query.type,
});
// 医生列表
function docList() {
	const query = {
		tagTermId: route.query.id,
		mobileTagType: state.mobileTagType,
	};
	tagDoctorList(query)
		.then((res) => {
			doctorList.value = res.result.doctorList;
			title.value = res.result.termValue;
			num.value = res.result.doctorList.length;
		})
		.catch((err) => {
			console.log(err);
		});
}

// 添加医生
function addDoctor() {
	proxy.$umeng('点击', `医生360-标签详情`, `选择添加医生`);
	router.push({ path: 'doctorAll', query: { id: route.query.id, type: route.query.type } });
}
// 删除医生
function removeDoctor() {
	proxy.$umeng('点击', `医生360-标签详情`, `选择删除医生`);
	showCheck.value = true;
}
// 点击多选框
function changeCheck(val, data1) {
	if (val) {
		proxy.$umeng('点击', `医生360-标签详情`, `选择删除${data1.doctorName}`);
		data.value.arr.push(data1.doctorId);
	} else {
		proxy.$umeng('点击', `医生360-标签详情`, `取消删除${data1.doctorName}`);
		const result = data.value.arr.findIndex((item) => item === data1.doctorId);
		if (result === -1) {
			data.value.arr.push(data1.doctorId);
		} else {
			data.value.arr.splice(result, 1);
		}
	}
}
// 保存删除
function saveFn() {
	proxy.$umeng('点击', `医生360-标签详情`, `保存标签`);
	removeDocTag(data.value).then((res) => {
		if (res.code === 200) {
			showCheck.value = false;
			docList();
		}
	});
}
function onClickLeft() {
	proxy.$umeng('点击', `医生360-标签详情`, `回到标签列表`);
	localStorage.removeItem('changeList');
	localStorage.removeItem('change');
	router.go(-1);
}
onMounted(() => {
	state.mobileTagType = route.query.type;
	docList();
});
</script>

<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	color: #172b4d;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		background-color: #ffffff;
		border-bottom: 1px solid #dfe1e6;
		.mc-header-icon {
			width: 30px;
			height: 30px;
			color: #0052cc;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.mc-header-name {
			span {
				font-size: 16px;
				color: #172b4d;
				font-weight: 600;
			}
		}
	}
	.mc-content {
		display: flex;
		flex-direction: column;
		overflow-y: auto;

		.list {
			height: 100%;
			padding: 0 10px;
			overflow: auto;

			.cardList {
				overflow: auto;
				height: calc(100% - 60px);
			}

			.text {
				display: flex;

				:nth-child(2) {
					position: absolute;
					right: 0;
					color: #0052cc;
				}
			}

			span {
				display: inline-block;
				color: #172b4d;
				margin: 10px 20px 10px 10px;
				font-size: 14px;
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 125px);
	}

	.mc-content__app {
		height: calc(100vh - 125px);
	}

	.box {
		height: 80px;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-evenly;
		background-color: #f4f5f7;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
}
</style>
