<template>
	<div class="docCard">
		<div class="messgList">
			<div class="clearfix" @click="viewHandler(data)">
				<div class="data_left">
					<!-- 头像 -->
					<img v-if="data.doctorSex === '女'" src="@/assets/img/demo/gril.png" class="img" />
					<img v-else src="@/assets/img/demo/men.png" class="img" />
					<!-- 已验证 -->
					<span v-if="data.verificationStatus === '1' || data.verificationStatus === 1" class="data_left--realName">已验证</span>
					<!-- 未验证 -->
					<span v-else class="data_left--realNotName">未验证</span>
				</div>
				<div class="data_right">
					<div class="right_top">
						<span class="top_name">{{ data.doctorName }}</span>
						<span class="top_president">{{ data.doctorTitle }}</span>
						<!-- 多点职业 -->
						<img v-if="data.servingHospital && data.servingHospital.length > 0" class="more_job" src="@/assets/img/more_job.png" />
					</div>

					<div class="data_title">
						<span>{{ data.hospital }}</span>
						<span class="depart">{{ data.department }}</span>
					</div>
					<input class="check" v-if="props.showCheck" type="checkbox" :value="data.check" @click.stop="changecheck(!data.check, data)" />
					<div v-if="props.isShowOperation" class="operation">
						<div class="focus-content" @click.stop="changeCollect(data.isFocus)">
							<span>{{ data.isFocus ? '已关注' : '关注' }}</span>
							<img v-if="data.isFocus" class="focus" src="@/assets/img/ac-follow.png" />
							<img v-else class="focus" src="@/assets/img/not-follow.png" />
						</div>

						<div style="display: flex; align-items: center" v-if="isTargetDoctor" @click.stop="target">
							<span>目标医生</span>
							<img src="@/assets/img/doc-close.png" class="target" v-if="data.isTarget" />
							<img v-else src="@/assets/img/doc-add.png" class="target" />
						</div>
					</div>
				</div>
			</div>

			<!-- 功能按钮 -->
			<div v-if="props.isShowOperation && data.isTarget && operationList.filter((item) => item.per).length > 0" class="bot-operation">
				<div v-for="item in operationList.filter((item) => item.per)" :key="item.id" class="bot-operation__item" @click.stop="operationHandler(item, data)">
					<!-- 图标 -->
					<img :src="item.icon" class="icon" />
					<!-- 描述 -->
					<span>{{ item.name }}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'DocCard',
};
</script>

<script setup>
import { ref, computed } from 'vue';
import wxApi from '@/api/wxApi.js';
import 'vant/es/tag/style/index';
import { showToast, showDialog } from 'vant';
import { isWeChat } from '@/utils/index';
import usePremissionStore from '@/store/modules/premission';
import { updateDoctorCoverage } from '@/api/doctor';
import useUserStore from '@/store/modules/user';
import btn1 from '@/assets/img/doc-bot-icon-1.png';
import btn2 from '@/assets/img/doc-bot-icon-2.png';
import btn3 from '@/assets/img/doc-bot-icon-3.png';
import btn4 from '@/assets/img/doc-bot-icon-4.png';
const userStore = useUserStore();
const userId = userStore.userInfo.username;

const router = useRouter();
const route = useRoute();
const perStore = usePremissionStore();
// 目标医生
const isTargetDoctor = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'isTargetDoctor');
});
// 图文
const isImgText = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'docList-imageText');
});
// 内容
const isContent = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'docList-articleContent');
});
// 会议
const isMeeting = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'docList-meeting');
});
// 拜访
const isVisitRecord = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'docList-visitRecord');
});

const operationList = ref([
	{
		id: 1,
		icon: btn1,
		name: '图文',
		per: isImgText.value,
	},
	{
		id: 2,
		icon: btn2,
		name: '内容',
		per: isContent.value,
	},
	{
		id: 3,
		icon: btn3,
		name: '会议',
		per: isMeeting.value,
	},
	{
		id: 4,
		icon: btn4,
		name: '拜访',
		per: isVisitRecord.value,
	},
]);

const operationHandler = (item, data) => {
	if (item.name === '拜访') {
		router.push({
			name: 'mAssistant',
			query: {
				isTabBar: route.query.isTabBar || '',
				fromSource: 'docModelInfo',
				doctorId: data.doctorId,
			},
		});
	} else if (item.name === '图文') {
		if (isWeChat() === '企业微信') {
			if (data.externalUserId) {
				wxApi
					.openEnterpriseChat({
						userIds: userId,
						externalUserIds: data.externalUserId,
						groupName: '',
						chatId: '',
					})
					.catch((err) => {
						console.log(err);
						showToast({
							message: err.errMsg || '会话创建失败',
							position: 'top',
						});
					});
			} else {
				showToast({
					message: '该医生不是您的企业微信好友，请先添加为好友',
					position: 'top',
				});
			}
		} else {
			showToast({
				message: '请您在企业微信中使用此功能',
				position: 'top',
			});
		}
	} else if (item.name === '内容') {
		if (isWeChat() === '企业微信') {
			if (data.externalUserId) {
				emit('shareArticle', {
					externalUserId: data.externalUserId,
					doctorId: data.doctorId,
					doctorName: data.doctorName,
				});
			} else {
				showToast({
					message: '该医生不是您的企业微信好友，请先添加为好友',
					position: 'top',
				});
			}
		} else {
			showToast({
				message: '请您在企业微信中使用此功能',
				position: 'top',
			});
		}
	}
};

const props = defineProps({
	data: {
		type: Object,
		default() {
			return {};
		},
	},
	isShowOperation: {
		type: Boolean,
		default: true,
	},
	showCheck: {
		type: Boolean,
		default: false,
	},
});
const emit = defineEmits(['changeFocus', 'changeCheck', 'viewInfo', 'shareArticle']);
const changeCollect = (bol) => {
	// eslint-disable-next-line vue/no-mutating-props
	props.data.collect = !bol;
	emit('changeFocus', bol, props.data);
};

const changecheck = (val) => {
	emit('changeCheck', val, props.data);
};

const viewHandler = (item) => {
	emit('viewInfo', item);
};
const target = async () => {
	const targetQuery = {
		ownerType: 'DOCTOR',
		id: props.data.doctorId,
		isAdd: !props.data.isTarget,
	};
	if (props.data.isTarget) {
		showDialog({
			title: '提示',
			message: '是否取消该目标医生',
			showCancelButton: true,
			confirmButtonText: '是',
			cancelButtonText: '否',
			confirmButtonColor: '#0052cc',
			className: 'cancelSvgDoctor',
		}).then(async () => {
			// 取消目标医生
			await updateDoctorCoverage(targetQuery);
			// eslint-disable-next-line vue/no-mutating-props
			props.data.isTarget = !props.data.isTarget;
			showToast({
				message: '已取消目标医生',
				position: 'top',
			});
		});
	} else {
		// 添加目标医生
		try {
			await updateDoctorCoverage(targetQuery);
			// eslint-disable-next-line vue/no-mutating-props
			props.data.isTarget = !props.data.isTarget;
			showToast({
				message: '已添加目标医生',
				position: 'top',
			});
		} catch (error) {
			console.log(error);
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep(.van-popup--center) {
	width: 80%;
}
.docCard {
	padding-bottom: 8px;
	background-color: #f4f5f7;
}
.verified {
	position: absolute;
	top: 35px;
	right: 7px;
	width: 18px !important;
}
.messgList {
	background: #fff;
	overflow: hidden;
	position: relative;
	padding: 0;
}
.clearfix {
	overflow: hidden;
	padding: 16px 15px 17px;
	display: flex;
	align-items: center;
	.data_left {
		position: relative;
		text-align: center;

		.data_left--realName {
			position: absolute;
			top: 45px;
			width: 35px;
			left: 8px;
			color: #ffffff;
			background-color: #0052cc;
			padding: 1px 3px;
			border-radius: 6px;
			font-size: 8px;
		}
		.data_left--realNotName {
			position: absolute;
			top: 45px;
			width: 35px;
			left: 8px;
			color: #0052cc;
			border: 1px solid #0052cc;
			background-color: #ffffff;
			padding: 1px 3px;
			border-radius: 6px;
			font-size: 8px;
		}
		.img {
			width: 50px;
			height: 50px;
			object-fit: fill;
		}
		.petname {
			margin-top: 7px;
			color: rgba(164, 162, 162, 92);
			font-size: 12px;
			display: block;
		}
	}
	.data_right {
		flex: 1;
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		margin-left: 10px;
		.right_top {
			display: flex;
			align-items: baseline;
			margin-bottom: 3px;
			margin-top: 4px;
			.top_name {
				color: rgba(0, 82, 204, 100);
				font-size: 14px;
				font-weight: 600;
				color: var(--var-default-color);
				margin-right: 5px;
			}
			.top_president {
				color: var(--var-default-color);
				font-size: 12px;
				margin-right: 5px;
			}

			.more_job {
				width: 37px;
				object-fit: cover;
			}
		}
		.data_title {
			color: var(--var-default-color);
			font-size: 12px;
			-webkit-box-orient: vertical;
			overflow: hidden;
			display: -webkit-box;
			line-clamp: 1;
			-webkit-line-clamp: 1;
			.depart {
				margin-left: 5px;
			}
		}

		.check {
			position: absolute;
			right: -8px;
			top: -12px;
			z-index: 1;
			width: 15px;
			height: 15px;
		}

		.message_tag {
			color: #808080;
			display: -webkit-box;
			-webkit-line-clamp: 1;
			line-clamp: 1;
			-webkit-box-orient: vertical;
			text-overflow: -o-ellipsis-lastline;
			.hosTagb {
				background-color: rgba(222, 235, 255, 80);
				padding: 4px 6px;
				color: rgba(0, 82, 204, 100);
				border-radius: 5px;
				margin-right: 8px;
			}
			.hosTagg {
				background-color: rgba(230, 252, 255, 80);
				padding: 4px 6px;
				color: rgba(0, 184, 217, 100);
				border-radius: 5px;
				margin-right: 8px;
			}
		}

		.operation {
			display: flex;
			align-items: center;
			justify-content: flex-end;
			position: absolute;
			color: #6b778c;
			font-size: 12px;
			right: -8px;
			top: -12px;
			z-index: 1;

			.focus-content {
				display: flex;
				align-items: center;
				margin-right: 10px;
				.focus {
					margin-left: 3px;
					width: 12px;
					height: 12px;
				}
			}

			.target {
				width: 12px;
				height: 12px;
				margin-left: 3px;
			}
		}
	}
}

.bot-operation {
	height: 34px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	border-top: 1px solid #dfe1e6;
	.bot-operation__item {
		display: flex;
		align-items: center;
		justify-content: center;
		color: #0052cc;
		font-size: 12px;
		width: 25%;

		.icon {
			width: 14px;
			object-fit: cover;
			margin-right: 9px;
		}
	}
}
</style>
