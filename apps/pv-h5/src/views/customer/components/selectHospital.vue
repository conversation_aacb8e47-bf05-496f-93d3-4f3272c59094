<template>
	<div class="nullTag">
		<van-nav-bar title="选择医院" right-text="确定" left-arrow @click-left="onClickLeft" @click-right="onClickRight" />
		<Search :text="'输入医院'" @changeFn="changeFn" @reset="resetFn"></Search>
		<van-list v-if="pageStatus === 'normal'" class="list" v-model:loading="loading" :immediate-check="false" :finished="finished" finished-text="没有更多了" @load="onLoad">
			<van-radio-group checked-color="#0052cc" v-model="checked">
				<van-radio v-for="item in hospitalList" :key="item.hospitalId" :name="item.hospitalId">
					<hosCard :data="item"></hosCard>
				</van-radio>
			</van-radio-group>
		</van-list>
		<van-empty v-if="pageStatus === 'loading'" description="加载中..." />
		<van-empty v-if="pageStatus === 'empty'" description="暂无数据" />
	</div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { getHospitalList } from '@/api/doctor';
import hosCard from './hospitalCard.vue';
import Search from './docSearch.vue';
const { proxy } = getCurrentInstance();
const emit = defineEmits('cancel', 'confirm');
const hospitalList = ref([]);
const loading = ref(false);
const finished = ref(false);
const pageStatus = ref('loading');
const checked = ref([]);
const query = ref({
	page: 0,
	size: 10,
	sort: '',
	hospitalName: '',
	total: 0,
	allClient: false,
});

function onClickLeft() {
	emit('cancel');
}
function onLoad() {
	query.value.page++;
	docData();
}
function onClickRight() {
	console.log(checked.value);
	if (!checked.value) return;
	const j = hospitalList.value.find((ele) => ele.hospitalId === checked.value);
	emit('confirm', {
		hospitalId: j.hospitalId,
		hospitalName: j.hospitalName,
	});
}
//搜索
function changeFn(text) {
	proxy.$umeng('搜索', `新建医生-选择医院`, text);
	hospitalList.value = [];
	query.value.hospitalName = text;
	pageStatus.value = 'loading';
	query.value.page = 0;
	docData();
}
function resetFn() {
	proxy.$umeng('搜索', `新建医生-选择医院`, '重置');
	hospitalList.value = [];
	query.value.hospitalName = '';
	checked.value = [];
	pageStatus.value = 'loading';
	query.value.page = 0;
	docData();
}
function docData() {
	getHospitalList(query.value, query.value.page, query.value.size).then((res) => {
		console.log(res);
		const rows = res.result?.content;
		loading.value = false;
		query.value.total = res.result.totalElements;
		pageStatus.value = 'normal';
		checked.value = '';
		if (rows == null || rows.length === 0) {
			finished.value = true;
			pageStatus.value = 'empty';
			return;
		}
		hospitalList.value = hospitalList.value.concat(rows);
		finished.value = false;
		if (hospitalList.value.length >= query.value.total) {
			finished.value = true;
		}
	});
}
onMounted(() => {
	docData();
});
</script>

<style lang="scss" scoped>
.nullTag {
	height: 100%;
	& ::v-deep(.van-nav-bar__text) {
		color: #0052cc;
	}
	& ::v-deep(.van-nav-bar .van-icon) {
		color: #0052cc;
	}
	.search {
		margin: 10px 0;
	}

	.list {
		height: calc(100% - 25.74vw);
		overflow: auto;
	}
}
::v-deep(.van-radio-group) {
	.van-radio {
		position: relative;
		.van-radio__icon {
			position: absolute;
			z-index: 2;
			left: 10px;
			top: 50%;
			transform: translateY(-80%);
			height: 20px;
			font-size: 20px;
			.van-icon {
				width: 20px;
				height: 20px;
			}
		}
		.van-radio__label {
			margin-left: 0;
			flex: 1;
		}
	}
}
</style>
