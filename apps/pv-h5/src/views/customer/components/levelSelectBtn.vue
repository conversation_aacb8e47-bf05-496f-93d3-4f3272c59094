<template>
	<div style="position: relative">
		<div class="level-box-title">
			{{ title }}
		</div>
		<div ref="spanBox" style="overflow: hidden">
			<span v-for="(i, ind) in items" :key="ind" class="level-box-span f12" :class="{ active: list.findIndex((ele) => ele.text === i.text) > -1 }" @click="clickItem(title, i, flag)">
				{{ i.text }}
			</span>
		</div>
	</div>
</template>

<script>
export default {
	name: 'LevelSelectBtn',
	props: {
		title: {
			type: String,
			require: true,
		},
		flag: {
			type: String,
			require: true,
		},
		items: {
			type: Array,
			require: true,
		},
		type: {
			type: Number /* 1 单选，2 多选 */,
			require: true,
		},
	},
	computed: {
		// 获取从医院详情页面跳转携带的值
		docHospitalText() {
			// return this.$store.state.client.docHospitalText
			return '';
		},
	},
	watch: {
		docHospitalText: {
			handler(val) {
				if (val !== '') {
					this.clickItem('', val, '1');
				}
			},
			immediate: true,
		},
	},
	data() {
		return {
			iconShow: false,
			list: [],
		};
	},
	mounted() {
		this.isIconShow();
	},
	methods: {
		isIconShow() {
			this.$nextTick(() => {
				const numHeight = this.$refs.spanBox.offsetHeight;
				if (numHeight > 67) {
					this.iconShow = this.title && true;
					if (this.iconShow) {
						this.$refs.spanBox.style.Height = '65px';
					}
				}
			});
		},
		showMore(e) {
			const target = e.currentTarget;
			if (this.$refs.spanBox.style.maxHeight === '65px') {
				this.$refs.spanBox.style.maxHeight = 'inherit';
				target.classList.remove('van-icon-arrow-down');
				target.classList.add('van-icon-arrow-up');
			} else {
				this.$refs.spanBox.style.Height = '65px';
				target.classList.remove('van-icon-arrow-up');
				target.classList.add('van-icon-arrow-down');
			}
		},
		clickItem(i1, i2, flag) {
			// return
			if (this.type === 1) {
				/* 单选 组 */
				this.list = [];
				this.list.push(i2);
				this.$parent.$parent.$parent.radioQuery.oneLevel = i1;
				this.$parent.$parent.$parent.radioQuery.twoLevel = i2.indexOf('全部') > -1 ? i1 : i2;
				this.$store.commit('setdocHospitalText', '');
			} else {
				/* 多选 组 */
				// eslint-disable-next-line no-prototype-builtins
				if (!this.$parent.$parent.$parent.listMultiple.hasOwnProperty(i1)) {
					this.$parent.$parent.$parent.listMultiple[i1] = [];
				}
				if (flag === 'single') {
					/* 多选组中的单选 */
					if (this.$parent.$parent.$parent.listMultiple[i1].length > 0) {
						const arr = this.$parent.$parent.$parent.listMultiple[i1]; // 保存当前单选数组内的值
						this.list.splice(this.list.indexOf(arr[0]), 1); // 从选中项中删除
						this.$parent.$parent.$parent.listMultiple[i1] = []; // 从分类值中删除
					}
				}
				if (this.list.indexOf(i2) < 0) {
					this.list.push(i2); // 记录选中项
				} else {
					this.list.splice(this.list.indexOf(i2), 1);
				}
				this.$parent.$parent.$parent.listMultiple[i1] = this.list; // 记录分类值
			}
		},
	},
};
</script>

<style scoped lang="scss">
.van-icon {
	position: absolute;
	right: 0;
	top: 0;
	padding: 10px;
}
.level-box-title {
	font-size: 15px;
	color: #172b4d;
	margin: 5px 15px 2px 0;
	padding: 8px 6px 8px 0;
	font-weight: 600;
}
.level-box-span {
	margin: 0 8px 8px 0;
	font-size: 13px;
	color: #172b4d;
	padding: 4px 10px;
	display: inline-block;
	border-radius: 4px;
	background-color: rgba(94, 91, 91, 0.15);
	&.active {
		color: #0052cc;
		background-color: rgba(51, 119, 255, 0.1);
	}
}
</style>
