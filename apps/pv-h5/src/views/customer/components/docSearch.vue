<template>
	<div class="search">
		<div class="input">
			<img class="icons" src="@/assets/img/search.png" alt="" />
			<input v-model="value" :placeholder="text" @keypress.enter="changeFn" />
			<van-icon size="16" @click="resetFn" color="#a0a2a2" v-if="value" class="input-icon" name="clear" />
		</div>
		<span class="search-btn" @click="changeFn">搜索</span>
	</div>
</template>

<script setup>
import { debounce } from 'lodash-es';
import { ref, watch } from 'vue';
const props = defineProps({
	text: {
		type: String,
	},
	searchValue: {
		type: String,
	},
});
watch(
	() => props.searchValue,
	(val) => {
		value.value = val;
	},
	{ deep: true }
);
const value = ref('');
const emit = defineEmits(['changeFn', 'reset']);

const changeFn = debounce(
	() => {
		emit('changeFn', value.value);
	},
	1000,
	{
		leading: true,
		trailing: false,
	}
);
function resetFn() {
	value.value = '';
	emit('reset', value.value);
}
</script>

<style lang="scss" scoped>
.search {
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 8px 0;
	padding: 0 6px 0 12px;
	.input {
		height: 35px;
		flex: 1;
		position: relative;
	}

	input {
		border: 0;
		height: 100%;
		width: 100%;
		line-height: 10px;
		border-radius: 25px;
		padding-left: 12%;
		padding-right: 12%;
		background-color: #f4f5f7;
		border: 1px solid #dfe1e6;
	}

	span {
		display: inline-block;
		// margin-left: 3%;
	}
}
.icons {
	position: absolute;
	width: 18px;
	height: 18px;
	top: 25%;
	left: 5%;
}

.input-icon {
	position: absolute;
	top: 25%;
	right: 5%;
}
.van-icon {
	font-size: 16px !important;
}
.search-btn {
	color: #0052cc;
	padding: 0 12px 0 10px;
	font-size: 14px;
}
</style>
