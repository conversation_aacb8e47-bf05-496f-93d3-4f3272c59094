<template>
	<div class="area">
		<van-popup v-model:show="showPopup" :style="{ height: '100%', width: '100%' }">
			<div class="search">
				<div class="search-back" @click="showPopup = false">
					<img src="@/assets/img/leftarrow.png" alt="" />
				</div>
				<div class="search-input">
					<input v-model="searchValue" :placeholder="placeholder" @keypress.enter="changeFn" />
					<van-icon v-if="searchValue" class="clear" name="clear" @click="clearSeach" />
				</div>
				<span class="search-btn" @click="changeFn">搜索</span>
			</div>
			<div class="info-dq">
				<van-tabs v-model:active="currentIndex" title-active-color="#0052CC">
					<van-tab>
						<div class="list">
							<div class="list-item" :class="{ active: provinceChecked.includes(item.id) }" v-for="item in provinceList" :key="item.code" @click="checked(item, 'province')">
								<span>{{ item.name }}</span>
								<van-icon name="success" style="float: right" v-show="provinceChecked.includes(item.id)" />
							</div>
						</div>
						<template #title>
							<van-badge :show-zero="false" :content="provinceChecked.length">
								<span>省份</span>
							</van-badge>
						</template>
					</van-tab>
					<van-tab title="城市">
						<div class="list">
							<div class="list-item" :class="{ active: cityChecked.includes(item.id) }" v-for="item in cityList" :key="item.code" @click="checked(item, 'city')">
								<span>{{ item.name }}</span>
								<van-icon name="success" style="float: right" v-show="cityChecked.includes(item.id)" />
							</div>
						</div>
						<template #title>
							<van-badge :show-zero="false" :content="cityChecked.length"> <span>城市</span></van-badge>
						</template>
					</van-tab>
					<van-tab>
						<div v-if="cityChecked.length > 0" class="list">
							<div class="list-item" :class="{ active: qxChecked.includes(item.id) }" v-for="item in qxList" :key="item.code" @click="checked(item, 'qx')">
								<span>{{ item.name }}</span>
								<van-icon name="success" style="float: right" v-show="qxChecked.includes(item.id)" />
							</div>
						</div>
						<div v-else style="text-align: center; margin: 2vw; color: #909399; font-size: 3.7vw">
							<span>请选择具体省份及城市</span>
						</div>
						<template #title>
							<van-badge :show-zero="false" :content="qxChecked.length"><span>区/县</span></van-badge>
						</template>
					</van-tab>
				</van-tabs>
			</div>
			<div class="btn">
				<div class="btn-reset" @click="handleReset">重置</div>
				<div class="btn-comfirm" @click="handleConfirm">确定</div>
			</div>
		</van-popup>
	</div>
</template>
<script setup>
import { ref, onMounted, getCurrentInstance, watch, toRaw } from 'vue';
import { queryProvince, queryCity, queryQx } from '@/api/user';
const props = defineProps(['defaultSelected']);
const { proxy } = getCurrentInstance();
const showPopup = ref(false);
const currentIndex = ref(0);
const searchValue = ref('');
const placeholder = ref('请输入省份');
watch(currentIndex, (n) => {
	let a = { 0: '请输入省份', 1: '请输入城市', 2: '请输入区/县' };
	placeholder.value = a[n];
	searchValue.value = searchValueList.value[n];

	// provinceList.value = provinceSource.value
	// cityList.value = citySource.value
	// qxList.value = qxSource.value
});

let provinceSource = ref([]);
let provinceList = ref([]);
let provinceChecked = ref([]);
const getProvince = async () => {
	let res = await queryProvince();
	provinceList.value = res.result;
	provinceSource.value = res.result;
};

const citySource = ref([]);
const cityList = ref([]);
const cityChecked = ref([]);
const getCity = async () => {
	let res = await queryCity(provinceChecked.value);
	cityList.value = res.result;
	citySource.value = res.result;
};

const qxSource = ref([]);
const qxList = ref([]);
const qxChecked = ref([]);
const getQx = async () => {
	let res = await queryQx(cityChecked.value);
	qxList.value = res.result;
	qxSource.value = res.result;
};

// search change
let searchValueList = ref(['', '', '']);
const updateList = (list, source) => {
	if (!searchValue.value) {
		list.value = source.value;
	} else {
		list.value = source.value.filter((ele) => ele.name.includes(searchValue.value));
	}
};
const clearSeach = () => {
	searchValue.value = '';
	changeFn();
};
const changeFn = () => {
	if (currentIndex.value === 0) {
		searchValueList.value[0] = searchValue.value;
		updateList(provinceList, provinceSource);
	} else if (currentIndex.value === 1) {
		searchValueList.value[1] = searchValue.value;
		updateList(cityList, citySource);
	} else {
		searchValueList.value[2] = searchValue.value;
		updateList(qxList, qxSource);
	}
};

const updateCheckedArray = (array, itemId) => {
	const index = array.indexOf(itemId);
	if (index === -1) {
		array.push(itemId);
	} else {
		array.splice(index, 1);
	}
};

const checked = (item, type) => {
	if (type === 'province') {
		updateCheckedArray(provinceChecked.value, item.id);
		cityChecked.value = [];
		cityList.value = [];
		qxChecked.value = [];
		qxList.value = [];
		searchValueList.value[1] = '';
		searchValueList.value[2] = '';
		getCity();
	} else if (type === 'city') {
		updateCheckedArray(cityChecked.value, item.id);
		if (cityChecked.value.length !== 0) {
			qxChecked.value = [];
			searchValueList.value[2] = '';
			getQx();
		}
	} else if (type === 'qx') {
		updateCheckedArray(qxChecked.value, item.id);
	}
};
// watch(
//   provinceChecked,
//   n => {
//     cityChecked.value = []
//     cityList.value = []
//     qxChecked.value = []
//     qxList.value = []
//     searchValueList.value[1] = ''
//     searchValueList.value[2] = ''
//     getCity()
//   },
//   { deep: true }
// )
// watch(
//   cityChecked,
//   n => {
//     if (n.length !== 0) {
//       qxChecked.value = []
//       searchValueList.value[2] = ''
//       getQx()
//     }
//   },
//   { deep: true }
// )
const handleReset = () => {
	provinceChecked.value = [];
	cityChecked.value = [];
	qxChecked.value = [];
};
const emit = defineEmits(['confirmArea']);
const handleConfirm = () => {
	showPopup.value = false;
	const getCheckedData = (checkedArray, list) => {
		const checkedSet = new Set(checkedArray.value);
		return list.value.filter((item) => checkedSet.has(item.id));
	};

	const provinceCheckedData = getCheckedData(provinceChecked, provinceList);
	const cityCheckedData = getCheckedData(cityChecked, cityList);
	const qxCheckedData = getCheckedData(qxChecked, qxList);
	emit('confirmArea', { province: provinceCheckedData, city: cityCheckedData, qx: qxCheckedData });
};
//回显
watch(showPopup, async (n) => {
	if (n) {
		let { province = [], city = [], qx = [] } = props.defaultSelected;
		provinceChecked.value = [];
		cityChecked.value = [];
		qxChecked.value = [];
		console.log(province, city, qx);
		provinceList.value.forEach((ele) => {
			province.forEach((i) => {
				if (i.id === ele.id && !provinceChecked.value.includes(ele.id)) {
					provinceChecked.value.push(ele.id);
				}
			});
		});
		if (province.length > 0) {
			await getCity();
		}
		cityList.value.forEach((ele) => {
			city.forEach((i) => {
				if (i.id === ele.id && !cityChecked.value.includes(ele.id)) {
					cityChecked.value.push(ele.id);
				}
			});
		});
		if (city.length > 0) {
			await getQx();
		}
		qxList.value.forEach((ele) => {
			qx.forEach((i) => {
				if (i.id === ele.id && !qxChecked.value.includes(ele.id)) {
					qxChecked.value.push(ele.id);
				}
			});
		});
	}
});
onMounted(async () => {
	getProvince();
	getCity();
});
defineExpose({ showPopup });
</script>
<style lang="scss" scoped>
::v-deep(.van-popup--center) {
	margin: 0;
	max-width: initial;
}
.search {
	padding: 5px 8px;
	height: 46px;
	display: flex;
	align-items: center;
	border-bottom: 1px solid #dfe1e6;
	&-back {
		margin-right: 15px;
		margin-left: 7px;
		img {
			width: 8px;
		}
	}
	&-input {
		flex: 1;
		height: 35px;
		position: relative;
		input {
			width: 100%;
			height: 100%;
			border: none;
			border-radius: 20px;
			background-color: #f4f5f7;
			padding-left: 17px;
			padding-right: 30px;
		}
		.clear {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			right: 15px;
		}
	}
}
.info-dq {
	height: calc(100% - 46px - 63px);
}
::v-deep(.van-tabs) {
	height: 100%;
	overflow-y: auto;
	.van-tabs__wrap {
		position: sticky;
		top: 0px;
		z-index: 1;
	}
	.van-tabs__nav {
		height: 41px !important;
		.van-tab--active {
			.van-tab__text {
				font-weight: bold;
				font-size: 14px;
			}
		}
		.van-tab__text {
			font-weight: 500;
			font-size: 14px;
			overflow: visible;
		}
		.van-tabs__line {
			width: 18px;
			height: 2px;
			bottom: 7px;
			background-color: #0052cc;
		}
	}
	.van-tabs__content {
		// background-color: red;
		.list {
			border-top: 1px solid #dfe1e6;
			&-item {
				color: #333333;
				font-size: 17px;
				padding: 12px;
				border-bottom: 1px solid #dfe1e6;
			}
			&-item.active {
				color: #0052cc;
				font-weight: bold;
			}
		}
	}
}

.btn {
	width: 100%;
	position: absolute;
	bottom: 10px;
	display: flex;
	padding: 0 30px;
	div {
		flex: 1;
		height: 34px;
		line-height: 34px;
		text-align: center;
		border: 1px solid #0052cc;
		color: #0052cc;
		border-radius: 16px;
	}
	.btn-reset {
		background: #fff;
		margin-right: 5px;
	}
	.btn-comfirm {
		background-color: #0052cc;
		color: #fff;
		margin-left: 5px;
	}
}
.search-btn {
	color: #0052cc;
	padding: 0 6px 0 10px;
	font-size: 15px;
}
</style>
