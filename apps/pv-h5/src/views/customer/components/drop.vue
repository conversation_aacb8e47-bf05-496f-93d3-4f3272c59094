<template>
	<div style="width: 100%; position: relative">
		<div class="filterDropdown">
			<div class="nav">
				<div v-for="(item, index) in menu" :key="index" class="first-menu" :class="{ on: showPage === index }" @click="toggleSelect(index)">
					<span class="name" :style="{ color: reMenu[index].indexOf(item) <= -1 ? '#0052cc' : '' }">{{ item.indexOf('.') > -1 ? $t(item) : item }}</span>
					<span v-if="item != '筛选'" class="iconfont triangle" :style="'transform:rotate(' + triangleDeg[index] + 'deg);'"></span>
					<svg-icon v-else class="iconStyle" :class="{ activeColor: showPage === menu.length - 1 }" icon-class="filter" />
				</div>
			</div>
		</div>
		<!-- task -->
		<teleport to="body">
			<div class="mask" :style="{ top: topNum || '0px' }" :class="{ show: showPage > -1, hide: showPage === -1 }" @click="toggleSelect(showPage)"></div>
		</teleport>
	</div>
</template>

<script>
import { overflowYauto, overflowYhiddern } from '@/utils/index';
export default {
	name: 'Drop',
	props: ['topNum', 'menu'],
	data() {
		return {
			showPage: -1,
			triangleDeg: [], // 小三角形的翻转动画控制,
			reMenu: ['医院/适应症', '医院/科室/大区/DSM', '科室/筛选/日期/MR', '筛选/日期'],
		};
	},
	methods: {
		toggleSelect(index) {
			if (index === this.showPage) {
				this.hideMask();
			} else {
				overflowYhiddern();
				this.$emit('closeCb'); // 关闭筛选
				this.triangleDeg[this.showPage] = 0;
				this.showPage = index;
				this.triangleDeg[this.showPage] = 180;
				this.$nextTick(() => {
					this.$emit('triggerCb', index);
				});
			}
		},
		hideMask() {
			this.$emit('closeCb'); // 关闭筛选
			this.triangleDeg[this.showPage] = 0;
			this.showPage = -1;
			overflowYauto();
		},
	},
};
</script>

<style scoped lang="scss">
/* 字体图标 */
@font-face {
	font-family: 'HM-FD-font';
	src: url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAALAAAsAAAAABpQAAAJzAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDBgp4gQIBNgIkAwwLCAAEIAWEbQc5G8sFERWMIbIfCbbzqA4hp7InSBibVsYGb4J42o82b3e/nJlHMw/NHbGOlwKJRCRpwzPtpAECCOZubdqxjYpQLMlVg+70/08edrgQOtx2ukpVyApZn+dyehPoQObHo3O85rYx9vOjXoBxQIHugW2yIkqIW2QXcScu4jwE8CSWbKSmrqUHFwOaJoCsLM5P4haSGIxRcRHshrUGucLCVcfqI3AZfV/+USguKCwNmtsxVztDxU/n55C+3W0Z4QQpEOTNFqCBbMCAjDUWB9CIwWk87aa70cYgqLkyd3dEmm+18R8eKATEBrV7A5CulBT8dKiWOYZk412XNcDdKSEKSGODnyKIDl+dmVt9/Dx4pu/xyeutkMlHISGPTsPCnoTNP9nOT6wTtDdlO6dPr47efvj942lkYuQzrhMKEjq9N6y98P3340gmlJ/RStUD6F31CAEEPtUW94/7rf+7XgaAz57X0ZHXAGsFFwVgw38yALuMb0IBbVyNamFYEw4oKMDTj3AHRQP5Pt4dci9VwSVkRNQh5r7CLskZadhsWHhRDBsXczk8ZYk3ewnCxmQeQKa3BOHvA8XXO2j+vqRhf7CE+sPmn4anvoL29JLa4qqaUQkmoK+QG2osCckq7txi2leK86aIPyJ3eQZ8xytXYmyQ51jQndJAxIJlqiGSLsOqImiZCjTiZCJt6Lq26U2OoXqwUo0hRaAE0K5AziANy/uLVeXzWyjVqyjcoeupjxDr5MMDn8MDkLG9Aenu5ZrOSSoghAUsRmogkkahSoWAtnlUARnCkY3It0Iu7mWhdmd9Z/19BwBP6GidEi0G56opckXTGZVSPxgAAAA=');
}
.iconfont {
	font-family: 'HM-FD-font' !important;
	font-size: 14px;
	font-style: normal;
	color: rgb(147, 144, 144);
	&.triangle {
		&:before {
			content: '\e65a';
		}
	}
	&.selected {
		&:before {
			content: '\e607';
		}
	}
}
.iconStyle {
	margin-left: 3px;
	font-size: 16px !important;
	color: #939090;
}
.activeColor {
	color: #0052cc;
}
.filterDropdown {
	background-color: #fff;
	border-radius: 0px 0px 5px 5px;
	flex-shrink: 0;
	width: 100%;
	height: 40px;
	// position: fixed;
	// z-index: 997;
	// z-index: 5;
	/* H5要漂浮在导航栏之下 注意,如果组件放在scroll-view里面,请修改下方的top数字*/
	/* #ifdef H5 */
	// top: 151px;
	// left: 0;
	/* #endif */
	flex-wrap: nowrap;
	display: flex;
	flex-direction: row;

	// box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
	div {
		display: flex;
		flex-wrap: nowrap;
	}
}
.nav {
	width: 100%;
	height: 40px;
	z-index: 144;
	background-color: #f4f5f7;
	flex-direction: row;
	.first-menu {
		flex: 1;
		font-size: 14px;
		color: #172b4d;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		transition: color 0.2s linear;
		&.on {
			color: #0052cc;
			.iconfont {
				color: #0052cc;
			}
		}
		.name {
			height: 20px;
			line-height: 20px;
			text-align: center;
			text-overflow: clip;
			overflow: hidden;
		}
		.iconfont {
			width: 13px;
			height: 18px;
			align-items: center;
			justify-content: center;
			transition: transform 0.2s linear, color 0.2s linear;
		}
		.svg-icon {
			width: 18px;
			height: 18px;
			color: #172b4d;
		}
	}
}

.mask {
	z-index: 140;
	position: absolute;
	top: 0;
	left: 0px;
	right: 0px;
	bottom: 0;
	height: 1000px;
	background-color: rgba(0, 0, 0, 0);
	transition: background-color 0.2s linear;
	&.show {
		background-color: rgba(0, 0, 0, 0.5);
	}
	&.hide {
		display: none;
	}
}
</style>
