<template>
	<div>
		<van-popup v-model:show="showBottom" :style="{ height: '100%', width: '100%', display: 'flex', flexDirection: 'column' }">
			<div class="search">
				<div class="search-back" @click="showBottom = false">
					<img src="@/assets/img/leftarrow.png" alt="" />
				</div>
				<div class="search-input">
					<input v-model="searchValue" placeholder="请输入科室名称" @keypress.enter="changeFn" />
					<van-icon size="16" @click="resetFn" color="#172b4d" v-if="searchValue" class="input-icon" name="clear" />
				</div>
				<span class="search-btn" @click="changeFn">搜索</span>
			</div>
			<div v-if="items.length > 0" class="list">
				<van-tree-select height="100%" class="tree-select" :items="items" @click-item="cliItem" v-model:active-id="activeId" v-model:main-active-index="activeIndex" />
			</div>
			<van-empty v-else description="暂无数据" />
			<div v-if="items.length > 0" class="btn_box">
				<div class="btn_reset" @click="handleReset">重置</div>
				<div class="btn_comfirm" @click="handleConfirm">确定</div>
			</div>
		</van-popup>
	</div>
</template>
<script setup>
import { ref, getCurrentInstance, onMounted, watch, reactive } from 'vue';
import { getDepartment } from '@/api/doctor';
const { proxy } = getCurrentInstance();
const emits = defineEmits(['radSelect']);
const showBottom = ref(false);
const searchValue = ref('');
const activeId = ref(['全部-all']);
const activeIndex = ref(0);
const activeObj = ref({});
const activeIdObj = ref({});
const jsonData = reactive({
	oneLevel: [],
	twoLevel: [],
});
const items = ref([]);
const resetFn = () => {
	searchValue.value = '';
	changeFn();
};
// 搜索
const changeFn = () => {
	getData();
};
const handleReset = (flag) => {
	activeId.value = ['全部-all'];
	activeIndex.value = 0;
	activeObj.value = {};
	activeIdObj.value = {};
	for (const i in items.value) {
		items.value[i].badge = undefined;
	}
	showBottom.value = false;
	emits('radSelect', flag);
};
const handleConfirm = () => {
	jsonData.oneLevel = [];
	jsonData.twoLevel = [];
	for (const i in activeObj.value) {
		jsonData.oneLevel.push(i);
		if (activeObj.value[i].length > 0) {
			jsonData.twoLevel.push(activeObj.value[i]);
		} else {
			/* 二级无数据，拼接全部数据 */
			for (const j in items.value) {
				if (items.value[j].text === i) {
					for (const k in items.value[j].children) {
						if (items.value[j].children[k].text !== '不限') {
							jsonData.twoLevel.push(items.value[j].children[k].text);
						}
					}
				}
			}
		}
	}
	jsonData.oneLevel = jsonData.oneLevel.join(',');
	jsonData.twoLevel = jsonData.twoLevel.join(',');
	showBottom.value = false;
	emits('radSelect', jsonData);
};
const cliItem = (data) => {
	if (data.id !== '全部-all') {
		// 选择了除全部地区以外的，将全部地区的高亮取消
		if (activeId.value.indexOf('全部-all') > -1) {
			activeId.value.splice([activeId.value.indexOf('全部-all')], 1);
		}
		const activeProvince = items.value[activeIndex.value].text;
		const activeProvinceId = items.value[activeIndex.value].text + '-id';
		// eslint-disable-next-line no-prototype-builtins
		if (!activeObj.value.hasOwnProperty(activeProvince)) {
			activeObj.value[activeProvince] = [];
			activeIdObj.value[activeProvinceId] = [];
		}
		if (data.id.indexOf('all') > -1) {
			/* 二级选了不限 */
			activeObj.value[activeProvince] = []; /* 二级置空 */
			for (let i = 0; i < activeIdObj.value[activeProvinceId].length; i++) {
				if (activeIdObj.value[activeProvinceId][i] !== data.id) {
					const index = activeId.value.indexOf(activeIdObj.value[activeProvinceId][i]);
					activeId.value.splice(index, 1); /* 删除对应一级中除了不限的所有高亮二级id */
				}
			}
			activeIdObj.value[activeProvinceId] = []; /* 二级id置空 */
			// 判断是否选中了不限
			if (activeId.value.indexOf(data.id) <= -1) {
				items.value[activeIndex.value].badge = undefined;
				delete activeObj.value[activeProvince];
			} else {
				items.value[activeIndex.value].badge = items.value[activeIndex.value].children.length - 1;
			}
		} else {
			/* 二级选了除不限以外的其他 */
			for (let i = 0; i < activeId.value.length; i++) {
				if (activeId.value[i] === activeProvince + '-all') {
					activeId.value.splice(i, 1); /* 删除不限对应高亮id */
					activeIdObj.value[activeProvinceId].splice(i, 1); /* 删除不限对应id */
				}
			}
			/* 无则添加，有则删除 */
			if (activeObj.value[activeProvince].indexOf(data.text) < 0) {
				activeObj.value[activeProvince].push(data.text);
				activeIdObj.value[activeProvinceId].push(data.id);
				items.value[activeIndex.value].badge = activeObj.value[activeProvince].length === 0 ? undefined : activeObj.value[activeProvince].length;
			} else {
				activeObj.value[activeProvince].splice(activeObj.value[activeProvince].indexOf(data.text), 1);
				activeIdObj.value[activeProvinceId].splice(activeIdObj.value[activeProvinceId].indexOf(data.id), 1);
				items.value[activeIndex.value].badge = activeObj.value[activeProvince].length === 0 ? undefined : activeObj.value[activeProvince].length;
			}
			/* 对应二级为空，删除一级 */
			if (activeObj.value[activeProvince].length === 0) {
				delete activeObj.value[activeProvince];
			}
		}
	} else {
		// 选择全部地区
		activeId.value = ['全部-all'];
		activeObj.value = {};
		activeIdObj.value = {};
		for (const i in items.value) {
			if (i !== 0) {
				items.value[i].badge = undefined;
			}
		}
	}
};
const getData = () => {
	getDepartment({ search: searchValue.value }).then((res) => {
		if (res.code === 200) {
			if (res.result.length > 0) {
				items.value = res.result;
				items.value.forEach((item) => {
					item.text = item.label;
					item.children.forEach((val) => {
						val.text = val.label;
						val.id = randomString(20);
					});
				});
				items.value.forEach((element) => {
					element.children.unshift({
						text: '不限',
						id: element.text + '-all',
					});
				});
				items.value.unshift({
					text: '全部科室',
					children: [
						{
							text: '全部科室',
							id: '全部-all',
						},
					],
				});
			} else {
				items.value = [];
			}
		}
	});
};
// 随机字符
const randomString = (n) => {
	const str = 'abcdefghijklmnopqrstuvwxyz9876543210';
	let tmp = '';
	let i = 0;
	const l = str.length;
	for (i = 0; i < n; i++) {
		tmp += str.charAt(Math.floor(Math.random() * l));
	}
	return tmp;
};
onMounted(() => {
	getData();
});
defineExpose({ showBottom });
</script>

<style lang="scss" scoped>
::v-deep(.van-popup--center) {
	margin: 0;
	max-width: initial;
}
.search {
	padding: 5px 8px;
	height: 46px;
	border-bottom: solid 1px #dfe1e6;
	display: flex;
	align-items: center;
	&-back {
		margin-right: 15px;
		margin-left: 7px;
		img {
			width: 8px;
		}
	}
	&-input {
		flex: 1;
		height: 35px;
		position: relative;
		input {
			width: 100%;
			height: 100%;
			border: none;
			border-radius: 20px;
			background-color: #f4f5f7;
			padding-left: 16px;
			padding-right: 30px;
		}
		.input-icon {
			position: absolute;
			top: 10px;
			right: 10px;
		}
	}
}

.list {
	height: calc(100% - 106px);
	&:deep(.van-tree-select__item--active) {
		color: #0052cc;
		font-weight: 400;
	}
	&:deep(.van-sidebar-item--select:before) {
		background-color: #0052cc;
	}
	&:deep(.van-sidebar-item__text) {
		font-weight: 400;
	}
	&:deep(.van-tree-select__item) {
		font-weight: 400;
	}
}

.btn_box {
	width: 100%;
	display: flex;
	padding: 12px 8px 15px;
	.btn_reset {
		flex: 1;
		width: 164px;
		height: 32px;
		line-height: 32px;
		text-align: center;
		border: 1px solid #0052cc;
		color: #0052cc;
		border-radius: 16px;
		background: #fff;
		margin-right: 10px;
	}
	.btn_comfirm {
		flex: 1;
		width: 164px;
		height: 32px;
		line-height: 32px;
		text-align: center;
		color: rgba(255, 255, 255, 100);
		border-radius: 16px;
		background-color: #0052cc;
		margin-left: 10px;
	}
}
.search-btn {
	color: #0052cc;
	padding: 0 6px 0 10px;
	font-size: 15px;
}
</style>
