<template>
	<div>
		<van-popup transition="van-fade" :overlay="false" :duration="0.15" position="top" :style="{ height: '330px', top: topNum }" v-model:show="showRadio" get-container="body">
			<!--  loading    -->
			<van-loading v-if="netStatus === 1" style="text-align: center; margin: 80px" size="40px" color="#1989fa" />
			<!--  错误提示    -->
			<van-empty v-if="netStatus === 0" image="search" description="暂无数据" />
			<!--   筛选内容   -->
			<div v-if="netStatus === 2" class="level-box clearfix">
				<div style="overflow: auto" class="max_height">
					<div v-for="(item, index) in items" :key="index">
						<selectBtn ref="selectBtnRef" :title="item.text" :items="item.children" :type="type" />
					</div>
				</div>
			</div>
			<div class="btn_box">
				<div class="btn_reset" @click="handleReset">重置</div>
				<div class="btn_comfirm" @click="handleConfirm">确定</div>
			</div>
		</van-popup>
	</div>
</template>

<script>
import selectBtn from './levelSelectBtn';
import { lockScroll2 } from './filterLockScroll';
export default {
	name: 'LevelSelect',
	mixins: [lockScroll2],
	components: {
		selectBtn,
	},
	props: {
		topNum: {
			type: String,
			require: true,
		},
		items: {
			type: Array,
			require: true,
		},
		netStatus: {
			type: Number,
			require: true,
		},
		type: {
			type: Number /* 1 单选，2 多选 */,
			require: true,
		},
	},
	data() {
		return {
			showRadio: false,
			list: [],
			radioQuery: {
				oneLevel: '',
				twoLevel: '',
			},
			listMultiple: {},
		};
	},
	methods: {
		handleReset() {
			for (const i in this.$refs.selectBtnRef) {
				this.$refs.selectBtnRef[i].list = [];
			}
			if (this.type === 1) {
				this.radioQuery = {
					oneLevel: '',
					twoLevel: '',
				};
			} else {
				this.listMultiple = {};
			}
		},
		handleConfirm() {
			if (this.type === 1) {
				this.$emit('select', this.radioQuery);
			} else {
				this.$emit('select', this.listMultiple);
			}
		},
	},
};
</script>

<style scoped lang="scss">
.level-box {
	padding: 5px 15px;
	height: calc(100% - 55px);
	.level-box-title {
		margin: 5px 0 2px;
		padding: 8px 6px;
	}
	.van-icon {
		padding: 10px;
	}
	.level-box-span {
		margin: 0 8px 8px 0;
		padding: 4px 10px;
		display: inline-block;
		border-radius: 20px;
		background-color: #f2f2f2;
		&.active {
			color: #3875c6;
			background-color: rgb(197, 224, 255);
		}
	}
	.level-box-btn {
		padding: 5px;
		div {
			float: right;
			padding: 8px 30px;
			&.level-box-btn__reset {
				color: #3875c6;
				background-color: #e4f0fc;
				border-radius: 15px 0 0 15px;
			}
			&.level-box-btn__confirm {
				color: #fff;
				background-color: #3875c6;
				border-radius: 0 15px 15px 0;
			}
		}
	}
}
.btn_box {
	width: 100%;
	position: absolute;
	bottom: 10px;
	display: flex;
	padding: 0 30px;
	.btn_reset {
		flex: 1;
		width: 164px;
		height: 36px;
		line-height: 36px;
		text-align: center;
		border: 1px solid #3875c6;
		color: #3875c6;
		border-radius: 16px;
		background: #fff;
		margin-right: 10px;
	}
	.btn_comfirm {
		flex: 1;
		width: 164px;
		height: 36px;
		line-height: 36px;
		text-align: center;
		color: rgba(255, 255, 255, 100);
		border-radius: 16px;
		background-color: #3875c6;
		margin-left: 10px;
	}
}
.max_height {
	max-height: 100%;
}
</style>
