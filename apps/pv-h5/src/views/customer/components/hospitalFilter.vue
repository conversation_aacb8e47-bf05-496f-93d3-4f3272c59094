<template>
	<div>
		<van-popup v-model:show="showBottom" :style="{ height: '100%', width: '100%' }">
			<div class="search">
				<div class="search-back" @click="showBottom = false">
					<img src="@/assets/img/leftarrow.png" alt="" />
				</div>
				<div class="search-input">
					<input v-model="searchValue" :placeholder="placeholder" @keypress.enter="changeFn" />
				</div>
				<span class="search-btn" @click="changeFn">搜索</span>
			</div>
			<div class="info-dq">
				<span>已为您展示</span>
				<span class="info-dq-ac">{{ infoDq }}</span>
				<span>地区的医院</span>
				<span class="info-dq-xg" @click="areaFilterRef.showPopup = true">修改地区></span>
			</div>
			<div class="list">
				<van-col v-if="hospitalList.length > 0" span="24">
					<div :class="activeName.includes(item.name) ? 'active' : 'box'" v-for="item in hospitalList" :key="item" @click="selectHandler(item)">
						<span>{{ item.name }}</span>
						<van-icon name="success" style="float: right" v-show="activeName.includes(item.name)" />
					</div>
				</van-col>
				<van-empty v-else description="暂无数据" />
			</div>
			<div class="btn">
				<div class="btn-reset" @click="handleReset">重置</div>
				<div class="btn-comfirm" @click="handleConfirm">确定</div>
			</div>
		</van-popup>

		<areaFilter ref="areaFilterRef" :defaultSelected="listInfo" @confirmArea="confirmArea"></areaFilter>
	</div>
</template>
<script setup>
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { findHospital } from '@/api/doctor';
import areaFilter from './areaFilter.vue';

const areaFilterRef = ref(null);
const props = defineProps(['customerListingType', 'defaultSelected']);
const { proxy } = getCurrentInstance();

const searchValue = ref('');
const placeholder = ref('请输入医院名称');
const infoDq = ref('全部');
const changeFn = () => {
	getData();
};
const showBottom = ref(false);
const hospitalList = ref([]);
const activeName = ref([]);
const getData = () => {
	const query = {
		customerListingType: props.customerListingType,
		search: searchValue.value,
		provinceCodes: listInfo.value.province?.map((ele) => ele.code),
		cityCodes: listInfo.value.city?.map((ele) => ele.code),
		areaCodes: listInfo.value.qx?.map((ele) => ele.code),
	};
	findHospital(query).then((res) => {
		hospitalList.value = res.result.map((ele) => {
			return { name: ele };
		});
	});
};
const selectHandler = (s) => {
	if (!activeName.value?.includes(s.name)) {
		activeName.value?.push(s.name);
	} else {
		activeName.value?.splice(activeName.value?.indexOf(s.name), 1);
	}
};
let listInfo = ref({});
const confirmArea = (list) => {
	// 获取最小一级有数据的对象
	const smallestUnitWithData = list.qx.length > 0 ? list.qx : list.city.length > 0 ? list.city : list.province.length > 0 ? list.province : {};
	infoDq.value = smallestUnitWithData.length > 0 ? smallestUnitWithData.map((ele) => ele.name).join('、') : '全部';
	listInfo.value = list;
	getData();
};

const handleReset = () => {
	activeName.value = [];
};
const emit = defineEmits(['fieIdConfirmArr']);

const handleConfirm = () => {
	showBottom.value = false;
	emit('fieIdConfirmArr', { hospitalName: activeName.value, ssq: listInfo.value });
};
//回显
watch(showBottom, (n) => {
	if (n) {
		hospitalList.value.forEach((ele) => {
			props.defaultSelected.forEach((i) => {
				if (ele.name === i && !activeName.value.includes(i)) {
					activeName.value.push(i);
				}
			});
		});
	}
});
onMounted(() => {
	getData();
});
defineExpose({ showBottom });
</script>
<style lang="scss" scoped>
::v-deep(.van-popup--center) {
	margin: 0;
	max-width: initial;
}
.search {
	padding: 5px 8px;
	height: 46px;
	display: flex;
	align-items: center;
	&-back {
		margin-right: 15px;
		margin-left: 7px;
		img {
			width: 9px;
		}
	}
	&-input {
		flex: 1;
		height: 35px;
		input {
			width: 100%;
			height: 100%;
			border: none;
			border-radius: 20px;
			background-color: #f4f5f7;
			padding-left: 17px;
		}
	}
}
.info-dq {
	color: #172b4d;
	font-weight: 400;
	font-size: 14px;
	padding: 5px 15px;
	background-color: rgba(0, 82, 204, 0.1);
	display: flex;
	&-ac {
		color: #0052cc;
		font-weight: bold;
		padding: 0 8px;
		max-width: 130px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	&-xg {
		margin-left: auto;
		color: #0052cc;
		font-weight: bold;
	}
}
.list {
	height: calc(100% - 46px - 44px - 50px);
	overflow-y: auto;
	.box {
		padding: 8px 30px;
	}
	.active {
		padding: 8px 30px;
		color: #0052cc;
		display: flex;
		align-items: center;
		span {
			margin-right: auto;
		}
	}
}
.btn {
	width: 100%;
	position: absolute;
	bottom: 10px;
	display: flex;
	padding: 0 30px;
	div {
		flex: 1;
		height: 34px;
		line-height: 34px;
		text-align: center;
		border: 1px solid #0052cc;
		color: #0052cc;
		border-radius: 16px;
	}
	.btn-reset {
		background: #fff;
		margin-right: 5px;
	}
	.btn-comfirm {
		background-color: #0052cc;
		color: #fff;
		margin-left: 5px;
	}
}
.search-btn {
	color: #0052cc;
	padding: 0 6px 0 10px;
	font-size: 15px;
}
</style>
