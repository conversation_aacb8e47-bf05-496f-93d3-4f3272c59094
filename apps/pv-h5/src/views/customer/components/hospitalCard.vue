<template>
	<div class="hospital-card">
		<div class="hospital-card-left">
			<img src="@/assets/img/hospital.png" alt="" />
		</div>
		<div class="hospital-card-right">
			<div class="hospital-card-right-name">{{ data.hospitalName }}</div>
			<div class="hospital-card-right-tag">
				<span>{{ data.hospitalType }}</span>
				<span>{{ data.hospitalLevel }}</span>
			</div>
		</div>
	</div>
</template>
<script setup>
const props = defineProps(['data']);
</script>
<style lang="scss" scoped>
.hospital-card {
	background: #fff;
	margin-bottom: 10px;
	padding: 20px 40px 20px 55px;
	border-bottom: 1px solid #e8e8e8;
	display: flex;
	&-left {
		width: 90px;
		display: flex;
		flex-direction: column;
		img {
			width: 100%;
			border-radius: 5px;
		}
		.status {
			text-align: center;
			margin-top: 7px;
			.tag {
				background-color: #eeecdf;
				color: #aca05e;
				border-radius: 999px;
				padding: 1px 5px;
				display: inline-block;
				font-size: 12px;
			}
		}
	}
	&-right {
		flex: 1;
		margin-left: 18px;
		&-name {
			color: #172b4d;
			font-size: 15px;
			margin-bottom: 7px;
		}
		&-tag {
			display: flex;
			flex-wrap: wrap;
			gap: 8px;
			span {
				background-color: #0052cc;
				padding: 2px 6px;
				border-radius: 5px;
				color: #ffffff;
			}
		}
	}
}
</style>
