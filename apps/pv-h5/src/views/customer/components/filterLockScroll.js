export const lockScroll = {
	watch: {
		fieRadio(val) {
			if (val) return (document.querySelector('#rn-customer').style.overflowY = 'hidden');
			document.querySelector('#rn-customer').style.overflowY = 'auto';
		},
	},
};

export const lockScroll2 = {
	watch: {
		showRadio(val) {
			if (val) return (document.querySelector('#rn-customer').style.overflowY = 'hidden');
			document.querySelector('#rn-customer').style.overflowY = 'auto';
		},
	},
};
