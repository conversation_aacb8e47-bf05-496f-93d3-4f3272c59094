<template>
	<van-row class="rad-main bgFFF">
		<van-popup transition="van-fade" :overlay="false" :duration="0.1" position="top" :style="{ height: '330px', top: topNum }" v-model:show="fieRadio" get-container="body">
			<!--  loading    -->
			<van-loading v-if="netStatus === 1" style="text-align: center; margin-top: 85px" size="40px" color="#1989fa" />
			<!--  错误提示    -->
			<van-empty v-if="netStatus === 0" image="search" :description="$t('common.noData')" />
			<!-- 内容显示 -->
			<div class="check_box">
				<van-col span="24" v-if="netStatus === 2">
					<div :class="activeName.includes(s.name) ? 'active' : 'box'" v-for="(s, index) in items" :key="index" @click="selectHandler(s)">
						<span v-if="isShow">{{ s.name || s.Name || s.brand }}</span>
						<van-icon name="success" style="float: right" v-show="activeName.includes(s.name)" />
					</div>
				</van-col>
			</div>
			<div class="btn_box">
				<div class="btn_reset" @click="handleReset">重置</div>
				<div class="btn_comfirm" @click="handleConfirm">确定</div>
			</div>
		</van-popup>
	</van-row>
</template>

<script>
import { lockScroll } from './filterLockScroll';
export default {
	name: 'RadioFilter',
	mixins: [lockScroll],
	props: {
		topNum: {
			type: String,
			require: true,
		},
		items: {
			type: Array,
			require: true,
		},
		netStatus: {
			type: Number,
			require: true,
		},
		colorShow: {
			type: Boolean,
		},
	},
	data() {
		return {
			isShow: true, // 为了触发页面的重新渲染
			fieRadio: false,
			activeName: [], // 用于记录选中的领域标签
			arr: [],
		};
	},
	watch: {
		colorShow(val) {
			if (val === true) {
				this.activeName = '';
			}
		},
	},
	methods: {
		selectHandler(s) {
			console.log(this.activeName);
			if (!this.activeName?.includes(s.name)) {
				this.activeName?.push(s.name);
			} else {
				this.activeName?.splice(this.activeName?.indexOf(s.name), 1);
			}
		},
		handleReset() {
			this.activeName = [];
		},
		handleConfirm() {
			this.$emit('fieIdConfirmArr', this.activeName);
		},
	},
};
</script>
<style lang="scss" scoped>
.rad-main {
	width: 100%;
}
.box {
	padding: 10px 30px;
}
.active {
	padding: 10px 30px;
	color: #0052cc;
}
.van-pop {
	border: 1px solid rgba(243, 243, 243, 100);
}
.check_box {
	height: calc(100% - 50px);
	overflow-y: auto;
}
.btn_box {
	width: 100%;
	position: absolute;
	bottom: 10px;
	display: flex;
	padding: 0 30px;
	.btn_reset {
		flex: 1;
		width: 164px;
		height: 36px;
		line-height: 36px;
		text-align: center;
		border: 1px solid #0052cc;
		color: #0052cc;
		border-radius: 16px;
		background: #fff;
		margin-right: 10px;
	}
	.btn_comfirm {
		flex: 1;
		width: 164px;
		height: 36px;
		line-height: 36px;
		text-align: center;
		color: rgba(255, 255, 255, 100);
		border-radius: 16px;
		background-color: #0052cc;
		margin-left: 10px;
	}
}
</style>
