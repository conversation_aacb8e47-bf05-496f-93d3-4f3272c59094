<template>
	<div id="rn-customer" class="rn" :class="{ 'rn-app': route?.query?.isTabBar === 'false' }">
		<!-- 常用应用 -->
		<div class="mc-content-middle__app">
			{{ $t('common.customer') }}
			<span @click="toVisitRecord" v-if="isVisitRecord" class="mc-content-middle__app__text">拜访记录</span>
		</div>
		<!-- 搜索 -->
		<doc-search :text="'输入医院或医生'" @changeFn="searchHandler" @reset="resetHandler" :searchValue="state.searchValue"></doc-search>
		<!-- 搜索历史 -->
		<div class="search_text">
			<span v-for="(item, index) in state.historyList" :key="index" @click="clickHistory(item)">{{ item }}</span>
		</div>

		<!-- 添加一个盒子 子元素做粘性定位 -->
		<div class="mc-content">
			<van-tabs v-model:active="active" @click-tab="changeFn">
				<van-tab color="#172b4d" :title="item.title" :name="item.name" v-for="item in state.tabList" :key="item.name"></van-tab>
			</van-tabs>
			<!-- 标签 -->
			<div class="tag" @click="goTag">
				<img src="@/assets/img/tag.png" alt="" />
				<span>标签</span>
			</div>
			<!-- 筛选器 -->
			<div id="contentPage" ref="shaixuan">
				<drop ref="dropRef" :topNum="state.topNum" :menu="state.menu" @closeCb="closeCb" @triggerCb="triggerCb"></drop>
			</div>
			<!-- 总共多少位医生，已筛选多少位 -->
			<p v-if="active !== '我的关注'" class="doc-total-tips">
				共有 <span style="color: red">{{ docAll }}</span> 位医生，您已筛选 <span style="color: red">{{ state.total }}</span> 位
			</p>
		</div>
		<!-- content -->
		<div class="rn-content">
			<!-- 加载中 -->
			<div v-if="state.pageStatus === 'loading'">
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton title :row="2" />
			</div>
			<!-- 无数据 -->
			<div class="no-data" v-if="state.pageStatus === 'empty'">
				<img src="@/assets/img/noData.png" />
				<span>暂无数据</span>
				<span v-if="enableNewDoctor && state.allClient === true && state.isFollow === false">请点击<span style="color: #0052cc" @click="toNewDoctor">新建</span></span>
			</div>

			<van-list v-if="state.pageStatus === 'normal'" v-model:loading="state.isLoading" ref="vantList" :offset="30" :immediate-check="false" :finished="state.finished" finished-text="没有更多了" @load="onLoad">
				<doc-card v-for="(item, index) in state.HCPList" :key="index" :data="item" @click="toView(item)" @changeFocus="changeFocus" @shareArticle="shareArticle"></doc-card>
			</van-list>
		</div>

		<!-- 产品筛选 -->
		<checkbox-filter ref="productRef" @fieIdConfirmArr="productConfirm" :netStatus="state.proStatus" :topNum="state.topNum" :items="state.productsList"></checkbox-filter>

		<!-- 其他筛选 -->
		<level-select ref="filterRef" @select="confirmFilter" :netStatus="state.otherStatus" :topNum="state.topNum" :items="state.filterList" :type="2" />

		<!-- 医院筛选2 -->
		<hospitalFilter v-if="hospitalFilterShow" ref="hospitalFilterRef" :customerListingType="state.customerListingType" :defaultSelected="hospitalIds" @fieIdConfirmArr="hosipitalConfirm1"></hospitalFilter>

		<!-- 科室筛选2 -->
		<departmentFilter @radSelect="confirmDepartment" ref="departmentFilterRef"></departmentFilter>
		<!-- 文章弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="shareDialog" position="bottom" :style="{ height: '90%' }">
			<article-list title="文章" @_articleDetail="articleDetail" @_activeArticle="activeArticle" @close="shareDialog = false"></article-list>
		</van-popup>

		<!-- 文章详情弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="articleDetailVis" position="bottom" :style="{ height: '90%' }">
			<ai-article-detail :sourceUrl="sourceUrl" :isToken="true" :title="articleTitle" @close="articleDetailVis = false"></ai-article-detail>
		</van-popup>
	</div>
</template>

<script setup>
import { showToast } from 'vant';
import wxApi from '@/api/wxApi.js';
import { scrollTop, isWeChat } from '@/utils/index';
import useUserStore from '@/store/modules/user';
import { getDoctorListNew, findFieldList, getAdministrativeTitle, getProfessionalTitle, focusDoctor } from '@/api/doctor';
import { getTrackId, recordShare } from '@/api/content';
import { wechatCrop, wechatAgent } from '@/api/login';
const userStore = useUserStore();
import docCard from './components/docCard.vue';
import docSearch from './components/docSearch.vue';
import drop from './components/drop.vue';
import checkboxFilter from './components/checkBoxFilter.vue';
import levelSelect from './components/levelSelect.vue';
import hospitalFilter from './components/hospitalFilter.vue';
import departmentFilter from './components/departmentFilter.vue';
import usePremissionStore from '@/store/modules/premission';
import useEnterStore from '@/store/modules/enterprise';
const perStore = usePremissionStore();

const enterStore = useEnterStore();
const isProxy = enterStore.enterInfo.isProxy;

const enableNewDoctor = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === '10000');
});

const regionalDoctors = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'regionalDoctors');
});

const allDoctors = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'regionalDoctors');
});
const isVisitRecord = computed(() => {
	return perStore.systemBtnList.some((item) => item.path === 'visitRecords');
});
const router = useRouter();
const route = useRoute();
const qyId = ref(userStore.userInfo.enterpriseVO.id);
const userId = ref(userStore.userInfo.username);
const active = ref('');
const dropRef = ref(null);
const shaixuan = ref(null);
const productRef = ref(null);
const filterRef = ref(null);
const departmentFilterRef = ref(null);
const hospitalFilterRef = ref(null);

const { proxy } = getCurrentInstance();

const state = reactive({
	menu: ['适应症', '医院', '科室', '筛选'],
	searchValue: '',
	historyList: [],
	HCPList: [],
	netStatus: 1,
	proStatus: 1,
	departmentStatus: 1,
	otherStatus: 1,
	topNum: '',
	productsList: [],
	hospitalList: [],
	departmentList: [],
	filterList: [],
	size: 10,
	page: 1,
	isLoading: false, // 列表的loading
	finished: false,
	products: [], // 产品筛选条件
	hospital: [], // 医院筛选条件
	doctorTitle: '', // 医生职称筛选条件
	departmentInfo: '', // 科室
	isFollow: false, // 是否是关注的医生
	administrativePosition: '', // 职务
	allClient: false, //查询全部客户,
	tabList: [
		{ title: '我的关注', name: '我的关注' },
		{ title: '我的客户', name: '我的客户' },
	],
	total: 0,
	pageStatus: 'loading', //加载中提示
	customerListingType: 'MY_CUSTOMER', //查询医院参数
});

// 内容分享
const shareDialog = ref(false);
const cusDetail = ref({});
const shareArticle = (val) => {
	proxy.$umeng('点击', '医生列表页', '内容分享');
	cusDetail.value = val;
	shareDialog.value = true;
};

const activeArticle = (item) => {
	getTrackId({ contentId: item.id, contentName: item.title }).then((res) => {
		// 跟踪id
		const trackId = res.result;
		const url = window.encodeURIComponent(item.contentSourceUrl);
		const option = {
			externalUserIds: [cusDetail.value.externalUserId],
			appid: enterStore.enterInfo.shareAppId || '',
			title: item.title,
			imgUrl: item.thumbMediaUrl || 'https://minioprod.pharmbrain.com/step/img/default.png',
			page: `pages/dashboard/dashboard.html?query=${url}&trackId=${trackId}&contentId=${item.id}&source=${userId.value}&sourceType=1`,
		};
		wxApi
			.shareToExternalContact(option)
			.then(() => {
				showToast({
					position: 'top',
					message: '分享成功',
				});
				// 记录分享成功
				recordShare(trackId);
			})
			.catch((res) => {
				showToast({
					position: 'top',
					message: res?.err_msg || '分享失败',
				});
			});
	});
};

// 文章详情
const articleTitle = ref('');
const articleDetailVis = ref(false);
const sourceUrl = ref('');
const articleDetail = (item) => {
	articleTitle.value = '';
	sourceUrl.value = item;
	articleDetailVis.value = true;
};

// 去拜访
const toVisitRecord = () => {
	proxy.$umeng('点击', '医生列表页', '拜访记录');
	router.push({
		name: 'randomNotes',
	});
};

const docAll = ref(0);

const toNewDoctor = () => {
	proxy.$umeng('点击', '医生列表页', '新建医生');
	router.push({
		name: 'newDoctor',
		query: {
			sourceType: 'mobilePhone',
		},
	});
};

// 所有医生
const getAllDoctor = async () => {
	if (active.value !== '我的关注') {
		const query = {
			page: 1,
			size: 10,
			isFocus: false,
		};
		if (active.value === '我的客户') {
			query.allClient = false;
		} else if (active.value === '全部客户') {
			query.allClient = true;
		} else {
			query.allClient = true;
			query.isRegionDoctor = true;
		}
		const res = await getDoctorListNew(query);
		docAll.value = res.result.total || 0;
	}
};

// 获取医生列表
const findDoctorTable = () => {
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async (resolve, reject) => {
		const query = {
			page: state.page,
			size: state.size,
			search: state.searchValue,
			doctorName: '',
			department: state.departmentInfo,
			isFocus: state.isFollow,
			allClient: state.allClient,
			hospital: state.hospital.join(','),
			indications: state.products.join(','),
			doctorTitle: state.doctorTitle,
			administrativePosition: state.administrativePosition,
			provinceCode: ssqList.value?.province?.map((ele) => ele.code).join(','),
			cityCode: ssqList.value?.city?.map((ele) => ele.code).join(','),
			countyCode: ssqList.value?.qx?.map((ele) => ele.code).join(','),
			developStatus: isProxy === 'true' || isProxy === true ? 'TO_BE_DEVELOPED' : 'SELF_DEVELOP',
		};
		if (active.value === '区域客户') {
			query.isRegionDoctor = true;
		}
		try {
			const res = await getDoctorListNew(query);
			const rows = res.result.doctors;
			state.isLoading = false;

			state.total = res.result.total;
			resolve(state.total);
			if (rows == null || rows.length === 0) {
				state.pageStatus = 'empty';
				state.finished = true;
				return;
			}
			state.HCPList = state.HCPList.concat(rows);
			state.pageStatus = 'normal';
			state.finished = false;
			if (state.HCPList.length >= state.total) {
				state.finished = true;
			}
		} catch (error) {
			state.isLoading = false;
			state.finished = true;
		}
	});
};

// 产品筛选确认
const productConfirm = (Json) => {
	proxy.$umeng('点击', `医生360`, `选择${JSON.stringify(Json)}`);
	// 滚动条回到顶部
	scrollToTop();
	resetQuery();
	if (Json.length > 0) {
		state.menu[0] = Json.toString();
		// 循环Json, 和state.productsList里的name比较,取出对应的code
		state.products = Json.map((ele) => {
			const item = state.productsList.find((item) => item.name === ele);
			return item.code;
		});
		// state.products = Json
	} else {
		state.products = [];
		state.menu[0] = '适应症';
	}
	findDoctorTable();
	dropRef.value.hideMask();
};

// 医院筛选确认
let hospitalIds = ref([]);
let ssqList = ref({});
const hosipitalConfirm1 = ({ hospitalName, ssq }) => {
	hospitalIds.value = hospitalName;
	ssqList.value = ssq;
	proxy.$umeng('点击', `医生360`, `选择${JSON.stringify(hospitalName)}`);
	// 滚动条回到顶部
	scrollToTop();
	resetQuery();
	if (hospitalName?.length > 0) {
		state.menu[1] = hospitalName.toString();
		state.hospital = hospitalName;
	} else {
		state.hospital = [];
		state.menu[1] = '医院';
	}
	findDoctorTable();
	dropRef.value.hideMask();
};

// 科室筛选确认
const confirmDepartment = (Json) => {
	proxy.$umeng('点击', `医生360`, `筛选医院-选择${Json.twoLevel}`);
	if (Json.twoLevel) {
		state.menu[2] = Json.twoLevel;
	} else {
		state.menu[2] = '科室';
	}
	// 滚动条回到顶部
	scrollToTop();
	resetQuery();
	state.departmentInfo = Json.twoLevel;
	findDoctorTable();
	dropRef.value.hideMask();
};

// 滚动条回到顶部
const scrollToTop = () => {
	const rnCustomer = document.getElementById('rn-customer');
	if (rnCustomer) {
		rnCustomer.scrollTop = 0;
	}
};

// 其他筛选确认
const confirmFilter = (Json) => {
	// 滚动条回到顶部
	scrollToTop();
	resetQuery();
	let doctorTitle1 = []; // 职称
	let docPosition = []; // 职务
	if (Json['医生职称'] && Json['医生职称'].length > 0) {
		doctorTitle1 = Json['医生职称'];
	} else {
		doctorTitle1 = [];
	}
	if (Json['行政职务'] && Json['行政职务'].length > 0) {
		docPosition = Json['行政职务'];
	} else {
		docPosition = [];
	}
	state.doctorTitle = doctorTitle1?.map((ele) => ele.value).toString();
	state.administrativePosition = docPosition?.map((ele) => ele.value).toString();

	const str = doctorTitle1
		?.map((ele) => ele.text)
		.concat(docPosition?.map((ele) => ele.text))
		.toString();
	if (str && str.length >= 8) {
		state.menu[3] = str.substr(0, 9);
	} else {
		state.menu[3] = '筛选';
	}
	findDoctorTable();
	dropRef.value.hideMask();
};
const closeCb = () => {
	productRef.value.fieRadio = false;
	filterRef.value.showRadio = false;
};

const triggerCb = (index) => {
	if (index === 0) {
		proxy.$umeng('点击', `医生360`, `适应症筛选`);
		getProductList();
	} else if (index === 1) {
		proxy.$umeng('点击', `医生360`, `医院筛选`);
		getHospitalList1();
	} else if (index === 2) {
		proxy.$umeng('点击', `医生360`, `科室筛选`);
		getDepartmentList1();
	} else {
		proxy.$umeng('点击', `医生360`, `其他筛选`);
		getFilterList();
	}
};

// 搜索重置 缓存历史数据
const searchHandler = async (val) => {
	state.searchValue = val;
	if (val) {
		if (localStorage.getItem('searchHistory')) {
			const arr = JSON.parse(localStorage.getItem('searchHistory'));
			arr.unshift(val);
			localStorage.setItem('searchHistory', JSON.stringify(Array.from(new Set(arr)).slice(0, 10)));
		} else {
			const arr = [];
			arr.unshift(val);
			localStorage.setItem('searchHistory', JSON.stringify(arr));
		}
	}
	state.historyList = JSON.parse(localStorage.getItem('searchHistory'));
	resetQuery();
	await findDoctorTable();
	proxy.$umeng('搜索', `医生360`, val);
};

const resetHandler = () => {
	proxy.$umeng('点击', `医生360`, `重置`);
	state.pageStatus = 'loading';
	state.HCPList = [];
	state.page = 1;
	state.searchValue = '';
	findDoctorTable();
};
const clickHistory = (val) => {
	proxy.$umeng('点击', `医生360`, `搜索记录-${val}`);
	resetQuery();
	state.searchValue = val;
	findDoctorTable();
};

const getProductList = () => {
	const shaixuan1 = shaixuan.value?.clientHeight;
	state.topNum = scrollTop('contentPage') + shaixuan1 + 'px';
	productRef.value.fieRadio = true;
	state.proStatus = 1; // 加载中
	findFieldList().then((res) => {
		const arr = res.result;
		if (res.result.length === 0) {
			state.proStatus = 0;
		} else {
			state.productsList = arr;
			state.proStatus = 2; // 加载中
		}
		// 循环state.products, 和state.productsList里的code比较,取出对应的name
		productRef.value.activeName = state.products.map((ele) => {
			const item = state.productsList.find((item) => item.code === ele);
			return item.name;
		});
		// productRef.value.activeName = state.products
	});
};

const getHospitalList1 = () => {
	dropRef.value.hideMask();
	hospitalFilterRef.value.showBottom = true;
};

const getDepartmentList1 = () => {
	dropRef.value.hideMask();
	departmentFilterRef.value.showBottom = true;
};

const getFilterList = async () => {
	const shaixuan1 = shaixuan.value?.clientHeight;
	state.topNum = scrollTop('contentPage') + shaixuan1 + 'px';
	filterRef.value.showRadio = true;
	if (state.filterList.length > 0) return;
	state.otherStatus = 1; // 加载中
	let { result: result1 } = await getProfessionalTitle(qyId.value);
	result1.forEach((ele) => (ele.text = ele.label));
	let { result: result2 } = await getAdministrativeTitle();
	result2.forEach((ele) => (ele.text = ele.label));

	state.filterList = [
		{
			text: '医生职称',
			children: [...result1],
		},
		{
			text: '行政职务',
			children: [...result2],
		},
	];
	state.otherStatus = 2; // 加载中
};

const hospitalFilterShow = ref(true);
const changeFn = (val) => {
	proxy.$umeng('点击', `医生360`, `${val.title}`);
	sessionStorage.tabName = val.title;
	state.isFollow = val.title === '我的关注';
	state.allClient = val.title === '我的关注' || val.title === '全部客户' || val.title === '区域客户';
	state.customerListingType = val.title === '我的关注' ? 'MY_FOCUS' : val.title === '我的客户' ? 'MY_CUSTOMER' : active.value === '区域客户' ? 'REGION_CUSTOMER' : 'ALL_CUSTOMER';
	closeCb();
	dropRef.value.hideMask();
	state.hospital = [];
	state.menu[1] = '医院';
	hospitalFilterShow.value = false;
	nextTick(() => {
		hospitalFilterShow.value = true;
	});
	hospitalIds.value = [];
	getAllDoctor();
	resetQuery();
	findDoctorTable();
};
const onLoad = () => {
	state.page++;
	findDoctorTable();
};

const resetQuery = () => {
	state.pageStatus = 'loading';
	state.HCPList = [];
	state.page = 1;
};
const goTag = () => {
	proxy.$umeng('点击', `医生360`, `标签列表`);
	router.push({ name: 'labelList' });
};
const vantList = ref(null);
const cacheScroll = ref();
const toView = (data) => {
	proxy.$umeng('点击', `医生360`, `${data.doctorName}详情`);
	cacheScroll.value = vantList.value.$el.scrollTop;
	router.push({
		name: 'doctorInfo',
		query: {
			doctorId: data.doctorId,
		},
	});
};

onActivated(() => {
	vantList.value && cacheScroll.value ? (vantList.value.$el.scrollTop = cacheScroll.value) : '';
});
const changeFocus = (val, val1) => {
	if (val) {
		proxy.$umeng('点击', `医生360`, `关注${val1.doctorName}`);
	} else {
		proxy.$umeng('点击', `医生360`, `取消关注${val1.doctorName}`);
	}
	const num = val ? 1 : 0;
	const data = {
		type: 1,
		businessId: val1.doctorId,
	};
	focusDoctor(data).then((res) => {
		// status.value = !status.value
		if (res.result.focusStatus) {
			showToast({
				position: 'top',
				message: '已关注',
			});
			val1.isFocus = true;
		} else {
			showToast({
				position: 'top',
				message: '已取消关注',
			});
			val1.isFocus = false;
		}
	});
};

// 注入企业微信sdk
const setWxworkSdk = async () => {
	if (isWeChat() === '企业微信') {
		try {
			// 1.调接口获取config认证信息
			let res = await wechatCrop({ url: window.location.href.split('#')[0] });
			// 2.调用企微config认证
			await wxApi.wxRegister(res.result);
			// 3.调接口获取agent-config信息
			let res1 = await wechatAgent({ url: window.location.href.split('#')[0] });
			// 4.调用企微agent-config
			await wxApi.registerAgentConfig(res1.result);
		} catch (err) {
			console.error('Error setting wxwork SDK:', err);
			showToast({
				position: 'top',
				message: err?.response?.data?.message || '企业微信sdk注入失败',
			});
		}
	}
};

onMounted(async () => {
	state.tabList = [
		{ title: '我的关注', name: '我的关注' },
		{ title: '我的客户', name: '我的客户' },
	];
	if (regionalDoctors.value) {
		state.tabList.push({ title: '区域客户', name: '区域客户' });
	}
	if (allDoctors.value) {
		state.tabList.push({ title: '全部客户', name: '全部客户' });
	}
	await nextTick();
	active.value = sessionStorage.tabName || '我的客户';
	state.isFollow = active.value === '我的关注';
	state.allClient = active.value === '我的关注' || active.value === '全部客户' || active.value === '区域客户';
	state.customerListingType = active.value === '我的关注' ? 'MY_FOCUS' : active.value === '我的客户' ? 'MY_CUSTOMER' : active.value === '区域客户' ? 'REGION_CUSTOMER' : 'ALL_CUSTOMER';

	await nextTick();
	state.menu[1] = '医院';
	hospitalFilterShow.value = false;
	hospitalIds.value = [];
	nextTick(() => {
		hospitalFilterShow.value = true;
	});
	// 获取所有医生
	getAllDoctor();
	findDoctorTable();
	if (localStorage.getItem('searchHistory')) {
		state.historyList = JSON.parse(localStorage.getItem('searchHistory'));
	}

	// 注入企业微信sdk
	setWxworkSdk();
});
</script>

<style lang="scss" scoped>
.rn {
	display: flex;
	flex-direction: column;
	height: calc(100vh - 75px);
	overflow-y: auto;
	.mc-content {
		position: sticky;
		top: 0;
		z-index: 55;
	}
	.mc-content-middle__app {
		background-color: #ffffff;
		padding: 7px;
		border-bottom: 1px solid rgb(223, 225, 230);
		font-size: 16px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: var(--var-default-color);
		font-weight: bold;
		position: relative;

		// 拜访记录
		.mc-content-middle__app__text {
			position: absolute;
			right: 9px;
			top: 9px;
			z-index: 1;
			color: #0052cc;
			font-size: 14px;
			font-weight: normal;
		}

		.app-header {
			display: flex;
			align-items: center;

			.app-header__img {
				width: 16px;
				height: 16px;
				margin-right: 5.4px;
			}

			.app-header__name {
				font-weight: 500;
			}
		}

		.app-bottom {
			display: flex;
			align-items: flex-start;
			justify-content: flex-start;
			margin-top: 8px;

			.app-bottom__item {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 20%;
				.item-top {
					width: 40px;
					height: 40px;
					border-radius: 6px;
					background-color: #f4f5f7;
					display: flex;
					align-items: center;
					justify-content: center;

					img {
						width: 20px;
						height: 20px;
						object-fit: cover;
					}

					.item-top__img {
						width: 28px;
						height: 28px;
					}
				}

				.item-text {
					font-size: 12px;
					line-height: 1.2;
					margin-top: 8px;
					text-align: center;
				}
			}
		}

		.app-bottom--show {
			display: none;
		}
	}

	.search_text {
		padding: 0px 14px;
		span {
			color: #a0a2a2;
			font-size: 12px;
			padding-right: 8px;
		}
	}

	.tag {
		padding: 8px 14px;
		height: 40px;
		display: flex;
		align-items: center;
		background-color: #ffffff;
		img {
			height: 18px;
			width: 18px;
		}
		span {
			display: inline-block;
			margin-left: 12px;
			color: #172b4d;
		}
	}

	.doc-total-tips {
		padding-left: 12px;
		font-size: 12px;
		height: 24px;
		line-height: 24px;
		background-color: #ffffff;
	}

	.rn-content {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.no-data {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #a0a2a2;
		img {
			width: 300px;
		}
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
}

::v-deep(.van-tabs__nav--line) {
	padding-bottom: 0;
	.van-tab {
		color: #172b4d;
		font-size: 15px;
	}

	.van-tab--active {
		color: #0052cc;
	}

	.van-tabs__line {
		background-color: #0052cc;
	}
}

::v-deep(.van-tabs__line) {
	position: absolute;
	bottom: 0px;
}

::v-deep(.van-tabs__wrap) {
	height: 40px;
}
::v-deep(.van-tabs) {
	border-bottom: 1px solid #dfe1e6;
}
</style>
