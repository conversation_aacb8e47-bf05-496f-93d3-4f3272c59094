<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">
				<span>标签列表</span>
			</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>

		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 搜索 -->
			<Search :text="'输入标签'" @changeFn="changeFn" @reset="resetFn"></Search>
			<div class="mc-content-cotainer">
				<p class="mc-content-cotainer__title">系统标签：</p>
				<van-loading style="text-align: center; padding-bottom: 12px" color="#0052cc" v-show="loading">加载中...</van-loading>
				<van-collapse v-model="activeNames">
					<template v-for="item in systemLabel" :key="item.id">
						<van-collapse-item title-class="titleClass" v-if="item.groupExplain" :title="item.groupName" :value="item.customerNumber" :name="item.id">
							<template #title>
								<svg-icon icon-class="bqts" class="icons" @click.stop="tipFn(item.groupExplain)"></svg-icon>
								{{ item.groupName }}
							</template>
							<van-cell v-for="(it, t) in item.tagTermList" :key="t" :value="it.customerNumber" is-link @click="goEditLabel(it)">
								<template #title>
									<span>{{ it.termValue }}</span>
								</template>
							</van-cell>
						</van-collapse-item>
						<van-collapse-item title-class="titleClass" v-else :title="item.groupName" :value="item.customerNumber" :name="item.id">
							<van-cell v-for="(it, t) in item.tagTermList" :key="t" :value="it.customerNumber" is-link @click="goEditLabel(it)">
								<template #title>
									<span>{{ it.termValue }}</span>
								</template>
							</van-cell>
						</van-collapse-item>
					</template>
				</van-collapse>

				<div class="personal">
					<p style="flex: 1" class="mc-content-cotainer__title">个人标签：</p>
					<van-button color="#0052cc" size="mini" icon="plus" type="primary" @click="addTag">新建</van-button>
				</div>
				<template v-for="item in personalList" :key="item.id">
					<van-cell title-class="titleClass" :title="item.termValue" :value="item.customerNumber" @click="goEditLabel(item)"> {{ item.customerNumber }}<van-icon name="arrow" /> </van-cell>
				</template>
			</div>
		</div>
	</div>
</template>
<script setup>
import Search from './components/docSearch.vue';
import { personalTag, getTagList } from '@/api/doctor';
import { ref, onMounted, getCurrentInstance, reactive } from 'vue';
import { showConfirmDialog } from 'vant';
const goBack = () => {
	router.go(-1);
};
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const search = ref('');
const systemLabel = ref(''); //系统标签
const personalList = ref([]); // 个人标签
const activeNames = ref([]);
const loading = ref(false);
function goEditLabel(val) {
	proxy.$umeng('点击', `医生360-标签列表`, `修改标签${val.termValue}`);
	router.push({ path: 'doctorTag', query: { id: val.id, type: val.type } });
}
function changeFn(text) {
	proxy.$umeng('搜索', `医生360-标签列表`, text);
	search.value = text;
	personalFn();
	if (text !== '') {
		systemFn().then((list) => {
			list.forEach((item) => {
				activeNames.value.push(item.id);
			});
		});
	} else {
		systemFn().then((list) => {
			activeNames.value = [];
		});
	}
}
function resetFn() {
	proxy.$umeng('点击', `医生360-标签列表`, '重置');
	search.value = '';
	personalFn();
	systemFn();
	activeNames.value = [];
}
function tipFn(val) {
	proxy.$umeng('点击', `医生360-标签列表`, `说明${val}`);
	showConfirmDialog({
		// title: '标题',
		showCancelButton: false,
		message: val,
	}).then(() => {
		// on confirm
	});
}
const personalFn = () => {
	const query = {
		keyWord: search.value,
	};
	personalTag(query)
		.then((res) => {
			personalList.value = res.result;
		})
		.catch((err) => {
			console.log(err);
		});
};
const systemFn = () => {
	loading.value = true;
	return new Promise((resolve, reject) => {
		const query = {
			tagType: 'CUSTOMER',
			keyWord: search.value,
			isEdit: true,
		};
		getTagList(query)
			.then((res) => {
				systemLabel.value = res.result || [];
				loading.value = false;
			})
			.catch((err) => {
				console.log(err);
			});
		resolve(systemLabel.value);
	});
};
const addTag = () => {
	proxy.$umeng('点击', `医生360-标签列表`, `新建个人标签`);
	router.push({ name: 'addPersonalTag' });
};
onMounted(() => {
	personalFn();
	systemFn();
});
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	color: #172b4d;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		background-color: #ffffff;
		border-bottom: 1px solid #dfe1e6;
		.mc-header-icon {
			width: 30px;
			height: 30px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			span {
				font-size: 16px;
				color: #172b4d;
				font-weight: 600;
			}
		}
	}
	.mc-content {
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-cotainer {
			background-color: #f4f5f7;
			.mc-content-cotainer__title {
				color: #172b4d;
				margin: 10px 20px;
				font-size: 15px;
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 45px);
	}

	.mc-content__app {
		height: calc(100vh - 45px);
	}
}

::v-deep .van-collapse-item__content {
	padding: 3px 12px;
	font-size: 12px;
	color: #172b4d;
}

.personal {
	display: flex;
	align-items: center;
	margin-right: 20px;
}

::v-deep .van-cell__title {
	padding-left: 15px;
	display: flex;
}

.svg-icon {
	width: 0.8em;
	height: 0.8em;
}

.icons {
	margin-right: 6px;
}

::v-deep .van-collapse-item__content {
	font-size: 14px;
	box-shadow: 0px 0px 2px 0px rgb(169 193 242);

	.van-cell {
		box-shadow: none;
		padding: 5px 10px;
		font-size: 12px;
		color: #172b4d;

		.van-icon {
			height: 25px;
			line-height: 25px;
			font-size: 12px;
		}
	}
}

.van-collapse-item {
	:deep(.van-badge__wrapper) {
		display: flex;
		justify-content: center;
		align-items: center;
	}
}
::v-deep .titleClass {
	display: flex;
	align-items: center;
	color: #172b4d;
}
</style>
