<template>
	<div class="new-doctor">
		<!-- 顶部内容 -->
		<div class="new-doctor-content">
			<van-form label-width="8em" ref="formRef">
				<div class="new-doctor-title">
					<div class="new-doctor-title-block"></div>
					<div class="new-doctor-title-text">基本信息</div>
				</div>
				<div class="doctor-basic-info">
					<!-- 医生姓名 -->
					<van-field class="reset-field" :required="true" v-model="formData.name" name="姓名" label="医生姓名：" placeholder="请输入医生姓名" label-align="top" :rules="[{ required: true, message: '请输入医生姓名' }]" />
					<!-- 性别 -->
					<van-field name="radio" label-align="top" label="性别：">
						<template #input>
							<van-radio-group shape="dot" checked-color="#0052cc" v-model="formData.sex" direction="horizontal">
								<van-radio name="男">男</van-radio>
								<van-radio name="女">女</van-radio>
							</van-radio-group>
						</template>
					</van-field>
					<!-- 主要任职医院 -->
					<van-field class="reset-field" :required="true" v-model="formData.hospitalName" name="主要任职医院" label="主要任职医院：" placeholder="请选择任职医院" label-align="top" readonly right-icon="arrow-down" :rules="[{ required: true, message: '请选择任职医院' }]" @click="selectHospital(-1)" />
					<!-- 标准科室 -->
					<van-field class="reset-field" :required="true" v-model="formData.department" name="标准科室" label="标准科室" placeholder="请选择标准科室" label-align="top" readonly right-icon="arrow-down" :rules="[{ required: true, message: '请选择标准科室' }]" @click="selectDepartment(-1)" />
					<!-- 职称 -->
					<van-field class="reset-field" :required="true" v-model="formData.professionalTitle" name="职称" label="职称" placeholder="请选择职称" label-align="top" readonly right-icon="arrow-down" :rules="[{ required: true, message: '请选择职称' }]" @click="selectTitle(-1)" />
					<!-- 职务 -->
					<van-field class="reset-field" :required="true" v-model="formData.administrativeTitle" name="职务" label="职务" placeholder="请选择职务" label-align="top" readonly right-icon="arrow-down" :rules="[{ required: true, message: '请选择职务' }]" @click="xzzc.showPicker = true" />
					<!-- 多点执业 -->
					<van-field name="radio" :required="true" :rules="[{ required: true, message: '请选择是否为多点执业' }]" label-align="top" label="是否为多点执业：">
						<template #input>
							<van-radio-group shape="dot" checked-color="#0052cc" v-model="formData.multiPointPractice" @change="jobChange" direction="horizontal">
								<van-radio name="是">是</van-radio>
								<van-radio name="否">否</van-radio>
							</van-radio-group>
						</template>
					</van-field>
					<!-- 多点执业信息 -->
					<div v-if="formData.multiPointPractice === '是'" class="job-info">
						<van-field name="jobInfo" :required="true" label="多点执业信息："> </van-field>
						<!-- 添加按钮 -->
						<div @click="addJobInfo" class="job-info-add">
							<img src="@/assets/img/add_job.png" />
							<span>添加</span>
						</div>
					</div>
					<!-- 执业信息 -->
					<template v-if="formData.multiPointPractice === '是'">
						<div v-for="(ite, index) in formData.multiPointPracticeList" :key="ite.id" class="job-info-list">
							<!-- title -->
							<div class="job-info-list-title">
								<span>分院-多点执业{{ index + 1 }}</span>
							</div>
							<!-- 删除 -->
							<img v-if="formData.multiPointPracticeList.length > 1" class="job-info-list-delete" src="@/assets/img/delete_job.png" alt="" @click="delJobInfo(ite.id)" />

							<!-- 医院名称 -->
							<van-field
								class="reset-field"
								:required="true"
								v-model="formData.multiPointPracticeList[index].hospital"
								name="医院名称"
								label="医院名称"
								placeholder="请选择医院"
								label-align="top"
								readonly
								right-icon="arrow-down"
								:rules="[{ required: true, message: '请选择医院' }]"
								@click="selectHospital(index)"
							/>

							<!-- 科室 -->
							<van-field
								class="reset-field"
								:required="true"
								v-model="formData.multiPointPracticeList[index].department"
								name="科室"
								label="科室"
								placeholder="请选择科室"
								label-align="top"
								readonly
								right-icon="arrow-down"
								:rules="[{ required: true, message: '请选择科室' }]"
								@click="selectDepartment(index)"
							/>

							<!-- 职称 -->
							<van-field
								class="reset-field"
								:required="true"
								v-model="formData.multiPointPracticeList[index].title"
								name="职称"
								label="职称"
								placeholder="请选择职称"
								label-align="top"
								readonly
								right-icon="arrow-down"
								:rules="[{ required: true, message: '请选择职称' }]"
								@click="selectTitle(index)"
							/>
						</div>
					</template>
				</div>
				<div class="new-doctor-title">
					<div class="new-doctor-title-block"></div>
					<div class="new-doctor-title-text">证件信息</div>
				</div>
				<!-- 证件信息 -->
				<div class="new-doctor-annex">
					<div class="new-doctor-annex-title">
						<span>医生有效支持件</span>
						<van-uploader multiple :before-read="beforeRead" :after-read="afterRead">
							<span class="new-doctor-annex-title-upload">添加附件</span>
						</van-uploader>
					</div>
					<div class="new-doctor-annex-tips">
						<img src="@/assets/img/info.png" alt="" />
						<span>请上传 医生职业资格证截图/医院照片墙/医院医生排班表/医生工牌或胸卡/医生名片/医院医生的挂号单中的至少一项，且文件不能大于4M。</span>
					</div>

					<div class="new-doctor-annex-list">
						<div class="new-doctor-annex-list-item" v-for="(item, index) in formData.imagesUrl" :key="index" @click="previewImg(item)">
							<div class="new-doctor-annex-list-item-img">
								<img :src="item.url" alt="" />
							</div>
							<div class="new-doctor-annex-list-item-info">
								<div class="name">{{ item.name }}</div>
								<div class="size">{{ item.size }}</div>
							</div>
							<img src="@/assets/img/close.png" alt="" class="new-doctor-annex-list-item-close" @click.stop="deleteImg(item, index)" />
						</div>
					</div>
				</div>
				<div v-if="$route.fullPath.includes('doctorId')" class="new-doctor-title">
					<div class="new-doctor-title-block"></div>
					<div class="new-doctor-title-text">医生有效信息</div>
				</div>
				<div v-if="$route.fullPath.includes('doctorId')" class="new-doctor-form">
					<van-field v-model="formData.effectiveness" name="医生有效性" label="医生有效性" readonly placeholder="请选择" @click="ysyxx.showPicker = true">
						<template #extra>
							<span class="link"></span>
						</template>
					</van-field>
					<van-field v-if="formData.effectiveness === '无效'" class="required" v-model="formData.invalidReason" name="无效原因" label="无效原因" readonly placeholder="请选择" :required="true" :rules="[{ required: true, message: '请选择无效原因' }]" @click="wxyy.showPicker = true">
						<template #extra>
							<span class="link"></span>
						</template>
					</van-field>
					<van-field v-if="formData.effectiveness === '无效'" class="remarks" v-model="formData.invalidReasonRemarks" placeholder="请输入无效原因备注" type="textarea" rows="4" autosize> </van-field>
				</div>
			</van-form>
		</div>
		<!-- 底部按钮组 -->
		<div class="new-doctor-bottom">
			<div v-if="isCancelBtn" @click="goBack">取消</div>
			<div :class="isCancelBtn === false ? 'new-doctor-bottom-btn' : ''" @click="confirm">确定</div>
		</div>

		<!-- 医院 -->
		<!-- 选择医院 -->
		<van-popup v-model:show="showBottom" position="center" :style="{ height: '100%', width: '100%', maxWidth: '100%' }">
			<s-hospital @cancel="showBottom = false" @confirm="confirmHospital"></s-hospital>
		</van-popup>

		<!-- 挂牌科室 -->
		<selector ref="gpks" title="科室" :columns="selectOption.ksList" @selectConfirm="ksConfirm" type="max"></selector>

		<!-- 专业职称 -->
		<selector ref="zyzc" title="职称" :columns="selectOption.zyzcList" :defaultValue="[formData.professionalTitleCode]" @selectConfirm="zyzcConfirm"></selector>

		<!-- 行政职称 -->
		<selector ref="xzzc" title="职务" :columns="selectOption.xzzcList" :defaultValue="[formData.administrativeTitleCode]" @selectConfirm="xzzcConfirm"></selector>

		<!-- 医生有效行 -->
		<selector ref="ysyxx" title="医生有效性" :columns="selectOption.ysyxxList" :defaultValue="[formData.effectiveness]" @selectConfirm="ysyxxConfirm"></selector>

		<!-- 无效原因 -->
		<selector ref="wxyy" title="无效原因" :columns="selectOption.wxyyList" :defaultValue="[formData.invalidReason]" @selectConfirm="wxyyConfirm"></selector>
	</div>
</template>
<script setup>
import { uuid, fileDate } from '@/utils/index';
import { showFailToast, showImagePreview, showLoadingToast, closeToast, showSuccessToast, showDialog } from 'vant';
import { uploadDynamicImg, getProfessionalTitle, getAdministrativeTitle, getDepartment, addDoctor, updataDoctor } from '@/api/doctor';
import { getDoctorDetail } from '@/api/randomNotes';
import selector from './components/selector.vue';
import sHospital from './components/selectHospital.vue';
const { proxy } = getCurrentInstance();
import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
const qyId = ref(userStore.userInfo.enterpriseVO.name);
const userId = userStore.userInfo.username;
const route = useRoute();
const router = useRouter();
const isCancelBtn = ref(true);

//ref
const xb = ref(null);
const zyzc = ref(null);
const xzzc = ref(null);
const ysyxx = ref(null);
const wxyy = ref(null);
const gpks = ref(null);
const formRef = ref(null);

// 表单数据
const formData = reactive({
	name: '',
	sex: '',
	professionalTitle: '',
	professionalTitleCode: '',
	administrativeTitle: '',
	administrativeTitleCode: '',
	hospitalName: '',
	hospitalId: '',
	department: '',
	effectiveness: '',
	invalidReason: '',
	invalidReasonRemarks: '',
	images: [],
	imagesUrl: [],
	firstDepartment: '',
	secondDepartment: '',
	defaultDepartment: '',
	multiPointPractice: '',
	multiPointPracticeList: [],
});

// 存储原始的多点执业数据
const originalMultiPointPracticeList = ref([]);

const clearForm = () => {
	formData.name = '';
	formData.sex = '';
	formData.professionalTitle = '';
	formData.professionalTitleCode = '';
	formData.administrativeTitle = '';
	formData.administrativeTitleCode = '';
	formData.hospitalName = '';
	formData.hospitalId = '';
	formData.department = '';
	formData.effectiveness = '';
	formData.invalidReason = '';
	formData.invalidReasonRemarks = '';
	formData.images = [];
	formData.imagesUrl = [];
	formData.firstDepartment = '';
	formData.secondDepartment = '';
	formData.defaultDepartment = '';
	formData.multiPointPractice = '';
	formData.multiPointPracticeList = [];
};

// 已选中医院信息
const confirmHospital = (val) => {
	if (whichHospital.value === -1) {
		formData.hospitalId = val.hospitalId;
		formData.hospitalName = val.hospitalName;
	} else {
		formData.multiPointPracticeList[whichHospital.value].hospitalId = val.hospitalId;
		formData.multiPointPracticeList[whichHospital.value].hospital = val.hospitalName;
	}

	showBottom.value = false;
};

//筛选项列表
const selectOption = reactive({
	sexList: [
		{ text: '男', value: '男' },
		{ text: '女', value: '女' },
	],
	zyzcList: [],
	xzzcList: [],
	ksList: [],
	ysyxxList: [
		{ text: '有效', value: '有效' },
		{ text: '无效', value: '无效' },
	],
	wxyyList: [
		{ text: '实习期满', value: '实习期满' },
		{ text: '进修期满', value: '进修期满' },
		{ text: '已离职', value: '已离职' },
		{ text: '已退休', value: '已退休' },
		{ text: '已去世', value: '已去世' },
		{ text: '其他', value: '其他' },
	],
});

const sexConfirm = (value) => {
	//性别确认
	formData.sex = value.value;
	proxy.$umeng('筛选', '新建医生页面', value);
};
//专业职称确认
const zyzcConfirm = (value) => {
	if (whichTitle.value === -1) {
		formData.professionalTitle = value.text;
		formData.professionalTitleCode = value.value;
	} else {
		formData.multiPointPracticeList[whichTitle.value].title = value.text;
	}

	proxy.$umeng('筛选', '新建医生页面', value.text);
};
//行政职称确认
const xzzcConfirm = (value) => {
	formData.administrativeTitle = value.text;
	formData.administrativeTitleCode = value.value;
	proxy.$umeng('筛选', '新建医生页面', value.text);
};
//科室确认
const ksConfirm = (value) => {
	if (whichDepartment.value === -1) {
		formData.defaultDepartment = [{ value: value[0] }, { value: value[1] }];
		formData.department = `${value[0]}/${value[1]}`;
		formData.firstDepartment = value[0];
		formData.secondDepartment = value[1];
	} else {
		formData.multiPointPracticeList[whichDepartment.value].department = `${value[0]}/${value[1]}`;
	}
};
//医生有效性确认
const ysyxxConfirm = (value) => {
	formData.effectiveness = value.value;
	proxy.$umeng('筛选', '新建医生页面', value);
};
//无效原因确认
const wxyyConfirm = (value) => {
	formData.invalidReason = value.value;
	proxy.$umeng('筛选', '新建医生页面', value);
};

const beforeRead = (file) => {
	if (Array.isArray(file)) {
		for (let item of file) {
			//上传前限制
			const isImage = item.type.startsWith('image');
			const isSizeValid = item.size <= 4 * 1024 * 1024; // 限制文件大小为4MB
			if (!isImage) {
				showFailToast('只能上传图片文件');
			} else if (!isSizeValid) {
				showFailToast('上传文件大小不能超过4MB');
			}
			return isImage && isSizeValid;
		}
	} else {
		//上传前限制
		const isImage = file.type.startsWith('image');
		const isSizeValid = file.size <= 4 * 1024 * 1024; // 限制文件大小为4MB
		if (!isImage) {
			showFailToast('只能上传图片文件');
		} else if (!isSizeValid) {
			showFailToast('上传文件大小不能超过4MB');
		}
		return isImage && isSizeValid;
	}
};
const afterRead = async (file) => {
	showLoadingToast({
		message: '上传中...',
		forbidClick: true,
		duration: 0,
		loadingType: 'spinner',
	});
	if (Array.isArray(file)) {
		file.forEach((ele) => {
			ele.file.sizeLength = (ele.file.size / 1024).toFixed(2) + 'KB';
			formData.images.push(ele);
		});
	} else {
		//本地上传成功
		file.file.sizeLength = (file.file.size / 1024).toFixed(2) + 'KB';
		formData.images.push(file);
	}
	try {
		const uploadData = new FormData();
		uploadData.append('path', `saas/${userId}/${fileDate()}`);
		if (Array.isArray(file)) {
			for (let i = 0; i < file.length; i++) {
				const beforeFile = file[i].file;
				console.log(beforeFile);
				uploadData.append('files', beforeFile);
			}
		} else {
			uploadData.append('files', file.file);
		}

		const res = await uploadDynamicImg(uploadData, 'front');
		formData.imagesUrl = formData.imagesUrl.concat(res.result);
		formData.images.forEach((item, index) => {
			if (item.file) {
				const { name, sizeLength } = item.file;
				formData.imagesUrl[index] = {
					name,
					size: sizeLength,
					url: formData.imagesUrl[index].url ? formData.imagesUrl[index].url : formData.imagesUrl[index],
				};
			}
		});
		showSuccessToast('上传成功');
	} catch (error) {
		console.log(error);
		showFailToast({
			message: '上传失败',
		});
	} finally {
		closeToast();
	}
};
const deleteImg = (file, index) => {
	formData.images.splice(index, 1);
	formData.imagesUrl.splice(index, 1);
};
//图片预览
const previewImg = (item) => {
	showImagePreview({
		images: [item.url],
		showIndex: false,
		overlayClass: 'preview-overlay',
		className: 'preview-img',
	});
};

// 整合查询query
const createQuery = (type) => {
	// 通过原始的多点执业数据和当前的多点执业数据进行对比，更新verification_status
	for (const item of formData.multiPointPracticeList) {
		const originalItem = originalMultiPointPracticeList.value.find((ele) => ele.doctorIdMulti === item.doctorIdMulti);
		if (originalItem) {
			if (originalItem.verification_status === '1') {
				// 进一步校验职业信息是否改变了
				if (item.hospital !== originalItem.hospital || item.department !== originalItem.department || item.title !== originalItem.title) {
					item.verification_status = '0'; // 职业信息改变了，设置为0
				} else {
					item.verification_status = '1'; // 职业信息没有改变，保持为1
				}
			} else {
				item.verification_status = '0'; // 原始数据的verification_status为0，设置为0
			}
		} else {
			item.verification_status = '0'; // 新增的执业信息，默认设置为0
		}
	}

	let addQuery = {
		doctorName: formData.name,
		doctorSex: formData.sex,
		doctorTitle: formData.professionalTitle,
		doctorTitleCode: formData.professionalTitleCode,
		administrativePosition: formData.administrativeTitle,
		administrativePositionCode: formData.administrativeTitleCode,
		hospitalId: formData.hospitalId,
		firstDepartment: formData.firstDepartment,
		department: formData.secondDepartment,
		annexUrl: JSON.stringify(formData.imagesUrl),
		doctorType: 'HCP',
		servingHospital: formData.multiPointPracticeList.length > 0 ? JSON.stringify(formData.multiPointPracticeList) : JSON.stringify([]),
	};
	let updataQuery = {
		ownerId: route.query.doctorId,
		ownerType: 'DOCTOR',
		hospital: formData.hospitalName,
		validity: formData.effectiveness === '有效' ? true : false,
		invalidReason: formData.invalidReason,
		invalidRemarks: formData.invalidReasonRemarks,
	};
	let query = {};
	query = type === 'add' ? addQuery : { ...addQuery, ...updataQuery };
	return query;
};
const addDoctorFn = () => {
	let query = createQuery('add');
	return addDoctor(query);
};
const updataDoctorFn = () => {
	let query = createQuery();
	return updataDoctor(query);
};

const goBack = () => {
	history.go(-1);
};

const confirm = async () => {
	try {
		await formRef.value.validate();
		if (formData.imagesUrl.length === 0) {
			showFailToast('请上传医生有效支持件');
			return;
		}
		let message = route.query.doctorId ? '更新中...' : '新建中...';
		showLoadingToast({
			message,
			forbidClick: true,
			duration: 0,
		});
		try {
			if (route.query.doctorId) {
				await updataDoctorFn();
				proxy.$umeng('点击', '档案更新页面', '更新成功');
				if (route.query.sourceType === 'mobilePhone') showSuccessToast('提交成功，请等待验证。');
			} else {
				await addDoctorFn();
				proxy.$umeng('点击', '新建医生页面', '新建成功');
				if (route.query.sourceType === 'mobilePhone') showSuccessToast('新建成功');
			}
			// 判断是否在外部联系人打开
			if (route.query.sourceType === 'mobilePhone') {
				// 清空表单信息
				formData.name = '';
				history.go(-1);
			} else {
				// 清空表单信息 并重置表单验证
				closeToast();
				clearForm();
				formRef.value.resetValidation();
				showDialog({
					title: '提示',
					message: '新建医生成功',
				}).then(() => {});
			}
		} catch (error) {
			console.log(error);
		}
	} catch (error) {
		console.error(error);
		showFailToast('请完整填写表单');
	}
};
//选择医院
const showBottom = ref(false);
const whichHospital = ref(null);
const selectHospital = (num) => {
	whichHospital.value = num;
	showBottom.value = true;
};
const whichDepartment = ref(null);
const selectDepartment = (num) => {
	whichDepartment.value = num;
	gpks.value.showPicker = true;
};
const whichTitle = ref(null);
const selectTitle = (num) => {
	whichTitle.value = num;
	zyzc.value.showPicker = true;
};
//获取专业职称
const getZyzc = () => {
	return getProfessionalTitle(qyId.value);
};
//获取行政职称
const getXzzc = () => {
	return getAdministrativeTitle();
};
//获取科室
const getKs = () => {
	return getDepartment();
};

onMounted(async () => {
	if (route.query.sourceType !== 'mobilePhone') {
		isCancelBtn.value = false;
	}
	if (route.query.doctorId) {
		const res = await getDoctorDetail(route.query.doctorId);
		const doctorInfo = res.result[0];
		formData.name = doctorInfo.doctorName;
		formData.sex = doctorInfo.doctorSex;
		formData.professionalTitle = doctorInfo.doctorTitle;
		formData.professionalTitleCode = doctorInfo.doctorTitleCode;
		formData.administrativeTitle = doctorInfo.administrativePosition;
		formData.administrativeTitleCode = doctorInfo.administrativePositionCode;
		formData.hospitalName = doctorInfo.hospital || '';
		formData.hospitalId = doctorInfo.hospitalId || '';
		formData.firstDepartment = doctorInfo.firstDepartment || '';
		formData.secondDepartment = doctorInfo.department || '';
		formData.department = `${formData.firstDepartment}/${formData.secondDepartment}`;
		formData.effectiveness = doctorInfo.validity;
		formData.invalidReason = doctorInfo.invalidReason;
		formData.invalidReasonRemarks = doctorInfo.invalidRemarks;
		formData.images = doctorInfo.annexUrl ? JSON.parse(doctorInfo.annexUrl) || [] : [];
		formData.imagesUrl = doctorInfo.annexUrl ? JSON.parse(doctorInfo.annexUrl) || [] : [];
		if (doctorInfo.servingHospital && doctorInfo.servingHospital.length > 0) {
			formData.multiPointPractice = '是';
			nextTick(() => {
				for (const ele of doctorInfo.servingHospital) {
					ele.id = uuid();
				}
				formData.multiPointPracticeList = doctorInfo.servingHospital;
				originalMultiPointPracticeList.value = doctorInfo.servingHospital.map((ele) => {
					return { ...ele };
				});
			});
		} else {
			formData.multiPointPractice = '';
			formData.multiPointPracticeList = [];
		}
	}
	try {
		showLoadingToast({
			message: '加载中...',
			forbidClick: true,
			duration: 0,
			loadingType: 'spinner',
		});
		let [{ result: res1 }, { result: res2 }, { result: res3 }] = await Promise.all([getZyzc(), getXzzc(), getKs()]);
		res1 = res1.map((ele) => {
			return { text: ele.label, value: ele.value };
		});
		res2 = res2.map((ele) => {
			return { text: ele.label, value: ele.value };
		});
		selectOption.zyzcList = res1;
		selectOption.xzzcList = res2;
		selectOption.ksList = recursive(res3);
	} finally {
		closeToast();
	}
});

// 添加执业信息
const addJobInfo = () => {
	formData.multiPointPracticeList.push(createJob());
};
// 删除执业信息
const delJobInfo = (id) => {
	formData.multiPointPracticeList = formData.multiPointPracticeList.filter((item) => item.id !== id);
};
// 执业信息改变事件
const jobChange = (val) => {
	if (val === '是') {
		formData.multiPointPracticeList = [createJob()];
	} else {
		formData.multiPointPracticeList = [];
	}
};
const createJob = () => {
	return {
		hospital: '',
		hospitalId: '',
		department: '',
		title: '',
		doctorIdMulti: '',
		verification_status: '0',
		id: uuid(),
	};
};

const recursive = (arr) => {
	for (let i in arr) {
		if (Array.isArray(arr[i].children) && arr[i].children.length > 0) {
			recursive(arr[i].children);
		}
		arr[i].text = arr[i].label;
		delete arr[i].label;
	}
	return arr;
};
</script>
<style lang="scss" scoped>
.new-doctor {
	display: flex;
	flex-direction: column;
	height: 100vh;

	.new-doctor-content {
		flex: 1;
		overflow-y: auto;
		// title
		.new-doctor-title {
			padding: 12px 15px;
			display: flex;
			align-items: center;
			background-color: #f4f5f7;
			&-block {
				width: 8px;
				height: 8px;
				border-radius: 50%;
				background-color: #0052cc;
				margin-right: 9px;
			}
			&-text {
				color: #172b4d;
				font-weight: 600;
				font-size: 14px;
			}
		}

		// 基础信息
		.doctor-basic-info {
			&:deep(.reset-field) {
				.van-field__value {
					.van-field__body {
						border: solid 1px #dfe1e6;
						padding: 8px;
						background-color: transparent;
						border-radius: 5px;
						.van-field__control {
							color: #172b4d;
							font-size: 13px;
						}

						.van-field__right-icon {
							i {
								font-size: 12px;
							}
						}
					}
				}
			}

			&:deep(.job-info) {
				display: flex;
				align-items: center;
				.job-info-add {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					padding-right: 15px;
					z-index: 2;
					width: 80px;
					img {
						width: 15px;
						height: 15px;
					}
					span {
						color: #0052cc;
						font-size: 14px;
						margin-left: 5px;
					}
				}
			}

			.job-info-list {
				border: solid 1px #dfe1e6;
				border-radius: 5px;
				position: relative;
				width: 345px;
				margin-left: 15px;
				margin-bottom: 12px;
				position: relative;
				padding-top: 30px;
				.job-info-list-title {
					position: absolute;
					top: 0;
					left: 50%;
					transform: translateX(-50%);
					background: rgba(0, 82, 204, 0.05);
					z-index: 2;
					border-radius: 0px 0px 8px 8px;
					padding: 4px 6px;
					span {
						color: #0052cc;
						font-size: 12px;
					}
				}

				.job-info-list-delete {
					position: absolute;
					top: 0;
					right: 0;
					z-index: 2;
					width: 35px;
					height: 35px;
				}
			}
		}

		// 证件信息
		.new-doctor-annex {
			border-radius: 5px;
			background-color: #fff;
			padding: 15px;
			&-title {
				color: #172b4d;
				font-size: 14px;
				display: flex;
				justify-content: space-between;
				& > span::before {
					content: '*';
					color: #ff0000;
					transform: scale(1) translate(1px, 0px);
					display: inline-block;
					margin-right: 3px;
				}
				&-upload {
					color: #0052cc;
					font-weight: normal;
				}
			}
			&-tips {
				margin-top: 8px;
				padding: 9px 12px 8px 9px;
				color: #172b4d;
				font-size: 12px;
				border-radius: 5px;
				background-color: rgba(221, 235, 255, 0.3);
				img {
					width: 10px;
					height: 10px;
					margin-right: 6px;
				}
			}
			&-list {
				&-item {
					border-radius: 4px;
					background-color: rgba(223, 225, 230, 0.1);
					border: 1px dotted #dfe1e6;
					display: flex;
					padding: 12px 16px;
					margin-top: 12px;
					position: relative;
					&-img {
						img {
							width: 30px;
							height: 37px;
							object-fit: contain;
						}
						margin-right: 12px;
					}
					&-info {
						.name {
							color: #172b4d;
							font-size: 14px;
						}
						.size {
							color: #6b778c;
							font-size: 12px;
						}
					}
					&-close {
						position: absolute;
						top: 4px;
						right: 4px;
						width: 15px;
						height: 15px;
					}
				}
			}
		}
	}

	&-bottom {
		padding: 12px 16px 16px;
		width: 100%;
		display: flex;
		border: 1px solid rgba(151, 151, 151, 0.1);
		box-shadow: 0px 2px 20px 0px rgba(95, 95, 95, 0.2);
		background-color: #ffffff;
		div {
			flex: 1;
			text-align: center;
			padding: 8px;
			border-radius: 5px;
			font-size: 15px;
		}
		div:nth-child(1) {
			color: #0052cc;
			border: 1px solid #0052cc;
			margin-right: 11px;
		}
		div:nth-child(2) {
			background-color: #0052cc;
			border: 1px solid #0052cc;
			color: #ffffff;
		}
		&-btn {
			background-color: #0052cc !important;
			border: 1px solid #0052cc !important;
			color: #ffffff !important;
		}
	}
}
</style>

<style>
.preview-img {
	background-color: initial;
}
</style>
