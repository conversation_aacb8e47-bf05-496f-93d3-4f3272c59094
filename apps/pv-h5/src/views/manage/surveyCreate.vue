<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('survey.surveyCreate') }}</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 内容 -->
			<div class="mc-content-cotainer">
				<!-- 拜访表单 -->
				<div class="mc-content__form">
					<van-form ref="form1">
						<!-- 基本信息 -->
						<div class="form-info">
							<!-- 客户 -->
							<van-field class="required" v-model="formData.doctorName" name="客户" label="客户" placeholder="请选择" :is-link="btnList.some((item) => item.path === 'isAddAndEditorSurvey')" readonly :rules="[{ validator: cusValidator, message: '请选择客户' }]" @click="selectDoctor"> </van-field>
							<!-- 问题1 -->
							<van-field class="inline-product" label="1. 该客户是否符合如下情况之一：(1) 非临床关键客户，(2) 潜力低（eg：每周不超过一个半天门诊且癫痫病人不超过5个）的临床客户但影响力大 " :rules="[{ required: true, message: '请选择答案' }]">
								<template #input>
									<van-radio-group v-model="formData.num1" direction="horizontal">
										<van-radio :disabled="!btnList.some((item) => item.path === 'isAddAndEditorSurvey')" checked-color="#0052cc" v-for="item in ['是', '否']" :key="item" :name="item">{{ item }}</van-radio>
									</van-radio-group>
								</template>
							</van-field>
							<!-- 问题2 -->
							<div class="form-desc">
								<span>
									<span style="color: #de350b">*</span>
									2. 最近3个月内，该客户平均每月接诊的癫痫患者数量是? 这里包括门诊、病房、线上接诊的所有癫痫患者
								</span>
								<van-field :readonly="!btnList.some((item) => item.path === 'isAddAndEditorSurvey')" v-model="formData.num2" placeholder="请填写患者数量" type="digit" :rules="[{ required: true, message: '请填写患者数量' }]"> </van-field>
							</div>
						</div>
					</van-form>
					<!-- 取消和确认 -->
					<div v-if="btnList.some((item) => item.path === 'isAddAndEditorSurvey')" class="mc-content__btn">
						<van-button class="size-color" color="#dde4f2" @click="goBack">取消</van-button>
						<van-button @click="confirmCreate()" color="#0052cc">确认</van-button>
					</div>
				</div>
			</div>
		</div>
		<!-- 更多医生 弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="docMoreVis" position="bottom" :style="{ height: '90%' }">
			<ai-more-doctors :doneDoctor="lDcotor" @close="docMoreVis = false" @activeDoc="activeDoc"></ai-more-doctors>
		</van-popup>
	</div>
</template>
<script setup>
import { showLoadingToast, showToast } from 'vant';
import { createSurvey, updateSurvey, getMyDoctors } from '@/api/my';
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
const router = useRouter();
const route = useRoute();
const surveyType = ref('分级');
const formData = reactive({
	doctorName: '',
	doctorId: '',
	doctorTitle: '',
	num1: '',
	num2: '',
});

// 元素
const btnList = computed(() => {
	return perStore.systemBtnList;
});
const form1 = ref(null);
// 选择客户
const docMoreVis = ref(false);
const selectDoctor = () => {
	if (btnList.value.some((item) => item.path === 'isAddAndEditorSurvey')) {
		docMoreVis.value = true;
	}
};
const activeDoc = (item) => {
	formData.doctorName = item.doctorName;
	formData.doctorId = item.doctorId;
	formData.doctorTitle = item.doctorTitle;
	docMoreVis.value = false;
};
// 客户校验函数
const cusValidator = (val) => {
	if (val) return true;
	return false;
};

const confirmCreate = async () => {
	try {
		await form1.value.validate();
		showLoadingToast({
			message: '加载中...',
			duration: 0,
			forbidClick: true,
		});
		const query = {
			doctorId: formData.doctorId,
			doctorName: formData.doctorName,
			doctorTitle: formData.doctorTitle,
			surveyInfo: JSON.stringify({
				num1: formData.num1,
				num1Text: '该客户是否符合如下情况之一：(1) 非临床关键客户，(2) 潜力低（eg：每周不超过一个半天门诊且癫痫病人不超过5个）的临床客户但影响力大',
				num2: formData.num2,
				num2Text: '最近3个月内，该客户平均每月接诊的癫痫患者数量是? 这里包括门诊、病房、线上接诊的所有癫痫患者',
			}),
			surveyType: surveyType.value,
		};
		if (route.query.id) {
			await updateSurvey({ id: route.query.id, ...query });
			showToast({
				message: '修改成功',
				position: 'top',
			});
		} else {
			await createSurvey(query);
			showToast({
				message: '创建成功',
				position: 'top',
			});
		}
		goBack();
	} catch (e) {
		console.log(e);
	}
};

// 返回
const goBack = () => {
	router.go(-1);
};
const lDcotor = ref([]);
onMounted(() => {
	if (route.query.id) {
		formData.doctorName = route.query.doctorName;
		formData.doctorId = route.query.doctorId;
		formData.doctorTitle = route.query.doctorTitle;
		formData.num1 = route.query.num1;
		formData.num2 = Number(route.query.num2);
	}
	// 获取已经填写的医生集合
	getMyDoctors({ surveyType: surveyType.value }).then((res) => {
		lDcotor.value = res.result;
	});
});
</script>

<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		width: 100vw;
		display: flex;
		background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;

		&:deep(.van-tab) {
			font-size: 15px;
			color: #6b778c;
		}
		&:deep(.van-tab--active) {
			color: #172b4d;
		}
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 0 15px;
			margin-bottom: 20px;

			// 拜访表单
			.mc-content__form {
				display: flex;
				flex-direction: column;

				.form-info {
					background-color: #ffffff;
					padding: 0;
					margin-top: 12px;
					border-radius: 5px;
					&:deep(.van-field) {
						padding: 12px 0;
						display: flex;
						.van-cell__title {
							label {
								color: #172b4d;
							}
						}
					}
					.required {
						&:deep(.van-field__label::before) {
							content: '*';
							color: #de350b;
							transform: scale(1.2) translate(1px, 0px);
							display: inline-block;
							margin-right: 5px;
						}
					}
					.inline {
						display: block;
						&:deep(.van-cell__title) {
							margin-bottom: 8px;
						}
						&:deep(.van-field__label) {
							width: 100%;
						}
						&:deep(.van-checkbox) {
							margin-bottom: 8px;
						}
					}

					.inline-product {
						display: block;
						&:deep(.van-cell__title) {
							margin-bottom: 8px;
						}
						&:deep(.van-checkbox) {
							margin-bottom: 8px;
						}
						&:deep(.van-field__label) {
							width: 100%;
						}
						&:deep(.van-field__label::before) {
							content: '*';
							color: #de350b;
							transform: scale(1.2) translate(1px, 0px);
							display: inline-block;
							margin-right: 5px;
						}
					}
				}

				.form-desc {
					background-color: #ffffff;
					margin-top: 12px;
					border-radius: 5px;
					span {
						color: #172b4d;
					}
					&:deep(.van-field) {
						background-color: #f4f5f7;
						margin-top: 8px;
						border-radius: 5px;
						padding-left: 12px;
						padding-right: 12px;
					}
				}

				// 签字信息
				.mc-content__sign {
					display: flex;
					flex-direction: column;
					background-color: #ffffff;
					padding: 15px;
					margin-top: 12px;
					border-radius: 5px;
					span {
						color: #172b4d;
						font-weight: 600;
						margin-bottom: 10px;
					}

					.sign-content {
						border-radius: 5px;
						background-color: #f4f5f7;
						height: 100px;
						display: flex;
						align-items: center;
						justify-content: center;
						color: #dfe1e6;
						font-size: 40px;
					}
				}

				// 按钮
				.mc-content__btn {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 50px;
					&:deep(.van-button) {
						border-radius: 5px;
						height: 40px;
						width: 167px;
					}

					&:deep(.size-color) {
						color: #0052cc !important;
					}
				}
			}

			.mc-content__suggest {
				display: flex;
				flex-direction: column;
				background-color: #ffffff;
				padding: 15px;
				margin-top: 12px;
				border-radius: 5px;
				.suggest-header {
					display: flex;
					align-items: center;
					justify-content: space-between;
					.suggest-header__left {
						display: flex;
						align-items: center;
						span {
							color: #6b778c;
						}
						& span.active {
							color: #172b4d;
						}
						img {
							width: 27px;
							height: 15px;
							margin-left: 5px;
						}
					}
					.suggest-header__right {
						display: flex;
						align-items: center;
						img {
							width: 16px;
							height: 16px;
							margin-left: 5px;
						}
					}
				}
				&:deep(.van-field) {
					padding: 12px;
					background-color: #f4f5f7;
					border-radius: 5px;
					margin-bottom: 15px;
					margin-top: 15px;
					textarea {
						color: #172b4d;
					}
				}

				.mc-content-list {
					width: 100%;
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					.list-item {
						width: 150px;
						height: 45px;
						border-radius: 5px;
						background-color: #f3f6fc;
						margin-bottom: 8px;
						display: flex;
						align-items: center;
						justify-content: center;
						img {
							width: 16px;
							height: 16px;
							margin-right: 8px;
						}
						span {
							color: #172b4d;
							font-size: 14px;
						}
					}
				}
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}
	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}

	&:deep(.van-picker__confirm) {
		color: #0052cc;
	}

	&:deep(.visitTimePicker) {
		.title {
			height: initial;
			color: #353639;
			font-weight: bold;
			font-size: 14px;
			padding: 0px 16px 16px;
			span {
				padding-left: 4px;
			}
		}
		.flex_box {
			display: flex;
			.van-picker:nth-child(1) {
				flex: 2;
			}
			.van-picker:nth-child(2) {
				flex: 1;
			}
			.van-picker-column {
				font-size: 15px;
			}
		}
		.operation {
			padding: 16px 16px 24px;
			display: flex;
			border: 1px solid rgba(151, 151, 151, 0.1);
			box-shadow: 0px 2px 20px 0px rgba(95, 95, 95, 0.2);
			background-color: #ffffff;
			div {
				flex: 1;
				text-align: center;
				padding: 10px;
				border-radius: 5px;
				font-size: 15px;
			}
			div:nth-child(1) {
				color: #0052cc;
				border: 1px solid #0052cc;
				margin-right: 11px;
			}
			div:nth-child(2) {
				background-color: #0052cc;
				border: 1px solid #0052cc;
				color: #ffffff;
			}
		}
	}
}
</style>
