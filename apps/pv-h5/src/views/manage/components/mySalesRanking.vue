<template>
	<div class="my-card">
		<div class="my-card-title">
			<span>销售达成率排名</span><span>｜{{ formatDateToYearMonth(props.startTime) }}达成总结</span>
		</div>
		<div class="my-card-rank" v-if="isShow">
			<div class="my-card-rank-info">
				<div class="my-card-rank-info-item">
					<div>0~80%</div>
					<div>
						<span>{{ achievementSumInfo.low }}</span
						>人
					</div>
				</div>
				<div class="my-card-rank-info-item">
					<div>80~100%</div>
					<div>
						<span>{{ achievementSumInfo.medium }}</span
						>人
					</div>
				</div>

				<div class="my-card-rank-info-item">
					<div>100%以上</div>
					<div>
						<span>{{ achievementSumInfo.high }}</span
						>人
					</div>
				</div>
			</div>
			<div class="my-card-rank-content">
				<div class="my-card-rank-content-list">
					<div class="my-card-rank-content-list-title">
						<span>
							<span>排名</span>
							<span>{{ achievementSumInfo.nextRole }}</span>
						</span>
						<span>达成</span>
					</div>
					<div class="my-card-rank-content-list-item" v-for="(item, index) in rankingData" :key="index">
						<span>
							<span v-if="item.salesRateMtdOrder === 1"><img src="@/assets/img/num-one.png" alt="" /></span>
							<span v-else-if="item.salesRateMtdOrder === 2"><img src="@/assets/img/num-two.png" alt="" /></span>
							<span v-else-if="item.salesRateMtdOrder === 3"> <img src="@/assets/img/num-three.png" alt="" /></span>
							<span v-else>{{ item.salesRateMtdOrder }}</span>
						</span>
						<span>{{ item.empName }}</span>
						<span class="bar">
							<span :style="{ width: widthComputed(item.salesRateMtd) }"></span>
						</span>
						<span>{{ item.salesRateMtd }}%</span>
					</div>
					<div class="my-card-rank-content-list-average" :style="{ left: averageComputed(achievementSumInfo.buAvg.slice(0, -1)) }">
						{{ achievementSumInfo.nextRole }}平均&ensp;<span>{{ achievementSumInfo.buAvg }}</span>
					</div>
				</div>
			</div>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.dataSyncTime" :isYMD="true" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
	</div>
</template>
<script setup>
import { achievementSum } from '@/api/my';
import { formatDateToYearMonth } from '@/utils/index';
const props = defineProps(['startTime', 'endTime', 'dataSyncTime', 'summary', 'inChat']);

let widthComputed = computed(() => {
	return function (value) {
		// 最大高度为最大达成
		//achievementSumInfo.value.max = 60vw
		if (Number(value) < 0) return '1%';
		return (value / achievementSumInfo.value.max) * 100 + '%';
	};
});
let averageComputed = computed(() => {
	return function (value) {
		//achievementSumInfo.value.max = 57.33334vw
		if (Number(value) < 0) {
			return 8 + 'vw';
		}
		return 8 + (value * 57.33334) / achievementSumInfo.value.max + 'vw';
	};
});
const rankingData = ref([]);
let achievementSumInfo = ref({});
let isShow = ref(false);
const emit = defineEmits(['summaryIng', 'openfilter']);
const getachievementSum = async () => {
	let res = await achievementSum();
	achievementSumInfo.value = res.result;
	rankingData.value = res.result.summarizeList;
	isShow.value = true;
	if (props.summary) {
		emit('summaryIng', { data: { ...res.result } });
	}
};
onMounted(async () => {
	getachievementSum();
});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.my-card {
	padding: 15px 10px 10px;
	border-radius: 5px;
	background-color: var(--pv-card-bgc);
	margin-top: 10px;
	overflow: hidden;
	&-title {
		font-weight: bold;
		font-size: 16px;
		padding-left: 4px;
		span:nth-last-child(1) {
			margin-left: 10px;
			color: #6b778c;
			font-weight: normal;
			font-size: 14px;
		}
	}
	&-rank {
		border-radius: 5px;
		background-color: #ffffff;
		margin-top: 11px;
		padding-bottom: 6px;
		&-info {
			display: flex;
			justify-content: space-around;
			align-items: center;
			&-item {
				padding: 21px 0;
				text-align: center;
				position: relative;
				div:nth-child(1) {
					color: var(--pv-no-active-color);
					font-size: 9px;
					margin-bottom: 3px;
				}
				div:nth-child(2) {
					font-weight: normal;
					font-size: 9px;
					span {
						font-weight: bold;
						font-size: 15px;
						margin-right: 4px;
					}
				}
				&::after {
					content: '';
					position: absolute;
					right: -37px;
					top: 50%;
					transform: translateY(-50%);
					width: 1px;
					height: 17px;
					background-color: #dfe1e6;
				}
				&:nth-last-child(1)::after {
					display: none;
				}
			}
		}

		&-content {
			height: 130px;
			overflow-y: auto;
			overflow-x: hidden;
			/* 设置纵向滚动条样式 */
			&::-webkit-scrollbar {
				width: 4px; /* 设置纵向滚动条宽度 */
			}

			&::-webkit-scrollbar-thumb {
				background-color: rgba(0, 0, 0, 0.2); /* 设置纵向滚动条拖动部分的颜色 */
				border-radius: 3px;
			}

			&::-webkit-scrollbar-track {
				background-color: #fff; /* 设置纵向滚动条轨道的颜色 */
				border-radius: 5px;
			}
			&-list {
				font-size: 9px;
				padding: 0px 13px 0px 11px;
				position: relative;
				&-title {
					display: flex;
					justify-content: space-between;
					color: var(--pv-default-color);
					padding-bottom: 8px;
					span:nth-child(1) {
						span:nth-child(1) {
							margin-right: 13px;
						}
					}
				}
				&-item {
					display: flex;
					align-items: center;
					margin-bottom: 8px;
					position: relative;
					z-index: 2;
					span:nth-child(1),
					span:nth-child(2) {
						width: 30px;
						text-align: center;
						margin-right: 4px;
						flex-shrink: 0;
						color: rgba(0, 0, 0, 0.45);
					}
					span:nth-child(1) {
						width: 20px;
						img {
							width: 20px;
							height: 13px;
						}
					}
					.bar {
						width: 215px !important;
						flex-shrink: 0;
						margin-right: 4px;
						height: 6px;
						span {
							background-color: var(--pv-my-gl);
							display: block;
							height: 6px;
						}
					}
					span:nth-last-child(1) {
						flex-shrink: 0;
					}
				}
				.isUserPart {
					background-color: #f4f5f7;
					box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
					padding: 7px 0;
					position: relative;
					span {
						color: var(--pv-default-color) !important;
					}
					&::before {
						content: '';
						display: block;
						position: absolute;
						width: 2px;
						height: 100%;
						background-color: var(--pv-gl);
						border-radius: 8px 0 0 8px;
						left: -2px;
					}
				}
				&-average {
					width: 80px;
					position: absolute;
					top: 12px;
					color: rgba(0, 0, 0, 0.45);
					height: 100%;
					span {
						color: #ff991f;
					}
					&::after {
						content: '';
						position: absolute;
						z-index: 2;
						top: 14px;
						left: 50%;
						transform: translateX(-50%);
						width: 1px;
						height: calc(100% - 20px);
						border-left: 1px dashed #dfe1e6;
					}
				}
			}
		}
		// }
	}
}
</style>
