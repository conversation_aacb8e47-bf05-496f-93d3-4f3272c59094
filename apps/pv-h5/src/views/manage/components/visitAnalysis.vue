<template>
	<div class="my-card">
		<div class="my-card-title">
			<div class="my-card-title">
				<span>拜访分析</span><span>｜{{ formatDateToYearMonth(props.startTime) }}拜访总结</span>
			</div>
		</div>
		<div class="my-card-box">
			<div class="analysis-box">
				<div class="visit-box">
					<div class="visit-box-block">
						<div class="visit-box-block-desc">拜访执行率</div>
						<div class="visit-box-block-info">
							<span>{{ visitResult.visitAch }}%</span>
						</div>
					</div>
					<div class="visit-box-block">
						<div class="visit-box-block-desc">AB客户覆盖数</div>
						<div class="visit-box-block-info">
							<span>{{ visitResult.abDoctorNum }}</span> <span class="visit-box-block-info-num"> 人</span>
						</div>
					</div>
					<div class="visit-box-block">
						<div class="visit-box-block-desc visit-box-block-ab">AB覆盖率</div>
						<div class="visit-box-block-info visit-box-block-ab">{{ visitResult.abAch && new Decimal(visitResult.abAch).toDecimalPlaces(0) }}%</div>
					</div>
				</div>
				<div class="visit-info-box">
					<div class="visit-info" v-for="(item, index) in visitList" :key="index">
						<div class="visit-info-level">
							<div class="visit-info-level-left">{{ item.doctorLevel }}级别客户</div>
							<div class="visit-info-level-right">
								每月目标平均拜访
								<span class="visit-info-level-num">{{ item.targetAvg }}</span
								>次/人，已平均拜访 <span class="visit-info-level-num">{{ item.visitAvg }}</span
								>次/人
							</div>
						</div>
						<div class="visit-info-level">
							<div class="visit-info-level-desc">客户人数</div>
							<div class="visit-info-level-progress-bar">
								<div class="visit-info-level-progress" :id="`visit-info-level-progress-${index}`" :style="{ backgroundColor: progressColors[index], width: `${item.visitCoverAch}%` }"></div>
							</div>
							<div class="visit-info-level-desc visit-info-level-desc-right">
								<span class="visit-info-level-desc-num">{{ formatNumberW(item.visitDoctor, '', item.targetDoctor) }}</span
								>/{{ formatNumberW(item.targetDoctor, '人') }}
							</div>
						</div>
						<div class="visit-info-level">
							<div class="visit-info-level-desc">拜访次数</div>
							<div class="visit-info-level-progress-bar">
								<div class="visit-info-level-progress" :id="`visit-info-level-progress-${index + 10}`" :style="{ backgroundColor: progressColors[index], width: `${item.visitAch}%` }"></div>
							</div>
							<div class="visit-info-level-desc visit-info-level-desc-right">
								<span class="visit-info-level-desc-num">{{ formatNumberW(item.visitNum, '', item.targetNum) }}</span
								>/{{ formatNumberW(item.targetNum, '次') }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 数据更新至 -->
		<dataUpdateBy :updateTime="props.dataSyncTime" :isYMD="true" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
	</div>
</template>
<script setup>
import Decimal from 'decimal.js';
import { formatNumberW, formatDateToYearMonth } from '@/utils/index';
import { myVisitSummary, visitSummaryInfo } from '@/api/report';
const props = defineProps(['startTime', 'endTime', 'dataSyncTime']);
const progressColors = ref(['#0052cc', '#79e1f2', ' #E95600']);

const visitResult = ref({});
let getVisitData = async () => {
	let res = await myVisitSummary({ startLocalDate: props.startTime, endLocalDate: props.endTime });
	visitResult.value = res.result;
	getVisitInfo();
};

const visitList = ref({});
let getVisitInfo = async () => {
	let res = await visitSummaryInfo({ startLocalDate: props.startTime, endLocalDate: props.endTime });
	visitList.value = res.result.data;
};

onMounted(async () => {
	getVisitData();
});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.my {
	background-color: var(--pv-bgc);
	color: var(--pv-default-color);
	padding-top: 7px;
	&-card {
		padding: 15px 10px 10px;
		border-radius: 5px;
		background-color: var(--pv-card-bgc);
		margin-top: 10px;
		overflow: hidden;
		&-title {
			font-weight: bold;
			font-size: 16px;
			padding-left: 4px;
			span:nth-last-child(1) {
				margin-left: 10px;
				color: #6b778c;
				font-weight: normal;
				font-size: 14px;
			}
		}
	}
	.my-card-box {
		.analysis-box {
			background-color: var(--pv-bgc);
			border-radius: 5px;
			position: relative;
			margin-top: 11px;

			.visit-box {
				display: flex;
				align-items: center;
				padding-top: 24px;
				justify-content: space-around;
				&-block {
					flex: 1;
					text-align: center;
					&-desc {
						font-size: 9px;
						color: var(--pv-no-active-color);
					}
					&-info {
						margin-top: 4px;
						font-weight: bold;
						font-size: 15px;
						&-num {
							font-size: 9px;
						}
					}
					&-ab {
						color: #dd340a;
					}
					&::after {
						content: '';
						position: absolute;
						right: -37px;
						top: 50%;
						transform: translateY(-50%);
						width: 1px;
						height: 17px;
						background-color: #dfe1e6;
					}
					&:nth-last-child(1)::after {
						display: none;
					}
				}
				&-line {
					border: 0.5px solid var(--pv-filter-bgc);
					width: 0.5px;
					height: 50%;
					margin: auto;
				}
			}
			.visit-info-box {
				padding: 20px 20px 4px;
				.visit-info {
					margin-bottom: 16px;
					&-level {
						display: flex;
						justify-content: space-between;
						margin: 8px 0px;
						align-items: center;
						&-left {
							color: #172b4d;
							font-weight: bold;
							font-size: 12px;
						}
						&-right {
							color: var(--pv-no-active-color);
							font-size: 10px;
						}
						&-num {
							color: var(--pv-tabbar-active);
							font-size: 10px;
						}
						&-desc {
							// width: 15%;
							margin-right: 8px;
							font-size: 9px;
							color: var(--pv-no-active-color);
							&-num {
								color: var(--pv-tabbar-active);
							}
						}
						&-desc-right {
							text-align: left;
							color: var(--pv-default-color);
							width: 64px;
							margin-left: 2px;
							margin-right: 0;
						}
						&-progress-bar {
							flex: 1;
							height: 9px;
							background-color: var(--pv-card-bgc);
							border-radius: 2.5px;
							overflow: hidden;
							.visit-info-level-progress {
								height: 100%;
								background-color: var(--pv-tabbar-active); /* 已完成进度的颜色 */
								width: 20%;
							}
						}
					}
				}
			}
		}
	}
}
</style>
