<template>
	<div>
		<div class="my-info-desc">
			<div v-html="dailyReportSummaryInfo.message"></div>
		</div>
		<div class="my-card">
			<div class="my-card-title">
				<span>销售洞察</span><span>｜{{ formatDateToYearMonth(props.startTime) }}报告总结</span>
			</div>
			<div class="my-card-content">
				<div class="my-card-content-item">
					<div class="my-card-content-item-title">销售达成</div>
					<div class="my-card-content-item-ach">
						<div class="my-card-content-item-ach-item">
							<div class="left">
								<van-circle v-model:current-rate="currentRate1" :speed="100" stroke-linecap="butt" :stroke-width="translateFont(180)" :text="dailyReportSummaryInfo.salesRateMtd + '%'" :size="translateFont(45)" :rate="currentRate2" color="#0052CC" layer-color="#F4F5F7" />
							</div>
							<div class="right">
								<div>MTD</div>
								<div>
									同比：<span>{{ dailyReportSummaryInfo.yoyMtd }}%</span>
									<span>
										<img v-if="Number(dailyReportSummaryInfo.yoyMtd) > 0" src="@/assets/img/up.png" alt="" />
										<img v-else src="@/assets/img/down.png" alt="" />
									</span>
								</div>
								<div>
									环比：<span>{{ dailyReportSummaryInfo.momMtd }}%</span>
									<span>
										<img v-if="Number(dailyReportSummaryInfo.yoyMtd) > 0" src="@/assets/img/up.png" alt="" />
										<img v-else src="@/assets/img/down.png" alt="" />
									</span>
								</div>
							</div>
						</div>
						<div class="my-card-content-item-ach-item">
							<div class="left">
								<van-circle v-model:current-rate="currentRate3" :speed="100" stroke-linecap="butt" :stroke-width="translateFont(180)" :text="dailyReportSummaryInfo.salesRateQtd + '%'" :size="translateFont(45)" :rate="currentRate4" color="#79E1F2" layer-color="#F4F5F7" />
							</div>
							<div class="right">
								<div>QTD</div>
								<div>
									同比：<span>{{ dailyReportSummaryInfo.yoyQtd }}%</span>
									<span>
										<img v-if="Number(dailyReportSummaryInfo.yoyMtd) > 0" src="@/assets/img/up.png" alt="" />
										<img v-else src="@/assets/img/down.png" alt="" />
									</span>
								</div>
								<div>
									环比：<span>{{ dailyReportSummaryInfo.qoqQtd }}%</span>
									<span>
										<img v-if="Number(dailyReportSummaryInfo.yoyMtd) > 0" src="@/assets/img/up.png" alt="" />
										<img v-else src="@/assets/img/down.png" alt="" />
									</span>
								</div>
							</div>
						</div>
						<div class="my-card-content-item-ach-item">
							<div class="left">
								<van-circle v-model:current-rate="currentRate5" :speed="100" stroke-linecap="butt" :stroke-width="translateFont(180)" :text="dailyReportSummaryInfo.salesRateYtd + '%'" :size="translateFont(45)" :rate="currentRate6" color="#FF991F" layer-color="#F4F5F7" />
							</div>
							<div class="right">
								<div>YTD</div>
								<div>
									同比：<span>{{ dailyReportSummaryInfo.yoyYtd }}%</span>
									<span>
										<img v-if="Number(dailyReportSummaryInfo.yoyMtd) > 0" src="@/assets/img/up.png" alt="" />
										<img v-else src="@/assets/img/down.png" alt="" />
									</span>
								</div>
								<!-- <div>环比：<span>{{ dailyReportSummaryInfo.yoyMtd }}%</span></div> -->
							</div>
						</div>
					</div>
				</div>
				<div class="my-card-content-item">
					<div class="my-card-content-item-title">本月销售额</div>
					<div class="my-card-content-item-sales">{{ formatNumber(dailyReportSummaryInfo.salesMtd) }}</div>
					<div class="my-card-content-item-title">有销售额终端</div>
					<div class="my-card-content-item-shop">
						<van-circle v-model:current-rate="currentRate7" :speed="100" stroke-linecap="butt" :stroke-width="translateFont(140)" :text="dailyReportSummaryInfo.hospSalesRate + '%'" :size="translateFont(60)" :rate="currentRate8" color="#0052CC" layer-color="#F4F5F7" start-position="left" />
					</div>
				</div>
			</div>
			<!-- 数据更新至 -->
			<dataUpdateBy :updateTime="props.dataSyncTime" :isYMD="true" :refreshTime="props.info?.reportRefreshTime"></dataUpdateBy>
		</div>
	</div>
</template>
<script setup>
import Decimal from 'decimal.js';
import { translateFont, formatNumber, formatDateToYearMonth } from '@/utils/index';
import { dailyReportSummary } from '@/api/my';
const props = defineProps(['startTime', 'endTime', 'dataSyncTime', 'summary', 'inChat']);

const currentRate1 = ref(0);
const currentRate2 = ref(0);
const currentRate3 = ref(0);
const currentRate4 = ref(0);
const currentRate5 = ref(0);
const currentRate6 = ref(0);
const currentRate7 = ref(0);
const currentRate8 = ref(0);
let dailyReportSummaryInfo = ref({});
const emit = defineEmits(['summaryIng', 'openfilter']);
const getDailyReportSummary = async () => {
	let res = await dailyReportSummary();
	dailyReportSummaryInfo.value = res.result;
	currentRate2.value = res.result.salesRateMtd;
	currentRate4.value = res.result.salesRateQtd;
	currentRate6.value = res.result.salesRateYtd;
	currentRate8.value = new Decimal(res.result.hospSalesRate).dividedBy(2).toDecimalPlaces(2).toString();
	if (props.summary) {
		emit('summaryIng', { data: { ...res.result } });
	}
};
onMounted(async () => {
	getDailyReportSummary();
});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.my-info-desc {
	margin-top: 8px;
	font-size: 12px;
	line-height: 16px;
}
.my-card {
	padding: 15px 10px 10px;
	border-radius: 5px;
	background-color: var(--pv-card-bgc);
	margin-top: 10px;
	overflow: hidden;
	&-title {
		font-weight: bold;
		font-size: 16px;
		padding-left: 4px;
		span:nth-last-child(1) {
			margin-left: 10px;
			color: #6b778c;
			font-weight: normal;
			font-size: 14px;
		}
	}
	&-content {
		display: flex;
		margin-top: 10px;
		gap: 8px;
		&-item {
			flex: 1;
			border-radius: 6px;
			background: linear-gradient(135.32deg, rgba(0, 138, 232, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
			overflow: hidden;
			&-title {
				padding: 9px 0 4px 9px;
				font-weight: bold;
				font-size: 14px;
				color: rgba(23, 43, 77, 0.8);
			}
			&-ach {
				background-color: #fff;
				border-radius: 0px 0px 6px 6px;
				margin: 1px;
				padding-top: 5px;
				&-item {
					padding: 6px 8px 3px;
					display: flex;
					position: relative;
					.right {
						margin-left: 11px;
						div:nth-child(1) {
							color: #33476b;
							font-weight: bold;
							font-size: 14px;
						}
						div:nth-child(2),
						div:nth-child(3) {
							padding-left: 2px;
							color: var(--pv-no-active-color);
							font-size: 9px;
							span {
								color: var(--pv-default-color);
							}
							img {
								width: 10px;
								position: relative;
								top: -2px;
								margin-left: 5px;
							}
						}
					}
					:deep(.van-circle__text) {
						color: #33476b;
						font-size: 10px;
						font-weight: initial;
					}
					&::after {
						position: absolute;
						left: 50%;
						transform: translateX(-50%);
						bottom: 0;
						content: '';
						width: 139px;
						height: 1px;
						background-color: rgba(120, 136, 162, 0.3);
					}
					&:nth-last-child(1)::after {
						display: none;
					}
				}
			}
			&-sales {
				width: 141px;
				height: 68px;
				line-height: 68px;
				border-radius: 4px;
				background-color: #ffffff;
				margin: 0 auto;
				font-weight: bold;
				font-size: 14px;
				text-align: center;
			}
			&-shop {
				width: 141px;
				height: 66px;
				border-radius: 4px;
				background-color: #ffffff;
				margin: 5px auto;
				display: flex;
				justify-content: center;
				overflow: hidden;
				position: relative;
				:deep(.van-circle) {
					position: relative;
					top: 15px;
					.van-circle__text {
						color: #33476b;
						font-size: 10px;
						position: relative;
						top: 24px;
					}
				}
				&::after {
					content: '';
					position: absolute;
					width: 100px;
					height: 22px;
					background-color: #fff;
					bottom: 0;
				}
			}
		}
	}
}
</style>
