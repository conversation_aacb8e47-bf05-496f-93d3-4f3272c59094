<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('manage.selectLanguage') }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon"></div>
		</div>
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<van-radio-group checked-color="#0052cc" v-model="checked">
				<van-cell-group inset>
					<van-cell title="简体中文" clickable @click="setType('zh')">
						<template #right-icon>
							<van-radio name="1" />
						</template>
					</van-cell>
					<van-cell title="English" clickable @click="setType('en')">
						<template #right-icon>
							<van-radio name="2" />
						</template>
					</van-cell>
				</van-cell-group>
			</van-radio-group>
		</div>
		<set-language ref="setLanguageRef"></set-language>
	</div>
</template>
<script setup>
const router = useRouter();
const route = useRoute();
const checked = ref('');
const setLanguageRef = ref(null);
onMounted(() => {
	const lang = localStorage.getItem('language') || 'zh';
	checked.value = lang === 'zh' ? '1' : '2';
});
const setType = (val) => {
	if (val === localStorage.getItem('language')) return;
	localStorage.setItem('language', val);
	checked.value = val === 'zh' ? '1' : '2';
	setLanguageRef.value.setLang(val);
};
const goBack = () => {
	router.go(-1);
};
</script>
<style lang="scss" scoped>
.mc {
	display: flex;
	flex-direction: column;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		display: flex;
		flex-direction: column;

		:deep(.van-cell-group--inset) {
			margin: 0;
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
