<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('manage.aboutAgentBox') }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon"></div>
		</div>
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<div class="content-top">
				<img src="@/assets/img/Agent-logo.png" alt="" />
				<div class="content-top-title">AgentBox</div>
				<div class="content-top-version">Version：1.1.0</div>
			</div>

			<div class="content-bottom">
				<div class="content-bottom-title">北京启云数联科技有限公司</div>
				<div class="content-bottom-sub">京ICP备16036370号-9</div>
				<div class="content-bottom-sub">Copyright © 北京启云数联科技有限公司</div>
			</div>
		</div>
	</div>
</template>
<script setup>
const router = useRouter();
const route = useRoute();
const goBack = () => {
	router.go(-1);
};
</script>
<style lang="scss" scoped>
.mc {
	display: flex;
	flex-direction: column;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		display: flex;
		flex-direction: column;

		.content-top {
			display: flex;
			align-items: center;
			flex-direction: column;
			margin-top: 53px;
			flex: 1;
			img {
				width: 70px;
				height: 70px;
				margin-bottom: 10px;
			}

			.content-top-title {
				color: #172b4d;
				font-weight: 500;
				font-size: 18px;
				margin-bottom: 8px;
			}

			.content-top-version {
				color: #6b778c;
				font-weight: 400;
				font-size: 10px;
			}
		}

		.content-bottom {
			display: flex;
			align-items: center;
			flex-direction: column;
			.content-bottom-title {
				margin-bottom: 30px;
				color: #172b4d;
				font-weight: 400;
				font-size: 12px;
			}

			.content-bottom-sub {
				color: #6b778c;
				font-weight: 400;
				font-size: 10px;
				margin-bottom: 8px;
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
