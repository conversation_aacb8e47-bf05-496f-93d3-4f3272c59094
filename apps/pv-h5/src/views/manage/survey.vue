<template>
	<div class="rn" :class="route?.query?.isTabBar === 'false' ? 'rn-content__app' : 'rn-content__h5'">
		<!-- header -->
		<div class="rn-header">
			<!-- 历史记录 -->
			<div
				@click="goBack"
				:style="{
					visibility: route?.query?.isHeaderBackShow === 'false' ? 'hidden' : '',
				}"
				class="rn-header-icon"
			>
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="rn-header-name">{{ $t('common.survey') }}</div>
			<!-- 右侧icon -->
			<div class="rn-header-icon"></div>
		</div>
		<!-- content -->
		<div class="rn-content">
			<!-- 记录 -->
			<div v-if="loadingStatus === 'finished'" class="rn-content__log">
				<div v-for="item in surveyList" :key="item.id" @click="editSurvey(item)" class="log-item">
					<van-swipe-cell>
						<div>
							<span class="log-item__name">{{ item.doctorName }}</span>
							<span class="log-item__title">{{ item.doctorTitle }}</span>
							<img src="@/assets/img/fjs.png" />
						</div>
						<span class="log-item__time">{{ item.lastModifiedDate.split('T')[0] }}</span>
						<template #right>
							<van-button square text="删除" @click="delVisited(item)" type="danger" class="delete-button" />
						</template>
					</van-swipe-cell>
				</div>
			</div>
			<!-- skeleton -->
			<div v-else-if="loadingStatus === 'loading'" class="rn-content__skeleton">
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton style="margin-bottom: 8px" title :row="2" />
				<van-skeleton title :row="2" />
			</div>
			<van-empty v-else :description="$t('survey.noSurvey')" />
		</div>
		<!-- 创建btn -->
		<div v-if="btnList.some((item) => item.path === 'isAddAndEditorSurvey')" class="rn-create">
			<van-button color="#0052cc" @click="createSurvey" round type="primary">{{ $t('survey.createLog') }}</van-button>
		</div>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import { findSurveyList, deleteSurvey } from '@/api/my';
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
const router = useRouter();
const route = useRoute();
const loadingStatus = ref('loading');
const surveyList = ref([]);
const query = reactive({
	page: 0,
	size: 1000,
});
const goBack = () => {
	router.go(-1);
};
// 元素
const btnList = computed(() => {
	return perStore.systemBtnList;
});
const init = () => {
	findSurveyList(query)
		.then((res) => {
			if (res.result.content.length > 0) {
				surveyList.value = res.result.content || [];
				loadingStatus.value = 'finished';
			} else {
				loadingStatus.value = 'empty';
			}
		})
		.catch(() => {
			loadingStatus.value = 'empty';
		});
};

const createSurvey = () => {
	router.push({
		name: 'surveyCreate',
		query: {
			isTabBar: route?.query?.isTabBar,
		},
	});
};
const editSurvey = (item) => {
	const l = JSON.parse(item.surveyInfo);
	router.push({
		name: 'surveyCreate',
		query: {
			id: item.id,
			isTabBar: route?.query?.isTabBar,
			doctorName: item.doctorName,
			doctorId: item.doctorId,
			doctorTitle: item.doctorTitle,
			num1: l.num1,
			num2: l.num2,
		},
	});
};

// 删除问卷
const delVisited = (item) => {
	deleteSurvey(item.id).then(() => {
		surveyList.value = surveyList.value.filter((i) => i.id !== item.id);
		if (surveyList.value.length === 0) loadingStatus.value = 'empty';
		showToast({
			position: 'top',
			message: '删除成功',
		});
	});
};

onMounted(() => {
	init();
});
</script>
<style lang="scss" scoped>
.rn {
	display: flex;
	flex-direction: column;
	background-color: #f4f5f7;

	.rn-header {
		width: 100vw;
		display: flex;
		background-color: #ffffff;
		height: 40px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.rn-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.rn-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.rn-content {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		padding: 0px 15px;

		flex: 1;
		.rn-content__log {
			display: flex;
			flex-direction: column;
			margin-top: 15px;
			border-radius: 5px;
			background-color: #ffffff;

			.log-item {
				flex: 1;
				display: flex;
				align-items: baseline;
				border-bottom: 1px solid #dfe1e6;
				color: #172b4d;
				.log-item__name {
					font-size: 16px;
					font-weight: 600;
					margin-right: 8px;
				}
				.log-item__title {
					font-size: 12px;
				}
				img {
					width: 20px;
					object-fit: cover;
					margin-left: 8px;
				}

				.log-item__time {
					width: 100px;
					font-size: 12px;
					text-align: right;
					color: #72777c;
				}
				&:deep(.van-swipe-cell) {
					width: 100%;
				}
				&:deep(.van-swipe-cell__wrapper) {
					padding: 12px;
				}
				&:deep(.van-swipe-cell__right) {
					text-align: right;
				}
				&:deep(.van-button__content) {
					span {
						width: 24px;
						display: inline-block;
					}
				}
				&:deep(.van-button) {
					height: 100%;
					width: 100%;
					border-bottom-right-radius: 5px;
					border-top-right-radius: 5px;
				}
				&:deep(.van-button__text) {
					font-size: 12px;
				}
			}
			.log-item:last-child {
				border-bottom: none;
			}
		}

		.rn-content__skeleton {
			padding: 12px 0;
			background-color: #ffffff;
			border-radius: 6px;
			margin-top: 15px;
		}
	}
	.rn-create {
		display: flex;
		justify-content: center;
		margin-top: 12px;
		margin-bottom: 75px;
		&:deep(button) {
			height: 35px;
			width: 200px;
		}
	}
}
.rn-content__h5 {
	height: calc(100dvh);
}

.rn-content__app {
	height: calc(100vh);
}
</style>
