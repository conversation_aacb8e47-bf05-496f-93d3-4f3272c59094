<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('manage.setting') }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon"></div>
		</div>

		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 头像 -->
			<div class="c-avatar">
				<!-- 头像 -->
				<img class="c-avatar__img" v-if="avater" :src="avater" alt="" />
				<img class="c-avatar__img" v-else src="@/assets/img/tx.png" alt="" />

				<!-- 替换头像图标 -->
				<img @click="changeAvatar" class="c-avatar__icon" src="@/assets/img/s3.png" />
			</div>

			<!-- 昵称 -->
			<div class="c-item">
				<img class="c-item__icon" src="@/assets/img/me-1.png" />
				<span class="c-item__name">{{ $t('manage.nickName') }}</span>
				<div class="c-item__content">
					<input class="c-item__input" @blur="blurEv" v-model="firstName" type="text" placeholder="请输入昵称" maxlength="12" />
				</div>
				<img class="item-header__right" src="@/assets/img/right.png" />
			</div>
			<!-- 部门 -->
			<div class="c-item">
				<img class="c-item__icon" src="@/assets/img/me-2.png" />
				<span class="c-item__name">{{ $t('manage.department') }}</span>
				<div class="c-item__content"></div>
				<span class="c-item__desc">{{ depName }}</span>
				<!-- <img class="item-header__right" src="@/assets/img/right.png" /> -->
			</div>
			<!-- 隐私政策 -->
			<div @click="goPrivacy" class="c-item">
				<img class="c-item__icon" src="@/assets/img/me-7.png" />
				<span class="c-item__name">{{ $t('manage.privacyPolicy') }}</span>
				<div class="c-item__content"></div>
				<img class="item-header__right" src="@/assets/img/right.png" />
			</div>
			<!-- 我要反馈 -->
			<div @click="goFeedback" class="c-item">
				<img class="c-item__icon" src="@/assets/img/me-3.png" />
				<span class="c-item__name">{{ $t('manage.feedBack') }}</span>
				<div class="c-item__content"></div>
				<img class="item-header__right" src="@/assets/img/right.png" />
			</div>

			<!-- 多语言 -->
			<div @click="setLanguage" class="c-item">
				<img class="c-item__icon" src="@/assets/img/me-8.png" />
				<span class="c-item__name">{{ $t('manage.languages') }}</span>
				<div class="c-item__content"></div>
				<img class="item-header__right" src="@/assets/img/right.png" />
			</div>

			<!-- 背景设置 -->
			<!-- <div class="c-item">
				<img class="c-item__icon" src="@/assets/img/me-5.png" />
				<span class="c-item__name">背景设置</span>
				<div class="c-item__content"></div>
				<span class="c-item__desc">浅色模式</span>
				<img class="item-header__right" src="@/assets/img/right.png" />
			</div> -->
			<!-- 账号设置 -->
			<!-- <div class="c-item">
				<img class="c-item__icon" src="@/assets/img/me-6.png" />
				<span class="c-item__name">账号设置</span>
				<div class="c-item__content"></div>
				<img class="item-header__right" src="@/assets/img/right.png" />
			</div> -->
			<!-- 关于智体视界 -->
			<div @click="goAbout" class="c-item">
				<img class="c-item__icon" src="@/assets/img/me-4.png" />
				<span class="c-item__name">{{ $t('manage.aboutAgentBox') }}</span>
				<div class="c-item__content"></div>
				<img class="item-header__right" src="@/assets/img/right.png" />
			</div>
			<!-- 退出登录 -->
			<van-button @click="logout" class="c-btn" type="default">{{ $t('common.logout') }}</van-button>
		</div>
		<van-uploader ref="upload" style="position: fixed; left: -10000px" :after-read="afterRead" />
	</div>
</template>
<script setup>
import { userLogout } from '@/api/login';
import { showToast } from 'vant';
import { updateBySelf, uploadImg } from '@/api/user';
import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
const userInfo = reactive(userStore.userInfo);
const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance();
const id = userInfo.id;
const username = userInfo.username;
const depName = ref('');
// 部门
if (userInfo.groups.length > 0) {
	depName.value = userInfo.groups[userInfo.groups.length - 1].name;
}
const avater = ref(userInfo.avatar);
const firstName = ref(userInfo.firstName);
const goBack = () => {
	router.go(-1);
};
const goPrivacy = () => {
	router.push({
		name: 'ys',
		query: {
			isTabBar: route?.query?.isTabBar || '',
		},
	});
};

const goAbout = () => {
	router.push({
		name: 'aboutUs',
		query: {
			isTabBar: route?.query?.isTabBar || '',
		},
	});
};

const setLanguage = () => {
	router.push({
		name: 'aLanguage',
		query: {
			isTabBar: route?.query?.isTabBar || '',
		},
	});
};

const goFeedback = () => {
	router.push({
		name: 'fedBack',
		query: {
			isTabBar: route?.query?.isTabBar || '',
		},
	});
};

// 修改头像
const upload = ref(null);
const changeAvatar = () => {
	upload.value.chooseFile();
};
const afterRead = ({ file }) => {
	const formData = new FormData();
	formData.append('file', file);
	formData.append('loginName', 'demo');
	formData.append('objectName', 'demo');
	uploadImg(formData).then((res) => {
		avater.value = res.result;
		updateBySelf({
			avatar: avater.value,
			username,
			id,
		}).then(() => {
			userStore.SET_AVATAR(avater.value);
			showToast({
				position: 'top',
				message: '头像修改成功',
			});
		});
	});
};
// 推出
const logout = () => {
	userLogout().then(() => {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: '',
				title: '退出登录',
				module: 'logout',
			});
		} else {
			location.href = '/app/mobile/logout';
		}
	});
};

const blurEv = () => {
	if (firstName.value) {
		updateBySelf({
			firstName: firstName.value,
			username,
			id,
		}).then(() => {
			userStore.SET_FIRSTNAME(firstName.value);
			showToast({
				position: 'top',
				message: '昵称修改成功',
			});
		});
	}
};
</script>
<style lang="scss" scoped>
.mc {
	display: flex;
	flex-direction: column;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;

		.c-avatar {
			margin-top: 30px;
			margin-bottom: 15px;
			margin-left: 137.5px;
			width: 100px;
			height: 100px;
			position: relative;
			.c-avatar__img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				border-radius: 50%;
				display: inline-block;
			}

			.c-avatar__icon {
				position: absolute;
				right: 4px;
				bottom: 6px;
				width: 20px;
				height: 20px;
			}
		}

		.c-item {
			margin: 0 15px 12px;
			border-radius: 5px;
			background-color: #f4f5f7;
			padding: 18px;
			width: calc(100% - 30px);
			display: flex;
			align-items: center;

			.c-item__icon {
				width: 15px;
				height: 15px;
				margin-right: 11px;
			}

			.c-item__name {
				font-size: 15px;
				font-weight: 500;
				color: #172b4d;
			}

			.c-item__content {
				flex: 1;

				.c-item__input {
					border: none;
					background-color: transparent;
					padding-left: 20px;
					font-size: 15px;
					color: #172b4d;
				}
			}

			.c-item__desc {
				color: #6b778c;
				margin-right: 8px;
			}

			.item-header__right {
				width: 4px;
				height: 8px;
			}
		}

		.c-btn {
			height: 50px;
			width: calc(100% - 30px);
			margin-top: 50px;
			margin-left: 15px;
			&:deep(.van-button__text) {
				font-size: 15px;
				font-weight: 500;
				color: #172b4d;
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
