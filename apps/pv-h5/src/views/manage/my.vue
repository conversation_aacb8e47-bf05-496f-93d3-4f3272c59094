<template>
	<div class="my">
		<!-- 背景图片 -->
		<img src="@/assets/img/editor_bg.png" alt="" class="my-bg" />
		<!-- 基本信息 -->
		<div class="my-header">
			<!-- 头像 -->
			<img v-if="userInfo.avatar" :src="userInfo.avatar" alt="" />
			<img v-else src="@/assets/img/tx.png" alt="" />
			<!-- 昵称 -->
			<span class="my-header-name">{{ userInfo.firstName }}</span>
			<!-- 部门 -->
			<span class="my-header-department">{{ depName }}</span>
		</div>
		<!-- 播报 -->
		<div class="mc-header-content">
			<span id="aiCon">{{ msg }}</span>
		</div>
		<!-- 其他 -->
		<div class="my-other">
			<div v-if="btnList.some((item) => item.path === 'isMyCustomer')" @click="qesMoreEv('我的客户')" class="my-other-item">
				<div class="item-header">
					<img class="item-header__left" src="@/assets/img/my-1.png" />
					<span>{{ isRelam !== 'muqiao' ? $t('manage.myCustomer') : '我的区域' }}</span>
					<img class="item-header__right" src="@/assets/img/right.png" />
				</div>
				<div class="item-text">{{ customerNum || 0 }}位</div>
			</div>
			<div v-if="btnList.some((item) => item.path === 'isMyMetrics')" @click="qesMoreEv('我的指标')" class="my-other-item">
				<div class="item-header">
					<img class="item-header__left" src="@/assets/img/my-5.png" />
					<span>{{ $t('manage.myMetrics') }}</span>
					<img class="item-header__right" src="@/assets/img/right.png" />
				</div>
				<div v-if="isRelam !== 'muqiao'" class="item-text">已达成</div>
			</div>
			<div v-if="btnList.some((item) => item.path === 'isPlan')" @click="qesMoreEv('关键任务')" class="my-other-item">
				<div class="item-header">
					<img class="item-header__left" src="@/assets/img/my-2.png" />
					<span>{{ $t('manage.planTask') }}</span>
					<img class="item-header__right" src="@/assets/img/right.png" />
				</div>
				<div class="item-text">{{ taskNum || 0 }}个</div>
			</div>
			<div v-if="btnList.some((item) => item.path === 'isSummary')" @click="qesMoreEv('工作总结')" class="my-other-item">
				<div class="item-header">
					<img class="item-header__left" src="@/assets/img/my-3.png" />
					<span>{{ $t('manage.workSummary') }}</span>
					<img class="item-header__right" src="@/assets/img/right.png" />
				</div>
				<div class="item-text">已生成</div>
			</div>
			<div v-if="btnList.some((item) => item.path === 'isPractice')" @click="qesMoreEv('快学快问')" class="my-other-item">
				<div class="item-header">
					<img class="item-header__left" src="@/assets/img/my-4.png" />
					<span>{{ $t('manage.quickly') }}</span>
					<img class="item-header__right" src="@/assets/img/right.png" />
				</div>
				<div class="item-text">10个</div>
			</div>
			<div @click="qesMoreEv('设置')" class="my-other-item">
				<div class="item-header">
					<img class="item-header__left" src="@/assets/img/my-6.png" />
					<span>{{ $t('manage.setting') }}</span>
					<img class="item-header__right" src="@/assets/img/right.png" />
				</div>
			</div>
		</div>
		<div class="mc-daily" v-html="textScript" id="daily"></div>
	</div>
</template>
<script setup>
import { getTaskList } from '@/api/task';
import { uuid } from '@/utils/index';
import { dailyReportSummary } from '@/api/my';
import { findVisitReportList } from '@/api/randomNotes';
import { getTargetDoctor } from '@/api/user';
import useUserStore from '@/store/modules/user';
import dayjs from 'dayjs';
import { showToast } from 'vant';
import { getAgentListApi } from '@/api/agent-tools';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const domain = enterStore.enterInfo.domain;
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const userId = userStore.userInfo.username;
const userInfo = reactive(userStore.userInfo);
const customerNum = ref(0);
const depName = ref('');
const taskNum = ref(0);
const textScript = ref('');
const msg = ref('');
const isRelam = ref(enterStore.enterInfo.id);
import usefilterStore from '@/store/modules/filter';
const filterStore = usefilterStore();
// 语言环境
const language = computed(() => {
	return filterStore.language === 'zh' ? '中文' : '英文';
});

// 元素
const btnList = computed(() => {
	return perStore.systemBtnList;
});
let appId = '';
let appKey = '';
let stream = true;
const getAgentInfo = async () => {
	const res = await getAgentListApi();
	const agentList = res.result.content || [];
	for (const ele of agentList) {
		if (ele.attributes.url.indexOf('myDailyBroadcast') > -1) {
			appId = ele.attributes.appID;
			appKey = ele.attributes.appKey;
			stream = ele.attributes?.stream === 'false' ? false : true;
			break;
		}
	}
};

onMounted(async () => {
	await getAgentInfo();
	// 部门
	if (userInfo.groups.length > 0) {
		depName.value = userInfo.groups[userInfo.groups.length - 1].name;
	}
	// 每日总结
	await getDailySummary();
	// 播报
	getTaskVolumn();
	if (btnList.value.some((item) => item.path === 'isMyCustomer')) {
		// 我的客户
		getTargetDoctor({
			page: 1,
			size: 200,
			isFocus: false,
			allClient: false,
		}).then((res) => {
			customerNum.value = res.result.total || 0;
		});
	}
});
const scrollBottom = () => {
	document.querySelector('#aiCon').scrollTo({
		top: document.querySelector('#aiCon').scrollHeight,
		behavior: 'smooth',
	});
};
const getTaskVolumn = () => {
	const json = {
		data: [],
		script: document.getElementById('daily').innerText.trim() || '',
		type: 'task_summary',
		language: language.value,
	};
	// 获取今日的处理中的任务
	getTaskList(
		{ page: 0, size: 10, sort: 'plan_end_time,desc' },
		{
			taskInfoSourceType: null,
			taskInfoType: [],
			taskInfoStatusType: 'PROGRESS',
			taskInfoSearchType: 'TODAY',
		}
	).then((res) => {
		if (res.result.content.length > 0) {
			taskNum.value = res.result.content.length;
			for (const item of res.result.content) {
				let ll = {
					taskId: item.id,
					task_title: item.taskDescription,
					task_type: item.taskInfoType === 'PHONE_VISIT' ? '电话拜访' : item.taskInfoType === 'WECHAT_VISIT' ? 'Wecom拜访' : item.taskInfoType === 'OFFLINE_VISIT' ? '线下拜访' : '会议邀请',
					end_date: item.endTime,
					creatorName: item.creatorName,
					recipientName: item.recipientName,
				};
				if (item.choiceDoctorVOList.length > 0) {
					ll.hospital = '';
					let doctorName = '';
					for (const s of item.choiceDoctorVOList) {
						doctorName += s.doctorName + '，';
					}
					ll.customer = doctorName.slice(0, doctorName.length - 1);
				} else {
					ll.customer = item.doctorVO.doctorName;
					ll.hospital = item.doctorVO.hospital;
				}
				json.data.push(ll);
			}
			json.data = JSON.stringify(json.data);
		} else {
			json.data = JSON.stringify([]);
		}
		setAiMessage(json);
	});
};
const getDailySummary = () => {
	return new Promise((resolve) => {
		dailyReportSummary()
			.then((res) => {
				textScript.value = res.result.message;
			})
			.finally(() => {
				resolve();
			});
	});
};
const isLoading = ref(false);

// 会话ID
const chatId = 'wdzs' + userStore.userInfo.username;
let controller = '';
const setAiMessage = async (val) => {
	isLoading.value = true;
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = 'start';
		let radom1 = uuid();
		let radom2 = uuid();
		let res = await fetch(`${enterStore.enterInfo.agentAddress}/api/v1/chat/completions`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${appKey}`,
			},
			body: JSON.stringify({
				appId: appId,
				chatId: chatId,
				stream: stream,
				detail: true,
				variables: val,
				messages: [{ role: 'user', content: message, dataId: radom1 }],
				responseChatItemId: radom2,
			}),
		});
		if (!stream) {
			// 非流式
			isLoading.value = false;
			res.json().then((res) => {
				msg.value = res.choices[0].message.content;
			});
		} else {
			// 流式
			if (!res?.body || !res?.ok) {
				throw new Error('Request Error');
			}
			const reader = res.body?.getReader();
			const decoder = new TextDecoder('utf-8');
			let buffer = '';
			function processStreamResult(result) {
				const chunk = decoder.decode(result.value, { stream: !result.done });
				buffer += chunk;
				// 逐条解析后端返回数据
				const lines = buffer.split('\n');
				buffer = lines.pop();
				lines.forEach((line) => {
					if (line.trim().length > 0) {
						if (line.indexOf('data:') > -1 && line.split('data:')[1] !== ' [DONE]') {
							const resData = JSON.parse(line.split('data:')[1]);
							if (resData.choices && resData.choices[0].delta.content) {
								const text = resData.choices[0].delta.content;
								msg.value += text;
							}
							nextTick(() => {
								scrollBottom();
							});
						}
					}
				});
				if (!result.done) {
					return reader.read().then(processStreamResult);
				} else {
					// 这里开始播报
					nextTick(() => {
						isLoading.value = false;
					});
				}
			}
			return reader.read().then(processStreamResult);
		}
	} catch (e) {
		isLoading.value = false;
		console.error(e);
	}
};

onBeforeUnmount(() => {
	// 删除body的属性
	if (isLoading.value) controller?.abort();
});

const qesMoreEv = (val) => {
	if (userId === 'rsm_roche' && val !== '设置' && val !== '我的指标') {
		return showToast('暂无权限');
	}

	if (val === '快学快问') {
		router.push({
			name: 'intelligenceAccompanyingPractice',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else if (val === '我的客户') {
		router.push({
			name: 'customerList',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else if (val === '关键任务') {
		router.push({
			name: 'keyTask',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else if (val === '工作总结') {
		router.push({
			name: 'dailySummary',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else if (val === '设置') {
		router.push({
			name: 'editor',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else {
		router.push({
			name: 'myMetrics',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	}
};
</script>
<style lang="scss" scoped>
.my {
	background-color: var(--pv-bgc);
	color: var(--pv-default-color);
	padding: 15px;
	position: relative;
	.my-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 240px;
		object-fit: cover;
	}

	.my-header {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 12px;
		margin-bottom: 15px;
		img {
			width: 100px;
			height: 100px;
			object-fit: cover;
			border-radius: 50%;
			margin-bottom: 12px;
		}
		.my-header-name {
			font-size: 18px;
			font-weight: 500;
			color: #172b4d;
		}
		.my-header-department {
			margin-top: 3px;
			font-size: 12px;
			color: #6b778c;
		}
	}

	.mc-header-content {
		background-color: rgba(42, 43, 48, 0.8);
		border-radius: 6px;
		margin-top: 30px;
		padding: 15px;
		height: 96px;
		span {
			display: inline-block;
			color: #ffffff;
			height: 100%;
			overflow-y: auto;
		}
	}

	.my-other {
		margin-top: 15px;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		.my-other-item {
			width: 165px;
			height: 83px;
			margin-bottom: 15px;
			padding: 15px;
			background-color: #f4f5f7;
			border-radius: 6px;
			display: flex;
			flex-direction: column;

			.item-header {
				display: flex;
				align-items: center;
				margin-bottom: 8px;
				.item-header__left {
					width: 16px;
					height: 16px;
					margin-right: 10px;
				}
				span {
					font-size: 16px;
					font-weight: 500;
					color: #172b4d;
					flex: 1;
				}
				.item-header__right {
					width: 4px;
					height: 8px;
				}
			}

			.item-text {
				border-radius: 12px;
				height: 24px;
				width: fit-content;
				line-height: 24px;
				padding: 0 12px;
				background-color: rgba(0, 82, 204, 0.1);
				font-size: 12px;
				color: #0052cc;
			}
		}
	}

	.mc-daily {
		position: fixed;
		left: -3000px;
		bottom: -3000px;
	}
}
</style>
