<template>
	<div>
		<button class="generate-btn" @click="exportPPT">生成报告</button>
		<exportProgress ref="progressRef" :topThreeStepsTimes="topThreeStepsTimes" :exportPPTComplate="exportPPTComplate"></exportProgress>
	</div>
</template>
<script setup>
const progressRef = ref(null);
const topThreeStepsTimes = ref(2000);
const exportPPTComplate = ref(false);
const exportPPT = () => {
	exportPPTComplate.value = false;
	progressRef.value.progressShow = true;
	setTimeout(() => {
		exportPPTComplate.value = true;
	}, 20000);
};
</script>
<style lang="scss" scoped>
/* 按钮样式 */
.generate-btn {
	background-color: #409eff;
	color: white;
	border: none;
	padding: 10px 16px;
	font-size: 14px;
	border-radius: 6px;
	cursor: pointer;
	transition: background-color 0.3s;
	width: 100%;
}

.generate-btn:hover {
	background-color: #66b1ff;
}
</style>
