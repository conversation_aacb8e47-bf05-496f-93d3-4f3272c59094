<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ title }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div id="aiCon" class="mc-content-list clearfix">
				<div class="card">
					<div class="card__title">问题</div>
					<div class="card__body">
						<van-field rows="1" v-model="q" :readonly="route.query.id" autosize type="textarea" placeholder="请输入问题内容" />
					</div>
				</div>
				<div class="card">
					<div class="card__title">答案</div>
					<div class="card__body">
						<van-field rows="3" v-model="a" ref="aRef" autosize type="textarea" placeholder="请输入答案内容" />
						<span class="card__ai" @click="checkQ">
							<template v-if="!loading">AI回答</template>
							<template v-else> <van-loading size="12" color="#fff">思考中</van-loading></template>
						</span>
					</div>
				</div>
				<div class="btn-group">
					<van-button class="btn" type="default" block @click="goBack">取消</van-button>
					<van-button class="btn" type="primary" block @click="postData">提交</van-button>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { getKnowledgeDetail, getKnowledgeMissionDetail, postKnowledge, putKnowledge } from '@/api/knowledge';
import { showToast } from 'vant';
import { getToken } from '@/utils/auth';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
// 接口地址
const interfaceUrl = enterStore.enterInfo.interfaceAddress;
// 返回
// .match(/\d+/g)
const route = useRoute();
const router = useRouter();
const goBack = () => {
	router.go(-1);
};
const title = computed(() => {
	if (route.query.type === 'FAQ') {
		return 'FAQ';
	} else if (route.query.type === 'MISSED') {
		return '待补充';
	} else if (route.query.type === 'HISTORY') {
		return '待确认';
	} else {
		return 'FAQ';
	}
});
const q = ref('');
const a = ref('');
const loading = ref(false);
const aRef = ref();
const checkQ = async () => {
	if (loading.value) return;
	if (!q.value) {
		showToast('请输入问题');
		return;
	}
	loading.value = true;
	// checkKnowledge(q.value)
	// 	.then((res) => {
	// 		if (res.data.bool_value) {
	// 			a.value = res.data.value;
	// 		} else {
	// 			showToast(res.data.value || '未匹配到答案');
	// 		}
	// 	})
	// 	.finally(() => {
	// 		loading.value = false;
	// 	});
	try {
		let res = await fetch(`${interfaceUrl}/API-OPENGPT/knowledge/conversation/check?message=${q.value}&agent_id=${route.query.agent_id}`, {
			signal: new AbortController().signal,
			method: 'get',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body) {
			loading.value = false;
			return;
		}
		a.value = '';
		aRef.value.focus();
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let buffer = '';
		function processStreamResult(result) {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			buffer += chunk;
			// 逐条解析后端返回数据
			const lines = buffer.split('\n');
			buffer = lines.pop();
			lines.forEach((line) => {
				if (line.trim().length > 0) {
					if (line.indexOf('data:') > -1 && line.split('data:')[1] !== ' [DONE]') {
						const resData = JSON.parse(line.split('data:')[1]);
						if (resData.choices && resData.choices[0].delta.content) {
							const text = resData.choices[0].delta.content;
							a.value += text;
							aRef.value.blur();
							console.log(text);
							nextTick(() => {
								scrollBottom();
								aRef.value.focus();
							});
						}
					}
				}
			});
			if (!result.done) {
				return reader.read().then(processStreamResult);
			} else {
				loading.value = false;
				nextTick(() => {
					scrollBottom();
					aRef.value.focus();
				});
			}
		}
		reader
			.read()
			.then(processStreamResult)
			.catch((e) => {
				loading.value = false;
			});
	} catch (e) {
		console.error(e);
		loading.value = false;
	}
};
const scrollBottom = () => {
	document.querySelector('#aiCon').scrollTo({
		top: document.querySelector('#aiCon').scrollHeight,
		behavior: 'smooth',
	});
};
const postData = () => {
	if (!a.value) {
		showToast('请输入答案');
		return;
	}
	if (route.query.type === 'MISSED' || route.query.type === 'HISTORY' || !route.query.id) {
		postKnowledge({
			message_id: route.query.id,
			q: q.value,
			a: a.value,
			// collection_type: 'FAQ',
			collection_id: route.query.collection_id || undefined,
		}).then(() => {
			showToast('提交成功');
			goBack();
		});
	} else {
		putKnowledge({
			data_id: route.query.id,
			q: q.value,
			a: a.value,
			count: detail.value.update,
		}).then(() => {
			showToast('提交成功');
			goBack();
		});
	}
};
const detail = ref({
	q: '',
});
const getDetail = () => {
	if (route.query.type === 'MISSED' || route.query.type === 'HISTORY') {
		getKnowledgeMissionDetail(route.query.id).then((res) => {
			detail.value = res.data;
			a.value = detail.value.response.replaceAll(/update=(\d+)/g, '');
			q.value = detail.value.query;
			q.value = q.value.replace(/<br\s*?\/?>/g, '');
		});
	} else {
		getKnowledgeDetail({
			data_id: route.query.id,
		}).then((res) => {
			detail.value = res.data;
			q.value = detail.value.q;
			a.value = detail.value.a.replaceAll(/update=(\d+)/g, '');
			q.value = q.value.replace(/<br\s*?\/?>/g, '');
		});
	}
};
onMounted(() => {
	if (route.query.id) {
		getDetail();
	}
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
});
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100vh;
	background: #f4f5f7;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		position: relative;
		z-index: 1;
		.mc-content-list {
			flex: 1 0 0px;
			height: 0px;
			padding: 0 0;
			overflow-y: auto;
			padding-bottom: 40px;
			.card {
				margin: 15px;
				background: #fff;
				border-radius: 10px;
				padding: 10px 15px;
				&__title {
					color: #172b4d;
					font-weight: 600;
					font-size: 14px;
					margin-bottom: 10px;
				}
				&__more {
					float: right;
					display: flex;
					align-items: center;
					--van-loading-spinner-size: 16px;
				}
				&__body {
					background-color: #f4f5f7;
					--van-cell-background: #f4f5f7;
					border-radius: 10px;
					position: relative;
					:deep(.van-field) {
						border-radius: 10px;
						overflow: hidden;
					}
				}
				&__content {
					background-color: #f4f5f7;
					border-radius: 10px;
					padding: 10px;
					position: relative;
				}
				&__ai {
					position: absolute;
					right: 10px;
					bottom: 4px;
					background: linear-gradient(-45deg, rgba(0, 82, 204, 1) 0%, rgba(71, 145, 255, 1) 100%);
					border-radius: 4px;
					padding: 0 4px;
					width: 64px;
					height: 22.5px;
					line-height: 23px;
					color: #fff;
					font-size: 12px;
					text-align: center;
					--van-loading-text-font-size: 12px;
				}
				&:nth-child(2) {
					.card__body {
						padding-bottom: 30px;
					}
					:deep(.van-field__body) {
						.van-field__control {
							max-height: calc(100vh - 360px);
							overflow-y: auto;
						}
					}
				}
			}
			.btn-group {
				position: fixed;
				bottom: 0px;
				left: 0px;
				right: 0px;
				display: flex;
				gap: 15px;
				// background-color: #fff;
				padding: 8px 15px;
				.btn {
					&:first-child {
						background-color: rgba(0, 82, 204, 0.1);
						color: #0052cc;
					}
				}
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
}
// loading
@keyframes ball-beat {
	50% {
		opacity: 0.2;
		transform: scale(0.7);
	}

	100% {
		opacity: 1;
		transform: scale(1);
	}
}
</style>
<style lang="scss">
.mc {
	.moke-wrap {
		display: block !important;
	}
	.rtc-container div div img,
	.show-video {
		padding-top: 22px !important;
	}
	.rtc-human {
		min-height: 80vh;
	}
}
.mc-content--end {
	.rtc-human {
		background-image: none !important;
		min-height: auto;
	}
	.rtc-container div {
		background-image: none !important;
	}
	.rtc-container div div img,
	.show-video {
		padding-top: 0 !important;
	}
}
</style>
