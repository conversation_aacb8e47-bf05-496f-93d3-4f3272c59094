<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ agentName }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<div class="mc-content-cotainer">
				<div class="header">
					<div class="header__logo">
						<img src="@/assets/img/bfjh.png" alt="" />
					</div>
					<div class="header__info">
						<div class="header__name">{{ agentName }}</div>
						<div class="header__desc">{{ agentInfo?.attributes?.short_description }}</div>
					</div>
				</div>
				<div class="info">您好，我是您的{{ agentName }}智能体，您可以在线查看和维护产品知识、医学知识、异议处理、FAQ等相关知识。也可以在线进行异常问题的确认和补充！</div>
				<div class="count">
					<div class="item">
						<div class="item__info">
							<span class="item__num">{{ total.used_total_count }}</span>
							<span>次</span>
						</div>
						<div class="item__title">问题</div>
					</div>
					<div class="item" @click="router.push('/medicalKnowledgeMaintained/qa-list?type=HISTORY&isTabBar=' + (route?.query?.isTabBar || '') + '&agent_id=' + (route?.query?.agent_id || '') + '&title=待确认')">
						<div class="item__info">
							<span class="item__num">{{ total.session_history_total }}</span>
							<span>条</span>
						</div>
						<div class="item__title item__title--link">待确认></div>
					</div>
					<div class="item" @click="router.push('/medicalKnowledgeMaintained/qa-list?type=MISSED&isTabBar=' + (route?.query?.isTabBar || '') + '&agent_id=' + (route?.query?.agent_id || '') + '&title=待补充')">
						<div class="item__info">
							<span class="item__num">{{ total.missed_total }}</span>
							<span>条</span>
						</div>
						<div class="item__title item__title--link">待补充></div>
					</div>
				</div>
				<div class="list">
					<div class="item" v-for="item in total.total_count" :key="item.dataset_id + item.collection_id" @click="goItem(item)">
						<div class="item__info">
							<span class="item__num"> {{ item.total }} </span>
							<span> {{ item.collection_id ? '个' : '篇' }}</span>
						</div>
						<div class="item__title">
							{{ item.description }}
							<img src="@/assets/img/demo/salesInsightNurturing/arrow.png" alt="" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { getKnowledgeTotal } from '@/api/knowledge';
import useUserStore from '@/store/modules/user';
import useEnterStore from '@/store/modules/enterprise';
import usePremissionStore from '@/store/modules/premission';
const enterStore = useEnterStore();
const relam = ref(enterStore.enterInfo.id);
// 返回
// .match(/\d+/g)
const route = useRoute();
const router = useRouter();
const goBack = () => {
	router.go(-1);
};

const userStore = useUserStore();
const userInfo = computed(() => {
	return userStore.userInfo;
});
const agentName = ref('');
const agentInfo = ref({});
const total = ref({
	total: 0,
	session_history_total: 0,
	missed_total: 0,
});
const getTotal = () => {
	getKnowledgeTotal().then((res) => {
		console.log(res);
		total.value = res.data;
	});
};
const goItem = (item) => {
	let path = '/medicalKnowledgeMaintained/list';
	if (item.collection_id) {
		path = '/medicalKnowledgeMaintained/qa-list';
	}
	router.push({
		path,
		query: {
			// type: item.type,
			title: item.description,
			dataset_id: item.dataset_id || undefined,
			collection_id: item.collection_id || undefined,
			isTabBar: route?.query?.isTabBar,
			agent_id: route?.query?.agent_id,
		},
	});
};
const { GET_USER_AGENT } = usePremissionStore();
onMounted(() => {
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
	GET_USER_AGENT().then(async (res) => {
		const agentList = res;
		for (const ele of agentList) {
			if (String(ele.id) === String(route.query.agent_id)) {
				agentName.value = ele.name;
				agentInfo.value = ele;
				break;
			}
		}
		getTotal();
	});
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
});
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	background-image: url('@/assets/img/demo/salesInsightNurturing/bg.png');
	background-position: center -20%;
	background-repeat: no-repeat;
	background-size: 100%;
	background-color: #f4f5f7;

	.mc-header {
		width: 100vw;
		display: flex;
		// background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		// border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 0 15px;
			margin-bottom: 20px;
		}
	}
	.header {
		display: flex;
		align-items: center;
		margin-bottom: 13px;
		&__logo {
			width: 50px;
			height: 50px;
			box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.05);
			background-color: #fff;
			text-align: center;
			line-height: 50px;
			margin-right: 15px;
			border-radius: 50%;
			img {
				max-width: 50px;
				max-height: 50px;
			}
		}
		&__info {
			flex: 1;
			width: 0;
		}
		&__name {
			color: #172b4d;
			font-weight: 500;
			font-size: 15px;
		}
		&__desc {
			color: #6b778c;
			font-weight: 400;
			font-size: 10px;
		}
		&__icon {
			img {
				width: 14px;
			}
		}
	}
	.info {
		background-color: rgba(255, 255, 255, 0.8);
		border-radius: 16px;
		padding: 15px;
		margin-bottom: 15px;
		position: relative;
		&:before {
			content: '';
			border-left: 7px solid transparent;
			border-right: 7px solid transparent;
			border-bottom: 7px solid rgba(255, 255, 255, 0.8);
			position: absolute;
			top: -7px;
			left: 20px;
		}
	}
	.count {
		border-radius: 6px;
		background-color: #fff;
		display: flex;
		padding: 15px;
		margin-bottom: 15px;
		.item {
			text-align: center;
			color: #172b4d;
			flex: 1;
			position: relative;
			&__info {
				font-weight: 500;
				font-size: 12px;
			}
			&__num {
				font-size: 20px;
			}
			&__title {
				font-weight: 400;
				font-size: 12px;
				&--link {
					color: #0052cc;
				}
			}
			&:after {
				content: '';
				width: 1px;
				height: 30px;
				background-color: #f0f2f5;
				position: absolute;
				top: 50%;
				right: 0;
				transform: translateY(-50%);
			}
			&:last-child {
				&:after {
					display: none;
				}
			}
		}
	}
	.list {
		display: flex;
		gap: 15px;
		flex-wrap: wrap;
		.item {
			// width: 50%;
			flex: 0 0 calc(50% - 7.5px);
			background-color: #fff;
			border-radius: 10px;
			padding: 15px;
			position: relative;
			box-sizing: border-box;
			&__info {
				font-weight: 500;
				font-size: 12px;
			}
			&__num {
				font-size: 20px;
			}
			&__title {
				font-weight: 400;
				font-size: 12px;
				img {
					float: right;
					width: 5px;
				}
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
