<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ route.query.title.replace('>', '') || title }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">
				<img
					src="@/assets/img/demo/medicalKnowledgeMaintained/file.png"
					@click.stop="router.push('/medicalKnowledgeMaintained/edit?type=' + route.query.type + '&isTabBar=' + (route.query.isTabBar || '') + '&agent_id=' + (route?.query?.agent_id || '') + '&collection_id=' + (route.query?.collection_id || ''))"
					alt=""
				/>
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div class="mc-content-list">
				<van-search v-model="search_text" placeholder="请输入文件名称搜索" @search="onSearch" />
				<van-list v-model:loading="loading" :finished="finished" @load="getList">
					<template v-for="(item, index) in list" :key="item">
						<div class="item" @click="router.push('/medicalKnowledgeMaintained/edit?id=' + item._id + '&type=' + route.query.type + '&isTabBar=' + route.query.isTabBar + '&agent_id=' + route?.query?.agent_id)">
							<div class="item__header">
								<div class="item__tag">#{{ index > 10 ? index : '0' + index }}</div>
								<img src="@/assets/img/editor.png" class="item__editor" alt="" />
							</div>
							<div class="item__title">
								<van-text-ellipsis :rows="1" :content="item.q"></van-text-ellipsis>
							</div>
							<div class="item__content">{{ item.a.replaceAll(/update=(\d+)/g, '') }}</div>
							<div class="item__notice" v-if="item.feedback_reason"><img src="@/assets/img/demo/medicalKnowledgeMaintained/notice.png" alt="" /> {{ item.feedback_reason }}</div>
						</div>
					</template>
				</van-list>
			</div>
		</div>
	</div>
</template>
<script setup>
import useUserStore from '@/store/modules/user';
import pdfPng from '@/assets/img/demo/medicalKnowledgeMaintained/pdf.png';
import { getKnowledgeList, getKnowledgeMissionList } from '@/api/knowledge';
// 返回
// .match(/\d+/g)
const route = useRoute();
const router = useRouter();
const goBack = () => {
	router.go(-1);
};
const title = computed(() => {
	if (route.query.type === 'FAQ') {
		return 'FAQ';
	} else if (route.query.type === 'MISSED') {
		return '待补充';
	} else if (route.query.type === 'HISTORY') {
		return '待确认';
	}
});
const userStore = useUserStore();
const search_text = ref('');
const page = ref(1);
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const onSearch = () => {
	page.value = 1;
	list.value = [];
	getList();
};
const getList = () => {
	if (route.query.type === 'MISSED' || route.query.type === 'HISTORY') {
		console.log(route.query.type);
		getKnowledgeMissionList({
			page_size: 10,
			page_num: page.value,
			search_text: search_text.value,
			is_confirmed: route.query.type === 'HISTORY',
		}).then((res) => {
			res.data.forEach((item) => {
				list.value.push({
					_id: item.id,
					q: item.query,
					a: item.response,
					...item,
				});
			});
			loading.value = false;
			if (res.data.length < 10) {
				finished.value = true;
			} else {
				finished.value = false;
			}
			page.value++;
		});
	} else {
		getKnowledgeList({
			collection_type: route.query.type,
			page_size: 10,
			page_num: page.value,
			search_text: search_text.value,
			collection_id: route.query.collection_id || undefined,
		}).then((res) => {
			list.value = list.value.concat(res.data.list);
			loading.value = false;
			if (res.data.list.length < 10) {
				finished.value = true;
			} else {
				finished.value = false;
			}
			page.value++;
		});
	}
};
onMounted(() => {
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
});
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100vh;
	background: #fff;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		position: relative;
		z-index: 1;
		.mc-content-list {
			flex: 1 0 0px;
			height: 0px;
			padding: 0 0;
			overflow-y: auto;
			--van-search-content-background: #f4f5f7;
			--van-radius-sm: 10px;
			.item {
				border-radius: 10px;
				background-color: #f4f5f7;
				border: 1px solid #dfe1e6;
				margin: 15px;
				padding: 8px;
				&__header {
					margin-bottom: 7px;
				}
				&__tag {
					border: 1px solid rgba(0, 82, 204, 0.3);
					background-color: rgba(0, 82, 204, 0.1);
					border-radius: 10px;
					color: #0052cc;
					font-weight: 400;
					font-size: 11px;
					display: inline-block;
					padding: 0 7px;
					line-height: 17px;
				}
				&__editor {
					float: right;
					width: 12px;
					margin-top: 6px;
				}
				&__title {
					color: #172b4d;
					font-weight: 500;
					font-size: 13px;
					margin-bottom: 6px;
				}
				&__content {
					color: #6b778c;
					font-weight: 400;
					font-size: 12px;
				}
				&__notice {
					img {
						width: 14px;
					}
					margin-top: 9px;
					color: #dd340a;
					font-weight: 400;
					font-size: 12px;
				}
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
}
// loading
@keyframes ball-beat {
	50% {
		opacity: 0.2;
		transform: scale(0.7);
	}

	100% {
		opacity: 1;
		transform: scale(1);
	}
}
</style>
<style lang="scss">
.mc {
	.moke-wrap {
		display: block !important;
	}
	.rtc-container div div img,
	.show-video {
		padding-top: 22px !important;
	}
	.rtc-human {
		min-height: 80vh;
	}
}
.mc-content--end {
	.rtc-human {
		background-image: none !important;
		min-height: auto;
	}
	.rtc-container div {
		background-image: none !important;
	}
	.rtc-container div div img,
	.show-video {
		padding-top: 0 !important;
	}
}
</style>
