<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ route.query.title.replace('>', '') }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">
				<van-uploader accept="application/msexcel,application/msword,application/pdf,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.wordprocessingml.document" :after-read="afterRead">
					<img src="@/assets/img/demo/medicalKnowledgeMaintained/file.png" alt="" />
				</van-uploader>
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div class="mc-content-list">
				<van-list v-model:loading="loading" :finished="finished" @load="getList">
					<template v-for="item in list" :key="item.id">
						<van-swipe-cell class="item">
							<van-card :desc="item.name" :title="item.name" :thumb="pdfPng" @click="openPdfFile(item)" />
							<template #right>
								<van-button square type="primary" text="重命名" @click="rename(item)">
									<template #icon>
										<img src="@/assets/img/demo/medicalKnowledgeMaintained/rename.png" alt="" />
									</template>
								</van-button>
								<van-button square type="danger" text="删除" @click="del(item)">
									<template #icon>
										<img src="@/assets/img/demo/medicalKnowledgeMaintained/delete.png" alt="" />
									</template>
								</van-button>
							</template>
						</van-swipe-cell>
					</template>
				</van-list>
			</div>
		</div>
		<preview-pdf ref="pf" :apiKey="apiKey" :sourceId="sourceId"></preview-pdf>

		<van-dialog v-model:show="show" title="修改" show-cancel-button :before-close="confirm" :confirm-button-disabled="!input">
			<van-field type="textarea" rows="3" v-model="input" placeholder="请输入内容" />
		</van-dialog>
	</div>
</template>
<script setup>
import useUserStore from '@/store/modules/user';
import pdfPng from '@/assets/img/demo/medicalKnowledgeMaintained/pdf.png';
import { deleteKnowledgeCollection, getKnowledgeCollections, postKnowledgeFile, putKnowledgeCollection } from '@/api/knowledge';
import { showConfirmDialog, showToast } from 'vant';
// 返回
// .match(/\d+/g)
const route = useRoute();
const router = useRouter();
const goBack = () => {
	router.go(-1);
};
const userStore = useUserStore();
const page = ref(1);
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const getList = () => {
	getKnowledgeCollections({
		page_size: 10,
		page_num: page.value,
		dataset_id: route.query.dataset_id,
	}).then((res) => {
		if (page.value === 1) {
			list.value = [];
		}
		list.value = list.value.concat(res.data.data);
		loading.value = false;
		if (res.data.data.length < 10) {
			finished.value = true;
		} else {
			finished.value = false;
		}
		page.value++;
	});
};
const editItem = ref();
const show = ref(false);
const input = ref('');
const rename = (item) => {
	editItem.value = item;
	input.value = item.name;
	show.value = true;
};
const confirm = (action) => {
	if (action === 'cancel') {
		return true;
	}
	putKnowledgeCollection({
		collection_id: editItem.value._id,
		parent_id: editItem.value.parent_id,
		name: input.value,
	}).then(() => {
		show.value = false;
		editItem.value.name = input.value;
		return true;
	});
};
const del = (item) => {
	showConfirmDialog({
		title: '提示',
		message: '确定删除吗?',
	}).then(() => {
		deleteKnowledgeCollection({
			collection_id: item._id,
		}).then(() => {
			list.value = list.value.filter((i) => i._id !== item._id);
		});
	});
};
const afterRead = (file) => {
	console.log(file);
	const toast = showToast({
		type: 'loading',
		duration: 0,
		message: '正在上传',
	});
	postKnowledgeFile(file.file, route.query.dataset_id).then((res) => {
		toast.close();
		showToast({
			message: '上传成功',
			type: 'success',
		});
		page.value = 1;
		getList();
	});
};
const apiKey = ref('');
const sourceId = ref('');
const pf = ref();
const openPdfFile = (item) => {
	// if (item.name.slice(-3) === 'pdf') {
	// 	sourceId.value = item._id;
	// 	// 打开pdf预览组件
	// 	nextTick(() => {
	// 		pf.value.getPdfUrl();
	// 	});
	// } else {
	// 	showToast({
	// 		position: 'top',
	// 		message: '该文件暂不支持预览',
	// 	});
	// }
};
onMounted(() => {
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
});
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100vh;
	background: #fff;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		position: relative;
		z-index: 1;
		.mc-content-list {
			flex: 1 0 0px;
			height: 0px;
			padding: 0 0;
			overflow-y: auto;
			.item {
				--van-card-thumb-size: 30px;
				--van-card-text-color: #172b4d;
				--van-card-font-size: 14px;
				--van-card-desc-color: #6b778c;
				--van-card-padding: 15px 15px 0;
				--van-card-background: #fff;
				--van-button-default-height: 67px;
				:deep(van-swipe-cell__right) {
					right: -1px;
				}
				:deep(.van-button) {
					width: 80px;
					.van-button__content {
						flex-direction: column;
						font-size: 10px;
						img {
							height: 15px;
							margin-bottom: 5px;
						}
					}
				}
			}
			:deep(.van-card) {
				img {
					width: 29px;
					height: 36px;
				}
				.van-card__title {
					font-weight: 400;
				}
				.van-card__desc {
					font-size: 10px;
				}
				.van-card__content {
					border-bottom: 1px solid #f4f5f7;
					padding-bottom: 15px;
				}
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
}
// loading
@keyframes ball-beat {
	50% {
		opacity: 0.2;
		transform: scale(0.7);
	}

	100% {
		opacity: 1;
		transform: scale(1);
	}
}
</style>
<style lang="scss">
.mc {
	.moke-wrap {
		display: block !important;
	}
	.rtc-container div div img,
	.show-video {
		padding-top: 22px !important;
	}
	.rtc-human {
		min-height: 80vh;
	}
}
.mc-content--end {
	.rtc-human {
		background-image: none !important;
		min-height: auto;
	}
	.rtc-container div {
		background-image: none !important;
	}
	.rtc-container div div img,
	.show-video {
		padding-top: 0 !important;
	}
}
</style>
