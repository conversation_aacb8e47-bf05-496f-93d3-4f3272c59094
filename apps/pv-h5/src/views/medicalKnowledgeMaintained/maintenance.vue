<template>
	<div class="mc" ref="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">知识维护</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div id="aiCon" class="mc-content-list clearfix" v-scroll="handleScroll">
				<div class="mc-content-list-loading" v-if="loading">
					<van-loading />
				</div>
				<template v-for="(item, index) in allList">
					<template v-if="item.type === 'user'">
						<div :key="item.id" class="mc-content-list-right">
							<!-- 用户内容模版 -->
							<div class="mc-content-template">
								<!-- 纯文本 -->
								<pre>{{ item.msg }}</pre>
								<!-- 是否可以修改 -->
								<!-- <div class="mc-content-template__edit" v-if="index === chatList.length - 2"></div> -->
							</div>
						</div>
					</template>
					<template v-else-if="item.type === 'ai'">
						<div :key="item.id" class="mc-content-list-left">
							<div style="display: flex">
								<!-- AI 文本 内容模版 -->
								<div class="mc-content-template">
									<!-- 纯文本 -->
									<ai-text :id="item.id" :class="{ 'first-br': item.showAction }" :is-loading="item.loading" :msg="item.msg"></ai-text>
									<div class="mc-content-template__edit" v-if="item.showAction && index === allList.length - 1">
										<div class="mc-content-template__edit-desc">您可以重新回答或在此回答基础上进行优化</div>
										<van-space class="mc-content-template__edit-btn">
											<van-button size="small" @click="postAnswer">保存到我的知识库</van-button>
											<van-button size="small" @click="chAnswer" type="primary">修改优化</van-button>
											<!-- <van-button size="small" @click="reAnswer" type="primary">重新作答</van-button> -->
										</van-space>
									</div>

									<div class="mc-content-template__edit" v-if="item.showConfirm && index === allList.length - 1">
										<div class="mc-content-template__edit-desc">请您确认是否保存到知识库</div>
										<van-space class="mc-content-template__edit-btn">
											<van-button size="small" @click="postAnswer">保存到我的知识库</van-button>
											<van-button size="small" @click="chAnswer" type="primary">修改优化</van-button>
											<!-- <van-button size="small" @click="reAnswer" type="primary">重新作答</van-button> -->
										</van-space>
									</div>
								</div>
							</div>
						</div>
						<template v-if="item.qList">
							<div :key="item.id" class="mc-content-more-head">推荐其他问题</div>
							<van-space direction="vertical" class="mc-content-more" :key="item.id" v-if="item.qList">
								<template v-for="(item, idx) in item.qList" :key="idx">
									<van-button type="primary" :disabled="index !== allList.length - 1" @click="handleClickQuestion(item)" plain>{{ item.q }}</van-button>
								</template>
							</van-space>
						</template>
					</template>
					<template v-if="index === historyList.length - 1">
						<van-divider :key="item.id + index" :style="{ borderColor: 'var(--van-divider-text-color)', width: '100%' }">以上为历史消息</van-divider>
					</template>
				</template>
			</div>
			<!-- 按钮组 -->
			<div class="mc-btns">
				<div class="mc-btns-item" @click="router.push('/medicalKnowledgeMaintained/list?type=MEDICINE&isTabBar=' + route?.query?.isTabBar + '&agent_id=' + route?.query?.agent_id)">医学知识</div>
				<div class="mc-btns-item" @click="router.push('/medicalKnowledgeMaintained/list?type=PRODUCT&isTabBar=' + route?.query?.isTabBar + '&agent_id=' + route?.query?.agent_id)">产品知识</div>
				<div class="mc-btns-item" @click="router.push('/medicalKnowledgeMaintained/qa-list?type=CLIENT&isTabBar=' + route?.query?.isTabBar + '&agent_id=' + route?.query?.agent_id)">客户异议</div>
				<div class="mc-btns-item" @click="router.push('/medicalKnowledgeMaintained/qa-list?type=FAQ&isTabBar=' + route?.query?.isTabBar + '&agent_id=' + route?.query?.agent_id)">FAQ</div>
			</div>
			<!-- 聊天框 -->
			<div ref="chatBox" class="mc-content-chat">
				<ai-input :editorText="editorText" @_sendMessage="sendMessage" @_changeEdit="changeEdit"></ai-input>
			</div>
		</div>
	</div>
</template>
<script setup>
import { uuid } from '@/utils/index';
import { nextTick } from 'vue';
import { useClickAway } from '@vant/use';
// import { getConversationTotal, getQuestions, getAnswerDetail, putBusinessDataset, postBusinessDataset, checkQuestion } from '@/api/conversation';
// import { getSession, postSession } from '@/api/session';
import { vScroll } from '@vueuse/components';
import { checkKnowledge, getKnowledgeDetail, getKnowledgeHistory, getKnowledgeList, getKnowledgeMissionDetail, getKnowledgeMissionList, postKnowledge, postKnowledgeHistory, putKnowledge } from '@/api/knowledge';

defineOptions({
	name: 'ChatView',
});
// 返回
const route = useRoute();
const router = useRouter();
const goBack = () => {
	router.go(-1);
};
const typeText = {
	FAQ: 'FAQ',
	PRODUCT: '产品知识',
	CLIENT: '客户异议',
	MEDICAL_KNOWLEDGE: '医学知识',
};

const isLoading = ref(false);
const chatList = ref([]);

// 发送的消息是否是二次编辑的消息
const isEdit = ref(false);
const editorText = ref('');
const changeEdit = () => {
	isEdit.value = false;
	editorText.value = '';
};

// 文本框失去焦点
const chatBox = ref();
useClickAway(
	chatBox,
	() => {
		if (isEdit.value) {
			editorText.value = '';
		}
		isEdit.value = false;
	},
	{ eventName: 'touchend' }
);

watch(
	() => chatList.value[chatList.value.length - 1],
	(val) => {
		nextTick(() => {
			scrollBottom();
		});
		// chatList.value.forEach((item) => {
		// 	if (!item.loading) {
		// 		if (!item.push && !item.pushIng) {
		// 			item.pushIng = true;
		// 			// postSession({
		// 			// 	session_id: 0,
		// 			// 	session_type: 'KNOWLEDGE_BASE',
		// 			// 	message: [JSON.stringify(item)],
		// 			// 	created_time: new Date(item.time).toISOString(),
		// 			// })
		// 			// 	.then(() => {
		// 			// 		item.push = true;
		// 			// 	})
		// 			// 	.finally(() => {
		// 			// 		item.pushIng = false;
		// 			// 	});
		// 		}
		// 	}
		// });
	},
	{
		deep: true,
	}
);
const sendType = (text, type) => {
	messageType.value = type || '';
	sendMessage(text);
};
const getQuestionsList = async () => {
	const type = route.query.type;
	let list = [];
	if (type === 'MISSED') {
		const res = await getKnowledgeMissionList({
			page_size: 3,
			page_num: 1,
		});
		res?.data.forEach((item) => {
			list.push({
				...item,
				id: item.id,
				q: item.query,
				a: item.response,
				type: type,
			});
		});
	} else {
		const res = await getKnowledgeList({
			collection_type: type,
			page_size: 3,
			page_num: 1,
			search_text: 'update=0',
		});
		res?.data?.data.forEach((item) => {
			list.push({
				...item,
				id: item._id,
				type: type,
			});
		});
	}
	if (list.length) {
		chatList.value[chatList.value.length - 1] = {
			...chatList.value[chatList.value.length - 1],
			loading: false,
			// msg: `${res[0].question_type === '补充知识' ? '以下为患者提问后在我的知识库没有匹配到答案的问题。点击问题进行知识库的补充。' : '以下为患者提问后在我的知识库没有匹配到答案的问题。点击问题进行知识库的优化。'}`,
			qList: list,
			time: new Date().getTime(),
		};
	}
	postHistory(chatList.value[chatList.value.length - 2], chatList.value[chatList.value.length - 1]);

	activeQuestion.value = {};
	messageType.value = '';
	isLoading.value = false;
};
const activeQuestion = ref();
const handleClickQuestion = (item) => {
	activeQuestion.value = item;
	sendType(item.q, 'answer');
};
const getAnswer = async () => {
	let data = {};
	if (activeQuestion.value.type === 'MISSED' || activeQuestion.value.type === 'HISTORY') {
		const res = await getKnowledgeMissionDetail(activeQuestion.value.id);
		data = {
			...res.data,
			a: res.data.response,
			q: res.data.query,
		};
	} else {
		const res = await getKnowledgeDetail({
			data_id: activeQuestion.value.id,
		});
		data = {
			...res.data,
			a: res.data.a,
		};
	}
	chatList.value[chatList.value.length - 1] = {
		...chatList.value[chatList.value.length - 1],
		loading: false,
		msg: `以下为AI大模型针对以上问题作出的回答


${data.a}
      `,
		showAction: true,
		time: new Date().getTime(),
	};
	postHistory(chatList.value[chatList.value.length - 2], chatList.value[chatList.value.length - 1]);
	activeQuestion.value = {
		...activeQuestion.value,
		...data,
	};
	isLoading.value = false;
};

const chAnswer = () => {
	sendType('修改优化', '1');
	chatList.value[chatList.value.length - 1] = {
		...chatList.value[chatList.value.length - 1],
		loading: false,
		msg: '请将修改后的文案发送给我即可完成修改。',
		time: new Date().getTime(),
	};
	postHistory(chatList.value[chatList.value.length - 2], chatList.value[chatList.value.length - 1]);
	nextTick(() => {
		editorText.value = activeQuestion.value.a;
	});
	isLoading.value = false;
	messageType.value = 'changeAnswer';
};
const cancelAnswer = () => {
	sendType('本次操作已经终止，请重新选择待优化问题', 'cancel');
	// messageType.value = ''
};
const postAnswer = async () => {
	console.log(activeQuestion.value);
	sendType('保存到我的知识库', '2');
	if (activeQuestion.value.type === 'MISSED' || !activeQuestion.value.id) {
		postKnowledge({
			message_id: activeQuestion.value.id,
			q: activeQuestion.value.q.replace(/"/g, '\\"'),
			a: activeQuestion.value.a.replace(/"/g, '\\"'),
		});
	} else {
		await putKnowledge({
			data_id: activeQuestion.value.id,
			q: activeQuestion.value.q.replace(/"/g, '\\"'),
			a: activeQuestion.value.a.replace(/"/g, '\\"'),
			count: activeQuestion.value.update,
		});
	}
	chatList.value[chatList.value.length - 1] = {
		...chatList.value[chatList.value.length - 1],
		// loading: false,
		msg: `已经为您将问题保存到知识库中了。`,
		time: new Date().getTime(),
	};
	// activeQuestion.value = {};
	// messageType.value = '';
	// isLoading.value = false;
	getQuestionsList();
};
const addMessage = (val) => {
	chatList.value.push({
		msg: val,
		type: 'user',
		id: uuid(),
		loading: false,
		time: new Date().getTime(),
	});
	// 生产一个ai的消息
	chatList.value.push({
		msg: '',
		type: 'ai',
		id: uuid(),
		loading: true,
		time: new Date().getTime(),
	});
};
// 发送消息
const sendMessage = (val) => {
	editorText.value = '';
	if (isEdit.value) {
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isEdit.value = false;
	}
	if (!isLoading.value) {
		if (val) {
			isLoading.value = true;
			addMessage(val);
			nextTick(() => {
				scrollBottom();
			});
			if (messageType.value === '') {
				checkKnowledge(val)
					.then((res) => {
						if (res.data.bool_value) {
							chatList.value[chatList.value.length - 1] = {
								...chatList.value[chatList.value.length - 1],
								loading: false,
								msg: `${'以下为大模型针对以上问题作出的回答：'}


${res.data.value}
      `,
								showAction: true,
								time: new Date().getTime(),
							};
							activeQuestion.value = {
								q: val,
								a: res.data.value,
							};
						} else {
							chatList.value[chatList.value.length - 1] = {
								...chatList.value[chatList.value.length - 1],
								loading: false,
								msg: `${res.data.value}`,
								time: new Date().getTime(),
							};
						}
						postHistory(chatList.value[chatList.value.length - 2], chatList.value[chatList.value.length - 1]);
						isLoading.value = false;
					})
					.catch(() => {
						chatList.value[chatList.value.length - 1] = {
							...chatList.value[chatList.value.length - 1],
							loading: false,
						};
						isLoading.value = false;
					});
			} else if (messageType.value === 'answer') {
				getAnswer();
			} else if (messageType.value === 'changeAnswer') {
				activeQuestion.value = {
					...activeQuestion.value,
					a: val,
				};
				chatList.value[chatList.value.length - 1] = {
					...chatList.value[chatList.value.length - 1],
					msg: `
 为了保证我的知识库内容严谨性，需要您再次确认问题与回答：


 问：${activeQuestion.value.q}


 答：${activeQuestion.value.a}。
          `,
					loading: false,
					showConfirm: true,
					time: new Date().getTime(),
				};
				postHistory(chatList.value[chatList.value.length - 2], chatList.value[chatList.value.length - 1]);

				isLoading.value = false;
			} else if (messageType.value === 'cancel') {
				// getQuestionsList();
			}
		}
	}
};

const scrollBottom = () => {
	document.querySelector('#aiCon').scrollTo({
		top: document.querySelector('#aiCon').scrollHeight,
		behavior: 'smooth',
	});
};
const page = ref(1);
const limit = ref(10);
const loading = ref(false);
const finished = ref(false);
const historyList = ref([]);
const allList = computed(() => {
	return [...historyList.value, ...chatList.value];
});
const postHistory = (q, a) => {
	const { msg: query, ...query_data } = q;
	const { msg: response, ...meta_data } = a;
	postKnowledgeHistory({
		query: query,
		response: response,
		conversation_id: '0',
		meta_data: meta_data,
		query_data: query_data,
		created_time: new Date().toISOString(),
	});
};
const nowDate = ref(new Date().toISOString());
const getList = () => {
	loading.value = true;
	return getKnowledgeHistory({
		skip: (page.value - 1) * limit.value,
		limit: limit.value,
		created_date: nowDate.value,
	}).then((res) => {
		console.log(res);
		res.data.forEach((item) => {
			if (item.response) {
				historyList.value.unshift({
					msg: item.response,
					...item.meta_data,
				});
			}
			if (item.query) {
				historyList.value.unshift({
					msg: item.query,
					...item.query_data,
				});
			}
		});
		loading.value = false;
		if (res.data.length < limit.value) {
			finished.value = true;
		} else {
			page.value++;
		}
	});
};
const scrollTop = ref(0);
const handleScroll = async (e) => {
	if (e.y.value < 20 && e.y.value !== 0 && e.directions.top && !loading.value && !finished.value) {
		scrollTop.value = document.querySelector('#aiCon').scrollHeight - e.y.value - 2;
		await getList();
		nextTick(() => {
			e.y.value = document.querySelector('#aiCon').scrollHeight - scrollTop.value;
		});
	}
};
onMounted(async () => {
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
	await getList();
	init();
	nextTick(() => {
		scrollBottom();
	});
});
onActivated(() => {
	loading.value = true;
	nextTick(() => {
		scrollBottom();
		nextTick(() => {
			setTimeout(() => {
				loading.value = false;
			}, 300);
		});
	});
});
onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
});
const messageType = ref('');
const init = () => {
	// 初始化会话的聊天记录
	// 生产一个ai的消息
	messageType.value = '';
	isLoading.value = true;
	const hello = `Hi，我是你的知识库助手，可以帮您总结或生成知识库问答 ，让我们共同来丰富知识库吧！`;
	chatList.value.push({
		msg: hello,
		type: 'ai',
		id: uuid(),
		loading: false,
		time: new Date().getTime(),
	});
	isLoading.value = false;
	if (route.query.id) {
		handleClickQuestion({
			id: route.query.id,
			q: route.query.q,
			type: route.query.type,
		});
	}
};
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	background: var(--bg-color);
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-list {
			flex: 1;
			height: 0px;
			padding: 0 15px;
			padding-top: 12px;
			overflow-y: auto;
			color: #2a2b30;
			.mc-content-list-loading {
				text-align: center;
				margin: 15px auto;
			}
			.mc-content-list-left {
				display: flex;
				flex-direction: column;
				width: 100%;
				margin-bottom: 12px;
				.mc-content-template {
					border-top-left-radius: 8px;
					border-bottom-right-radius: 8px;
					border-top-right-radius: 8px;
					&__edit {
						border-top: 1px dashed #e1e4eb;
						margin-top: 15px;
						padding-top: 15px;
						&-desc {
							margin-bottom: 10px;
						}
						&-btn {
							display: flex;
							justify-content: end;
							--van-button-primary-background: var(--ac-bg-active-color);
							--van-button-primary-border-color: var(--ac-bg-active-color);
						}
					}
				}
				img {
					width: 26px;
					height: 26px;
					margin-right: 6px;
					margin-top: 6px;
				}
			}

			.mc-content-list-right {
				display: flex;
				width: 95%;
				float: right;
				margin-bottom: 12px;
				justify-content: flex-end;
				pre {
					margin: 0;
				}
				.mc-content-template {
					border-top-right-radius: 8px;
					border-bottom-left-radius: 8px;
					border-top-left-radius: 8px;
					color: var(--ac-bg-color--fff);
					background-color: var(--ac-bg-active-color);
					.mc-content-template__edit {
						border-top: solid 1px rgba(223, 225, 230, 0.3);
						margin-top: 2px;
					}
				}
			}

			.mc-content-template {
				max-width: calc(95vw - 30px);
				background-color: var(--ac-bg-color--fff);
				padding: 10px 15px;
			}
			.mc-content-more-head {
				color: #6b778c;
				font-weight: 400;
				font-size: 12px;
				margin-bottom: 8px;
			}
			.mc-content-more {
				--van-button-default-height: auto;
				display: flex;
				flex-wrap: nowrap;
				margin-bottom: 15px;
				overflow-y: auto;
				&::-webkit-scrollbar {
					display: none;
				}
				:deep(.van-space-item) {
					flex-shrink: 0;
				}
				:deep(.van-button) {
					border-radius: 34px;
					font-size: 14px;
					height: auto;
					max-width: 300px;
					padding: 5px 15px;
					text-align: left;
					border-radius: 12px;
				}
			}
			.mc-content-number {
				margin-bottom: 15px;
				width: 100%;
				&__item {
					background: #ffffff;
					border-radius: 8px;
					text-align: center;
					padding: 6px;
				}
				&__title {
					font-size: 12px;
					color: #2a2b30;
				}
				&__number {
					font-weight: 600;
					font-size: 32px;
				}
				&__desc {
					color: #676978;
				}
			}
		}

		// 按钮组
		.mc-btns {
			width: 100%;
			margin: 0px auto;
			padding: 4px 6px;
			overflow-x: auto;
			display: flex;
			white-space: nowrap;
			.mc-btns-item {
				margin-right: 6px;
				border: 1px solid #dfe1e6;
				padding: 4px 8px;
				border-radius: 20px;
				font-size: 14px;
				color: var(--ac-font-color);
			}
		}

		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 8px;
			padding-bottom: 6px;
			min-height: 64px;
			position: relative;
			display: flex;
			align-items: flex-start;
			.chat-img {
				width: 25px;
				margin-top: 12.5px;
				object-fit: contain;
				margin-left: 15px;
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
}
</style>
