<template>
	<div class="noexist">
		<div class="img">
			<img src="@/assets/img/401.png" alt="" />
		</div>
		<div class="tx">温馨提示：</div>
		<div class="sc">您已成功登录应用，</div>
		<div>但目前暂未配置浏览权限。</div>
		<div>如需访问更多内容，</div>
		<div>
			请联系管理
			<span class="sc">{{ a }}</span>
		</div>
		<div>感谢您的理解与支持！</div>
	</div>
</template>
<script setup>
let a = `<<EMAIL>>`;
</script>
<style lang="scss" scoped>
.noexist {
	text-align: center;
	line-height: 30px;
	color: #6b778c;
	.img {
		width: 258px;
		margin: 55px auto 22px;
		img {
			width: 100%;
		}
	}
	.tx {
		font-size: 15px;
		color: #172b4d;
		font-weight: bold;
	}
	.sc {
		color: #0052cc;
	}
	.btn {
		font-size: 15px;
		width: 295px;
		height: 40px;
		line-height: 40px;
		border-radius: 40px;
		background-color: #0052cc;
		color: #fff;
		margin: 62px auto 0;
	}
}
</style>
