<template>
	<div class="noexist">
		<div class="img">
			<img src="@/assets/img/logo.png" alt="" />
		</div>
		<div class="tx">《口袋报告使用说明》</div>
		<div class="content">
			<div class="sc">1、口袋报告系统为百济内部管理使用，其所包含的数据和信息不应被用于未经公司允许的任何其他目的。</div>
			<div class="sc">2、本系统涉及商业秘密，授权用户应根据适用法律及公司政策对此严格保密，不得以任何方式对外或对内部非授权用户披露、也不得用于其他任何非内部管理目的，授权用户违反前述规定的，将承担相应的法律责任；</div>
			<div class="sc">3、数据保密安全使用规范声明（适用于授权用户执行本系统内数据查看和下载的情形）</div>
			<div class="sc mt">您正在执行的操作涉及公司商业秘密，请确认该等操作的必要性，同时请确保相关数据不会以任何方式对外或对非授权用户披露、也不会用于其他非内部管理目的：</div>
			<div class="sc">1、仅限将数据用于内部统计分析;</div>
			<div class="sc">2、未经审批，不得向其他无权限人员提供数据；</div>
			<div class="sc">3、如后续收到删除通知，应当按照公司指引及时删除已下载的数据；</div>
			<div class="sc">4、务必保障数据安全，严格防范数据泄露</div>
		</div>
		<div class="check">
			<van-checkbox v-model="checked" shape="square">我已知悉并确认按照要求使用</van-checkbox>
		</div>
		<div class="btn" :class="{ disabled: !checked }" @click="asyncU">确定</div>
	</div>
</template>
<script setup>
import { asyncUser } from '@/api/user';
import { showLoadingToast, showSuccessToast } from 'vant';
let router = useRouter();
let checked = ref(false);
const asyncU = async () => {
	showLoadingToast({
		message: '授权中...',
		forbidClick: true,
		duration: 0,
	});
	let res = await asyncUser();
	showSuccessToast('授权成功');
	router.push('/');
};
</script>
<style lang="scss" scoped>
.noexist {
	text-align: center;
	line-height: 30px;
	color: #6b778c;
	.img {
		width: 149px;
		margin: 72px auto 50px;
		img {
			width: 100%;
		}
	}
	.tx {
		color: #000000;
		font-weight: 400;
		font-size: 25px;
		margin-bottom: 20px;
	}
	.content {
		padding: 0 38px;
		line-height: 15px;
	}
	.sc {
		color: #000000;
		font-size: 12px;
		text-align: initial;
	}
	.mt {
		margin-top: 15px;
	}
	.check {
		margin-left: 47px;
		margin-top: 89px;
		margin-bottom: 16px;
		::v-deep(.van-checkbox__icon),
		::v-deep(.van-icon) {
			width: 13px;
			height: 13px;
			font-size: 11px;
			border-radius: 2px;
			border: 1px solid #dfe1e6;
			background-color: #f4f5f7;
		}
		::v-deep(.van-checkbox__icon) {
			border: none;
		}
		::v-deep(.van-checkbox__icon--checked .van-icon) {
			background-color: #f4f5f7;
			color: #0052cc;
		}
		::v-deep(.van-checkbox__label) {
			color: #000000;
			font-size: 12px;
		}
	}
	.btn {
		font-size: 15px;
		width: 295px;
		height: 40px;
		line-height: 40px;
		border-radius: 40px;
		background-color: #0052cc;
		color: #fff;
		margin: 0px auto 0;
		transition: all 0.3s;
	}
	.disabled {
		background-color: #c3c7d1;
		pointer-events: none;
	}
}
</style>
