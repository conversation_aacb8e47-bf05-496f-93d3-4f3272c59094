<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">陪练历史</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<van-cell-group inset>
				<van-cell center is-link :to="'/xiaoiceDetail?appId=' + item.appId + '&chatId=' + item.chatId + '&isTabBar=' + route?.query?.isTabBar" v-for="(item, index) in history" :key="index" title="肺癌相关知识" :label="parseTime(item.updateTime)">
					<template #value>
						<span v-if="item.customTitle && item.customTitle !== '未完成'">{{ item.customTitle }}</span>
						<span style="color: var(--van-danger-color)" v-else>未完成</span>
					</template>
				</van-cell>
			</van-cell-group>
		</div>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import useUserStore from '@/store/modules/user';
import { uuid } from '@/utils/index';
import axios from 'axios';
import { nextTick } from 'vue';
import { parseTime } from '@/utils/pb';
import usefilterStore from '@/store/modules/filter';
const filterStore = usefilterStore();

import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
// 返回
const route = useRoute();
const router = useRouter();
const goBack = () => {
	router.go(-1);
};

const userStore = useUserStore();
onMounted(() => {
	for (const ele of filterStore.agentList) {
		if (ele.attributes.url.indexOf('intelligenceAccompanyingPractice') > -1) {
			appId = ele.attributes.appID;
			appKey = ele.attributes.appKey;
			break;
		}
	}
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
	init();
	// qesType();
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
});
const history = ref([]);
let appId = '';
let appKey = '';
const init = () => {
	// 初始化会话的聊天记录
	const data = {
		appId: appId,
	};
	axios({
		url: enterStore.enterInfo.agentAddress + '/api/core/chat/getHistories',
		method: 'POST',
		headers: {
			Authorization: `Bearer ${appKey}`,
		},
		data,
	}).then((res) => {
		if (res.data.code === 200) {
			res.data.data.forEach((item) => {
				if (item.chatId.indexOf(userStore.userInfo.username) !== -1) {
					history.value.push(item);
				}
			});
		}
	});
};
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	background-color: #f4f5f7;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		background-color: #fff;

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: 15px;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;
		padding-top: 15px;
		:deep() {
			.van-cell__title {
				color: #172b4d;
				font-size: 13px;
			}
			.van-cell__label {
				color: #6b778c;
				font-size: 10px;
			}
			.van-cell__value {
				color: var(--van-primary-color);
				font-size: 18px;
			}
			.van-cell-group--inset {
				overflow-y: auto;
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
}
</style>
