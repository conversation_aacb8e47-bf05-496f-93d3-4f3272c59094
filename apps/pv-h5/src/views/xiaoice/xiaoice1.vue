<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('chat.intelligenceAccompanyingPractice') }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">
				<img src="@/assets/img/history.png" @click="router.push('xiaoiceHistory?isTabBar=' + route?.query?.isTabBar)" alt="" />
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="{ 'mc-content--end': isEnd }">
			<!-- 聊天列表 -->
			<div class="mc-content-list"></div>
			<!-- 聊天框 -->
			<div ref="chatBox" class="mc-content-chat" v-if="!isEnd">
				<div class="chat-status" v-if="isEdit">
					<template v-if="loading">
						<div class="loading">
							加载中
							<div class="q-loading">
								<div></div>
								<div></div>
								<div></div>
							</div>
						</div>
					</template>
					<template v-else-if="voiceLoading">
						<div class="loading">
							<div>
								<template v-if="voiceText">
									{{ voiceText }}
								</template>
								<template v-else-if="!voiceIsStar"> 请稍候 </template>
								<template v-else> 请开始说话 </template>
								<div class="q-loading">
									<div></div>
									<div></div>
									<div></div>
								</div>
							</div>
						</div>
					</template>
					<template v-else-if="error">
						<div class="error">
							{{ error }}
						</div>
					</template>
					<template v-else-if="chatList.length">
						<div class="text">
							<ai-text :id="chatList[chatList.length - 1].id" :is-loading="chatList[chatList.length - 1].loading" :msg="chatList[chatList.length - 1].msg"></ai-text>
						</div>
					</template>
				</div>
				<ai-input2 @_sendMessage="sendMessage" @_changeEdit="changeEdit" @_changeVoice="changeVoice" @_stop="stop"></ai-input2>
			</div>
			<div class="mc-content-score" v-else-if="chatList.length">
				<div class="score-desc">{{ scoreDesc }}</div>
				<div class="score-number">{{ score }} <span>分</span></div>
				<div class="score-text">
					<ai-text :id="chatList[chatList.length - 1].id" :is-loading="chatList[chatList.length - 1].loading" :msg="chatList[chatList.length - 1].msg"></ai-text>
				</div>
				<div class="score-history">
					<van-button type="text" :to="'/xiaoiceHistory?appId=' + route.query.appId">查看历史成绩</van-button>
				</div>
				<div class="score-btn">
					<van-button type="primary" block @click="reset">再试一次</van-button>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import useUserStore from '@/store/modules/user';
import ClipboardJS from 'clipboard';
import CryptoJS from 'crypto-js';
import AudioPlayer from '@/utils/ap';
import { uuid, getRandomString } from '@/utils/index';
import { getQuestionType } from '@/api/user';
const audioPlayer = new AudioPlayer();
import axios from 'axios';
import { nextTick } from 'vue';
import { useClickAway } from '@vant/use';
import { xiaoiceAuth, xiaoiceInvalidate } from '@/api/agent-tools';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
// 返回
// .match(/\d+/g)
const route = useRoute();
const router = useRouter();
const goBack = () => {
	router.go(-1);
};

const userStore = useUserStore();

const isLoading = ref(false);
const isEnd = ref(false);
const score = ref();
const scoreDesc = computed(() => {
	if (score.value >= 90) {
		return '成绩优异，继续保持，再创佳绩！';
	} else if (score.value >= 60) {
		return '成绩良好，继续加油，更上一层楼！';
	} else if (score.value >= 30) {
		return '表现尚可，努力会有更大进步！';
	} else {
		return '成绩有待提升，加油，未来可期！';
	}
});
const chatList = ref([]);
const isEdit = ref(true);
const changeEdit = (val) => {
	isEdit.value = val;
};

const voiceLoading = ref(false);
const voiceText = ref('');
const voiceIsStar = ref(false);
const stop = async () => {
	await stopXiaoIce();
	await changeTitle();
	goBack();
};
const changeVoice = async (val, text, isStar) => {
	console.log(val, text);
	if (val && talking.value) {
		await stopXiaoIce();
	}
	voiceLoading.value = val;
	voiceText.value = text;
	voiceIsStar.value = isStar;
};

// 发送消息
const sendMessage = (val) => {
	console.log(val, isLoading.value);
	if (!isLoading.value) {
		if (val) {
			isLoading.value = true;
			chatList.value.push({
				msg: val,
				dataId: '',
				type: 'user',
				chatId: chatId.value,
				id: uuid(),
				loading: false,
				zeStatus: '0',
				isGuess: true,
			});
			// 生产一个ai的消息
			chatList.value.push({
				msg: '',
				dataId: '',
				type: 'ai',
				chatId: chatId.value,
				templateType: 'text',
				id: uuid(),
				loading: true,
				zeStatus: '0',
				isGuess: true,
				guessList: [],
				filesList: [],
			});
			setAiMessage(val);
		}
	}
};
const reset = () => {
	stopXiaoIce();
	isEnd.value = false;
	chatList.value = [];
	isEdit.value = true;
	score.value = 0;
	random.value = getRandomString();
	talk('您好，我是您的医学知识陪练，随时帮助您学习医学相关知识，接下来我将会问您一系列问题，请您根据实际情况回答，回答完毕后我们会为您提供本次练习的评价，请点击开始。');
};
let controller = '';
const setAiMessage = async (val) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = val || '';
		let radom1 = uuid();
		let radom2 = uuid();
		let res = await fetch(`${enterStore.enterInfo.agentAddress}/api/v1/chat/completions`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: import.meta.env.VITE_APP_ENV === 'development' ? `Bearer appKey-tgLnHyZrzz9k9QSotTKz7sjc1g5bqt2yrxqizqKY9JsvH4WZxLaQFnjO9Kx8sLKo` : `Bearer appKey-ZY1yAOHcPVGUjnPw3nr18LycVeAZaUEM2ju2uisi6EMybxClWQL4IBg6K1U`,
			},
			body: JSON.stringify({
				appId: route.query.appId || '6678e25a6ec3dc39f17da9e3',
				chatId: chatId.value,
				stream: true,
				detail: true,
				variables: {},
				messages: [
					{ role: 'user', content: message, dataId: radom1 },
					{ role: 'assistant', content: '', dataId: radom2 },
				],
			}),
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Request Error');
		}
		// 插入 dataId
		chatList.value[chatList.value.length - 2].dataId = radom1;
		chatList.value[chatList.value.length - 1].dataId = radom2;

		const reader = res.body?.getReader();
		const decoder = new TextDecoder('utf-8');
		const isLast = chatList.value.length === 7;
		let buffer = '';
		let xiaoiceText = '';
		function processStreamResult(result) {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			buffer += chunk;
			// 逐条解析后端返回数据
			const lines = buffer.split('\n');
			buffer = lines.pop();
			lines.forEach(async (line) => {
				if (line.trim().length > 0) {
					if (line.indexOf('data:') > -1 && line.split('data:')[1] !== ' [DONE]') {
						const resData = JSON.parse(line.split('data:')[1]);
						// eslint-disable-next-line no-debugger
						if (resData.choices && resData.choices[0].delta.content) {
							const text = resData.choices[0].delta.content;
							if (!chatList.value[chatList.value.length - 1].msg.length && !isLast) {
								await stopXiaoIce();
							}
							if (!isLast) {
								chatList.value[chatList.value.length - 1].msg += text;
							}
							if (text.indexOf('。') !== -1 && !isLast) {
								const textArray = text.split('。');
								for (let i = 0; i < textArray.length; i++) {
									if (textArray[i].length > 0) {
										xiaoiceText += textArray[i];
									}
									if (i < textArray.length - 1) {
										xiaoiceText += '。';
										talk(xiaoiceText);
										xiaoiceText = '';
									}
								}
							} else {
								xiaoiceText += text;
							}
						} else if (Array.isArray(resData)) {
							const filesList = [];
							const lar = resData.filter((ele) => ele.moduleType === 'datasetSearchNode');
							if (lar.length > 0 && lar[0].quoteList) {
								let num = 0;
								for (const k of lar[0].quoteList) {
									if (k.sourceId) {
										num++;
										filesList.push({
											q: k.q.replace(/\n/g, '<br/>'),
											sourceId: k.sourceId,
											sourceName: k.sourceName,
											isShow: !filesList.some((ele) => ele.sourceId === k.sourceId),
											num,
										});
									}
								}
							}
							chatList.value[chatList.value.length - 1].filesList = filesList;
						}
						nextTick(() => {
							scrollBottom();
						});
					}
				}
			});
			if (!result.done) {
				return reader.read().then(processStreamResult);
			} else {
				isLoading.value = false;
				chatList.value[chatList.value.length - 1].loading = false;
				if (isLast) {
					isEnd.value = true;
					chatList.value[chatList.value.length - 1].msg = xiaoiceText;
					let { msg } = chatList.value[chatList.value.length - 1];
					if (msg) {
						// 总分为100分，我给您的评分是20分。
						let numberArray = msg.match(/\d+分/g);
						console.log(numberArray);
						if (!numberArray || !numberArray.length) {
							// 评分是20/100
							numberArray = msg.match(/\d+\/\d+/g);
							numberArray = numberArray ? numberArray[0].split('/') : numberArray;
						}
						if (numberArray.length === 1) {
							score.value = parseInt(numberArray[0].replace('分', ''));
						} else if (numberArray.length === 2) {
							numberArray.map((item) => Number(item.replace('分', ''))).sort((a, b) => a - b);
							console.log(numberArray);
							score.value = numberArray[1];
						}
						if (score.value === '') {
							score.value = 0;
						}
					}
					changeTitle();
				}
				if (xiaoiceText) {
					talk(xiaoiceText);
					xiaoiceText = '';
				}
			}
		}
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

const changeTitle = () => {
	const title = score.value ? `${score.value}分` : '未完成';
	const data = {
		appId: route.query.appId || '6678e25a6ec3dc39f17da9e3',
		chatId: chatId.value,
		customTitle: title,
		title,
	};
	axios({
		url: enterStore.enterInfo.agentAddress + '/api/core/chat/updateHistory',
		method: 'POST',
		headers: {
			Authorization: import.meta.env.VITE_APP_ENV === 'development' ? `Bearer appKey-tgLnHyZrzz9k9QSotTKz7sjc1g5bqt2yrxqizqKY9JsvH4WZxLaQFnjO9Kx8sLKo` : `Bearer appKey-ZY1yAOHcPVGUjnPw3nr18LycVeAZaUEM2ju2uisi6EMybxClWQL4IBg6K1U`,
		},
		data,
	}).then((res) => {
		if (res.data.code === 200) {
			console.log(res.data.data);
		}
	});
};

// 请求或者解析失败提示并删除消息
const errorFun = (e) => {
	if (e.message !== 'signal is aborted without reason') {
		showToast({
			position: 'top',
			zIndex: 8888,
			message: '请求失败,请重试',
		});
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isLoading.value = false;
	}
};

const error = ref();
const loading = ref(false);
const talking = ref(false);
const xiaoiceClient = ref();
const signature = ref('');
const initXiaoice = async () => {
	console.log(111, window.RTCInteraction);
	error.value = '';
	loading.value = true;
	if (window.RTCInteraction) {
		const { signature: signature1 } = await xiaoiceAuth();
		signature.value = signature1;
		nextTick(() => {
			console.log(document.querySelector('.mc-content-list'));
			xiaoiceClient.value = new window.RTCInteraction({
				mountClass: 'mc-content-list',
				signature: signature.value,
				projectId: '1eb7e0e4-2477-11ef-b07e-75e13e9c01ce',
				// exclusiveVirtualHumanId: '',
				includeUI: true, //是否需要展示在员⼯平台配置的ui，置false只展示虚拟⼈
				showDefaultStaticImage: true, // 否在开启交互前后，展示默认数字⼈形象。初始化sdk后，拉流前显示静态数字⼈形象图片，拉流结束（endRTC后）显示静态数字⼈形象图片
				bitrateEnum: 'R_1080P', // R_1080P、R_720P
				onError: (e) => {
					error.value = e.message ?? e;
					console.log('error', e);
					loading.value = false;
				},
				onInited: (res) => {
					console.log('inited', res);
					startXiaoIce();
				},
				onPlayStream: (v3orv4) => {
					console.log(v3orv4);
					talk('您好，我是您的医学知识陪练，随时帮助您学习医学相关知识，接下来我将会问您一系列问题，请您根据实际情况回答，回答完毕后我们会为您提供本次练习的评价，请点击开始。');
				},
				onStopStream: () => {
					console.log('stop');
					// error.value = '断开';
				},
				onTalkStart: (talkRes) => {
					error.value = '';
					loading.value = false;
					talking.value = true;
					if (!chatList.value.length) {
						chatList.value.push({
							msg: talkRes.content,
							dataId: '',
							type: 'ai',
							chatId: chatId.value,
							templateType: 'text',
							id: uuid(),
							loading: false,
							zeStatus: '0',
							isGuess: true,
							guessList: [],
							filesList: [],
						});
					}
					console.log('播放开始', talkRes);
				},
				onTalkEnd: (talkRes) => {
					console.log('播放结束', talkRes);
					talking.value = false;
				},
			});
			console.log(xiaoiceClient.value);
		});
	}
};
const stopXiaoIce = async () => {
	await xiaoiceClient.value?.breakTalking();
	// xiaoiceClient.value?.endRTC();
};
const startXiaoIce = () => {
	if (xiaoiceClient.value) {
		xiaoiceClient.value.startRTC();
	}
};
const talk = (message) => {
	console.log(message);

	message = message.replace(/#/g, '').replace(/\*/g, '');
	console.log(message);
	xiaoiceClient.value?.talk(message, true);
};
onMounted(() => {
	initXiaoice();
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
	xiaoiceClient.value?.endRTC();
	xiaoiceClient.value?.destroy();
	if (signature.value) {
		xiaoiceInvalidate(signature.value);
		signature.value = '';
	}
});
// 会话ID
const random = ref(getRandomString());
const chatId = computed(() => userStore.userInfo.username + '--' + random.value);
const scrollBottom = () => {
	document.querySelector('.chat-status').scrollTo({
		top: document.querySelector('.chat-status').scrollHeight,
		behavior: 'smooth',
	});
};
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100vh;
	background: #f4f5f7;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		position: relative;
		z-index: 1501;
		// border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		// display: flex;
		// flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		position: fixed;
		top: 0;
		width: 100vw;
		height: 100vh;
		&--end {
			top: auto;
			height: auto;
			.mc-content-list {
				width: 193px;
				height: 300px !important;
				overflow: hidden;
				font-size: 18px !important;
			}
			.mc-content-score {
				padding: 15px;
				margin-top: -40px;
				position: relative;
				z-index: 3;
				.score-desc {
					width: 136px;
					top: 70px;
					left: 182px;
					border-radius: 12px;
					background-color: #ffffff;
					padding: 12px;
					position: fixed;
				}
				.score-number {
					color: #0052cc;
					font-weight: 400;
					font-size: 80px;
					top: 170px;
					left: 200px;
					position: fixed;
					span {
						font-weight: 600;
						font-size: 20px;
					}
				}
				.score-text {
					border-radius: 12px;
					background-color: #ffffff;
					height: 30vh;
					max-height: calc(100vh - 300px - 80px);
					overflow-y: auto;
					padding: 12px;
					:deep() {
						h3 {
							text-align: center;
						}
					}
				}
				.score-history {
					position: fixed;
					bottom: 50px;
					text-align: center;
					left: 0;
					right: 0;
					.van-button {
						background-color: transparent;
						border: 0;
						color: var(--van-primary-color);
					}
				}
				.score-btn {
					position: fixed;
					bottom: 10px;
					left: 12px;
					right: 12px;
				}
			}
		}
		.mc-content-list {
			height: 100%;
			transition: font-size 0.3s;
		}
		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			position: fixed;
			bottom: 0px;
			height: 20vh;
			background: linear-gradient(179deg, rgba(244, 245, 247, 0.44) 8%, #f4f5f7 14%);
			z-index: 3;
			box-sizing: border-box;
			padding: 15px;
			padding-top: 40px;
			.chat-status {
				background-color: #fff;
				border-radius: 10px;
				position: absolute;
				left: 15px;
				right: 15px;
				height: 180px;
				top: -150px;
				padding: 10px;
				overflow-y: auto;
				.loading {
					display: flex;
					align-items: center;
					justify-content: center;
					text-align: center;
					overflow-y: auto;
					height: 100%;
					.q-loading,
					.q-loading > div {
						position: relative;
						box-sizing: border-box;
					}
					.q-loading {
						display: inline-block;
						font-size: 0;
						color: #dcdfe5;
						vertical-align: 3px;
					}
					.q-loading.la-dark {
						color: #ffffff;
					}
					.q-loading > div {
						display: inline-block;
						float: none;
						background-color: currentColor;
						border: 0 solid currentColor;
					}

					.q-loading > div {
						width: 6px;
						height: 6px;
						margin-right: 5px;
						border-radius: 100%;
						animation: ball-beat 1.5s -0.3s infinite linear;
					}
					.q-loading > div:last-child {
						margin-right: 0;
					}
					.q-loading > div:nth-child(2n-1) {
						animation-delay: -1s;
					}
				}
				.error {
					color: var(--van-warning-color);
					text-align: center;
				}
			}
			.chat-bottom {
				padding: 0 16px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.chat-bottom-middle {
					flex: 1;
				}

				img {
					width: 20px;
					height: 22px;
					object-fit: contain;
					margin-left: 15px;
				}
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
}
// loading
@keyframes ball-beat {
	50% {
		opacity: 0.2;
		transform: scale(0.7);
	}

	100% {
		opacity: 1;
		transform: scale(1);
	}
}
</style>
<style lang="scss">
.mc {
	.moke-wrap {
		display: block !important;
	}
	.rtc-container div div img,
	.show-video {
		padding-top: 22px !important;
	}
	.rtc-human {
		min-height: 80vh;
	}
}
.mc-content--end {
	.rtc-human {
		background-image: none !important;
		min-height: auto;
	}
	.rtc-container div {
		background-image: none !important;
	}
	.rtc-container div div img,
	.show-video {
		padding-top: 0 !important;
	}
}
</style>
