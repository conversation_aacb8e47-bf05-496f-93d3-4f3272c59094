<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="openHistory" class="mc-header-icon">
				<img src="@/assets/img/history_card.png" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('chat.myAssistant') }}</div>
			<!-- 新建会话 -->
			<div @click="newChat" class="mc-header-icon">
				<img src="@/assets/img/news-chat.png" />
			</div>
		</div>
		<!-- 中部 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div id="aiCon" class="mc-content-list clearfix">
				<!-- 为空时的欢迎组件 -->
				<empty-welcome v-if="chatList.length === 0"></empty-welcome>
				<template v-else>
					<template v-for="(item, index) in chatList" :key="item.id || index">
						<component v-if="canRenderCard" @_stopFetch="stopFetch" @_sendMessage="sendMessage" @_editorMsg="editorMsg" :ref="setChatComponentRef(index)" :skillInfo="acticeSkill" :chat-num="index" :is="com(item)" @_ze="zeEv" @voiceEv="voiceEv"></component>
					</template>
				</template>
				<div id="page-footer-sentinel"></div>
				<Transition name="slide">
					<div v-if="showScrollDown" class="scroll-down" @click="scrollBottom()">
						<img src="@/assets/img/scrolldown.png" alt="" />
					</div>
				</Transition>
			</div>
			<!-- 声明 -->
			<div class="mc-declare">数据和资料持续补充中，仅供内部参考</div>

			<!-- 技能类型 -->
			<skill-type :skillList="skillList" :activeAgent="acticeSkill.name" @_qesAgent="changeChoiceAgent"></skill-type>
			<!-- 聊天组件-->
			<div ref="chatBox" class="mc-content-chat">
				<ai-voice
					:agentName="acticeSkill.name"
					:agentFunction="acticeSkill.agentFunction"
					:sMenu="false"
					:ol="true"
					:toolsList="[]"
					:editorText="editorText"
					@_changeEdit="changeEdit"
					@_delAgent="initAgent"
					@_changeDeepSeek="changeDeepSeek"
					@_sendMessage="sendMessage"
					@_changeInternet="changeInternet"
					@_scrollBottom="scrollBottom"
				></ai-voice>
			</div>
		</div>
		<!-- 左侧 历史记录 -->
		<chatHistory :historyList="historyList" @_openChat="openChat" @_openHistoryPage="openHistoryPage" @_newChat="newChat" ref="cHis"></chatHistory>
		<!-- 踩的理由 -->
		<van-action-sheet class="cai-action-sheet" v-model:show="caiShow" title="反馈">
			<van-field v-model="zeJson.feedback_reason" type="textarea" placeholder="请输入您觉得回答不满意的地方" />
			<van-button color="#0B67E4" @click="caiSubmit">提交</van-button>
		</van-action-sheet>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import { useClickAway } from '@vant/use';
import { getHistoryApi, likeMsgApi, cancelLikeMsgApi, dislikeMsgApi, cancelDislikeMsgApi } from '@/api/agent-tools';
import { ruleAllList } from '@/utils/index';
// 技能组件
import skillType from './components/skillType.vue';
// 历史回话组件
import chatHistory from './components/chatHistory.vue';

//会话
import { useScriptAssistant } from '@/hooks/web/useScriptAssistant.js';
const { createUserMsg, createAiMsg, zeJson, caiShow, getCardData, canRenderCard } = useScriptAssistant();

//界面滚动
import { useScriptAssistantScroll } from '@/hooks/web/useScriptAssistantScroll';
const { scrollBottom, observeVisibility, showScrollDown, isExpand, observeExpansion } = useScriptAssistantScroll();

const { com } = useMessage();
const { chatId, chatList, isLoading, getChatList, setChatId, initChat, updateChatDesc } = useAssistant();

//语音
import { useScriptAssistantAudioPlayer } from '@/hooks/web/useScriptAssistantAudioPlayer.js';
const { voiceEv, closeAudioVoice, language } = useScriptAssistantAudioPlayer(chatList);

import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const route = useRoute();
const router = useRouter();

// 技能类型
const skillList = ref([
	{
		name: '业绩播报',
		skillType: 'intelligentAnalysis',
	},
	{
		name: '智能分析',
		skillType: 'intelligentAnalysis',
	},
]);
// 选中的技能
const acticeSkill = ref({
	name: '',
	skillType: '',
	agentFunction: {
		// 深度研究
		deepseek: '',
		// 联网搜索
		internet: '',
	},
});

// 引用所有动态组件的 ref
const chatComponentRefs = ref([]);
// 生成 ref 函数
const setChatComponentRef = (index) => (el) => {
	chatComponentRefs.value[index] = el;
};

// 改变智能体类型的功能
const changeDeepSeek = async (val) => {
	acticeSkill.value.agentFunction.deepseek = val;
	if (val) {
		// 是否需要生成chatId和修改会话名称
		await createChatIdandUpTitle(0, '系统报告生成');
		// 生成一个ai-message,让用户手动选择报告
		chatList.value.push(createAiMsg('', 'generateSystemReport', '', true, true));
		acticeSkill.value.skillType = acticeSkill.value.skillType + 'DeepSeekReport';
	} else {
		acticeSkill.value.skillType = acticeSkill.value.skillType.replace('DeepSeekReport', '');
	}
};
const changeInternet = (val) => {
	acticeSkill.value.agentFunction.internet = val;
};
// 改变选中的智能体
const changeChoiceAgent = async (val) => {
	if (val.name === '业绩播报') {
		// 是否需要生成chatId和修改会话名称
		await createChatIdandUpTitle(0, val.name);
		// 走业绩播报的线路
		chatList.value.push(createAiMsg(t('common.gdprfy'), val.skillType, '', true, true));
	} else {
		acticeSkill.value = {
			name: val.name,
			skillType: val.skillType,
			agentFunction: {
				// 深度研究
				deepseek: false,
				// 联网搜索
				internet: false,
			},
		};
		// 页面加载完毕
		nextTick(() => {
			scrollBottom();
		});
	}
};

// 发送的消息是否是二次编辑的消息
const isEdit = ref(false);
const editorText = ref('');
const editorMsg = (index) => {
	let msg = chatList.value[index].msg.replace(/<br\s*\/?\s*>/gi, '\n');
	editorText.value = msg + 'jjjjjj' + new Date().getTime();
	isEdit.value = true;
};
const changeEdit = () => {
	isEdit.value = false;
	editorText.value = '';
};
// 文本框失去焦点
const chatBox = ref();
useClickAway(
	chatBox,
	() => {
		if (isEdit.value) {
			editorText.value = '';
		}
		isEdit.value = false;
	},
	{ eventName: 'touchend' }
);

// 历史记录
const cHis = ref(null);
const openHistory = () => {
	cHis.value.showLeft = true;
};
// 历史页面
const openHistoryPage = () => {};

// 新会话
const newChat = () => {
	// 中断上次的回话请求
	stopFetch();
	chatList.value = [];
	setChatId('');
	initAgent();
};

// 发出消息
const sendMessage = async (val) => {
	// 确保 val 存在且有效
	if (!val) {
		showToast({
			position: 'top',
			message: '请输入内容',
		});
		return;
	}
	// 这里需要校验是否是二次编辑的消息
	if (isEdit.value) {
		// 清除chatList最后2位 ---- 注意这里没有删除库里的历史记录，
		// 如果有需要可以通过消息体里的message_id删除消息
		chatList.value.splice(-2);
		isEdit.value = false;
	}
	// 先停止当前请求
	stopFetch();
	// 生成一条userMessage
	const message = typeof val === 'string' ? val : val.title;
	chatList.value.push(createUserMsg(message));
	// 是否需要生成chatId和修改会话名称
	await createChatIdandUpTitle(1);
	// 判断是否需要走意图识别
	chatList.value.push(createAiMsg('', acticeSkill.value.skillType, message));
	scrollBottom();
};

const stopFetch = () => {
	if (isLoading.value) {
		//  中断请求 --- 获取最后一条ai消息，并终止请求
		const num = chatList.value
			.map((item, index) => ({ ...item, index }))
			.reverse()
			.find((item) => item.type === 'ai')?.index;
		chatComponentRefs.value[num]?.stopFetch();
	}
};

// 是否需要生成chatId和修改会话名称
const createChatIdandUpTitle = async (num, title = '') => {
	if (chatId.value || chatList.value.length > num) return;
	try {
		await initChat();
		await updateChatDesc(num === 0 ? title : '');
	} catch (error) {
		console.error('[chatId初始化失败]', error);
	}
};

// 打开有历史的会话记录
const openChat = ({ id }) => {
	if (id === chatId.value) return;
	if (isLoading.value) {
		showToast({
			message: '正在回答，请稍后...',
			position: 'top',
		});
		return;
	}
	chatList.value = [];
	setChatId(id);
	getChatListFun();
};

// 初始化智能体
const initAgent = () => {
	acticeSkill.value = {
		name: '',
		skillType: 'llmMessage',
		agentFunction: {
			// 深度研究
			deepseek: '',
			// 联网搜索
			internet: '',
		},
	};
};

// 初始化
const init = async () => {
	// 回话id
	if (route.query.chatId) {
		setChatId(route.query.chatId);
		getChatListFun();
	}
	// 初始化智能体
	initAgent();
	// 会话列表
	getHistoryList();
};
// 根据会话id获取对话详情
const getChatListFun = async () => {
	await getChatList(chatId.value);
	// TODO: 时而有效，时而无效
	scrollBottom();
};

// 回话列表
const historyList = ref([]);
const getHistoryList = async () => {
	let res = await getHistoryApi({ page: 1, size: 100 });
	let allList = res.data.items.filter((ele) => ele.description);
	allList = allList.reduce((acc, cur) => {
		const index = acc.findIndex((item) => item.created_at.split('T')[0] === cur.created_at.split('T')[0]);
		if (index !== -1) {
			acc[index].children.push(cur);
		} else {
			acc.push({
				created_at: cur.created_at,
				name: cur.created_at.split('T')[0],
				children: [cur],
			});
		}
		return acc;
	}, []);
	if (allList.length > 0) {
		allList = ruleAllList(allList, language.value);
	}
	historyList.value = allList;
};

//点踩提交
const caiSubmit = () => {
	if (!zeJson.feedback_reason) {
		showToast({
			message: '请输入您觉得回答不满意的地方',
		});
		return false;
	}
	caiShow.value = false;
	const j = chatList.value.filter((ele) => ele.id === zeJson.id)[0];
	dislikeFeedbackApi(j.dataId, { feedback: zeJson.feedback_reason });
};
watch(caiShow, (n) => {
	if (n === false) {
		if (!zeJson.feedback_reason) {
			const j = chatList.value.filter((ele) => ele.id === zeJson.id)[0];
			cancelDislikeMsgApi(j.dataId);
			j.zeStatus = '0';
		}
	}
});
//点赞点踩
const zeEv = ({ id, like, remark = '' }) => {
	const j = chatList.value.filter((ele) => ele.id === id)[0];
	if (like === '2') {
		// 踩
		zeJson.id = id;
		zeJson.feedback_reason = '';
		caiShow.value = true;
		dislikeMsgApi(j.dataId);
		j.zeStatus = like;
		return;
	} else if (like === '1') {
		// 赞
		likeMsgApi(j.dataId);
	} else {
		cancelLikeMsgApi(j.dataId);
		cancelDislikeMsgApi(j.dataId);
	}
	j.zeStatus = like;
};
// 1. 创建一个计算属性来动态计算 bottom 的值
const bottomPosition = computed(() => {
	return isExpand.value ? '400px' : '200px';
});
onMounted(async () => {
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
	chatId.value = '';
	chatList.value = [];
	isLoading.value = false;
	init();
	await getCardData();
	observeVisibility(document.querySelector('#page-footer-sentinel'));
	observeExpansion();
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
	// 如果正在请求，取消请求
	isLoading.value && stopFetch();
	// 语音停止播放
	closeAudioVoice();
});
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 20px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;

		// 回话列表
		.mc-content-list {
			flex: 1 0 0px;
			overflow-y: auto;
			padding: 12px 0 0 0;
			scroll-behavior: smooth;
			position: relative;
		}

		// 声明
		.mc-declare {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 3px 0px;
			font-size: 12px;
			color: var(--ac-colors-myGray-500);
		}

		// 聊天窗口
		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 3px;
			position: relative;
			display: flex;
			align-items: flex-start;
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 120px);
	}

	.mc-content__app {
		height: calc(100vh - 120px);
	}
	:deep(.cai-action-sheet) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 0;
		.van-action-sheet__header {
			font-size: 15px;
			color: #172b4d;
			font-weight: 500;
		}
		i {
			font-size: 16px;
			color: #000000;
		}

		.van-cell {
			background-color: #f4f5f7;
			margin: 0 15px 15px;
			width: calc(100% - 30px);
			border-radius: 6px;
			height: 125px;
		}

		.van-button {
			width: 238px;
			height: 40px;
			border-radius: 21px;
			font-size: 17px;
			margin-bottom: 20px;
			margin-left: 68.5px;
		}
	}
	.scroll-down {
		position: fixed;
		z-index: 100;
		left: 50%;
		bottom: v-bind(bottomPosition);
		transform: translateX(-50%);
		transition: all 0.4s ease;
		img {
			width: 29px;
			height: 29px;
		}
	}
	/* 1. 定义进入和离开过程中的动画效果 */
	/* a. enter-active: 定义进入动画的持续时间、延迟和曲线函数。*/
	/* b. leave-active: 定义离开动画的持续时间、延迟和曲线函数。*/
	.slide-enter-active,
	.slide-leave-active {
		transition: all 0.4s ease;
	}

	/* 2. 定义动画的起始和结束状态 */
	/* a. enter-from: 这是进入动画的起始状态（元素刚被添加到DOM，动画开始前）。*/
	/* 我们让它在原始位置下方40px处，并且完全透明。*/
	/* b. leave-to: 这是离开动画的结束状态（动画结束后，元素将从DOM中移除）。*/
	/* 我们也让它移动到下方并变为透明。*/
	.slide-enter-from,
	.slide-leave-to {
		opacity: 0;
		/* 在原有的 translateX(-50%) 基础上，再增加一个 Y 轴的位移。
    这会让它从最终位置的下方 40px 处开始动画。
  */
		transform: translateX(-50%) translateY(40px);
	}
}
</style>
