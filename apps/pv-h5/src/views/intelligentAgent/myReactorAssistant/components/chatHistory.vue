<template>
	<van-popup teleport="body" :safe-area-inset-top="true" :safe-area-inset-bottom="true" v-model:show="showLeft" position="left" :style="{ width: '84%', height: '100%' }">
		<div class="chs">
			<!-- 智体视界 -->
			<div class="chs-header">
				<span>智体视界</span>
				<img @click="goHistoryPage" src="@/assets/img/search.png" />
			</div>
			<!-- 新增回话 -->
			<div @click="newChat" class="chs-add">
				<img src="@/assets/img/news-chat.png" />
				<span>新增对话</span>
			</div>
			<!-- 内容列表 -->
			<div v-if="filteredList.length > 0" class="chs-content">
				<!--时间段 -->
				<div v-for="val in filteredList" :key="val.name" class="chs-contet-time">
					<span class="time-title">{{ val.sName }}</span>
					<!-- 会话列表 -->
					<div class="chs-chat-list">
						<span v-for="s in val.children" :key="s.id" @click="setHistoryItem(s)" class="chat-item ell">{{ s.description }}</span>
					</div>
				</div>
			</div>
			<van-empty v-else :description="$t('history.noMore')" />
		</div>
	</van-popup>
</template>
<script setup>
const showLeft = ref(false);
const props = defineProps(['historyList']);
const emits = defineEmits(['_openChat', '_newChat', '_openHistoryPage']);
const { proxy } = getCurrentInstance();
// 数据列表
const filteredList = computed(() => props.historyList ?? []);
// 选中事件
const setHistoryItem = (s) => {
	showLeft.value = false;
	proxy.$umeng('点击', '历史会话', s.description);
	emits('_openChat', {
		id: s.id,
	});
};

// 历史搜索页面
const goHistoryPage = () => {
	showLeft.value = false;
	emits('_openHistoryPage');
};

// 新增回话
const newChat = () => {
	showLeft.value = false;
	emits('_newChat');
};

defineExpose({
	showLeft,
});
</script>
<style lang="scss" scoped>
.chs {
	padding: 16px 0;
	display: flex;
	flex-direction: column;
	height: 100%;

	.chs-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0px 15px 10px;
		span {
			font-size: 16px;
			color: var(--pv-default-color);
			font-weight: 600;
		}
		img {
			width: 20px;
		}
	}

	.chs-add {
		margin: 0 15px 12px 15px;
		background-color: var(--pv-card-bgc);
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 8px 0;
		img {
			width: 16px;
			margin-right: 12px;
		}
		span {
			font-size: 14px;
			color: var(--pv-default-color);
		}
	}

	.chs-content {
		flex: 1;
		overflow-y: scroll;

		.chs-contet-time {
			display: flex;
			flex-direction: column;

			.time-title {
				font-size: 14px;
				color: #333333;
				margin-bottom: 12px;
				padding: 0 16px;
				font-weight: 500;
			}

			.chs-chat-list {
				display: flex;
				flex-direction: column;
				width: 100%;
				margin-bottom: 12px;

				.chat-item {
					font-size: 14px;
					margin-bottom: 8px;
					margin-top: 8px;
					padding: 0px 24px;
					color: #6b778c;
				}
				.chat-item:active {
					background-color: #f4f5f7;
					border-radius: 5px;
				}
			}
		}
	}
}
</style>
