<template>
	<div>
		<div v-if="item" class="mc-content-list-left">
			<div class="par" style="display: flex">
				<!-- AI 文本 内容模版 -->
				<div class="mc-content-template">
					<loading1 v-if="item.loadingAnimation" :text="item.loadingText"></loading1>
					<!-- 纯文本 -->
					<ai-text v-else-if="!item.isIframe && item.msg" :id="item.id" :is-loading="item.performanceReportLoading" :msg="item.msg" :think="item.think || ''" :name="item.name"></ai-text>
					<!-- 卡片 -->
					<div v-else-if="item.isIframe">
						<div v-if="!item.loadingAnimation" style="margin-top: 5px">
							<component
								class="mp0"
								:is="dom[item.iframeUrl.id]"
								:info="cardInfoC(item.iframeUrl.id)"
								:dinfo="cardInfoC(item.iframeUrl.id)"
								:summary="!item.noSummary"
								:inChat="true"
								:defaultValue="item.iframeUrl.params"
								:startTime="cardInfoC(item.iframeUrl.id).startTime"
								:endTime="cardInfoC(item.iframeUrl.id).endTime"
								:dataSyncTime="cardInfoC(item.iframeUrl.id).dataSyncTime"
								@summaryIng="afterMessage"
								@openfilter="openfilter"
							></component>
						</div>
						<!-- summary -->
						<div v-if="item.iframeLoadingFinished && !item.summary">正在生成点评，请稍后<span class="dots"></span></div>
						<div v-if="item.summary">
							<ai-text :id="item.id" :is-loading="item.summaryLoading" :msg="item.summary"></ai-text>
						</div>
					</div>
					<div v-else-if="item.responseType === 'followup'" :id="item.id">
						<span>{{ item.responseResult.message }}</span>
						<div class="options">
							<div v-for="val in item.responseResult.options" :key="val" @click="sendMessage(val)" class="followup">{{ val.title }}</div>
						</div>
					</div>
					<div v-else-if="item.responseType === 'tool_selection'" :id="item.id">
						<span>{{ item.responseResult.content }}</span>
						<div>
							<div class="cards">
								<div class="cards-item" v-for="val in item.responseResult.options" :key="val" @click="sendMessage(val)">
									<img :src="val?.properties?.thumbnail" alt="" />
									<div>{{ val.title }}</div>
								</div>
							</div>
						</div>
					</div>
					<div v-else-if="item.responseType === 'help'" :id="item.id">
						<span>{{ item.responseResult.introduction }}</span>
					</div>
					<div v-else-if="item.responseType === 'suggestion'" :id="item.id">
						<span>{{ item.responseResult.text }}</span>
					</div>
					<div v-else-if="item.responseType === 'error'" :id="item.id">
						<span>{{ item.responseResult.message }}</span>
					</div>
					<!-- 小按钮组 - 语音播报和复制 -->
					<ai-btns v-if="item.isBtns && !item.performanceReportLoading && !item.loadingAnimation" :id="item.id" v-bind="$attrs" :reGen="item.reGen" :zeStatus="item.zeStatus" @_reGen="reGen" :voiceStatus="item.voiceStatus" @copyText="copyText"></ai-btns>
				</div>
			</div>
			<!-- 问题列表 -->
			<div class="ql" v-if="item.questionList && item.questionList.length > 0 && showEtc(item)">
				<div class="nk">您可能还想问</div>
				<div class="mc-content__groupList">
					<span v-for="(val, index) in item.questionList" :key="val" @click="qesEv(val, index, item.questionList)">{{ val }}</span>
				</div>
			</div>
		</div>
		<!-- 筛选 -->
		<van-overlay :show="comFilterShow" z-index="101" :lock-scroll="false">
			<div v-if="comFilterShow" class="wrapper">
				<div
					class="coms"
					:style="{
						paddingLeft: cardInfo.reportCd === 'topHospitalAnalysis' ? (isPc ? '13px' : '3.46667vw') : '',
						paddingRight: cardInfo.reportCd === 'topHospitalAnalysis' ? (isPc ? '13px' : '3.46667vw') : '',
					}"
				>
					<component ref="comRef" :is="dom[currentCom]" :info="cardInfo" :dinfo="cardInfo" :defaultValue="currentComDefault" :startTime="cardInfo.startTime" :endTime="cardInfo.endTime" :dataSyncTime="cardInfo.dataSyncTime"></component>
				</div>
				<div class="operation">
					<div class="cancel" @click="comFilterShow = false">取消</div>
					<div class="confirm" @click="createNewAnalysis">确认</div>
				</div>
			</div>
		</van-overlay>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import { useI18n } from 'vue-i18n';
import axios, { CancelToken, isCancel } from 'axios';
import { uuid, getCurrentMonthRange, findItemInTree } from '@/utils/index';
import { sendMsgApi, updateMsgApi, getInterpretationApi, getGuessQuestionApi, addMsgApi } from '@/api/agent-tools';
import { branchHospitalSalesOverviewAmount, ddiTransactionSales, salesAchievementStatus, getTeamSalesTrends } from '@/api/sales';
const { t } = useI18n();
//会话
import { useScriptAssistant } from '@/hooks/web/useScriptAssistant.js';
let { interfaceUrl, isPc, isStream, copyText, cardsInfoList, cardInfoC, relam, createUserMsg, createAiMsg, getReportDateRange, renameKeys, processSalesBrandData, extractTextFromHtml, questionListAll } = useScriptAssistant();

import { useAssistant } from '@/hooks/web/useAssistant';
let { chatList, chatId, isLoading, getLastUserMsg, getLastChat, getLastUser } = useAssistant();

//界面滚动
import { useScriptAssistantScroll } from '@/hooks/web/useScriptAssistantScroll';
const { scrollBottom, aiConScroll } = useScriptAssistantScroll();
//语音
import { useScriptAssistantAudioPlayer } from '@/hooks/web/useScriptAssistantAudioPlayer.js';
const { ttsWS, audioPlayer, resetAudioVoice, userStore } = useScriptAssistantAudioPlayer(chatList);
import usefilterStore from '@/store/modules/filter';
let filterStore = usefilterStore();

const props = defineProps(['chatNum']);
const emit = defineEmits(['_stopFetch']);
let item = ref({});

//猜你想问
const qesEv = (val) => {
	emit('_stopFetch');
	chatList.value.push(createUserMsg(val.name || val));
	// 生产一个ai的消息
	chatList.value.push(createAiMsg('', 'intelligentAnalysis', val.name || val));
};
// 重新生成
const reGen = (id) => {
	// 获取文本信息
	let prevItemMsg = '';
	const index = chatList.value.findIndex((item) => item.id === id);
	if (index !== -1) {
		prevItemMsg = chatList.value[index - 1]?.msg ?? '';
	}
	try {
		emit('_stopFetch');
		chatList.value.push(createUserMsg(prevItemMsg));
		// 生产一个ai的消息
		chatList.value.push(createAiMsg('', 'intelligentAnalysis', prevItemMsg));
	} catch (e) {
		console.log(e);
		showToast({
			position: 'top',
			message: '重新生成失败！',
		});
	}
};

const sendMessageBaseFather = (val) => {
	// 如果正在加载，先停止当前请求
	if (isLoading.value) {
		stopFetch();
	}
	// 设置加载状态
	isLoading.value = true;
	// 滚动到底部并发送消息
	scrollBottom();
	setTextMessage(val);
};
// 发送消息
const sendMessage = (val) => {
	// 添加用户消息和 AI 消息
	const message = typeof val === 'string' ? val : val.title;
	emit('_stopFetch');
	chatList.value.push(createUserMsg(message));
	isLoading.value = true;
	chatList.value.push(createAiMsg('', 'intelligentAnalysis', message, '', '', { isCreateNew: true }));
	scrollBottom();
	setTextMessage(val);
};
// 获取数据

//停止当前请求
const stopFetch = () => {
	while (controllerList.length > 0) {
		controllerList.pop()('取消请求');
	}
	controllerFetchList.forEach((ele) => ele.abort());

	if (!getLastChat.value) {
		controllerList = [];
		controllerFetchList = [];
		return;
	}
	getLastChat.value.performanceReportLoading = false;
	getLastChat.value.loadingAnimation = false;
	if (getLastChat.value.msg) {
		getLastChat.value.msg = getLastChat.value.msg + '...';
	}
	if (getLastChat.value.summary) {
		getLastChat.value.summary = getLastChat.value.summary + '...';
	}
	if (!getLastChat.value.msg && !getLastChat.value.summary) {
		getLastChat.value.msg = '您停止生成了本次回答，请重新提交问题...';
		getLastChat.value.summary = '您停止生成了本次回答，请重新提交问题...';
	}
	getLastChat.value.summaryLoading = false;
	getLastChat.value.questionList = '';
	getLastChat.value.temQuestionList = '';
	isLoading.value = false;

	controllerList = [];
	controllerFetchList = [];
};

//发送信息
const setTextMessage = async (val) => {
	try {
		getLastChat.value.loadingAnimation = true;
		await sendMsgRequest(val);
	} catch (e) {
		handleError(e);
	}
};
let handleError = (e) => {
	console.error(e);
};

const init = async () => {
	// 1. 初始化状态
	isLoading.value = true;
	getLastChat.value.loadingAnimation = true;
	// 2. 获取基础参数 (区域代码, 权限)
	let territoryCode = [];
	if (relam === 'muqiao') {
		const value = '{"id":"salesTrendsOverview","text":"月度销售趋势分析","filterList":["org"],"filterInfo":[{"id":"org","dataRange":[],"dataDefault":["NCNSC"],"defaultList":[]}]}';
		const filterConfig = JSON.parse(value);
		const result = useOrg(filterConfig.filterInfo);
		if (result.personId) {
			territoryCode.push(result.personId);
		}
	}
	const isManager = !userStore.permission.includes('MR');
	const defaultDateRange = getCurrentMonthRange();

	// 3. 动态构建API请求列表
	const apiPromises = [];
	const salesStatusDates = getReportDateRange('salesAchievementStatus', defaultDateRange);
	apiPromises.push(
		salesAchievementStatus({
			nowLocalDateTime: salesStatusDates.startTime,
			endLocalDate: salesStatusDates.endTime,
			territoryCode,
		})
	);

	// 根据权限决定是否添加 getTeamSalesTrends 请求
	if (isManager) {
		const teamSalesDates = getReportDateRange('salesAnalysisTeam', defaultDateRange);
		apiPromises.push(
			getTeamSalesTrends({
				startLocalDate: teamSalesDates.startTime,
				endLocalDate: teamSalesDates.endTime,
				territoryCode,
			})
		);
	}

	const branchDates = getReportDateRange('branchSalesOverview2', defaultDateRange);
	apiPromises.push(
		branchHospitalSalesOverviewAmount({
			type: 'sales',
			startLocalDate: branchDates.startTime,
			endLocalDate: branchDates.endTime,
			territoryCode,
		})
	);

	const ddiDates = getReportDateRange('ddi', defaultDateRange);
	apiPromises.push(
		ddiTransactionSales(
			{
				startLocalDate: ddiDates.startTime,
				// [FIXED] 修正了原代码中可能存在的bug, endLocalDate使用了endTime
				endLocalDate: ddiDates.endTime,
				territoryCode,
			},
			relam
		)
	);

	// 4. 并发执行所有API请求
	const results = await Promise.all(apiPromises);

	// 5. 解析和处理API返回结果
	const [res3, res4, res5, res6] = isManager ? [results[0], results[1], results[2], results[3]] : [results[0], null, results[1], results[2]]; // 非Manager时, res4为null

	// 定义键名映射 (只需定义一次)
	const statusKeyMap = { salesMtd: '月销售额', salesRateMtd: '月销售达成率', yoyMtd: '月同比增长率', momMtd: '月环比增长率', salesRateQtd: '季度销售达成率', yoyQtd: '季度同比增长率', qoqQtd: '季度环比增长率', salesRateYtd: '年销售达成率', yoyYtd: '年同比增长率', hospSalesRate: '有销售额终端' };
	const productKeyMap = { achievementRate: '销售达成率', chain: '环比增长率', moMGrowth: '环比净增长销售额', name: '产品名称', salesV: '销售金额', salesVLm: '上月同期销售金额', salesVLy: '去年同期销售金额', targetV: '销售目标金额', yearOnYear: '同比增长率', yearOnYearGrowth: '同比净增长销售额' };

	// 使用辅助函数处理数据
	const processedSalesStatus = renameKeys(res3.result, statusKeyMap);
	const hospitalSalesText = extractTextFromHtml(res5.result.message);
	const processedProductSales = renameKeys(processSalesBrandData(res6.result.salesBrand), productKeyMap);

	// 6. 组合最终数据
	const data = {
		...processedSalesStatus,
		...(isManager && res4.result), // 使用条件展开, 仅在isManager为true时添加res4.result
		分医院: hospitalSalesText,
		分产品: processedProductSales,
	};
	console.log(data);

	// 7. 执行后续操作

	await getGuess();
	getReport(data);
};
// 收到iframe传入的消息后处理卡片
const comFilterShow = ref(false);
let currentCom = ref('');
let cardInfo = ref({});
let currentComDefault = ref({});
//加载异步组件
const { dom } = useChatCard();
const afterMessage = async (event) => {
	console.log(event);
	/* 	卡片加载完成后获取数据
  有数据进行下一步获取总结和猜你想问、下一步行动计划 */
	if (event.data) {
		isLoading.value = true;
		getLastChat.value.summary = '';
		getSummary({
			type: 'cardComSummary',
			Question: getLastUserMsg.value,
			data: { ...event.data },
			params: event.params,
		});
		getLastChat.value.iframeLoadingFinished = true;
	}
	if (event.noSummary) {
		getLastChat.value.summaryLoading = false;
		getLastChat.value.loadingAnimation = false;
		isLoading.value = false;
		//更新消息
		getLastChat.value.noSummary = true;
		await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
	}
};
const openfilter = (data) => {
	currentComDefault.value = data.defaultValue;
	currentCom.value = data.comName;
	cardInfo.value = cardsInfoList.value.find((ele) => ele.reportCd === data.comName);
	console.log(cardInfo.value);
	comFilterShow.value = true;
};
//创建新分析
const comRef = ref(null);
const comQuery = ref({});
const createNewAnalysis = async () => {
	comFilterShow.value = false;
	comQuery.value = JSON.parse(JSON.stringify(comRef.value.query));
	console.log(comQuery.value);
	// return;
	if (isLoading.value) {
		stopFetch();
	}
	emit('_stopFetch');
	chatList.value.push(createUserMsg('基于新的数据范围帮您分析'));
	let res = await addMsgApi(
		{ conversation_id: chatId.value, content: JSON.stringify({ ...getLastChat.value, skillType: 'userMessage' }), role: 'user', message_type: 'string' },
		{
			cancelToken: new CancelToken((c) => controllerList.push(c)),
		}
	);
	getLastChat.value.dataId = res.data.message_id;
	await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
	// 生产一个ai的消息
	let message = {
		msg: '',
		dataId: '',
		type: 'ai',
		id: uuid(),
		isBtns: true,
		reGen: true,
		voiceStatus: true,
		zeStatus: '0',
		isIframe: true,
		summaryLoading: true, //加载summary
		loadingAnimation: false,
		skillType: 'intelligentAnalysis',
		isCreateNew: true,
	};
	// 构建查询字符串
	if (comQuery.value) {
		let newQuery = JSON.parse(JSON.stringify(comQuery.value));
		newQuery.startTime = `${newQuery.startDate?.slice(0, 4)}-${newQuery.startDate?.slice(4, 6)}-01`;
		newQuery.endTime = `${newQuery.endDate?.slice(0, 4)}-${newQuery.endDate?.slice(4, 6)}-01`;
		newQuery.brandName = (newQuery.productName ? newQuery.productName : newQuery.brandName) || '';
		newQuery.brandId = (newQuery.productId ? newQuery.productId : newQuery.brandId) || '';

		delete newQuery.startDate;
		delete newQuery.endDate;
		delete newQuery.productName;
		delete newQuery.productId;

		console.log(newQuery);
		message.iframeUrl = {
			id: currentCom.value,
			params: useParams(currentCom.value, cardsInfoList.value, newQuery),
		};
	}
	chatList.value.push(message);
	let res1 = await addMsgApi(
		{ conversation_id: chatId.value, content: JSON.stringify(getLastChat.value), role: 'assistant', message_type: 'string' },
		{
			cancelToken: new CancelToken((c) => controllerList.push(c)),
		}
	);
	getLastChat.value.dataId = res1.data.message_id;
	await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
	scrollBottom();
};

//axios取消
let controllerList = [];
//查卡片
const sendMsgRequest = async (val) => {
	try {
		// 1. Initiate both API calls at the same time without "await"
		const sendMsgPromise = sendMsgApi(
			{
				conversation_id: chatId.value,
				user_query: typeof val === 'string' ? val : '',
				selection_type: val.type,
				selection_value: val.value,
			},
			{
				cancelToken: new CancelToken((c) => controllerList.push(c)),
			}
		);
		const getGuessPromise = getGuess();

		// 2. Use Promise.all to wait for both to complete concurrently
		// The result 'res' will correspond to 'sendMsgPromise'
		const [res] = await Promise.all([sendMsgPromise, getGuessPromise]);

		// 3. Process the result of sendMsgApi after it has finished
		const { metadata, type, payload } = res.data;
		const lastChat = getLastChat.value;
		const lastUser = getLastUser.value;

		lastChat.responseType = type;
		lastChat.responseResult = payload;
		lastChat.dataId = metadata.assistant_message_id;
		lastUser.dataId = metadata.user_message_id;
		lastChat.loadingAnimation = false;
		isLoading.value = false;

		// Handle card logic if necessary
		if (type === 'tool' && payload.category === 'card') {
			handleCard();
		}

		// 4. Concurrently update both messages after processing
		await Promise.all([updateMsgApi(lastChat.dataId, { content: JSON.stringify(lastChat) }), updateMsgApi(lastUser.dataId, { content: JSON.stringify(lastUser) })]);

		return res; // An async function returns a resolved promise with this value
	} catch (error) {
		// Promise.all will reject if any of the promises fail
		console.log(error, 'xxxxxxxxxxxxxxxxxxxxxxx');
		// Optionally, re-throw the error or handle it as needed
		throw error;
	}
};
const handleCard = async (type) => {
	try {
		let resData = getLastChat.value.responseResult;
		if (resData) {
			getLastChat.value.isIframe = true;
			getLastChat.value.summaryLoading = true; //加载summary
			//完成后的逻辑
			if (getLastChat.value.isIframe) {
				let params = {
					startTime: resData.filters.find((ele) => (ele = ele.key === 'time'))?.value.start_time,
					endTime: resData.filters.find((ele) => (ele = ele.key === 'time'))?.value.end_time,
					brandId: resData.filters.find((ele) => (ele = ele.key === 'product'))?.value.id,
					brandName: resData.filters.find((ele) => (ele = ele.key === 'product'))?.value.name,
					personId: resData.filters.find((ele) => (ele = ele.key === 'person'))?.value.id,
					personName: resData.filters.find((ele) => (ele = ele.key === 'person'))?.value.name,
					province: resData.filters.find((ele) => (ele = ele.key === 'region'))?.value.properties?.province,
					city: resData.filters.find((ele) => (ele = ele.key === 'region'))?.value.properties?.city,
					hospitalId: resData.filters.find((ele) => (ele = ele.key === 'hospital'))?.value.id,
					hospitalName: resData.filters.find((ele) => (ele = ele.key === 'hospital'))?.value.name,
					indicatorType: resData.tabs?.find((ele) => ele.properties.type === 'ratio')?.id,
				};
				const productFilter = resData.filters.find((ele) => ele.key === 'product')?.value;
				const excludedRoutes = ['inventorySalesPurchaseTrends', 'productAchievement', 'salesSituation', 'sentFromChannels', 'teamSales'];
				if (productFilter?.properties?.level !== 'sku' && !excludedRoutes.includes(resData.route_key)) {
					const id = productFilter?.id;
					// 确保 id 存在再进行搜索
					if (id) {
						const brandList = filterStore.skuInfoTree;
						const foundItem = findItemInTree(brandList, id);
						// 使用可选链安全地访问 skuCode，并在找到时更新 brandId
						if (foundItem?.skuCode) {
							params.brandId = foundItem.skuCode;
						}
					}
				}
				getLastChat.value.iframeUrl = {
					id: resData.route_key,
					params: useParams(resData.route_key, cardsInfoList.value, params),
				};
				getLastChat.value.loadingAnimation = false;
			}
		}
	} catch (e) {
		console.error('解析响应失败：', e);
		return null; // 或根据需要处理错误
	}
};
// 1. 导入 useApi
import { useApi } from '@/hooks/web/fetchApi';
const { apiFetch } = useApi();
let controllerFetchList = [];
/**
 * 通用的流式API请求处理器
 * @param {object} config - 配置对象
 * @param {string} config.endpoint - API的端点URL
 * @param {object} config.payload - 发送给API的请求体数据
 * @param {function(object): void} config.onData - 每当接收并解析到一条数据时调用的回调函数
 * @param {function(): Promise<void>} config.onDone - (可选) 数据流接收完毕时调用的异步回调函数
 * @param {function(Error): void} [config.onError] - (可选) 发生错误时调用的回调函数
 */
const handleStreamedApiRequest = async (config) => {
	const { endpoint, payload, onData, onDone, onError } = config;
	const controller = new AbortController();
	controllerFetchList.push(controller); // 管理AbortController实例
	try {
		const response = await apiFetch(endpoint, {
			signal: controller.signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
			},
			body: JSON.stringify(payload),
		});

		if (!response.ok || !response.body) {
			throw new Error(`API请求失败，状态码: ${response.status}`);
		}

		const reader = response.body.getReader();
		const decoder = new TextDecoder('utf-8');
		// 创建一个缓冲区来存储不完整的数据
		let buffer = '';
		// 循环读取流
		// eslint-disable-next-line no-constant-condition
		while (true) {
			const { done, value } = await reader.read();
			if (done) {
				console.log('数据流接收完毕。');
				if (onDone) onDone();
				break;
			}
			buffer += decoder.decode(value, { stream: true });
			// SSE消息以 '\n' 分隔
			while (buffer.includes('\n')) {
				const separatorIndex = buffer.indexOf('\n');
				// 1. 提取出完整的一行数据
				const line = buffer.slice(0, separatorIndex).trim();
				// 2. 从缓冲区中移除已经处理过的行和换行符
				buffer = buffer.slice(separatorIndex + 1);
				// 如果行是空的，则跳过（这可以处理连续的换行符或心跳空行）
				if (!line) {
					continue;
				}
				// 3. 直接处理这一行
				if (line.startsWith('data:')) {
					const jsonString = line.substring(5).trim();
					if (jsonString && !jsonString.includes('[DONE]')) {
						try {
							const dataObject = JSON.parse(jsonString);
							// 假设 onData 回调函数存在
							if (onData) onData(dataObject);
						} catch (error) {
							console.error('解析JSON失败:', error, '原始JSON字符串:', jsonString);
						}
					}
				}
			}
		}
	} catch (error) {
		console.error('流式请求或处理过程中发生错误:', error);
		if (onError) onError(error); // 调用错误处理回调
	}
};
// 获取解读（优化后）
const getSummary = async (data) => {
	if (isStream.value) {
		await handleStreamedApiRequest({
			endpoint: `${interfaceUrl}/API-AIP/chat/analyze`,
			payload: {
				stream: true,
				conversation_id: chatId.value,
				data: JSON.stringify(data),
			},
			onData: (dataObject) => {
				if (dataObject.type === 'content') {
					getLastChat.value.summary += dataObject.data.content;
				}
			},
			onDone: async () => {
				getLastChat.value.summaryLoading = false;
				isLoading.value = false;
				getLastChat.value.noSummary = true;
				await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
				scrollBottom();
			},
			onError: () => {
				// 统一处理错误状态
				getLastChat.value.summaryLoading = false;
				isLoading.value = false;
			},
		});
	} else {
		let res = await getInterpretationApi(
			{ conversation_id: chatId.value, data: JSON.stringify(data), stream: false },
			{
				cancelToken: new CancelToken((c) => controllerList.push(c)),
			}
		);
		getLastChat.value.summary = res.data.analysis;
		getLastChat.value.summaryLoading = false;
		isLoading.value = false;
		//更新消息
		getLastChat.value.noSummary = true;
		await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
	}
};

// 获取业绩播报（优化后）
const getReport = async (data) => {
	if (isStream.value) {
		getLastChat.value.performanceReportLoading = true;
		await handleStreamedApiRequest({
			endpoint: `${interfaceUrl}/API-AIP/chat/generic_analysis`,
			payload: {
				stream: true,
				conversation_id: chatId.value,
				data: JSON.stringify(data),
				response_format: 'markdown',
				system_prompt: '基于销售数据生成每日销售播报。简洁生成返回200字内',
				max_tokens: 500,
			},
			onData: (dataObject) => {
				if (dataObject.type === 'content') {
					getLastChat.value.loadingAnimation = false;
					getLastChat.value.msg += dataObject.data.content;
				}
			},

			onDone: async () => {
				isLoading.value = false;
				getLastChat.value.performanceReportLoading = false;
				let res = await addMsgApi(
					{ conversation_id: chatId.value, content: JSON.stringify(getLastChat.value), role: 'assistant', message_type: 'string' },
					{
						cancelToken: new CancelToken((c) => controllerList.push(c)),
					}
				);
				getLastChat.value.dataId = res.data.message_id;
				await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
				scrollBottom();
			},
			onError: () => {
				isLoading.value = false;
				getLastChat.value.performanceReportLoading = false;
			},
		});
	}
	// 原函数没有else分支，这里保持一致
};

//获取猜你想问
const getGuess = async () => {
	let res = await getGuessQuestionApi(
		{ data: questionListAll, conversation_id: chatId.value, response_format: 'text', stream: false, system_prompt: '请根据提供的业务数据和用户历史对话，从中选3个相关问题。只返回示例数据中存在的。固定返回格式 ["问题一","问题二","问题三"]' },
		{
			cancelToken: new CancelToken((c) => controllerList.push(c)),
		}
	);
	getLastChat.value.questionList = res.data.analysis ? JSON.parse(res.data.analysis) : [];
};
//处理猜你想问和下一步行动计划出现时机
const showEtc = computed(() => {
	return (item) => {
		if (item.isIframe) {
			if (!item.summaryLoading) {
				return true;
			}
		} else if (!item.isPerformanceReport) {
			if (!item.loadingAnimation) {
				return true;
			}
		} else {
			if (!item.performanceReportLoading) {
				return true;
			}
		}
	};
});

watch(
	() => props.chatNum,
	(n) => {
		item.value = chatList.value[n];
	},
	{ immediate: true }
);
watch(
	item,
	(n) => {
		console.log(n);
		if (!n) return;
		if (n.isCreateNew) return;
		if (!n.dataId && !n.isPerformanceReport) {
			sendMessageBaseFather(n.userMsg);
		} else if (!n.dataId && n.isPerformanceReport) {
			init();
		}
	},
	{ immediate: true }
);

onMounted(async () => {});
onBeforeUnmount(() => {});

onUnmounted(() => {
	window.removeEventListener('scroll', aiConScroll);
});
defineExpose({ userStore, stopFetch });
</script>
<style lang="scss" scoped>
.mc-content-list-left {
	display: flex;
	flex-direction: column;
	width: 100%;
	margin-bottom: 12px;
	.mc-content-template {
		border-top-left-radius: 8px;
		border-bottom-right-radius: 8px;
		border-top-right-radius: 8px;
		max-width: 330px;
		min-width: 52px;
		background-color: var(--ac-bg-color--fff);
		padding: 10px;
	}
	.mc-content__groupList {
		padding: 8px 0 0 0;
		padding-left: 15px;
		display: flex;
		display: flex;
		overflow-x: auto;
		width: 100%;
		span {
			flex: none;
			margin-right: 8px;
			margin-bottom: 8px;
			color: #0b67e4;
			border-radius: 5px;
			border: 1px solid #0b67e4;
			padding: 3px 6px;
		}
	}
}
:deep(.van-popup--bottom) {
	border-top-right-radius: 20px;
	border-top-left-radius: 20px;
	padding: 16px 0;
	background-color: #f4f5f7;
}

.par {
	margin-left: 15px;
}
.ql {
	margin-top: 12px;
	.nk {
		margin-left: 15px;
		color: #6b778c;
		font-size: 12px;
	}
	.mc-content__groupList {
		scrollbar-width: none; /* firefox */
		-ms-overflow-style: none; /* IE 10+ */
		&::-webkit-scrollbar {
			display: none; /* Chrome Safari */
		}
	}
}
::v-deep(.van-overlay) {
	background: rgba(0, 0, 0, 0.5);
	.wrapper {
		border-radius: 6px;
		background-color: #f4f5f7;
		// width: 90%;
		position: relative;
		top: 20%;
		left: 50%;
		transform: translateX(-50%);
		padding-bottom: 18px;
		.card-title {
			.icon {
				display: none;
			}
		}
		.update-box {
			display: none;
		}
		.coms {
			max-height: 400px;
			overflow-y: auto;
		}
		& > .operation {
			display: flex;
			padding: 0 22px;
			div {
				width: 145px;
				flex: 1;
				line-height: 44px;
				border-radius: 6px;
				text-align: center;
				box-shadow: 0px 2px 9px 0px rgba(0, 82, 204, 0.1);
				font-size: 13px;
			}
			.cancel {
				color: #0052cc;
				background-color: rgba(0, 82, 204, 0.1);
				margin-right: 13px;
			}
			.confirm {
				background-color: #0052cc;
				color: #fff;
			}
		}
		&::before {
			content: '';
			display: block;
			width: 3%;
			height: 100%;
			position: absolute;
			left: 0;
			top: 0;
			background: rgba(0, 0, 0, 0.5);
		}
		&::after {
			content: '';
			display: block;
			width: 3%;
			height: 100%;
			position: absolute;
			right: 0;
			top: 0;
			background: rgba(0, 0, 0, 0.5);
		}
	}
}
@keyframes dotAnimation {
	0% {
		content: '';
	}
	33% {
		content: '.';
	}
	66% {
		content: '..';
	}
	100% {
		content: '...';
	}
}

.dots::after {
	content: '...';
	display: inline-block;
	animation: dotAnimation 1s infinite steps(3);
}
//新逻辑
:deep(.mp0) {
	width: 310px;
	margin: 0 !important;
	padding: 0 !important;
	.top .card-title {
		.icon {
			display: none;
		}
	}
}
.options {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	& > div {
		flex: 0 0 48%; /* 禁止增长，固定宽度为 50% */
		// margin-right: 2px;
	}
}
.cards {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	&-item {
		flex: 0 0 calc(50% - 4px);
		img {
			width: 100% !important;
			height: auto !important;
			object-fit: cover;
			aspect-ratio: 16 / 9; /* 设置宽高比，例如 16:9 */
		}
		div {
			font-size: 12px;
		}
	}
}
.followup {
	border: 1px solid #0052cc;
	border-radius: 5px;
	color: #0052cc;
	padding: 5px;
	margin-bottom: 5px;
}
</style>
