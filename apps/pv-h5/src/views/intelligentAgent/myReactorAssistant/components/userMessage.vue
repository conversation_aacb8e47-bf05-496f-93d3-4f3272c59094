<template>
	<div class="mc-content-list-right">
		<!-- 用户内容模版 -->
		<div @click="editorMsg" class="mc-content-template">
			<p style="white-space: pre-wrap">{{ chatList[chatNum].msg || '' }}</p>
			<!-- 是否可以修改 -->
			<div class="mc-content-template__edit" v-if="chatNum === chatList.length - 2"></div>
		</div>
	</div>
</template>
<script setup>
const props = defineProps(['chatNum']);
const { chatList, isLoading } = useAssistant();
const emits = defineEmits('_editorMsg', '_stopFetch');
const editorMsg = () => {
	// 是否可以进行二次编辑
	if (props.chatNum === chatList.value.length - 2) {
		// 中断请求
		if (isLoading.value) emits('_stopFetch');
		nextTick(() => {
			emits('_editorMsg', props.chatNum);
		});
	}
};
</script>
<style lang="scss" scoped>
.mc-content-list-right {
	padding-right: 15px;
	display: flex;
	margin-bottom: 12px;
	justify-content: flex-end;
	width: 100%;
	.mc-content-template {
		border-top-right-radius: 8px;
		border-bottom-left-radius: 8px;
		border-top-left-radius: 8px;
		color: var(--ac-bg-color--fff);
		background-color: var(--ac-bg-active-color);
		.mc-content-template__edit {
			border-top: solid 1px rgba(223, 225, 230, 0.3);
			margin-top: 2px;
		}
	}
}

.mc-content-template {
	max-width: 330px;
	min-width: 52px;
	background-color: var(--ac-bg-color--fff);
	padding: 10px;
}
</style>
