<template>
	<div class="mc-content-list-left">
		<!-- 用户内容模版 -->
		<div class="mc-content-template">
			<!-- 纯文本 -->
			<loading1 v-if="loadingAnimation" :text="textMsg"></loading1>
			<ai-text v-else :id="item.id" :is-loading="item.loading" :msg="item.msg"></ai-text>
			<!-- 附件 -->
			<!-- 小按钮组 - 语音播报和复制 -->
			<ai-btns v-if="item.isBtns && !item.loading" :id="item.id" v-bind="$attrs" :zeStatus="item.zeStatus" :voiceStatus="item.voiceStatus" @copyText="copyText"></ai-btns>
		</div>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
const { chatList, chatId, isLoading, addHistoryMessage, getLastUserMsg, getLastChat, getLastUser } = useAssistant();
import { updateMsgApi, addMsgApi } from '@/api/agent-tools';
import { getToken } from '@/utils/auth';

import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
const userId = userStore.userInfo.username;

import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const interfaceUrl = enterStore.enterInfo.interfaceAddress;

//界面滚动
import { useScriptAssistantScroll } from '@/hooks/web/useScriptAssistantScroll';
const { showScrollDown, scrollBottom, aiConScroll } = useScriptAssistantScroll();

//会话
import { useScriptAssistant } from '@/hooks/web/useScriptAssistant.js';
let { copyText } = useScriptAssistant();

const props = defineProps(['chatNum', 'skillInfo']);
const loadingAnimation = ref(false);
// const stream = props.skillInfo.agentFunction?.stream || true;
const stream = true;
const item = computed(() => {
	return chatList.value[props.chatNum];
});

const textMsg = ref('加载中');

let controller = '';
const setAiMessage = async (val) => {
	loadingAnimation.value = true;
	controller = new AbortController();
	const { signal } = controller;
	isLoading.value = true;
	try {
		const message = val || '';
		// ${interfaceUrl} http://***************:8001
		let res = await fetch(`${interfaceUrl}/API-GPT/report/generate_report?user_id=${userId}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
			body: JSON.stringify({
				question: message,
			}),
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		if (!stream) {
			const json = await res.json();
			isLoading.value = false;
			item.value.loading = false;
			if (json.code === 200) {
				item.value.msg = json.data.response;
				// 更新历史消息
				updateHistoryMsg();
			} else {
				item.value.isBtns = false;
				loadingAnimation.value = false;
				item.value.msg = '接口响应出现错误，中断生成报告。';
			}
		} else {
			const reader = res.body.getReader();
			const decoder = new TextDecoder('utf-8');
			let data = '';
			const processStreamResult = async (result) => {
				const chunk = decoder.decode(result.value, { stream: !result.done });
				if (!result.done) {
					try {
						data = JSON.parse(chunk);
					} catch (e) {
						console.error(e);
						throw new Error('JSON解析失败');
					}
					if (data.code === 200) {
						if (data.data?.final_report) {
							item.value.msg = data.data?.final_report;
							item.value.loading = false;
							isLoading.value = false;
							loadingAnimation.value = false;
							// 更新历史消息
							updateHistoryMsg();
						} else {
							textMsg.value = data.data?.event;
							return reader
								.read()
								.then(processStreamResult)
								.catch((e) => {
									errFun(e);
								});
						}
					} else {
						throw new Error('code !== 200');
					}
				}
			};
			return reader
				.read()
				.then(processStreamResult)
				.catch((e) => {
					errFun(e);
				});
		}
	} catch (e) {
		errFun(e);
	}
};

// 错误处理
const errFun = (e) => {
	isLoading.value = false;
	item.value.isBtns = false;
	item.value.loading = false;
	loadingAnimation.value = false;
	if (e.message.indexOf('aborted') > -1) {
		item.value.msg += ' 您停止生成了本次回答，请重新提交问题...';
	} else {
		item.value.msg += ' 接口响应出现错误，中断生成报告。';
	}
};

// 更新历史消息
const updateHistoryMsg = async () => {
	scrollBottom();
	await addHistoryMessage({ conversation_id: chatId.value, content: JSON.stringify({ ...getLastUser.value, skillType: 'userMessage' }), role: 'user', message_type: 'string' });
	let res1 = await addMsgApi({ conversation_id: chatId.value, content: JSON.stringify({ ...item.value }), role: 'assistant', message_type: 'string' });
	getLastChat.value.dataId = res1.data.message_id;
	await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
};

// 停止当前请求
const stopFetch = () => {
	// 如果正在请求，取消请求
	if (isLoading.value) {
		controller?.abort();
		isLoading.value = false;
	}
};

watch(
	item,
	(n) => {
		if (n) {
			nextTick(() => {
				init();
			});
		}
	},
	{ immediate: true }
);

const init = () => {
	// 如果msg没有信息，代表是新的消息
	if (!item.value.msg) setAiMessage(item.value.userMsg);
};

// 抛出函数
defineExpose({
	stopFetch,
});
</script>
<style lang="scss" scoped>
.mc-content-list-left {
	display: flex;
	flex-direction: column;
	padding-left: 15px;
	float: left;
	margin-bottom: 12px;
	.mc-content-template {
		border-top-left-radius: 8px;
		border-bottom-right-radius: 8px;
		border-top-right-radius: 8px;
	}
	img {
		width: 26px;
		height: 26px;
		margin-right: 6px;
		margin-top: 6px;
	}
}

.mc-content-template {
	max-width: 330px;
	min-width: 52px;
	background-color: var(--ac-bg-color--fff);
	padding: 10px;
}
</style>
