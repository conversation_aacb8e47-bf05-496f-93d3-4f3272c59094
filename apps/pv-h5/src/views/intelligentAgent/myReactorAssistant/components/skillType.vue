<template>
	<div v-if="props.skillList.length > 0 && props.skillList.every((item) => item.name !== activeAgent)" class="mc-btns">
		<div v-for="(i, index) in props.skillList" @click="qesMoreEv(i)" :key="index" class="mc-btns-item">{{ i.name }}</div>
	</div>
</template>
<script setup>
const props = defineProps(['skillList', 'activeAgent']);

const emits = defineEmits(['_qesAgent']);
const qesMoreEv = (i) => {
	emits('_qesAgent', i);
};
</script>

<style lang="scss" scoped>
// 按钮组
.mc-btns {
	width: 100%;
	margin: 0px auto;
	padding: 4px 6px;
	overflow-x: auto;
	display: flex;
	white-space: nowrap;
	.mc-btns-item {
		margin-right: 6px;
		border: 1px solid var(--ac-border-color);
		padding: 4px 10px;
		border-radius: 20px;
		font-size: 12px;
		color: var(--pv-no-active-color);
	}

	.mc-btns-item__actice {
		background-color: #d7e4ff;
		color: var(--ac-font-color-active);
		border-color: var(--ac-font-color-active);
	}
}
</style>
