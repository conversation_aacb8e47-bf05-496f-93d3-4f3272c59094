<template>
	<div class="mc-content-list-left">
		<!-- 用户内容模版 -->
		<div class="mc-content-template">
			<p>系统内置多种深度研究场景，您可根据需要选择不同思考方式或直接提问。</p>
			<div v-for="val in msgList" :key="val" @click="acTil(val)" class="mc-content-template-item">
				<van-cell :title="val" is-link />
			</div>
		</div>
	</div>
</template>
<script setup>
const msgList = ref(['整体销售情况报告', '下属团队销售情况报告', '各产品销售综合情况报告']);
const { chatList, chatId, isLoading, addHistoryMessage, getLastUserMsg, getLastChat, getLastUser } = useAssistant();

//界面滚动
import { useScriptAssistantScroll } from '@/hooks/web/useScriptAssistantScroll';
const { showScrollDown, scrollBottom, aiConScroll } = useScriptAssistantScroll();
const props = defineProps(['chatNum', 'skillInfo']);
const emits = defineEmits(['_sendMessage']);
const item = computed(() => {
	return chatList.value[props.chatNum];
});
watch(
	item,
	(n) => {
		if (n) {
			nextTick(() => {
				init();
			});
		}
	},
	{ immediate: true }
);

const init = () => {
	nextTick(() => {
		scrollBottom();
	});
};

const acTil = (val) => {
	emits('_sendMessage', val);
};
</script>
<style lang="scss" scoped>
.mc-content-list-left {
	display: flex;
	flex-direction: column;
	padding-left: 15px;
	float: left;
	margin-bottom: 12px;
	.mc-content-template {
		border-top-left-radius: 8px;
		border-bottom-right-radius: 8px;
		border-top-right-radius: 8px;

		.mc-content-template-item {
			margin-top: 8px;

			&:deep(.van-cell) {
				border-radius: 5px;
				padding: 7px 15px;
				color: var(--pv-default-color);
			}
		}
	}
}

.mc-content-template {
	max-width: 330px;
	min-width: 52px;
	background-color: var(--ac-bg-color--fff);
	padding: 10px;
}
</style>
