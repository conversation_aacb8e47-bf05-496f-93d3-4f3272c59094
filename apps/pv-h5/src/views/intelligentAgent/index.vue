<template>
	<div class="chat-list">
		<!-- loading -->
		<div v-if="loadingStatus === 'loading'">
			<van-skeleton style="margin-bottom: 8px" title :row="2" />
			<van-skeleton style="margin-bottom: 8px" title :row="2" />
			<van-skeleton style="margin-bottom: 8px" title :row="2" />
			<van-skeleton style="margin-bottom: 8px" title :row="2" />
			<van-skeleton style="margin-bottom: 8px" title :row="2" />
			<van-skeleton title :row="2" />
		</div>
		<van-empty v-else-if="loadingStatus === 'empty'" :description="$t('common.nodata')" />
		<div class="chat-list-item" v-for="item in agentList" :key="item.id" @click="toDetail(item)">
			<div class="chat-list-item-left">
				<img v-lazy="item.thumbnail" alt="" />
			</div>
			<div class="chat-list-item-right">
				<div>{{ item.name }}</div>
				<div>{{ item.attributes?.short_description }}</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { getQueryObject } from '@/utils/index';
const loadingStatus = ref('loading');
const agentList = ref([]);
import usefilterStore from '@/store/modules/filter';
import usePremissionStore from '@/store/modules/premission';
const filterStore = usefilterStore();
const router = useRouter();

const toDetail = (item) => {
	const name = item.attributes.url.split('?')[0];
	const query = getQueryObject(item.attributes.url);
	router.push({
		name,
		query: {
			...query,
			agent_id: item.id,
		},
	});
};
const { GET_USER_AGENT } = usePremissionStore();
const getAgentList = () => {
	GET_USER_AGENT().then((res) => {
		// 过滤掉工具类的智能体
		agentList.value = res.filter((item) => item.attributes?.agentType !== 'tools') || [];
		if (agentList.length === 0) {
			loadingStatus.value = 'empty';
		} else {
			loadingStatus.value = 'value';
			filterStore.SET_AGENTLIST(agentList.value);
		}
	});
};

const init = () => {
	// 获取智能体列表
	getAgentList();
};
onMounted(() => {
	init();
});
</script>
<style lang="scss" scoped>
.chat-list {
	&-item {
		display: flex;
		align-items: center;
		padding: 12px 20px;
		&-left {
			margin-right: 12px;
			img {
				width: 55px;
			}
		}
		&-right {
			div:nth-child(1) {
				color: #172b4d;
				font-weight: bold;
				font-size: 16px;
			}
			div:nth-child(2) {
				color: #6b778c;
				font-size: 12px;
				margin-top: 2px;
			}
		}
	}
}
</style>
