<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<div class="mc-header-name">{{ $t('chat.businessInsights') }}</div>
			<div class="mc-header-icon">
				<img @click="getChatHistory" class="history-img" src="@/assets/img/history.png" />
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div id="aiCon" class="mc-content-list clearfix">
				<template v-for="(item, index) in chatList">
					<template v-if="item.type === 'user'">
						<div :key="item.id" class="mc-content-list-right">
							<!-- 用户内容模版 -->
							<div @click="editorMsg(index, item)" class="mc-content-template">
								<p>{{ item.msg }}</p>
								<!-- 是否可以修改 -->
								<div class="mc-content-template__edit" v-if="index === chatList.length - 2"></div>
							</div>
						</div>
					</template>
					<template v-else>
						<div :key="item.id" class="mc-content-list-left">
							<div class="par" style="display: flex">
								<!-- AI 文本 内容模版 -->
								<div class="mc-content-template">
									<loading1 v-if="item.isLoading" :text="item.loadingText"></loading1>
									<!-- 纯文本 -->
									<ai-text v-if="!item.isIframe && !item.isLoading" :id="item.id" :is-loading="item.loading" :msg="item.msg"></ai-text>
									<!-- 卡片 -->
									<div v-if="item.isIframe && !item.isLoading">
										<div v-if="!item.isLoading" style="margin-top: 5px">
											<iframe class="ifr" :style="{ height: item.iframeHeight ? item.iframeHeight : '' }" v-if="item.iframeUrl" :src="item.iframeUrl" :ref="(el) => setRef(el, index)"></iframe>
										</div>
										<!-- summary -->
										<div v-if="item.summary">
											<ai-text :id="item.id" :is-loading="item.summaryLoading" :msg="item.summary"></ai-text>
										</div>
									</div>
									<!-- 下一步行动计划 -->
									<div v-if="item.nextAction && showEtc(item)">
										<div class="btns toPlan" v-for="(item, index) in item.nextAction" :key="index" @click="toNextAction(item)">
											<span class="toPlan">{{ item }}</span>
											<div class="right"></div>
										</div>
									</div>
									<!-- 小按钮组 - 语音播报和复制 -->
									<ai-btns v-if="item.isBtns && !item.loading && !item.isLoading" :id="item.id" :reGen="item.reGen" :zeStatus="item.zeStatus" @_ze="zeEv" @_reGen="reGen" :voiceStatus="item.voiceStatus" @copyText="copyText" @voiceEv="voiceEv"></ai-btns>
								</div>
							</div>
							<!-- 问题列表 -->
							<div class="ql" v-if="item.questionList && showEtc(item)">
								<div class="nk">您可能还想问</div>
								<div class="mc-content__groupList">
									<span v-for="(val, index) in item.questionList" :key="val" @click="qesEv(val, index, item.questionList)">{{ val }}</span>
								</div>
							</div>
						</div>
					</template>
				</template>
			</div>
			<!-- 声明 -->
			<div v-if="inForUse" class="mc-declare">{{ inForUse }}</div>
			<!-- 聊天框 -->
			<div ref="chatBox" class="mc-content-chat">
				<ai-input :editorText="editorText" @_sendMessage="sendMessage" @_changeEdit="changeEdit"></ai-input>
			</div>
			<!-- 滚动到底部 -->
			<Teleport to="body">
				<Transition name="slide">
					<div v-if="showScrollDown" class="scroll-down" @click="scrollBottom">
						<img src="@/assets/img/scrolldown.png" alt="" />
					</div>
				</Transition>
			</Teleport>
		</div>
		<!-- 历史会话 -->
		<chat-history :historyList="historyList" ref="cHs" @_openChat="openHistoryChat"></chat-history>
	</div>
</template>
<script setup>
import { getToken } from '@/utils/auth';
import { showToast } from 'vant';
import useUserStore from '@/store/modules/user';
import useEnterStore from '@/store/modules/enterprise';
import ClipboardJS from 'clipboard';
import CryptoJS from 'crypto-js';
import AudioPlayer from '@/utils/ap';
import axios from 'axios';
import { uuid, getRandomString, ruleAllList, addOrUpdateQueryParameter } from '@/utils/index';
import { getSalesAchievement } from '@/api/sales';
import { useClickAway } from '@vant/use';
import usefilterStore from '@/store/modules/filter';
import { getAgentListApi } from '@/api/agent-tools';
const filterStore = usefilterStore();
const enterStore = useEnterStore();
// 接口地址
const interfaceUrl = enterStore.enterInfo.interfaceAddress;
const domain = enterStore.enterInfo.domain;
const audioPlayer = new AudioPlayer();
const userStore = useUserStore();

const isLoading = ref(false); //页面是否有正在回答的消息
const chatList = ref([]); //消息列表
// 返回
const route = useRoute();
const goBack = () => {
	router.go(-1);
};

// 语言环境
const language = computed(() => {
	return filterStore.language === 'zh' ? '中文' : '英文';
});

// 赞和踩
const zeEv = (val) => {
	const j = chatList.value.filter((ele) => ele.id === val.id)[0];
	const json = {
		username: userStore.userInfo.username,
		message_id: j.dataId,
		feedback_score: '',
		feedback_reason: '',
	};
	j.zeStatus = val.like;
	if (val.like === '2') {
		// 踩
		json.feedback_score = 0;
	} else if (val.like === '1') {
		// 赞
		json.feedback_score = 100;
	} else {
		json.feedback_score = -1;
	}
	axios({
		url: interfaceUrl + '/API-OPENGPT/api/session/history/update_history_feedback',
		method: 'put',
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
		params: json,
	});
};
//生成一条用户消息
const createUserMsg = (text) => {
	return {
		msg: text,
		dataId: '',
		type: 'user',
		id: uuid(),
		loading: false,
		zeStatus: '0',
	};
};
//生成一条ai消息
const createAiMsg = (text) => {
	let message = {
		msg: '',
		dataId: '',
		type: 'ai',
		id: uuid(),
		loading: true,
		isBtns: true,
		reGen: true,
		voiceStatus: true,
		zeStatus: '0',
		isIframe: '',
	};
	text ? (message.loadingText = '正在为您生成每月经营情况播报') : '';
	return message;
};
//猜你想问
const qesEv = (val) => {
	if (!isLoading.value) {
		isLoading.value = true;
		chatList.value.push(createUserMsg(val));
		// 生产一个ai的消息
		chatList.value.push(createAiMsg());
		scrollBottom();
		gd(val);
	}
};
// 重新生成
const reGen = (id) => {
	// 获取文本信息
	let prevItemMsg = '';
	const index = chatList.value.findIndex((item) => item.id === id);

	if (index !== -1) {
		if (chatList.value[index].afterStopAutoRead) {
			chatList.value.splice(index, 1);
			init();
			return;
		}
		prevItemMsg = chatList.value[index - 1]?.msg ?? '';
	}
	try {
		chatList.value.push(createUserMsg(prevItemMsg));
		// 生产一个ai的消息
		chatList.value.push(createAiMsg());
		scrollBottom();
		gd(prevItemMsg);
	} catch (e) {
		console.log(e);
		showToast({
			position: 'top',
			message: '重新生成失败！',
		});
	}
};
// 历史记录
const cHs = ref(null);
const getChatHistory = () => {
	cHs.value.showLeft = true;
};
//查询左侧会话记录
const historyList = ref([]);
const getConversChatList = async () => {
	// 获取fastGpt历史会话记录
	try {
		let allList = await getOtherChatList();
		allList = allList.reduce((acc, cur) => {
			const index = acc.findIndex((item) => item.updateTime.split('T')[0] === cur.updateTime.split('T')[0]);
			if (index !== -1) {
				acc[index].children.push(cur);
			} else {
				acc.push({
					updateTime: cur.updateTime,
					name: cur.updateTime.split('T')[0],
					children: [cur],
				});
			}
			return acc;
		}, []);
		// 把allList根据updateTime做个降序
		allList.sort((a, b) => {
			return new Date(b.updateTime) - new Date(a.updateTime);
		});
		for (const item of allList) {
			item.children.sort((a, b) => {
				return new Date(b.updateTime) - new Date(a.updateTime);
			});
		}
		if (allList.length > 0) {
			allList = ruleAllList(allList);
		}
		historyList.value = allList;
	} catch (error) {
		console.log(error);
		showToast({
			position: 'top',
			zIndex: 8888,
			message: '会话记录获取失败',
		});
	}
};

// 其他模块的会话列表
const getOtherChatList = () => {
	return new Promise((resolve, reject) => {
		const json = {
			username: userStore.userInfo.username,
			skip: 0,
			limit: 70,
			agent_id: '008',
		};
		axios({
			url: interfaceUrl + '/API-OPENGPT/api/session/history/user/page',
			method: 'get',
			params: json,
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		})
			.then((res) => {
				// 把 res.data.data 根据 updateTime 拆分成treeList
				for (const cur of res.data.data) {
					cur.chatType = 'jydc';
					cur.updateTime = cur.created_time.substring(0, 19);
					cur.chatId = cur.conversation_id;
					cur.title = cur.query;
				}
				resolve(res.data.data);
			})
			.catch(() => {
				reject('other会话出错了');
			});
	});
};
// 打开历史记录会话
const openHistoryChat = (item) => {
	if (item.chatId === chatId1.value) return;
	if (isLoading.value) {
		showToast({
			message: '正在回答，请稍后...',
			position: 'top',
		});
		return;
	}
	chatList.value = [];
	// router.replace({
	// 	name: route.name,
	// 	query: {
	// 		chatId: item.chatId,
	// 	},
	// });
	chatId1.value = item.chatId;
	const params = new URLSearchParams(window.location.search);
	params.set('chatId', chatId1.value); // 添加或更新 query 参数
	const newUrl = window.location.pathname + '?' + params.toString();
	history.replaceState(null, '', newUrl);
};

// 获取历史记录
const getHistoryList = async () => {
	const json = {
		username: userStore.userInfo.username,
		chat_type: 'jydc',
		conversation_id: chatId1.value,
		limit: 10,
		skip: 0,
	};
	axios({
		url: interfaceUrl + '/API-OPENGPT/api/session/history/find_by_conversation_id',
		method: 'get',
		params: json,
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
	})
		.then((res) => {
			if (res.data.code === 200 && res.data.data.length > 0) {
				for (const item of res.data.data) {
					console.log(item);
					chatList.value.push(createUserMsg(item.query));
					chatList.value.push(item.meta_data);
				}
				return Promise.resolve('1');
			}
		})
		.finally(() => {
			nextTick(() => {
				scrollBottom();
			});
		});
};

// 把fastgpt的会话记录同步到公司库
const asyncChatLog = (message, response, metaData, qa = '') => {
	return new Promise((resolve) => {
		axios({
			url: interfaceUrl + '/API-OPENGPT/api/session/history/create',
			method: 'post',
			headers: {
				Authorization: `Bearer ${getToken()}`,
				'Content-Type': 'application/json;charset=utf-8',
			},
			data: {
				meta_data: metaData,
				user_id: userStore.userInfo.username,
				conversation_id: chatId1.value,
				query: message,
				response: response,
				chat_type: 'jydc',
				data_temp1: qa,
				agent_id: '008',
			},
		})
			.then((res) => {
				getLastChat.value.dataId = res.data.data.id;
			})
			.finally(() => {
				resolve();
			});
	});
};

//获取最后一条用户消息
const getLastUser = () => {
	let ar = chatList.value.filter((ele) => ele.type === 'user');
	let msg = ar?.[ar.length - 1]?.msg;
	return msg;
};
//获取最后一条chat
const getLastChat = computed(() => {
	return chatList.value[chatList.value.length - 1];
});
//固定消息
const gd = (value) => {
	getLastChat.value.isLoading = true;
	//获取猜你想问
	// getSuggestion({ type: 'cardSuggestion', Question: getLastUser() });
	setTextMessage(value);
};

// 发送消息
const sendMessage = (val) => {
	if (!isLoading.value) {
		if (val) {
			isLoading.value = true;
			chatList.value.push(createUserMsg(val));
			// 生产一个ai的消息
			chatList.value.push(createAiMsg());
			scrollBottom();
			gd(val);
		} else {
			showToast({
				position: 'top',
				message: '请输入内容',
			});
		}
	} else {
		if (val) {
			isLoading.value = true;
			stopFetch();
			chatList.value.push(createUserMsg(val));
			// 生产一个ai的消息
			chatList.value.push(createAiMsg());
			scrollBottom();
			gd(val);
		} else {
			showToast({
				position: 'top',
				message: '请输入内容',
			});
		}
	}
	if (!route.query.chatId) {
		//向url中添加query chatId
		const params = new URLSearchParams(window.location.search);
		params.set('chatId', chatId1.value); // 添加或更新 query 参数
		const newUrl = window.location.pathname + '?' + params.toString();
		history.replaceState(null, '', newUrl);
	}
};
//停止当前请求
const stopFetch = () => {
	controllerList.forEach((ele) => ele.abort());
	getLastChat.value.loading = false;
	getLastChat.value.isLoading = false;
	if (getLastChat.value.msg) {
		getLastChat.value.msg = getLastChat.value.msg + '...';
	}
	if (getLastChat.value.summary) {
		getLastChat.value.summary = getLastChat.value.summary + '...';
	}
	getLastChat.value.summaryLoading = false;
	getLastChat.value.nextAction = '';
	getLastChat.value.temNextAction = '';
	getLastChat.value.questionList = '';
	getLastChat.value.temQuestionList = '';
	isLoading.value = false;
	controllerList = [];
	if (getLastChat.value.afterStopAutoRead) {
		getLastChat.value.voiceStatus = false;
		voiceEv({
			id: getLastChat.value.id,
			vType: 'play',
		});
	}
};
// 发送的消息是否是二次编辑的消息
const isEdit = ref(false);
const editorText = ref('');
const editorMsg = (index, item) => {
	if (index === chatList.value.length - 2) {
		// 如果请求正在进行需要终止请求
		if (isLoading.value) {
			stopFetch();
			isLoading.value = false;
		}
		editorText.value = item.msg + 'jjjjjj' + new Date().getTime();
		isEdit.value = true;
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isEdit.value = false;
	}
};
const changeEdit = () => {
	isEdit.value = false;
	editorText.value = '';
};
// 文本框失去焦点
const chatBox = ref();
useClickAway(
	chatBox,
	() => {
		if (isEdit.value) {
			editorText.value = '';
		}
		isEdit.value = false;
	},
	{ eventName: 'touchend' }
);

// 会话ID
let chatId1 = ref(route.query.chatId ? route.query.chatId : 'jydc' + getRandomString());
let appId = '';
let appKey = '';
watch(chatId1, (n) => {
	getHistoryList();
});
//处理猜你想问和下一步行动计划出现时机
const showEtc = computed(() => {
	return (item) => {
		if (item.isIframe) {
			if (!item.summaryLoading) {
				return true;
			}
		} else {
			if (!item.loading) {
				return true;
			}
		}
	};
});
let controllerList = [];
const fetchRequest = (val) => {
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async (resolve) => {
		try {
			let controller = new AbortController();
			controllerList.push(controller);
			const { signal } = controller;
			const message = val || '';
			let radom1 = uuid();
			let radom2 = uuid();
			let url = enterStore.enterInfo.agentAddress;
			let res = await fetch(`${url}/api/v1/chat/completions`, {
				signal,
				method: 'POST',
				headers: {
					'Content-Type': 'application/json;charset=utf-8',
					Authorization: 'Bearer ' + appKey,
				},
				body: JSON.stringify({
					appId,
					chatId: chatId1.value,
					stream: true,
					detail: true,
					variables: {
						language: language.value,
					},
					messages: [
						{
							role: 'user',
							content: JSON.stringify({ message, role: getRole.value }),
							dataId: radom1,
						},
						{ role: 'assistant', content: '', dataId: radom2 },
					],
				}),
			});
			if (!res?.body || !res?.ok) {
				throw new Error('Request Error');
			}
			resolve(res);
		} catch (error) {
			console.log(error, 'xxxxxxxxxxxxxxxxxxxxxxx');
		}
	});
};
const handleStream = (res, type) => {
	let fixedVariable = {
		Suggestion: ['temQuestionList', 'questionList'],
		NextAction: ['temNextAction', 'nextAction'],
	};
	const reader = res.body?.getReader();
	const decoder = new TextDecoder('utf-8');
	let buffer = '';
	function processStreamResult(result) {
		const chunk = decoder.decode(result.value, { stream: !result.done });
		buffer += chunk;
		// 逐条解析后端返回数据
		const lines = buffer.split('\n');
		buffer = lines.pop();
		lines.forEach((line) => {
			if (line.trim().length === 0) return;
			if (line.indexOf('data:') === -1 || line.split('data:')[1] === ' [DONE]') return;
			const resData = JSON.parse(line.split('data:')[1]);
			if (resData.choices && resData.choices[0].delta.content) {
				const text = resData.choices[0].delta.content;
				console.log(text);
				if (type === 'Summary') {
					getLastChat.value.summary += text;
				} else if (type === 'cardOrMsg') {
					getLastChat.value.msg += text;
					if (getLastChat.value.msg.includes('cardCom')) {
						getLastChat.value.isIframe = true;
						getLastChat.value.summaryLoading = true; //加载summary
					}
					if (!getLastChat.value.msg.includes('```')) {
						getLastChat.value.isLoading = false;
					}
				} else {
					getLastChat.value[fixedVariable[type][0]] += text;
				}
			}
		});
		if (!result.done) {
			return reader.read().then(processStreamResult);
		} else {
			if (type === 'Summary') {
				//如果时summary
				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value.summary);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
				getLastChat.value.summaryLoading = false;
				isLoading.value = false;
				getLastChat.value.loading = false;
				// 命中卡片的情况, 分析结束后同步信息
				let iframeUrl = addOrUpdateQueryParameter(getLastChat.value.iframeUrl, 'summary', 0);
				asyncChatLog(getLastUser(), getLastChat.value.summary, { ...getLastChat.value, iframeUrl }, 'QA');
			} else if (type === 'cardOrMsg') {
				if (getLastChat.value.isIframe) {
					// 去除开头和结尾的 ```json 标记
					let trimmedText = getLastChat.value.msg
						.trim()
						.replace(/^```json\s+/, '')
						.replace(/\s+```$/, '');
					// 解析 JSON 字符串为 JavaScript 对象或数组
					const jsonData = JSON.parse(trimmedText);
					console.log(jsonData);
					// 提取 params 和 QA
					const { params, url } = jsonData[0];
					// 构建查询字符串
					const queryString = Object.entries(params)
						.map(([key, value]) => `${key}=${value}`)
						.join('&');
					getLastChat.value.msg = jsonData;
					if (queryString) {
						getLastChat.value.iframeUrl = url + '?' + queryString;
					} else {
						getLastChat.value.iframeUrl = url;
					}
					getLastChat.value.isLoading = false;
				} else if (getLastChat.value.msg.includes('```json') && getLastChat.value.msg.includes('[]')) {
					// 查询卡片为空的情况
					getLastChat.value.isLoading = false;
					getLastChat.value.loading = false;
					getLastChat.value.msg = '当前找不到合适的数据来回答您的问题。';
					isLoading.value = false;
					stopFetch();

					// 没有命中卡片的情况, 直接同步绘画信息
					asyncChatLog(getLastUser(), getLastChat.value.msg, getLastChat.value, 'AI');
					// let ar = chatList.value.filter((ele) => ele.type === 'user');
					// let msg = ar?.[ar.length - 1]?.msg;
					// setTextMessage({ type: 'cardComSummary', Question: msg });
				} else {
					if (getLastChat.value.afterStopAutoRead) {
						getLastChat.value.voiceStatus = false;
						voiceEv({
							id: getLastChat.value.id,
							vType: 'play',
						});
					}
					isLoading.value = false;
					getLastChat.value.loading = false;
				}
				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value.msg);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
			} else {
				//如果时猜你想问和下一步行动
				// 去除开头和结尾的 ```json 标记
				let trimmedText = getLastChat.value[fixedVariable[type][0]]
					.trim()
					.replace(/^```json\s+/, '')
					.replace(/\s+```$/, '');
				getLastChat.value[fixedVariable[type][1]] = JSON.parse(trimmedText).data;
				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value[fixedVariable[type][1]], type);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
			}
			scrollBottom();
		}
	}
	reader.read().then(processStreamResult);
};
//发送信息
const setTextMessage = async (val) => {
	try {
		let res = await fetchRequest(val);
		handleStream(res, 'cardOrMsg');
	} catch (e) {
		handleError(e);
	}
};
//获取summary
const getSummary = async (data) => {
	try {
		getLastChat.value.summary = '';
		let res = await fetchRequest(data);
		handleStream(res, 'Summary');
	} catch (e) {
		handleError(e);
	}
};
//获取猜你想问
const getSuggestion = async (data) => {
	try {
		getLastChat.value.questionList = '';
		getLastChat.value.temQuestionList = '';
		let res = await fetchRequest(data);
		handleStream(res, 'Suggestion');
	} catch (e) {
		handleError(e);
	}
};
//获取下一步行动计划
const nextAction = async (data) => {
	try {
		getLastChat.value.nextAction = '';
		getLastChat.value.temNextAction = '';
		let res = await fetchRequest(data);
		handleStream(res, 'NextAction');
	} catch (e) {
		handleError(e);
	}
};
let handleError = (e) => {
	showToast({
		position: 'top',
		message: '请求失败,请重试',
	});
	isLoading.value = false;
	// 清除chatList最后一位
	chatList.value.splice(-1, 1);
	console.error(e);
};
let router = useRouter();
const toNextAction = (value) => {
	let action = {
		创建拜访计划: 'keyTaskDetail',
		记录实地拜访: 'mAssistant',
		发送会议邀请: 'toMeet',
		传递关键资料: 'visitArticle',
	};
	if (action[value] === 'keyTaskDetail') {
		router.push({
			name: 'keyTaskDetail',
			query: {
				isCreateTask: 'create',
				taskType: '线下拜访',
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else {
		router.push({
			name: action[value],
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	}
};
let isScrollIng = ref(false);
const scrollBottom = () => {
	isScrollIng.value = true;
	nextTick(() => {
		document.querySelector('#aiCon').scrollTo({
			top: document.querySelector('#aiCon').scrollHeight,
			behavior: 'smooth',
		});
		setTimeout(() => {
			isScrollIng.value = false;
		}, 500);
	});
};
let showScrollDown = ref(false);
// 语音相关
const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let ttsWS = null;
const voiceEv = async (val) => {
	// chatList里所有的voiceStatus改位true
	resetAudioVoice();
	// 通过val.id获取chatList的相等元素
	const index = chatList.value.findIndex((item) => item.id === val.id);
	chatList.value[index].voiceStatus = val.vType === 'play' ? false : true;

	ttsWS?.close();
	audioPlayer.reset();
	if (val.vType === 'play') {
		// 开始语音播报
		const url = getWebSocketUrl(API_KEY, API_SECRET);
		if ('WebSocket' in window) {
			ttsWS = new WebSocket(url);
		} else if ('MozWebSocket' in window) {
			ttsWS = new MozWebSocket(url);
		} else {
			showToast({
				position: 'top',
				message: '浏览器不支持WebSocket',
			});
			return;
		}
		// 发送消息
		ttsWS.onopen = () => {
			audioPlayer.start({
				autoPlay: true,
				sampleRate: 16000,
				resumePlayDuration: 1000,
			});
			let text = document.getElementById(val.id).innerText;
			let tte = 'UTF8';
			let params = {
				common: {
					app_id: APPID,
				},
				business: {
					aue: 'raw',
					auf: 'audio/L16;rate=16000',
					vcn: 'x4_lingfeichen_assist', //发音人
					speed: 50, // 语速
					volume: 50, // 音量
					pitch: 50, // 音色
					bgs: 0,
					tte,
				},
				data: {
					status: 2,
					text: encodeText(text, tte),
				},
			};
			ttsWS.send(JSON.stringify(params));
		};

		ttsWS.onmessage = (e) => {
			let jsonData = JSON.parse(e.data);
			// 合成失败
			if (jsonData.code !== 0) {
				showToast({
					position: 'top',
					message: '语音合成失败, 请稍后再试',
				});
				return;
			}
			audioPlayer.postMessage({
				type: 'base64',
				data: jsonData.data.audio,
				isLastData: jsonData.data.status === 2,
			});
			if (jsonData.code === 0 && jsonData.data.status === 2) {
				//0是成功2是最后一次
				ttsWS.close();
			}
		};

		ttsWS.onerror = (e) => {
			console.error(e);
		};
		ttsWS.onclose = () => {};
	}
};
const resetAudioVoice = () => {
	for (const i of chatList.value) {
		if (i.type === 'user') continue;
		if (i.isBtns === false) continue;
		if (i.voiceStatus === true) continue;
		i.voiceStatus = true;
	}
};

// 播放停止把voiceStatus改位true
audioPlayer.onStop = () => {
	resetAudioVoice();
};
const encodeText = (text, type) => {
	if (type === 'unicode') {
		let buf = new ArrayBuffer(text.length * 4);
		let bufView = new Uint16Array(buf);
		for (let i = 0, strlen = text.length; i < strlen; i++) {
			bufView[i] = text.charCodeAt(i);
		}
		let binary = '';
		let bytes = new Uint8Array(buf);
		let len = bytes.byteLength;
		for (let i = 0; i < len; i++) {
			binary += String.fromCharCode(bytes[i]);
		}
		return window.btoa(binary);
	} else {
		return Base64.encode(text);
	}
};
const getWebSocketUrl = (apiKey, apiSecret) => {
	var url = 'wss://tts-api.xfyun.cn/v2/tts';
	var host = location.host;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};

// 收到iframe传入的消息后处理卡片
const afterMessage = (event) => {
	if (event.data.source) return;
	console.log('=======================收到子iframe消息=======================');
	console.log(event);
	// 获取内嵌页面的高度
	if (!event.data.cardHeight) return;
	const height = event.data.cardHeight + 'px';
	// 设置 iframe 的高度
	boxRefs.value[boxRefs.value.length - 1].style.height = height;
	getLastChat.value.iframeHeight = height;
	//有数据进行下一步获取总结和猜你想问、下一步行动计划
	if (event.data.data) {
		let ar = chatList.value.filter((ele) => ele.type === 'user');
		let msg = ar?.[ar.length - 1]?.msg;
		getSummary({ type: 'cardComSummary', Question: msg, data: { ...event.data.data } });
		// nextAction({ type: 'cardNextAction', Question: msg });
	}
	if (event.data.noSummary) {
		let iframeUrl = addOrUpdateQueryParameter(getLastChat.value.iframeUrl, 'summary', 0);
		asyncChatLog(getLastUser(), '暂无数据或权限', { ...getLastChat.value, iframeUrl }, 'QA');
	}
};
const init = async () => {
	isLoading.value = true;
	chatList.value.push({
		...createAiMsg('正在为您生成每日业绩播报'),
		afterStopAutoRead: true,
	});

	let data = {};
	let [res1, res2, res3] = await Promise.all([getSalesAchievement({ date: '202406', type: '净利润' }), getSalesAchievement({ date: '202406', type: '收入' }), getSalesAchievement({ date: '202406', type: '费用' })]);
	data = {
		净利润: { ...res1.result },
		收入: { ...res2.result },
		费用: { ...res3.result },
	};
	getLastChat.value.isLoading = true;
	//获取猜你想问
	// let msg = getLastUser();
	// getSuggestion({ type: 'cardSuggestion', Question: { ...data } });
	// nextAction({ type: 'cardNextAction', Question: { ...data } });
	setTextMessage({ ...data, type: 'cardDailyReport' });
};

const aiConScroll = (e) => {
	const container = e.target;
	const scrollTop = container.scrollTop;
	const scrollHeight = container.scrollHeight;
	const clientHeight = container.clientHeight;
	// 计算可滚动的高度
	const scrollableHeight = scrollHeight - clientHeight;

	// 如果当前滚动的位置小于可滚动的高度，表示页面没有滚动到底部
	const isScrollAtBottom = scrollTop + 50 >= scrollableHeight;
	if (!isScrollAtBottom) {
		// 其他处理滚动事件的代码
		requestAnimationFrame(() => {
			!isScrollIng.value && (showScrollDown.value = true);
		});
	} else {
		showScrollDown.value = false;
	}
};
const inForUse = ref('');
onMounted(async () => {
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
	// 智能体基本信息
	const agentList = await getAgentListApi();
	for (const ele of agentList.result.content) {
		if (ele.attributes.url.indexOf('businessInsights') > -1) {
			appId = ele.attributes.appID;
			appKey = ele.attributes.appKey;
			inForUse.value = ele.attributes?.inForUse || '';
			break;
		}
	}

	if (!route.query.chatId) {
		init();
	}

	window.addEventListener('message', afterMessage);
	nextTick(() => {
		document.querySelector('#aiCon').addEventListener('scroll', aiConScroll);
	});
	getConversChatList();
	if (route.query.chatId) {
		await getHistoryList();
	}
});
onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
});
onUnmounted(() => {
	window.removeEventListener('message', afterMessage);
	window.removeEventListener('scroll', aiConScroll);
});

const getRole = computed(() => {
	let roles = ['ADMIN', 'BUD', 'RSD', 'RSM', 'DSM', 'MR'];
	return userStore.permission.find((ele) => roles.includes(ele));
});
const copyText = (id) => {
	const clipboard = new ClipboardJS('.copy-btn', {
		target: function () {
			return document.getElementById(id);
		},
	});

	clipboard.on('success', () => {
		showToast({
			position: 'top',
			message: '复制成功',
		});
		clipboard.destroy();
	});
};

const boxRefs = ref([]);
const setRef = (el, index) => {
	if (el) {
		if (!boxRefs.value.includes(el)) {
			boxRefs.value.push(el);
		}
	}
};
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-list {
			flex: 1 0 0px;
			height: 0px;
			padding: 0 0;
			padding-top: 12px;
			overflow-y: auto;
			.mc-content-list-left {
				display: flex;
				flex-direction: column;
				width: 100%;
				margin-bottom: 12px;
				.mc-content-template {
					border-top-left-radius: 8px;
					border-bottom-right-radius: 8px;
					border-top-right-radius: 8px;
				}
				img {
					width: 26px;
					height: 26px;
					margin-right: 6px;
					margin-top: 6px;
				}

				.mc-content__groupList {
					padding: 8px 0 0 0;
					padding-left: 15px;
					display: flex;
					display: flex;
					overflow-x: auto;
					width: 100%;
					span {
						flex: none;
						margin-right: 8px;
						margin-bottom: 8px;
						color: #0b67e4;
						border-radius: 5px;
						border: 1px solid #0b67e4;
						padding: 3px 6px;
					}
				}
			}

			.mc-content-list-right {
				padding-right: 15px;
				display: flex;
				margin-bottom: 12px;
				justify-content: flex-end;
				.mc-content-template {
					border-top-right-radius: 8px;
					border-bottom-left-radius: 8px;
					border-top-left-radius: 8px;
					color: var(--ac-bg-color--fff);
					background-color: var(--ac-bg-active-color);
					.mc-content-template__edit {
						border-top: solid 1px rgba(223, 225, 230, 0.3);
						margin-top: 2px;
					}
				}
			}
			.mc-content-template {
				max-width: 330px;
				min-width: 52px;
				background-color: var(--ac-bg-color--fff);
				padding: 10px;
			}
		}
		// 声明
		.mc-declare {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 6px 0px 3px 0px;
			font-size: 12px;
			color: var(--ac-colors-myGray-500);
		}
		.mc-content-chat {
			background-color: #fff;
			z-index: 99;
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 8px;
			padding-bottom: 6px;
			position: relative;
			height: 64px;
			display: flex;
			align-items: center;
			.chat-img {
				width: 25px;
				object-fit: contain;
				margin-left: 15px;
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
.btns {
	width: 100%;
	height: 35px;
	line-height: 35px;
	border-radius: 5px;
	background-color: #ffffff;
	color: #172b4d;
	margin-top: 10px;
	padding-left: 12px;
	padding-right: 15px;
	display: flex;
	align-items: center;

	span {
		flex: 1;
	}

	.right {
		width: 8px;
		height: 8px;
		border-top: 1px solid #6b778c;
		border-right: 1px solid #6b778c;
		transform: rotate(45deg);
	}
}
.par {
	margin-left: 15px;
}
.ql {
	margin-top: 12px;
	.nk {
		margin-left: 15px;
		color: #6b778c;
		font-size: 12px;
	}
	.mc-content__groupList {
		scrollbar-width: none; /* firefox */
		-ms-overflow-style: none; /* IE 10+ */
		&::-webkit-scrollbar {
			display: none; /* Chrome Safari */
		}
	}
}
.ifr {
	width: 100%;
	border: none;
	height: 1px;
	border-radius: 5px;
	background-color: var(--ac-bg-color--fff);
	min-width: 310px;
}
.scroll-down {
	position: fixed;
	bottom: calc(65px + constant(safe-area-inset-bottom)); //兼容 IOS<11.2
	bottom: calc(65px + env(safe-area-inset-bottom));
	left: 50%;
	transform: translateX(-50%);
	img {
		width: 29px;
		height: 29px;
	}
}
// 华东动画
.slide-enter-active,
.slide-leave-active {
	transition: all 0.2s ease-in;
}
.slide-enter-from,
.slide-leave-to {
	transform: translate(-50%, 25px);
	transform: translate(-50%, 25px);
	opacity: 0;
}
.history-img {
	margin-left: 12px;
	margin-right: 24px;
}
</style>
