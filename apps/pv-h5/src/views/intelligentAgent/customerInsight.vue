<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div
				@click="goBack"
				:style="{
					visibility: route?.query?.isHeaderBackShow === 'false' ? 'hidden' : '',
				}"
				class="mc-header-icon"
			>
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">
				{{ relam === 'muqiao' ? agentName : $t('chat.customerVisitSuggestions') }}
			</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">
				<img @click="acGlobalVoice(false)" v-show="isGlobalVoice" src="@/assets/img/global-voice-active.png" />
				<img @click="acGlobalVoice(true)" v-show="!isGlobalVoice" src="@/assets/img/global-voice.png" />
				<img @click="getChatHistory" class="history-img" src="@/assets/img/history.png" />
			</div>
		</div>
		<div class="mc-j-header"></div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div id="aiCon" class="mc-content-list clearfix">
				<template v-for="(item, index) in chatList">
					<template v-if="item.type === 'user'">
						<div :key="item.id" class="mc-content-list-right">
							<!-- 用户内容模版 -->
							<div v-if="item.templateType === 'text'" @click="editorMsg(index, item)" class="mc-content-template">
								<!-- 纯文本 -->
								<p v-html="item.msg"></p>
								<!-- 是否可以修改 -->
								<div class="mc-content-template__edit" v-if="index === chatList.length - 2"></div>
							</div>
							<div v-else class="mc-content-info">
								<div style="margin-top: 0" class="docList-list-item">
									<img v-if="item.msg.doctorSex === '女'" src="@/assets/img/demo/gril.png" />
									<img v-else src="@/assets/img/demo/men.png" />
									<div class="amd-content-item-desc">
										<div>
											<span class="desc-name" style="margin-right: 8px">{{ item.msg.doctorName }}</span>
											<span>{{ item.msg.doctorTitle }}</span>
										</div>
										<div class="desc-hp ell1">
											<span style="margin-right: 8px">{{ item.msg.hospital }}</span>
											<span style="margin-right: 8px">{{ item.msg.department }}</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</template>
					<template v-else-if="item.html">
						<div :key="item.id" class="mc-content-list-left">
							<div style="display: flex">
								<!-- AI 头像 -->
								<img src="@/assets/img/demo/ai.png" />
								<!-- AI 文本 内容模版 -->
								<div v-if="item.templateType === 'text'" :class="{ 'mc-content-template': true, chatLoading: item.loading }">
									<!-- 纯文本 -->
									<ai-html :id="item.id" :msg="item.html" @to360="to360"></ai-html>
									<!-- 小按钮组 - 语音播报和复制 -->
									<ai-btns :id="item.id" :zeStatus="item.zeStatus" @_ze="zeEv" :voiceStatus="item.voiceStatus" @copyText="copyText" @voiceEv="voiceEv"></ai-btns>
								</div>
							</div>
						</div>
					</template>
					<template v-else>
						<div :key="item.id" class="mc-content-list-left">
							<div style="display: flex">
								<!-- AI 头像 -->
								<img src="@/assets/img/demo/ai.png" />
								<!-- AI 文本 内容模版 -->
								<div v-if="item.templateType === 'text'" :class="{ 'mc-content-template': true }">
									<loading1 v-if="item.isLoading" :text="item.loadingText"></loading1>

									<!-- 纯文本 -->
									<ai-text v-if="!item.isIframe && !item.isLoading" :id="item.id" :msg="item.msg" :is-loading="item.loading"></ai-text>
									<!-- 下一步行动计划 -->
									<div v-if="item.nextAction && showEtc(item)">
										<div class="btns toPlan" v-for="(item, index) in item.nextAction" :key="index" @click="toNextAction(item)">
											<span class="toPlan">
												<span v-if="item === 'Wecome沟通'">
													<strong style="color: #0b67e4">{{ item.slice(0, 6) }}</strong>
													<span> {{ item.slice(6) }}</span>
												</span>
												<span v-else>
													<strong style="color: #0b67e4">{{ item.slice(0, 2) }}</strong>
													<span> {{ item.slice(2) }}</span>
												</span>
											</span>
											<div class="right"></div>
										</div>
									</div>
									<!-- 小按钮组 - 语音播报和复制 -->
									<ai-btns v-if="item.isBtns && !item.loading" :id="item.id" :reGen="item.reGen" @_reGen="reGen" :zeStatus="item.zeStatus" @_ze="zeEv" :voiceStatus="item.voiceStatus" @copyText="copyText" @voiceEv="voiceEv"></ai-btns>
								</div>

								<div v-else-if="item.templateType === 'docCard'" class="content-docList">
									<div class="docList-list">
										<!-- header -->
										<div class="docList-list-header">
											<span class="docList-list-header__name">医生360</span>
										</div>
										<div style="margin-top: 8px" class="docList-list-item">
											<img v-if="item.msg.doctorSex === '女'" src="@/assets/img/demo/gril.png" />
											<img v-else src="@/assets/img/demo/men.png" />
											<div class="amd-content-item-desc">
												<div>
													<span class="desc-name" style="margin-right: 8px">{{ item.msg.doctorName }}</span>
													<span>{{ item.msg.doctorTitle }}</span>
												</div>
												<div class="desc-hp ell1">
													<span style="margin-right: 8px">{{ item.msg.hospital }}</span>
												</div>
											</div>
										</div>
									</div>
									<div class="docList-btns">
										<div @click="docBtnItem(item.msg, '概览')" class="docList-btns-item">
											<span>概览</span>
											<van-icon size="12" color="#6B778C" name="arrow" />
										</div>
										<div @click="docBtnItem(item.msg, '内部信息')" class="docList-btns-item">
											<span>内部信息</span>
											<van-icon size="12" color="#6B778C" name="arrow" />
										</div>
										<div @click="docBtnItem(item.msg, '学术动态')" class="docList-btns-item">
											<span>学术动态</span>
											<van-icon size="12" color="#6B778C" name="arrow" />
										</div>
										<div @click="docBtnItem(item.msg, '科研动态')" class="docList-btns-item">
											<span>科研动态</span>
											<van-icon size="12" color="#6B778C" name="arrow" />
										</div>
										<div @click="docBtnItem(item.msg, '外部活动动态')" class="docList-btns-item">
											<span>外部活动动态</span>
											<van-icon size="12" color="#6B778C" name="arrow" />
										</div>
										<div @click="docBtnItem(item.msg, '内部活动参与')" class="docList-btns-item">
											<span>内部活动参与</span>
											<van-icon size="12" color="#6B778C" name="arrow" />
										</div>
										<div @click="docBtnItem(item.msg, '合作')" class="docList-btns-item">
											<span>合作</span>
											<van-icon size="12" color="#6B778C" name="arrow" />
										</div>
										<!-- <div @click="docBtnItem(item.msg, '认知倾向')" class="docList-btns-item">
											<span>认知倾向</span>
											<van-icon size="12" color="#6B778C" name="arrow" />
										</div> -->
										<div @click="docBtnItem(item.msg, '拜访记录')" class="docList-btns-item">
											<span>拜访记录</span>
											<van-icon size="12" color="#6B778C" name="arrow" />
										</div>
									</div>
								</div>
								<div v-else-if="item.templateType === 'docNum'" class="content-docList">
									<ai-text :id="item.id" :msg="item.msg.desc"></ai-text>
									<div class="content-docList-num">
										<div class="num-item" v-for="(s, index) in item.msg.list" @click="newIframeMessage(s, item)" :key="index">
											<span class="num-item-name ell1">
												{{ s.name }}<span v-if="s.num">({{ s.num }})</span>
											</span>
											<van-icon size="12" color="var(--ac-font-color)" name="arrow" />
										</div>
									</div>
									<!-- 小按钮组 - 语音播报和复制 -->
									<ai-btns v-if="item.isBtns && !item.loading" :id="item.id" :zeStatus="item.zeStatus" @_ze="zeEv" :voiceStatus="item.voiceStatus" @copyText="copyText" @voiceEv="voiceEv"></ai-btns>
								</div>
								<div v-else-if="item.templateType === 'docActivityList'" class="content-docList">
									<!-- 纯文本 -->
									<ai-text :id="item.id" :msg="item.msg.desc"></ai-text>
									<div class="content-docList-activity">
										<div
											:style="{
												height: item.msg.moreShow ? 'auto' : '172px',
												overflow: 'hidden',
											}"
										>
											<div class="activity-item" v-for="(val, index) in item.msg.list" :key="index" @click="openDocActivity(val)">
												<p class="activity-item--name ell2">{{ val.name }}</p>
												<div class="activity-item--desc">{{ val.author }} | {{ val.time }}</div>
											</div>
										</div>
										<span v-if="item.msg?.list && item.msg.list.length > 2" class="content-docList-activity-more" @click="item.msg.moreShow = !item.msg.moreShow">{{ item.msg.moreShow ? '收起' : '查看更多' }}</span>
									</div>
									<!-- 小按钮组 - 语音播报和复制 -->
									<ai-btns v-if="item.isBtns && !item.loading" :id="item.id" :zeStatus="item.zeStatus" @_ze="zeEv" :voiceStatus="item.voiceStatus" @copyText="copyText" @voiceEv="voiceEv"></ai-btns>
								</div>
								<!-- iframe -->
								<div v-else-if="item.templateType === 'iframe'" class="content-iframe">
									<ai-iframe :key="item.id" :url="item.msg"></ai-iframe>
								</div>

								<div v-else class="content-docList">
									<div>{{ item.msg.message }}</div>
									<div class="docList-list">
										<div v-for="s in item.msg.list" :key="s.doctorId" @click="activeDoc(s, true, index)" class="docList-list-item">
											<img v-if="s.doctorSex === '女'" src="@/assets/img/demo/gril.png" />
											<img v-else src="@/assets/img/demo/men.png" />
											<div class="amd-content-item-desc">
												<div>
													<span class="desc-name" style="margin-right: 8px">{{ s.doctorName }}</span>
													<span>{{ s.doctorTitle }}</span>
												</div>
												<div class="desc-hp ell1">
													<span style="margin-right: 8px">{{ s.hospital }}</span>
													<span>{{ s.department }}</span>
												</div>
											</div>
											<van-icon size="12" color="var(--ac-font-color)" name="arrow" />
										</div>
									</div>
								</div>
							</div>

							<div class="mc-content__groupList" v-if="item.btnGroupList && item.btnGroupList.length > 0">
								<span v-for="val in item.btnGroupList" @click="docBtnItem({ doctorId: item.doctorId }, val)" :key="val">{{ val }}</span>
							</div>
						</div>
					</template>
				</template>
			</div>
			<!-- 声明 -->
			<div v-if="inForUse" class="mc-declare">{{ inForUse }}</div>
			<!-- 按钮组 -->
			<div v-if="qtList.length > 0" class="mc-btns">
				<div v-for="(i, index) in qtList" :key="index" @click="docMore(i)" class="mc-btns-item">
					{{ i }}
				</div>
			</div>
			<!-- 聊天框 -->
			<div ref="chatBox" class="mc-content-chat">
				<!-- 选择对话场景 -->
				<ai-textarea :sMenu="false" :editorText="editorText" @_sendMessage="sendMessage" :ol="false" :toolsList="[]" @_changeEdit="changeEdit"></ai-textarea>
			</div>
		</div>

		<!-- 更多医生 弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="docMoreVis" position="bottom" :style="{ height: '90%' }">
			<ai-customer-doctors @close="docMoreVis = false" @activeDoc="activeDoc"></ai-customer-doctors>
		</van-popup>

		<!-- 文章列表弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="quesMoreVis" position="bottom" :style="{ height: '90%' }">
			<ai-more-articles :title="qesTitle" @_activeArticle="activeArticle" @_articleDetail="articleDetail" @close="quesMoreVis = false"></ai-more-articles>
		</van-popup>

		<!-- 文章推送 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="pushArticleVis" position="bottom" :style="{ height: '90%' }">
			<ai-push-articles @close="pushArticleVis = false"></ai-push-articles>
		</van-popup>

		<!-- 文章详情弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="articleDetailVis" position="bottom" :style="{ height: '90%' }">
			<ai-article-detail :sourceUrl="sourceUrl" :isToken="isToken" :title="articleTitle" @close="articleDetailVis = false"></ai-article-detail>
		</van-popup>
		<!-- 360 -->
		<van-popup v-model:show="showBottom" position="bottom" :style="{ height: '90%' }">
			<iframe style="width: 100%; height: 100%; border: none" :src="docUrl"></iframe>
		</van-popup>

		<!-- 查看医生 弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="viewDoctors" position="bottom" :style="{ height: '90%' }">
			<view-doctor-list :doctotListFront="doctotListFront" @close="viewDoctors = false" @activeDoc="to360"></view-doctor-list>
		</van-popup>

		<!-- 下一步行动计划弹出层-底部弹出 -->
		<van-popup v-model:show="actionPopup" closeable position="bottom" :safe-area-inset-bottom="true" :style="{ height: '70%' }" class="action-popup">
			<div class="action">
				<span class="action-title">{{ actionPopupTitle }}</span>
				<div class="action-list">
					<div class="action-list-item" v-for="item in missionList" :key="item.id">
						<div class="task-item__header">
							<div class="task-item__header-left">
								<span class="left-icon"></span>
								<span>任务ID：{{ item.id }}</span>
								<van-tag class="left-tag" :color="item.taskInfoStatusType === 'PROGRESS' ? '#0052cc' : item.taskInfoStatusType === 'CLOSE' ? '#6B778C' : '#FF991F'" type="primary">{{
									item.taskInfoStatusType === 'PROGRESS' ? '处理中' : item.taskInfoStatusType === 'CLOSE' ? '已关闭' : '已完成'
								}}</van-tag>
							</div>
							<div v-if="item.taskInfoStatusType === 'PROGRESS'" class="task-item__header-right">
								<span @click="closeTask(item.id)">关闭任务</span>
							</div>
						</div>
						<div @click="goDetail(item)" class="task-item__body">
							<img class="task-item__body--img" src="@/assets/img/right.png" alt="" />
							<div class="body-item">
								<span>任务描述：</span>
								<span>{{ item.taskDescription }}</span>
							</div>

							<div class="body-item">
								<span>任务类型：</span>
								<span>{{ item.taskInfoType === 'PHONE_VISIT' ? '电话拜访' : item.taskInfoType === 'WECHAT_VISIT' ? 'Wecom拜访' : item.taskInfoType === 'OFFLINE_VISIT' ? '线下拜访' : '会议邀请' }}</span>
							</div>
							<div class="body-item">
								<span>客户：</span>
								<span>{{ item.doctorName }}</span>
							</div>
							<div v-if="item.hospital" class="body-item">
								<span>医院：</span>
								<span>{{ item.hospital }}</span>
							</div>
							<div class="body-item">
								<span>截止时间：</span>
								<span>{{ item.planEndTime }}</span>
							</div>
							<div class="body-item">
								<span>发起人：</span>
								<span>{{ item.creatorName }}</span>
							</div>
						</div>
						<div v-if="item.taskInfoStatusType === 'PROGRESS'" class="task-item__bottom">
							<van-button @click="forwardTaskDialog(item.id)" class="task-item__bottom_btn" size="small" color="#e7edf8">转发</van-button>
							<van-button size="small" color="#0052cc" @click="executeTask(item)">执行</van-button>
						</div>
					</div>
				</div>
			</div>
		</van-popup>
		<!-- 同事列表组件 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="showForward" position="bottom" :style="{ height: '90%' }">
			<team-list @close="showForward = false" @activeTask="activeTask" :list="teammateList"></team-list>
		</van-popup>
		<!-- 历史会话 -->
		<chat-history :historyList="historyList" ref="cHs" @_openChat="openHistoryChat"></chat-history>
	</div>
</template>
<script setup>
import axios from 'axios';
import { getAgentListApi } from '@/api/agent-tools';
import { getTargetDoctor } from '@/api/user';
import { getToken } from '@/utils/auth';
import useUserStore from '@/store/modules/user';
import ClipboardJS from 'clipboard';
import CryptoJS from 'crypto-js';
import AudioPlayer from '@/utils/ap';
import { useClickAway } from '@vant/use';
import usefilterStore from '@/store/modules/filter';
import useEnterStore from '@/store/modules/enterprise';
import { myVisitSummary, visitSummaryInfo } from '@/api/my';
import { getTaskList, closeTaskInterface, colleagueList, taskTransferIn } from '@/api/task';
import { getDoctorDynamics } from '@/api/agent-tools';
import { showToast, showConfirmDialog } from 'vant';
import { findVisitReportList } from '@/api/randomNotes';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { getTodayAndYesterday, uuid, getRandomString, ruleAllList, getQueryString } from '@/utils/index';
const filterStore = usefilterStore();
const enterStore = useEnterStore();
// 接口地址
const interfaceUrl = enterStore.enterInfo.interfaceAddress;
const domain = enterStore.enterInfo.domain;
const relam = enterStore.enterInfo.id;
const { proxy } = getCurrentInstance();

import cpdc from '@/assets/img/cpdc.png';
import hszs from '@/assets/img/hszs.png';
import khdc from '@/assets/img/khdc.png';
import zlfx from '@/assets/img/zlfx.png';
import khbfxxsj from '@/assets/img/khbfxxsj.png';
import bfbj from '@/assets/img/bfbj.png';

let chatId = '';
const agentName = ref('');
// 语言环境
const language = computed(() => {
	return filterStore.language === 'zh' ? '中文' : '英文';
});

const isGlobalVoice = computed(() => {
	return filterStore.isGlobalVoice;
});

//获取最后一条chat
const getLastChat = computed(() => {
	return chatList.value[chatList.value.length - 1];
});

const getRole = computed(() => {
	let roles = ['ADMIN', 'BUD', 'RSD', 'RSM', 'DSM', 'MR'];
	return userStore.permission.find((ele) => roles.includes(ele));
});

const acGlobalVoice = (val) => {
	filterStore.SET_IS_GLOBAL_VOICE(val);
};
const route = useRoute();

watch(
	() => route.query.chatId,
	(newVal, oldVal) => {
		if (newVal && !oldVal) {
			editorText.value = '';
			chatId = newVal;
			// 获取历史记录
			getHistoryList();
		}
		// 改变chatId
		if (oldVal && newVal && oldVal !== newVal) {
			editorText.value = '';
			// 清空聊天记录
			chatList.value = [];
			chatId = newVal;
			// 获取历史记录
			getHistoryList();
		}
	}
);

// 更新用户发出的消息
const updateUserMessage = (id, msg) => {
	axios({
		url: interfaceUrl + '/API-GPT/chat/message/update_history_query_meta_data',
		method: 'POST',
		params: {
			message_id: id,
		},
		data: msg,
		headers: {
			Authorization: `Bearer ${getToken()}`,
			'Content-Type': 'application/json;charset=UTF-8',
		},
	});
};

// 更新ai回复的消息
const updateAiMessage = (id, msg) => {
	axios({
		url: interfaceUrl + '/API-GPT/chat/message/update_history_meta_data',
		method: 'POST',
		params: {
			message_id: id,
		},
		data: msg,
		headers: {
			Authorization: `Bearer ${getToken()}`,
			'Content-Type': 'application/json;charset=UTF-8',
		},
	});
};

const fixPseudoJson = (pseudoJsonStr) => {
	try {
		// 1. 去除首尾空格
		let str = pseudoJsonStr.trim();

		// 2. 检查是否已经是有效的JSON
		try {
			return JSON.parse(str);
		} catch (e) {
			// 不是有效JSON，继续处理
		}

		// 3. 处理单引号包围的JSON对象
		if (str.startsWith("{'") && str.endsWith("'}")) {
			// 3.1 替换键的单引号为双引号
			str = str.replace(/'([^']+?)'\s*:/g, '"$1":');

			// 3.2 处理值中的换行符和引号
			let result = '';
			let inQuote = false;

			for (let i = 0; i < str.length; i++) {
				// 处理单引号开始或结束的情况
				if (str[i] === "'" && (i === 0 || str[i - 1] !== '\\')) {
					if (!inQuote) {
						// 开始引号
						inQuote = true;
						result += '"';
					} else {
						// 结束引号
						inQuote = false;
						result += '"';
					}
				} else if (inQuote && str[i] === '\n') {
					// 在引号内的换行符转为 \n
					result += '\\n';
				} else if (inQuote && str[i] === '"' && (i === 0 || str[i - 1] !== '\\')) {
					// 在单引号内的双引号需要转义
					result += '\\"';
				} else {
					// 其他字符直接添加
					result += str[i];
				}
			}

			str = result;
		}

		// 4. 尝试解析修复后的JSON
		return JSON.parse(str);
	} catch (err) {
		console.error('转换失败，请确认格式正确：', err);
		console.error('错误详情：', err.message);
		return null;
	}
};

// 获取历史记录
const getHistoryList = async () => {
	let cl = [];
	const json = {
		user_id: userStore.userInfo.username,
		chat_type: 'hcp_data_insight',
		conversation_id: chatId,
		skip: 0,
		limit: 40,
	};
	axios({
		url: interfaceUrl + '/API-GPT/chat/message/history',
		method: 'get',
		params: json,
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
	})
		.then((res) => {
			if (res.data.code === 200 && res.data.data.length > 0) {
				for (const item of res.data.data) {
					// 1. 插入用户发出的消息
					if (item.query_meta_data.type && item.query_meta_data.type === 'docInfo') {
						cl.push({
							msg: {
								doctorName: item.query_meta_data.data.doctorName,
								doctorId: item.query_meta_data.data.doctorId,
								doctorSex: item.query_meta_data.data.doctorSex,
								doctorTitle: item.query_meta_data.data.doctorTitle,
								hospital: item.query_meta_data.data.hospital,
								department: item.query_meta_data.data.department,
							},
							type: 'user',
							doctorId: item.query_meta_data.data.doctorId,
							dataId: item.id,
							chatId: item.conversation_id,
							templateType: 'docInfo',
							id: uuid(),
							loading: false,
							isBtns: false,
							voiceStatus: false,
							isTarDoc: false,
							zeStatus: '0',
						});
					} else {
						cl.push({
							msg: item.query,
							type: 'user',
							doctorId: item.doctor_id || '',
							dataId: item.id,
							chatId: item.conversation_id,
							templateType: 'text',
							id: uuid(),
							loading: false,
							isBtns: false,
							voiceStatus: false,
							isTarDoc: false,
							zeStatus: '0',
						});
					}
					// 2. 插入ai回复的消息
					if (item.meta_data.type && item.meta_data.type !== 'text') {
						let jn = {
							msg: '',
							think: '',
							type: 'ai',
							doctorId: item.doctor_id || '',
							dataId: item.id,
							chatId: item.conversation_id,
							templateType: item.meta_data.type,
							id: uuid(),
							loading: false,
							isBtns: true,
							reGen: false,
							voiceStatus: true,
							isTarDoc: false,
							zeStatus: item.feedback_score === -1 ? '0' : item.feedback_score === 100 ? '1' : '2',
							btnGroupList: item.btn_group_list.btn_group_list || [],
							apiUrl: item.api_url,
						};
						if (item.meta_data.type === 'docNum') {
							if (item.meta_data.name === '活动参与') {
								const l = [];
								for (const ite of item.query_data.action_data) {
									let json = {
										name: ite.dataType,
										num: ite.count,
									};
									l.push(json);
								}
								if (item.response.indexOf('reasoning_content') > -1) {
									item.response = fixPseudoJson(item.response);
								} else {
									item.response = {
										content: item.response,
										reasoning_content: '',
									};
								}
								jn.msg = {
									desc: item.response.content || '',
									list: l,
								};
							} else {
								const l = [];
								// eslint-disable-next-line no-unsafe-optional-chaining
								for (const ite of item.query_data?.relation_data?.result) {
									let json = {
										name: ite.label,
										num: ite.num,
									};
									l.push(json);
								}
								if (item.response.indexOf('reasoning_content') > -1) {
									item.response = fixPseudoJson(item.response);
								} else {
									item.response = {
										content: item.response,
										reasoning_content: '',
									};
								}
								jn.msg = {
									desc: item.response.content || '',
									list: l,
								};
							}
						} else {
							const l = [];
							for (const ite of item.query_data.activity_info) {
								let json = {
									name: ite.activityTitle,
									time: ite.activityTime,
									author: ite.role,
									url: ite.url,
								};
								l.push(json);
							}
							if (item.response.indexOf('reasoning_content') > -1) {
								item.response = fixPseudoJson(item.response);
							} else {
								item.response = {
									content: item.response,
									reasoning_content: '',
								};
							}
							jn.msg = {
								desc: item.response.content || '',
								moreShow: false,
								list: l,
							};
						}
						cl.push(jn);
					} else {
						if (item.response.indexOf('reasoning_content') > -1) {
							item.response = fixPseudoJson(item.response);
						} else {
							item.response = {
								content: item.response,
								reasoning_content: '',
							};
						}
						cl.push({
							msg: item.response.content.indexOf('doctorInfo/internalInfo') > -1 ? `${enterStore.enterInfo.domain}${item.response.content}` : item.response.content || '',
							type: 'ai',
							doctorId: item.doctor_id || '',
							dataId: item.id,
							chatId: item.conversation_id,
							templateType: item.response.content.indexOf('doctorInfo/internalInfo') > -1 ? 'iframe' : 'text',
							id: uuid(),
							loading: false,
							isBtns: true,
							reGen: false,
							voiceStatus: true,
							isTarDoc: false,
							zeStatus: item.feedback_score === -1 ? '0' : item.feedback_score === 100 ? '1' : '2',
							btnGroupList: item.btn_group_list.btn_group_list || [],
							apiUrl: item.api_url,
						});
						if (item.meta_data.new_msg && item.meta_data.new_msg.type === 'docCard') {
							cl.push({
								msg: {
									doctorName: item.meta_data.new_msg.data.doctorName,
									doctorId: item.meta_data.new_msg.data.doctorId,
									doctorSex: item.meta_data.new_msg.data.doctorSex,
									doctorTitle: item.meta_data.new_msg.data.doctorTitle,
									hospital: item.meta_data.new_msg.data.hospital,
									department: item.meta_data.new_msg.data.department,
								},
								type: 'ai',
								dataId: '',
								doctorId: item.meta_data.new_msg.data.doctorId,
								templateType: 'docCard',
								id: Date.now() + 675,
								loading: true,
								isBtns: false,
								voiceStatus: true,
								isTarDoc: false,
								zeStatus: '0',
							});
						}
					}
				}
			}
		})
		.finally(() => {
			chatList.value = [...cl];
			nextTick(() => {
				scrollBottom();
			});
		});
};

// 历史记录
const cHs = ref(null);
const getChatHistory = () => {
	cHs.value.showLeft = true;
};
//查询左侧会话记录
const historyList = ref([]);
const getConversChatList = () => {
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async (resolve) => {
		// 获取fastGpt历史会话记录
		try {
			let allList = await getOtherChatList();
			allList = allList.reduce((acc, cur) => {
				const index = acc.findIndex((item) => item.updateTime.split('T')[0] === cur.updateTime.split('T')[0]);
				if (index !== -1) {
					acc[index].children.push(cur);
				} else {
					acc.push({
						updateTime: cur.updateTime,
						name: cur.updateTime.split('T')[0],
						children: [cur],
					});
				}
				return acc;
			}, []);
			// 把allList根据updateTime做个降序
			allList.sort((a, b) => {
				return new Date(b.updateTime) - new Date(a.updateTime);
			});
			for (const item of allList) {
				item.children.sort((a, b) => {
					return new Date(b.updateTime) - new Date(a.updateTime);
				});
			}
			if (allList.length > 0) {
				allList = ruleAllList(allList, language.value);
			}
			historyList.value = allList;
			resolve();
		} catch (error) {
			console.log(error);
			showToast({
				position: 'top',
				zIndex: 8888,
				message: '会话记录获取失败',
			});
			resolve();
		}
	});
};
const getOtherChatList = () => {
	return new Promise((resolve, reject) => {
		const json = {
			user_id: userStore.userInfo.username,
			skip: 0,
			limit: 700,
			chat_type: 'hcp_data_insight',
		};
		axios({
			url: interfaceUrl + '/API-GPT/chat/message/history_list_by_chat_type',
			method: 'get',
			params: json,
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		})
			.then((res) => {
				// 把 res.data.data 根据 updateTime 拆分成treeList
				for (const cur of res.data.data) {
					cur.chatType = 'hcp_data_insight';
					cur.updateTime = cur.created_time.substring(0, 19);
					cur.chatId = cur.conversation_id;
					cur.title = cur.query;
				}
				resolve(res.data.data);
			})
			.catch(() => {
				reject('other会话出错了');
			});
	});
};
// 打开历史记录会话
const openHistoryChat = (item) => {
	if (item.chatId === chatId) return;
	if (isLoading.value) {
		showToast({
			message: '正在回答，请稍后...',
			position: 'top',
		});
		return;
	}
	chatList.value = [];
	chatId = item.chatId;
	router.replace({
		name: 'customerInsight',
		query: {
			chatId: item.chatId,
			isTabBar: route.query.isTabBar || '',
			kType: route.query.kType || '',
		},
	});
};

// 聊天类型
const showPopover = ref(false);

const userStore = useUserStore();
const actions = ref([
	{
		img: khdc,
		name: t('chat.customerVisitSuggestions'),
		desc: t('chat.salesCustomerInsightDesc'),
		type: 'customerInsight',
		permission: true,
	},
	{
		img: cpdc,
		name: t('chat.productKnowledgQuery'),
		desc: t('chat.productKnowledgQueryDesc'),
		type: 'productKnowledgQuery',
		permission: relam === 'muqiao' ? false : true,
	},
	{
		img: zlfx,
		name: t('chat.visitMateriaDisplayAndSharing'),
		desc: t('chat.visitMateriaDisplayAndSharingDesc'),
		type: '',
		permission: true,
	},
	{
		img: hszs,
		name: t('chat.cloundAssistant'),
		desc: t('chat.cloundAssistantDesc'),
		type: 'cloundAssistant',
		permission: relam === 'muqiao' ? false : true,
	},
	{
		img: khbfxxsj,
		name: t('chat.khbfxxsj'),
		desc: t('chat.khbfxxsjDesc'),
		type: '2',
		permission: relam === 'muqiao' ? false : true,
	},
	{
		img: bfbj,
		name: route.query.kType === 'medicine' ? t('chat.medicalVisitRecords') : ['msl_demo'].includes(userStore.userInfo.username) ? t('chat.medicalVisitRecords') : t('chat.salesVisitRecords'),
		desc: t('chat.medicalVisitRecordsDesc'),
		type: 'randomNotes',
		permission: true,
	},
]);

// 模块id
const chatType = ref('customerInsight');

const chatTypeEv = (item) => {
	showPopover.value = false;
	if (item.type === '') {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/mobile/contentCenter/contentCenterIndex/marketKnowledge',
				title: '拜访材料建议与分享',
				module: 'third',
			});
		} else {
			location.href = domain + '/mobile/contentCenter/contentCenterIndex/marketKnowledge';
		}
		return;
	}
	if (item.type === '2') {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/mobile/questionResearch',
				title: '客户拜访信息收集',
				module: 'third',
			});
		} else {
			location.href = domain + '/mobile/questionResearch';
		}

		return;
	}
	if (item.type === 'customerInsight') return;
	if (item.type === 'randomNotes') {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/app/mobile/random/randomNotes',
				title: '拜访记录',
				module: 'third',
			});
		} else {
			router.push({
				name: item.type,
				query: {
					isTabBar: route.query.isTabBar || '',
				},
			});
		}
	} else {
		router.replace({
			name: item.type,
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	}
};

// 发送的消息是否是二次编辑的消息
const isEdit = ref(false);
const editorText = ref('');
const editorMsg = (index, item) => {
	if (index === chatList.value.length - 2) {
		// 如果请求正在进行需要终止请求
		if (isLoading.value) {
			controller?.abort();
			chatList.value[chatList.value.length - 1].loading = false;
			chatList.value[chatList.value.length - 1].msg = chatList.value[chatList.value.length - 1].msg + '...';
			isLoading.value = false;
		}
		let msg = item.msg.replace(/<br\s*\/?\s*>/gi, '\n');
		editorText.value = msg + 'jjjjjj' + new Date().getTime();
		isEdit.value = true;
	}
};

const changeEdit = () => {
	isEdit.value = false;
	editorText.value = '';
};

// 文本框失去焦点
const chatBox = ref();
useClickAway(
	chatBox,
	() => {
		if (isEdit.value) {
			editorText.value = '';
		}
		isEdit.value = false;
	},
	{ eventName: 'touchend' }
);

let controller = '';

const router = useRouter();
// 返回
const goBack = () => {
	router.go(-1);
};

const audioPlayer = new AudioPlayer();

const isToken = ref(false);
const articleTitle = ref('');
const quesMoreVis = ref(false);
const qesTitle = ref('');
const qesMoreEv = () => {
	quesMoreVis.value = true;
	qesTitle.value = '文章分享';
};

const pushArticleVis = ref(false);
const activeArticle = () => {
	pushArticleVis.value = true;
};

// 文章详情
const articleDetailVis = ref(false);
const sourceUrl = ref('');
const articleDetail = (item) => {
	isToken.value = true;
	articleTitle.value = '';
	sourceUrl.value = item;
	articleDetailVis.value = true;
};

const resetAudioVoice = () => {
	for (const i of chatList.value) {
		if (i.type === 'user') continue;
		if (i.isBtns === false) continue;
		if (i.voiceStatus === true) continue;
		i.voiceStatus = true;
	}
};

// 播放停止把voiceStatus改位true
audioPlayer.onStop = () => {
	resetAudioVoice();
};

// 目标医生
const doctorList = ref([]);
// 是否显示查询更多
const showMore = ref(false);

// 问题类型
const qtList = ref(['查医生', t('visit.createVisit')]);

const isLoading = ref(false);
const chatList = ref([]);

// 按钮组事件
const docBtnItem = (info, msg) => {
	if (!isLoading.value) {
		isLoading.value = true;
		chatList.value.push({
			msg,
			templateType: 'text',
			type: 'user',
			id: Date.now(),
			loading: false,
		});
		// 生产一个ai的消息
		chatList.value.push({
			msg: '',
			type: 'ai',
			dataId: '',
			doctorId: info.doctorId,
			templateType: 'text',
			id: Date.now() + 1,
			loading: true,
			isBtns: true,
			voiceStatus: true,
			isTarDoc: false,
			zeStatus: '0',
			btnGroupList: [],
		});
		nextTick(() => {
			scrollBottom();
		});
		switch (msg) {
			case '概览':
				setBaseInfoDoc(msg, info.doctorId);
				break;
			case '内部信息':
				setInternalMessage(msg, info.doctorId);
				break;
			case '科研动态':
				setScientificDynamicsMessage(msg, info.doctorId);
				break;
			case '学术动态':
				setAcademicArticles(msg, info.doctorId);
				break;
			case '内部活动参与':
				chatList.value[chatList.value.length - 1].sourceType = 'activity';
				setActivityMessage(msg, info.doctorId);
				break;
			case '合作':
				setCooperationMessage(msg, info.doctorId);
				break;
			case '外部活动动态':
				setDoctorMessage(msg, info.doctorId);
				break;
			case '临床指南':
				setClinicalGuidanceMessage(msg, info.doctorId);
				break;
			case '学术会议':
				setAcademicMeetingMessage(msg, info.doctorId);
				break;
			case '学术直播':
				setAcademicLiveMessage(msg, info.doctorId);
				break;
			case '国家自然科学基金项目':
				setCountryMessage(msg, info.doctorId);
				break;
			case '发明专利':
				setInventionPatentMessage(msg, info.doctorId);
				break;
			// case '认知倾向':
			// 	chatList.value[chatList.value.length - 1].sourceType = 'article';
			// 	setProfileMessage(msg, info.doctorId);
			// 	break;
			case 'KLK':
				chatList.value[chatList.value.length - 1].sourceType = 'article';
				setProfileMessage(msg, info.doctorId);
				break;
			case 'AKR':
				chatList.value[chatList.value.length - 1].sourceType = 'article';
				setProfileMessage(msg, info.doctorId);
				break;
			case 'UTI':
				chatList.value[chatList.value.length - 1].sourceType = 'article';
				setProfileMessage(msg, info.doctorId);
				break;
			case 'UK':
				chatList.value[chatList.value.length - 1].sourceType = 'article';
				setProfileMessage(msg, info.doctorId);
				break;
			case '拜访记录':
				bfjl(msg, info.doctorId);
				break;
		}
	}
};

// 请求或者解析失败提示并删除消息
const errorFun = (e) => {
	if (e.message !== 'signal is aborted without reason') {
		showToast({
			position: 'top',
			zIndex: 8888,
			message: '请求失败,请重试',
		});
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isLoading.value = false;
	}
};

// 观念画像 uk uti akr klk
const setProfileMessage = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/hcp_image_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&prod=${message}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 合并多个json
const mergeJson = (chunkArr, svg, num = 2) => {
	let nim = chunkArr.indexOf(svg);
	if (nim > 0) {
		chunkArr = chunkArr.substring(0, nim + num) + ',' + chunkArr.substring(nim + num);
		return mergeJson(chunkArr, svg, num);
	} else {
		chunkArr = JSON.parse(chunkArr);
		return chunkArr;
	}
};

// 基本信息
const setBaseInfoDoc = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/hcp_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 内部信息
const setInternalMessage = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/hcp_internal_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const textDec = new TextDecoder();
		const { value } = await reader.read();
		const chunkText = textDec.decode(value);
		const data = JSON.parse(chunkText).data;
		chatList.value[chatList.value.length - 1].dataId = data.message_id || '';
		chatList.value[chatList.value.length - 1].btnGroupList = data.btn_group_list || [];
		if (import.meta.env.VITE_APP_ENV !== 'development') {
			data.output = enterStore.enterInfo.domain + data.output;
		}
		chatList.value[chatList.value.length - 1].msg = data.output + '&token=' + getToken();
		chatList.value[chatList.value.length - 1].templateType = 'iframe';
		chatList.value[chatList.value.length - 1].loading = false;
		getLastChat.value.isLoading = false;

		nextTick(() => {
			scrollBottom();
		});
		isLoading.value = false;
	} catch (e) {
		errorFun(e);
	}
};
// 发明专利
const setInventionPatentMessage = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_hcp_patent_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
//拜访记录
const bfjl = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_hcp_visit_record?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 国家自然科学基金项目
const setCountryMessage = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_hcp_fund_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 学术直播
const setAcademicLiveMessage = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_hcp_live_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 学术会议
const setAcademicMeetingMessage = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_hcp_meeting_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 临床指南
const setClinicalGuidanceMessage = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_hcp_guide_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 临床研究接口
const setScientificDynamicsMessage = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_hcp_research_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 学术文章接口
const setAcademicArticles = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_hcp_paper_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 活动参与接口
const setActivityMessage = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_hcp_action_detail_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (data.data.data.action_data.length > 0) {
							const l = [];
							for (const ite of data.data.data.action_data) {
								let json = {
									name: ite.dataType,
									num: ite.count,
								};
								l.push(json);
							}
							chatList.value[chatList.value.length - 1].msg = {
								desc: chatList.value[chatList.value.length - 1].msg || data.data.output,
								list: l,
							};
							chatList.value[chatList.value.length - 1].templateType = 'docNum';
							updateAiMessage(data.data.message_id, JSON.stringify({ type: 'docNum', name: '活动参与' }));
						} else {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 合作动态
const setCooperationMessage = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_hcp_relation_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			method: 'POST',
			signal,
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (data.data.data?.relation_data?.result && data.data.data.relation_data.result.length > 0) {
							const l = [];
							for (const ite of data.data.data.relation_data.result) {
								let json = {
									name: ite.label,
									num: ite.num,
								};
								l.push(json);
							}
							chatList.value[chatList.value.length - 1].msg = {
								desc: chatList.value[chatList.value.length - 1].msg || data.data.output,
								list: l,
							};
							chatList.value[chatList.value.length - 1].templateType = 'docNum';
							updateAiMessage(data.data.message_id, JSON.stringify({ type: 'docNum', name: '合作动态' }));
						} else {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 医生动态
const setDoctorMessage = async (msg, doctorId) => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_hcp_activity_info?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					// json解析失败的原因是后端返回了两个json数据，这里需要做合并
					try {
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (data.data.data.activity_info.length > 0) {
							const l = [];
							for (const ite of data.data.data.activity_info) {
								let json = {
									name: ite.activityTitle,
									time: ite.activityTime,
									author: ite.role,
									url: ite.url,
								};
								l.push(json);
							}
							chatList.value[chatList.value.length - 1].msg = {
								desc: chatList.value[chatList.value.length - 1].msg || data.data.output,
								moreShow: false,
								list: l,
							};
							chatList.value[chatList.value.length - 1].templateType = 'docActivityList';
							updateAiMessage(data.data.message_id, JSON.stringify({ type: 'docActivityList', name: '外部活动动态' }));
						} else {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 打开医生动态 对应的文章链接
const openDocActivity = (val) => {
	sourceUrl.value = val.url;
	articleDetailVis.value = true;
};

//生成一个iframe消息
const baseUrl = domain + '/mobile/';
const newIframeMessage = (val, item) => {
	let url = '';
	switch (val.name) {
		// 学术文章
		case '万方医学网':
			url = baseUrl + 'doctor/doctorInfo/internalInfo?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		case '百度学术':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		case 'pubmed':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		case '中国知网':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		// 活动参与
		case '转发':
			url = baseUrl + 'doctor/doctorInfo/interactiveDynamic?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		case '阅读':
			url = baseUrl + 'doctor/doctorInfo/interactiveDynamic?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		case '收藏':
			url = baseUrl + 'doctor/doctorInfo/interactiveDynamic?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		case '医学教育':
			url = baseUrl + 'doctor/doctorInfo/interactiveDynamic?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		case '医生直播':
			url = baseUrl + 'doctor/doctorInfo/interactiveDynamic?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		case '朋友圈互动':
			url = baseUrl + 'doctor/doctorInfo/interactiveDynamic?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		case '问卷调研':
			url = baseUrl + 'doctor/doctorInfo/interactiveDynamic?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		case '线上会议':
			url = baseUrl + 'doctor/doctorInfo/interactiveDynamic?doctorId=' + item.doctorId + '&token=' + getToken();
			break;
		// 合作动态
		case '全部':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
		case '论文合作':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
		case '基金项目':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
		case '协会合作':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
		case '协会关系':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
		case '同事':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
		case '同事合作':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
		case '临床':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
		case '会议':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
		case '师生':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
		case '编委合作':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
		case '编委关系':
			url = baseUrl + 'doctor/doctorInfo/publicInfo?doctorId=' + item.doctorId + '&token=' + getToken() + '&aType=cooperate';
			break;
	}
	if (url) {
		chatList.value.push({
			msg: url,
			type: 'ai',
			dataId: '',
			doctorId: item.doctorId,
			templateType: 'iframe',
			id: Date.now(),
			loading: false,
			isBtns: false,
			voiceStatus: false,
			isTarDoc: false,
			zeStatus: '0',
			btnGroupList: [],
		});
		nextTick(() => {
			scrollBottom();
		});
	}
};

// 赞和踩
const zeEv = (val) => {
	const j = chatList.value.filter((ele) => ele.id === val.id)[0];
	const json = {
		message_id: j.dataId,
		feedback_score: '',
	};
	j.zeStatus = val.like;
	if (val.like === '2') {
		// 踩
		json.feedback_score = 0;
	} else if (val.like === '1') {
		// 赞
		json.feedback_score = 100;
	} else {
		json.feedback_score = -1;
	}
	axios({
		url: interfaceUrl + '/API-GPT/chat/message/update_history_feedback',
		method: 'get',
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
		params: json,
	});
};

// 发送消息
const sendMessage = (val) => {
	if (!isLoading.value) {
		if (val) {
			isLoading.value = true;
			val = val.replace(/\n/g, '<br/>');
			chatList.value.push({
				msg: val,
				templateType: 'text',
				type: 'user',
				id: Date.now(),
				loading: false,
			});
			// 生产一个ai的消息
			chatList.value.push({
				msg: '',
				type: 'ai',
				dataId: '',
				doctorId: '',
				templateType: 'text',
				id: Date.now() + 1,
				loading: true,
				isBtns: true,
				voiceStatus: true,
				isTarDoc: false,
				zeStatus: '0',
				btnGroupList: [],
			});
			nextTick(() => {
				scrollBottom();
			});
			setAiMessage(val, '', false);
		} else {
			showToast({
				position: 'top',
				message: '请输入内容',
			});
		}
	}
};

const setAiMessage = async (val, doctorId = '', isBool = false) => {
	getLastChat.value.isLoading = true;
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = val || '';
		const userId = userStore.userInfo.username || '';
		let dId = "doctor_id=''";
		if (doctorId) {
			dId = `doctor_id=${doctorId}`;
		} else if (chatList.value[chatList.value.length - 3].doctorId) {
			dId = `doctor_id=${chatList.value[chatList.value.length - 3].doctorId}`;
		}
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/hcp_data_insight_v2?question=${message}&user_id=${userId}&${dId}&conversation_id=${chatId}&stream=true&is_same_hcp_id=${isBool}&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					getLastChat.value.isLoading = false;
					if (data.data.output.is_search_hcp && data.data.output.is_search_hcp === 1) {
						const doctorName = data.data.output.doctor_name;
						const pinyin = data.data.output.pinyin;
						if (doctorName) {
							sameNameDoctorList(doctorName, pinyin, 'name');
						} else {
							sameNameDoctorList(pinyin, '', 'py');
						}
						// eslint-disable-next-line no-prototype-builtins
					} else if (data.data.output.hasOwnProperty('is_search_hcp') && data.data.output.is_search_hcp === 0) {
						// 没有找到医生，查询我的医生
						getTargetDoctor({
							customerListingType: 'MY_CUSTOMER',
							sortBy: 'position',
							search: '',
							allClient: false,
							page: 1,
							size: 300,
							returnTerm: true,
						})
							.then((res) => {
								if (res.result?.doctors.length > 0) {
									let larr = [];
									for (const item of res.result.doctors) {
										larr.push({
											doctorId: item.doctorId,
											doctorName: item.doctorName,
											doctorSex: item.doctorSex,
											doctorTitle: item.doctorTitle,
											hospital: item.hospital,
											department: item.department,
										});
									}
									chatList.value[chatList.value.length - 1].templateType = 'docList';
									chatList.value[chatList.value.length - 1].msg = {
										list: larr,
										message: '未匹配到您要查询的医生，推荐查看您负责的客户，请选择：',
									};
									chatList.value[chatList.value.length - 1].loading = false;
									getLastChat.value.isLoading = false;

									nextTick(() => {
										scrollBottom();
									});
									isLoading.value = false;
								} else {
									chatList.value[chatList.value.length - 1].templateType = 'text';
									chatList.value[chatList.value.length - 1].msg = '您暂无负责的医生。';
									chatList.value[chatList.value.length - 1].isBtns = false;
									chatList.value[chatList.value.length - 1].loading = false;
									getLastChat.value.isLoading = false;
									nextTick(() => {
										scrollBottom();
									});
									isLoading.value = false;
								}
							})
							.catch(() => {
								chatList.value[chatList.value.length - 1].templateType = 'text';
								chatList.value[chatList.value.length - 1].msg = '获取负责的医生出错，请重试！';
								chatList.value[chatList.value.length - 1].isBtns = false;
								chatList.value[chatList.value.length - 1].loading = false;
								getLastChat.value.isLoading = false;
								nextTick(() => {
									scrollBottom();
								});
								isLoading.value = false;
							});
						// eslint-disable-next-line no-prototype-builtins
					} else if (data.data.output.hasOwnProperty('answer') && data.data.output.answer.indexOf('query_doctor_medical_insights_tool') > -1) {
						console.log(data.data.output.output);
						// medical_visit 医学见解    sales_visit  拜访
						let jType = 'sales_visit';
						// if (data.data.output.output.indexOf('sales_visit') > -1) {
						// jType = 'sales_visit';
						// }
						// 这里校验msg是字符串不是对象
						router.push({
							name: 'mAssistant',
							query: {
								isTabBar: route.query.isTabBar || '',
								jType: jType,
								queryText: Object.prototype.toString.call(chatList.value[chatList.value.length - 2].msg) === '[object Object]' ? '' : chatList.value[chatList.value.length - 2].msg,
								doctorId: doctorId || '',
							},
						});
					} else {
						if (!data.data.output.end) {
							chatList.value[chatList.value.length - 1].msg += data.data.output.answer || '';
						}
						if (data.data.output.end) {
							if (!chatList.value[chatList.value.length - 1].msg) {
								chatList.value[chatList.value.length - 1].msg = data.data.output.answer || '';
							}
							chatList.value[chatList.value.length - 1].apiUrl = data.data.output.api_url || '';
							chatList.value[chatList.value.length - 1].dataId = data.data.output.message_id || '';
							chatList.value[chatList.value.length - 1].doctorId = data.data.output.doctorId || data.data.output.doctor_id || '';
							chatList.value[chatList.value.length - 1].btnGroupList = data.data.output.btn_group_list || [];
							chatList.value[chatList.value.length - 1].loading = false;

							nextTick(() => {
								scrollBottom();
							});
							isLoading.value = false;
							if (!getQueryString('chatId')) {
								getConversChatList().then(() => {
									const params = new URLSearchParams(window.location.search);
									params.set('chatId', chatId); // 添加或更新 query 参数
									const newUrl = window.location.pathname + '?' + params.toString();
									history.replaceState(null, '', newUrl);
								});
							} else if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
								// 如果打开全局的语音播报，这里要触发语音播报
								nextTick(() => {
									voiceEv({
										id: chatList.value[chatList.value.length - 1].id,
										vType: 'play',
									});
								});
							}
						} else {
							nextTick(() => {
								scrollBottom();
							});
							return reader.read().then(processStreamResult);
						}
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

const sameNameDoctorList = (name, val, type) => {
	getTargetDoctor({
		customerListingType: 'MY_CUSTOMER',
		sortBy: 'position',
		search: name,
		allClient: false,
		page: 1,
		size: 8,
		returnTerm: true,
	})
		.then((res) => {
			if (res.result?.doctors.length > 0) {
				// 一个医生以上
				if (res.result?.doctors.length > 1) {
					let larr = [];
					for (const item of res.result.doctors) {
						larr.push({
							doctorId: item.doctorId,
							doctorName: item.doctorName,
							doctorSex: item.doctorSex,
							doctorTitle: item.doctorTitle,
							hospital: item.hospital,
							department: item.department,
						});
					}
					chatList.value[chatList.value.length - 1].templateType = 'docList';
					chatList.value[chatList.value.length - 1].msg = {
						list: larr,
						message: '为您匹配到了以下医生，请选择：',
					};
					chatList.value[chatList.value.length - 1].loading = false;
					getLastChat.value.isLoading = false;

					nextTick(() => {
						scrollBottom();
					});
					isLoading.value = false;
				} else {
					// 如果是拼音需要用户确认
					if (type === 'py') {
						let larr = [];
						for (const item of res.result.doctors) {
							larr.push({
								doctorId: item.doctorId,
								doctorName: item.doctorName,
								doctorSex: item.doctorSex,
								doctorTitle: item.doctorTitle,
								hospital: item.hospital,
								department: item.department,
							});
						}
						chatList.value[chatList.value.length - 1].templateType = 'docList';
						chatList.value[chatList.value.length - 1].msg = {
							list: larr,
							message: '为您匹配到了以下医生，请选择：',
						};
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						nextTick(() => {
							scrollBottom();
						});
						isLoading.value = false;
					} else {
						setAiMessage(chatList.value[chatList.value.length - 2].msg, res.result.doctors[0].doctorId, true);
					}
				}
			} else {
				// 没有找到医生， 如果val有值需要重新查询，val没有值显示我的医生
				if (val) {
					sameNameDoctorList(val, '', 'py');
				} else {
					getTargetDoctor({
						customerListingType: 'MY_CUSTOMER',
						sortBy: 'position',
						search: '',
						allClient: false,
						page: 1,
						size: 300,
						returnTerm: true,
					})
						.then((res) => {
							if (res.result?.doctors.length > 0) {
								let larr = [];
								for (const item of res.result.doctors) {
									larr.push({
										doctorId: item.doctorId,
										doctorName: item.doctorName,
										doctorSex: item.doctorSex,
										doctorTitle: item.doctorTitle,
										hospital: item.hospital,
										department: item.department,
									});
								}
								chatList.value[chatList.value.length - 1].templateType = 'docList';
								chatList.value[chatList.value.length - 1].msg = {
									list: larr,
									message: '未匹配到您要查询的医生，推荐查看您负责的客户，请选择：',
								};
								chatList.value[chatList.value.length - 1].loading = false;
								getLastChat.value.isLoading = false;

								nextTick(() => {
									scrollBottom();
								});
								isLoading.value = false;
							} else {
								chatList.value[chatList.value.length - 1].templateType = 'text';
								chatList.value[chatList.value.length - 1].msg = '您暂无负责的医生。';
								chatList.value[chatList.value.length - 1].isBtns = false;
								chatList.value[chatList.value.length - 1].loading = false;
								getLastChat.value.isLoading = false;
								nextTick(() => {
									scrollBottom();
								});
								isLoading.value = false;
							}
						})
						.catch(() => {
							chatList.value[chatList.value.length - 1].templateType = 'text';
							chatList.value[chatList.value.length - 1].msg = '获取负责的医生出错，请重试！';
							chatList.value[chatList.value.length - 1].isBtns = false;
							chatList.value[chatList.value.length - 1].loading = false;
							getLastChat.value.isLoading = false;
							nextTick(() => {
								scrollBottom();
							});
							isLoading.value = false;
						});
				}
			}
		})
		.catch(() => {
			chatList.value[chatList.value.length - 1].templateType = 'text';
			chatList.value[chatList.value.length - 1].msg = '获取医生信息出错，请重试！';
			chatList.value[chatList.value.length - 1].isBtns = false;
			chatList.value[chatList.value.length - 1].loading = false;
			getLastChat.value.isLoading = false;
			nextTick(() => {
				scrollBottom();
			});
			isLoading.value = false;
		});
};
// 全部医生
const sameNameAllDoctorList = (name) => {
	getTargetDoctor({
		sortBy: 'position',
		search: name,
		allClient: true,
		page: 1,
		size: 8,
		isFocus: false,
	})
		.then((res) => {
			if (res.result?.doctors.length > 0) {
				// 一个医生以上
				if (res.result?.doctors.length > 1) {
					let larr = [];
					for (const item of res.result.doctors) {
						larr.push({
							doctorId: item.doctorId,
							doctorName: item.doctorName,
							doctorSex: item.doctorSex,
							doctorTitle: item.doctorTitle,
							hospital: item.hospital,
							department: item.department,
						});
					}
					chatList.value[chatList.value.length - 1].templateType = 'docList';
					chatList.value[chatList.value.length - 1].msg = {
						list: larr,
						message: '请问您想看哪个医生呢？',
					};
					chatList.value[chatList.value.length - 1].loading = false;
					getLastChat.value.isLoading = false;

					nextTick(() => {
						scrollBottom();
					});
					isLoading.value = false;
				} else {
					setAiMessage(chatList.value[chatList.value.length - 2].msg, res.result.doctors[0].doctorId, true);
				}
			} else {
				chatList.value[chatList.value.length - 1].templateType = 'text';
				chatList.value[chatList.value.length - 1].msg = '该医生不存在，请重新输入？';
				chatList.value[chatList.value.length - 1].isBtns = false;
				chatList.value[chatList.value.length - 1].loading = false;
				getLastChat.value.isLoading = false;

				nextTick(() => {
					scrollBottom();
				});
				isLoading.value = false;
			}
		})
		.catch(() => {
			chatList.value[chatList.value.length - 1].templateType = 'text';
			chatList.value[chatList.value.length - 1].msg = '获取医生信息出错，请重试。';
			chatList.value[chatList.value.length - 1].isBtns = false;
			chatList.value[chatList.value.length - 1].loading = false;
			getLastChat.value.isLoading = false;

			nextTick(() => {
				scrollBottom();
			});
			isLoading.value = false;
		});
};
const setTemplateMessage = async (val, doctorId = '') => {
	getLastChat.value.isLoading = true;

	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = val || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/hcp_data_summary?question=${message}&user_id=${userId}&doctor_id=${doctorId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'post',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: 'Bearer ' + getToken(),
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body?.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
						getLastChat.value.isLoading = false;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id || '';
						chatList.value[chatList.value.length - 1].loading = false;
						getLastChat.value.isLoading = false;

						data.data.data.doctorId = doctorId;
						// 改变用户的历史记录信息
						await updateUserMessage(data.data.message_id, JSON.stringify({ type: 'docInfo', data: data.data.data }));
						// 改变ai回答的历史记录
						await updateAiMessage(
							data.data.message_id,
							JSON.stringify({
								type: 'text',
								new_msg: { type: 'docCard', data: data.data.data },
							})
						);
						// 显示医生信息卡片
						setBaseDocInfo(doctorId, data.data.data);
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('Network res was not ok');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 显示医生基本信息卡片
const setBaseDocInfo = (doctorId, info) => {
	chatList.value.push({
		msg: {
			doctorName: info.doctorName,
			doctorId: doctorId,
			doctorSex: info.doctorSex,
			doctorTitle: info.doctorTitle,
			hospital: info.hospital,
			department: info.department,
		},
		type: 'ai',
		dataId: '',
		doctorId: doctorId,
		templateType: 'docCard',
		id: Date.now() + 892,
		loading: true,
		isBtns: false,
		voiceStatus: true,
		isTarDoc: false,
		zeStatus: '0',
		isCollection: false,
		isMore: false,
	});
	nextTick(() => {
		scrollBottom();
	});
	isLoading.value = false;
	// 请求完毕后判断url是否存在chatId
	if (!getQueryString('chatId')) {
		getConversChatList().then(() => {
			const params = new URLSearchParams(window.location.search);
			params.set('chatId', chatId); // 添加或更新 query 参数
			const newUrl = window.location.pathname + '?' + params.toString();
			history.replaceState(null, '', newUrl);
		});
	} else {
		// 如果打开全局的语音播报，这里要触发语音播报
		if (isGlobalVoice.value && chatList.value[chatList.value.length - 2].msg) {
			nextTick(() => {
				voiceEv({
					id: chatList.value[chatList.value.length - 2].id,
					vType: 'play',
				});
			});
		}
	}
};

const scrollBottom = () => {
	document.querySelector('#aiCon').scrollTo({
		top: document.querySelector('#aiCon').scrollHeight,
		behavior: 'smooth',
	});
};

const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let ttsWS = null;
const voiceEv = async (val) => {
	// chatList里所有的voiceStatus改位true
	resetAudioVoice();
	// 通过val.id获取chatList的相等元素
	const index = chatList.value.findIndex((item) => item.id === val.id);
	chatList.value[index].voiceStatus = val.vType === 'play' ? false : true;

	ttsWS?.close();
	audioPlayer.reset();
	if (val.vType === 'play') {
		// 开始语音播报
		const url = getWebSocketUrl(API_KEY, API_SECRET);
		if ('WebSocket' in window) {
			ttsWS = new WebSocket(url);
		} else if ('MozWebSocket' in window) {
			ttsWS = new MozWebSocket(url);
		} else {
			showToast({
				position: 'top',
				message: '浏览器不支持WebSocket',
			});
			return;
		}
		// 发送消息
		ttsWS.onopen = () => {
			audioPlayer.start({
				autoPlay: true,
				sampleRate: 16000,
				resumePlayDuration: 1000,
			});
			let text = document.getElementById(val.id).innerText;
			let tte = 'UTF8';
			let params = {
				common: {
					app_id: APPID,
				},
				business: {
					aue: 'raw',
					auf: 'audio/L16;rate=16000',
					vcn: 'x4_lingfeichen_assist', //发音人
					speed: 50, // 语速
					volume: 50, // 音量
					pitch: 50, // 音色
					bgs: 0,
					tte,
				},
				data: {
					status: 2,
					text: encodeText(text, tte),
				},
			};
			ttsWS.send(JSON.stringify(params));
		};

		ttsWS.onmessage = (e) => {
			let jsonData = JSON.parse(e.data);
			// 合成失败
			if (jsonData.code !== 0) {
				console.error(jsonData);
				showToast({
					position: 'top',
					message: '语音合成失败, 请稍后再试',
				});
				return;
			}
			audioPlayer.postMessage({
				type: 'base64',
				data: jsonData.data.audio,
				isLastData: jsonData.data.status === 2,
			});
			if (jsonData.code === 0 && jsonData.data.status === 2) {
				ttsWS.close();
			}
		};

		ttsWS.onerror = (e) => {
			console.error(e);
		};
		ttsWS.onclose = () => {};
	}
};

const encodeText = (text, type) => {
	if (type === 'unicode') {
		let buf = new ArrayBuffer(text.length * 4);
		let bufView = new Uint16Array(buf);
		for (let i = 0, strlen = text.length; i < strlen; i++) {
			bufView[i] = text.charCodeAt(i);
		}
		let binary = '';
		let bytes = new Uint8Array(buf);
		let len = bytes.byteLength;
		for (let i = 0; i < len; i++) {
			binary += String.fromCharCode(bytes[i]);
		}
		return window.btoa(binary);
	} else {
		return Base64.encode(text);
	}
};

const getWebSocketUrl = (apiKey, apiSecret) => {
	var url = 'wss://tts-api.xfyun.cn/v2/tts';
	var host = location.host;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
	if (isLoading.value) {
		controller && controller?.abort();
	}
	resetAudioVoice();
	ttsWS?.close();
	audioPlayer.reset();
});
const msg = () => {
	if (userStore.userInfo.username === 'demo01') {
		chatList.value.push({
			msg: 'Hi，我帮助您洞察医生、医院的最新动态。您通过我可以快速找到医生，获得最新动态。您还能随时安排拜访、会议或分享文章。现在，您可以随便点击任何一位医生啦....',
			html: `
    <div class="customer">
      <div class="customer-title">每日拜访播报</div>
      <div>尊敬的代表，以下是您今天的拜访概览及关键信息传递情况，同时包括了对医生学术和活动动态的分析：</div>
      <div class="customer-title1">1.拜访状况</div>
      <div class="fa"><span class="dian"></span>本月计划拜访次数390次，截至昨天您本月共拜访了340次，拜访执行率87%。AB类客户覆盖率为90%，未达到100%的目标覆盖率。</div>
      <div class="fa"><span class="dian"></span>其中A级客户平均拜访频次8.73次/人，拜访执行率109%，均已完成目标，但客户覆盖率95%，请关注未拜访客户情况。</div>
      <div class="fa"><span class="dian"></span>B级客户平均拜访频次4.32次/人，拜访执行率108%，均已完成目标，但客户覆盖率86%，请关注未拜访客户情况。</div>
      <div class="fa"><span class="dian"></span>C级客户平均拜访频次0.53次/人，拜访执行率26%，客户覆盖率31%，均未完成目标，请及时跟进拜访执行情况。</div>
      <div class="fa"><span class="dian"></span><span>AB级客户近两周有3位医生未拜访，其中A级医生有<span doctorName="zsy" class="blue">张抒扬</span>、<span doctorName="hhb" class="blue">胡海波</span>您本周还未拜访，B级医生<span doctorName="nsp" class="blue">聂绍平</span>，建议及时制定相应拜访计划。</span></div>
      <div class="customer-title1">2.客户动态</div>
      <div class="fa"><span class="dian"></span><span><span doctorName="lmy" class="blue">刘梅颜</span>医生近期发表了《老年冠心病患者血运重建术后谵妄及认知障碍的风险评估与干预》的期刊，属于冠状动脉疾病领域。</span></div>
      <div class="mt10"><span style="font-weight:bold">建议</span>您可以从以下方面去跟进:</div>
      <div class="mt10">1.拜访3位医生，分别是A级医生<span doctorName="zsy" class="blue">张抒扬</span>、<span doctorName="hhb" class="blue">胡海波</span>、B级医生<span doctorName="nsp" class="blue">聂绍平</span>。</div>
      <div class="mt10">2.<span doctorName="lmy" class="blue">刘梅颜</span>医生近期发表了论文推荐您重点拜访。</div>
      <div class="btns toPlan"><span class="toPlan"><strong style="color: #0b67e4">创建</strong>拜访计划</span><div class="right "></div></div>
      <div class="btns toReport"><span class="toReport"><strong style="color: #0b67e4">记录</strong>实地拜访</span><div class="right "></div></div>
    </div>
    `,
			type: 'ai',
			dataId: '',
			templateType: 'text',
			id: Date.now(),
			loading: false,
			isBtns: false,
			voiceStatus: true,
			isTarDoc: doctorList.value.length > 0 ? true : false,
			zeStatus: '0',
			guessList: [],
		});
	} else if (userStore.userInfo.username === 'demo02') {
		chatList.value.push({
			msg: 'Hi，我帮助您洞察医生、医院的最新动态。您通过我可以快速找到医生，获得最新动态。您还能随时安排拜访、会议或分享文章。现在，您可以随便点击任何一位医生啦....',
			html: `
    <div class="customer">
      <div class="customer-title">每日拜访播报</div>
      <div>尊敬的代表，以下是您今天的拜访概览及关键信息传递情况，同时包括了对医生学术和活动动态的分析：</div>
      <div class="customer-title1">1.拜访状况</div>
      <div class="fa"><span class="dian"></span>本月计划拜访次数390次，截至昨天您本月共拜访了340次，拜访执行率87%。AB类客户覆盖率为90%，未达到100%的目标覆盖率。</div>
      <div class="fa"><span class="dian"></span>其中A级客户平均拜访频次8.73次/人，拜访执行率109%，均已完成目标，但客户覆盖率95%，请关注未拜访客户情况。</div>
      <div class="fa"><span class="dian"></span>B级客户平均拜访频次4.32次/人，拜访执行率108%，均已完成目标，但客户覆盖率86%，请关注未拜访客户情况。</div>
      <div class="fa"><span class="dian"></span>C级客户平均拜访频次0.53次/人，拜访执行率26%，客户覆盖率31%，均未完成目标，请及时跟进拜访执行情况。</div>
      <div class="fa"><span class="dian"></span><span>AB级客户近两周有2位医生未拜访，其中A级医生有<span doctorName="wmz" class="blue">王孟昭</span>您本周还未拜访，B级医生<span doctorName="mxl" class="blue">穆新林</span>，建议及时制定相应拜访计划。</span></div>
      <div class="customer-title1">2.客户动态</div>
      <div class="fa"><span class="dian"></span><span><span doctorName="sah" class="blue">石安辉</span>医生近期发表了《肺癌、食管癌等常见恶性肿瘤精准放疗》的论文，属于肿瘤放疗领域。</span></div>
      <div class="mt10"><span style="font-weight:bold">建议</span>您可以从以下方面去跟进:</div>
      <div class="mt10">1.拜访2位医生，分别是A级医生<span doctorName="wmz" class="blue">王孟昭</span>、B级医生<span doctorName="mxl" class="blue">穆新林</span>。</div>
      <div class="mt10">2.<span doctorName="sah" class="blue">石安辉</span>医生近期发表了论文推荐您重点拜访。</div>
      <div class="btns toPlan"><span class="toPlan"><strong style="color: #0b67e4">创建</strong>拜访计划</span><div class="right "></div></div>
      <div class="btns toReport"><span class="toReport"><strong style="color: #0b67e4">记录</strong>实地拜访</span><div class="right "></div></div>
    </div>
    `,
			type: 'ai',
			dataId: '',
			templateType: 'text',
			id: Date.now(),
			loading: false,
			isBtns: false,
			voiceStatus: true,
			isTarDoc: doctorList.value.length > 0 ? true : false,
			zeStatus: '0',
			guessList: [],
		});
	} else if (userStore.userInfo.username === 'demo03') {
		chatList.value.push({
			msg: 'Hi，我帮助您洞察医生、医院的最新动态。您通过我可以快速找到医生，获得最新动态。您还能随时安排拜访、会议或分享文章。现在，您可以随便点击任何一位医生啦....',
			html: `
    <div class="customer">
      <div class="customer-title">每日业绩播报</div>
      <div>尊敬的代表，以下是我们今天的业绩概览及关键信息传递情况，同时包括了对医生学术和活动动态的分析:</div>
      <div class="customer-title1">1.拜访状况</div>
      <div class="fa"><span class="dian"></span>截止到昨天您本月共拜访了20次，AB类客户覆盖率为70%</div>
      <div class="fa"><span class="dian"></span><span>AB级客户中您有2位医生未拜访，其中A级医生<span doctorName="lxb" class="blue">李先宾</span>您本周还未拜访，B级医生<span doctorName="wcy" class="blue">王传跃</span>您连续两周还未拜访。</span></div>
      <div class="customer-title1">2.客户动态</div>
      <div class="fa"><span class="dian"></span><span><span doctorName="ywh" class="blue">岳伟华</span>医生近期发表了《无抽搐电休克治疗中脑电的小波样本熵分析》的论文，属于精神障碍疾病临床治疗领域。</span></div>
      <div class="mt10"><span style="font-weight:bold">建议</span>您可以从以下方面去跟进:</div>
      <div class="mt10">1.您本周2位医生还未拜访，分别是A级医生<span doctorName="lxb" class="blue">李先宾</span>、B级医生<span doctorName="wcy" class="blue">王传跃</span>。</div>
      <div class="mt10">2.岳伟华医生近期发表了论文推荐您重点拜访。</div>
      <div class="btns toPlan"><span class="toPlan"><strong style="color: #0b67e4">创建</strong>拜访计划</span><div class="right "></div></div>
      <div class="btns toReport"><span class="toReport"><strong style="color: #0b67e4">记录</strong>实地拜访</span><div class="right "></div></div>
    </div>
    `,
			type: 'ai',
			dataId: '',
			templateType: 'text',
			id: Date.now(),
			loading: false,
			isBtns: false,
			voiceStatus: true,
			isTarDoc: doctorList.value.length > 0 ? true : false,
			zeStatus: '0',
			guessList: [],
		});
	} else if (userStore.userInfo.username === 'demo04mr') {
		chatList.value.push({
			msg: 'Hi，我帮助您洞察医生、医院的最新动态。您通过我可以快速找到医生，获得最新动态。您还能随时安排拜访、会议或分享文章。现在，您可以随便点击任何一位医生啦....',
			html: `
    <div class="customer">
      <div class="customer-title">每日拜访播报</div>
      <div>尊敬的代表，以下是您今天的拜访概览及关键信息传递情况，同时包括了对医生学术和活动动态的分析：</div>
      <div class="customer-title1">1.拜访状况</div>
      <div class="fa"><span class="dian"></span>本月计划拜访次数390次，截至昨天您本月共拜访了340次，拜访执行率87%。AB类客户覆盖率为90%，未达到100%的目标覆盖率。</div>
      <div class="fa"><span class="dian"></span>其中A级客户平均拜访频次8.73次/人，拜访执行率109%，均已完成目标，但客户覆盖率95%，请关注未拜访客户情况。</div>
      <div class="fa"><span class="dian"></span>B级客户平均拜访频次4.32次/人，拜访执行率108%，均已完成目标，但客户覆盖率86%，请关注未拜访客户情况。</div>
      <div class="fa"><span class="dian"></span>C级客户平均拜访频次0.53次/人，拜访执行率26%，客户覆盖率31%，均未完成目标，请及时跟进拜访执行情况。</div>
      <div class="fa"><span class="dian"></span><span>AB级客户近两周有3位医生未拜访，其中A级医生有<span doctorName="jln" class="blue">纪立农</span>、<span doctorName="xwb" class="blue">夏维波</span>您本周还未拜访，B级医生<span doctorName="zjq" class="blue">张俊清</span>，建议及时制定相应拜访计划。</span></div>
      <div class="customer-title1">2.客户动态</div>
      <div class="fa"><span class="dian"></span><span><span doctorName="lyx" class="blue">李玉秀</span>医生近期发表了《禅绕画对情绪调节的影响》的论文，属于精神康复领域。</span></div>
      <div class="mt10"><span style="font-weight:bold">建议</span>您可以从以下方面去跟进:</div>
      <div class="mt10">1.拜访3位医生，分别是A级医生<span doctorName="jln" class="blue">纪立农</span>、<span doctorName="xwb" class="blue">夏维波</span>、B级医生<span doctorName="zjq" class="blue">张俊清</span>。</div>
      <div class="mt10">2.<span doctorName="lyx" class="blue">李玉秀</span>医生近期发表了论文推荐您重点拜访。</div>
      <div class="btns toPlan"><span class="toPlan"><strong style="color: #0b67e4">创建</strong>拜访计划</span><div class="right "></div></div>
      <div class="btns toReport"><span class="toReport"><strong style="color: #0b67e4">记录</strong>实地拜访</span><div class="right "></div></div>
    </div>
    `,
			type: 'ai',
			dataId: '',
			templateType: 'text',
			id: Date.now(),
			loading: false,
			isBtns: false,
			voiceStatus: true,
			isTarDoc: doctorList.value.length > 0 ? true : false,
			zeStatus: '0',
			guessList: [],
		});
	} else {
		chatList.value.push({
			msg: 'Hi，我帮助您洞察医生、医院的最新动态。您通过我可以快速找到医生，获得最新动态。您还能随时安排拜访、会议或分享文章。现在，您可以随便点击任何一位医生啦....',
			html: `
    <div class="customer">
      <div class="customer-title">每日业绩播报</div>
      <div>尊敬的代表，以下是我们今天的业绩概览及关键信息传递情况，同时包括了对医生学术和活动动态的分析:</div>
      <div class="customer-title1">1.拜访状况</div>
      <div class="fa"><span class="dian"></span>截止到昨天您本月共拜访了20次，AB类客户覆盖率为70%</div>
      <div class="fa"><span class="dian"></span><span>AB级客户中您有2位医生未拜访，其中A级医生<span doctorName="snl" class="blue">孙宁玲</span>您本周还未拜访，B级医生<span doctorName="wmz" class="blue">王孟昭</span>您连续两周还未拜访。</span></div>
      <div class="customer-title1">2.客户动态</div>
      <div class="fa"><span class="dian"></span><span><span doctorName="lxb" class="blue">李先宾</span>医生近期发表了《禅绕画对情绪调节的影响》的论文，属于精神康复领域。</span></div>
      <div class="mt10"><span style="font-weight:bold">建议</span>您可以从以下方面去跟进:</div>
      <div class="mt10">1.您本周2位医生还未拜访，分别是A级医生<span doctorName="snl" class="blue">孙宁玲</span>、B级医生<span doctorName="wmz" class="blue">王孟昭</span>。</div>
      <div class="mt10">2.武丽君医生近期发表了论文推荐您重点拜访。</div>
      <div class="btns toPlan"><span class="toPlan"><strong style="color: #0b67e4">创建</strong>拜访计划</span><div class="right "></div></div>
      <div class="btns toReport"><span class="toReport"><strong style="color: #0b67e4">记录</strong>实地拜访</span><div class="right "></div></div>
    </div>
    `,
			type: 'ai',
			dataId: '',
			templateType: 'text',
			id: Date.now(),
			loading: false,
			isBtns: false,
			voiceStatus: true,
			isTarDoc: doctorList.value.length > 0 ? true : false,
			zeStatus: '0',
			guessList: [],
		});
	}
};
const getDoctorList = () => {
	getTargetDoctor({
		customerListingType: 'MY_CUSTOMER',
		sortBy: 'position',
		allClient: false,
		page: 1,
		size: 3,
		returnTerm: true,
	}).then((res) => {
		doctorList.value = res.result.doctors;
		showMore.value = res.result.total > 3 ? true : false;
		msg();
		nextTick(() => {
			scrollBottom();
		});
		// 是否自动播报
		if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
			nextTick(() => {
				voiceEv({
					id: chatList.value[chatList.value.length - 1].id,
					vType: 'play',
				});
			});
		}
	});
};

// 获取更多目标医生
const docMoreVis = ref(false);
const docMore = (i) => {
	if (i === '查医生') {
		docMoreVis.value = true;
	} else {
		router.push({
			name: 'mAssistant',
			query: {
				isTabBar: route.query.isTabBar || '',
				jType: 'sales_visit',
			},
		});
	}
};

// 点击医生生产消息
let dotcorInfo = ref({});
const activeDoc = (item, same, num) => {
	if (!isLoading.value) {
		isLoading.value = true;
		chatList.value.push({
			msg: {
				doctorName: item.doctorName,
				doctorId: item.doctorId,
				doctorSex: item.doctorSex,
				doctorTitle: item.doctorTitle,
				hospital: item.hospital,
				department: item.department,
			},
			type: 'user',
			templateType: 'docInfo',
			id: Date.now(),
			loading: false,
		});
		// 生产一个ai的消息
		chatList.value.push({
			msg: '',
			type: 'ai',
			dataId: '',
			doctorId: item.doctorId,
			templateType: 'text',
			id: Date.now() + 1,
			loading: true,
			isBtns: true,
			voiceStatus: true,
			isTarDoc: false,
			zeStatus: '0',
			guessList: [],
		});
		nextTick(() => {
			scrollBottom();
		});
		if (same) {
			setAiMessage(chatList.value[num - 1].msg, item.doctorId, true);
		} else {
			setTemplateMessage(item.doctorName, item.doctorId);
		}
	}
};

const copyText = (id) => {
	const clipboard = new ClipboardJS('.copy-btn', {
		target: function () {
			return document.getElementById(id);
		},
	});

	clipboard.on('success', () => {
		showToast({
			position: 'top',
			message: '复制成功',
		});
		clipboard.destroy();
	});
};
const showBottom = ref(false);
const docUrl = ref('');
const to360 = (v) => {
	showBottom.value = true;
	docUrl.value = `${domain}/mobile/doctor/doctorInfo/internalInfo?doctorId=${v.id}`;
};

// 会话ID
const chatId1 = '7bzlclv2pdop';
let appId = '';
let appKey = '';
//处理猜你想问和下一步行动计划出现时机
const showEtc = computed(() => {
	return (item) => {
		if (item.isIframe) {
			if (!item.summaryLoading) {
				return true;
			}
		} else {
			if (!item.loading) {
				return true;
			}
		}
	};
});
let controllerList = [];
const fetchRequest = (val) => {
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async (resolve) => {
		try {
			let controller = new AbortController();
			controllerList.push(controller);
			const { signal } = controller;
			const message = val || '';
			let radom1 = uuid();
			let radom2 = uuid();
			let url = enterStore.enterInfo.agentAddress;
			let res = await fetch(`${url}/api/v1/chat/completions`, {
				signal,
				method: 'POST',
				headers: {
					'Content-Type': 'application/json;charset=utf-8',
					Authorization: 'Bearer ' + appKey,
				},
				body: JSON.stringify({
					appId,
					chatId: chatId1,
					stream: true,
					detail: true,
					variables: { token: `Bearer ${getToken()}`, language: language.value },
					messages: [
						{
							role: 'user',
							content: JSON.stringify({ message, role: getRole.value }),
							dataId: radom1,
						},
						// { role: 'assistant', content: '', dataId: radom2 },
					],
				}),
			});
			if (!res?.body || !res?.ok) {
				throw new Error('Request Error');
			}
			resolve(res);
		} catch (error) {
			console.log(error, 'xxxxxxxxxxxxxxxxxxxxxxx');
		}
	});
};
const handleStream = (res, type) => {
	let fixedVariable = {
		Suggestion: ['temQuestionList', 'questionList'],
		NextAction: ['temNextAction', 'nextAction'],
	};
	const reader = res.body?.getReader();
	const decoder = new TextDecoder('utf-8');
	let buffer = '';
	let aceptInfo = false;
	let stepLists = [];
	function processStreamResult(result) {
		const chunk = decoder.decode(result.value, { stream: !result.done });
		buffer += chunk;
		// 逐条解析后端返回数据
		const lines = buffer.split('\n');
		buffer = lines.pop();
		lines.forEach((line) => {
			if (line.trim().length === 0) return;
			if (line.indexOf('data:') === -1 || line.split('data:')[1] === ' [DONE]') return;
			const resData = JSON.parse(line.split('data:')[1]);
			console.log(resData);

			if (resData.name) {
				stepLists.push(resData.name);
			}
			if (resData.name === 'AI 对话-每日业绩解读') {
				aceptInfo = true;
				getLastChat.value.isLoading = false;
			}
			if (resData.name === 'AI 对话-下一步行动计划') {
				aceptInfo = true;
			}
			if (resData.name === 'AI 对话-卡片解读') {
				aceptInfo = true;
			}
			if (resData.name === 'AI 对话-猜你想问') {
				aceptInfo = true;
			}
			if (resData.name === '查询内部信息') {
				aceptInfo = true;
			}
			if (stepLists.includes('任务调度（工具）')) {
				aceptInfo = true;
				if (stepLists.includes('查询卡片信息')) {
					getLastChat.value.isLoading = true;
				} else {
					getLastChat.value.isLoading = false;
				}
			}

			if (resData.choices && resData.choices[0].delta.content && aceptInfo) {
				const text = resData.choices[0].delta.content;
				// console.log(text);
				if (type === 'Summary') {
					getLastChat.value.summary += text;
				} else if (type === 'cardOrMsg') {
					getLastChat.value.msg += text;
					if (getLastChat.value.msg.includes('cardCom')) {
						getLastChat.value.isIframe = true;
						getLastChat.value.summaryLoading = true; //加载summary
					}
					if (getLastChat.value.msg.includes('internalInfo')) {
						getLastChat.value.isIframe = true;
						getLastChat.value.isInternalInfo = true;
					}
					// if (!getLastChat.value.msg.includes('```')) {
					// 	getLastChat.value.isLoading = false;
					// }
				} else {
					getLastChat.value[fixedVariable[type][0]] += text;
				}
			}
		});
		if (!result.done) {
			return reader.read().then(processStreamResult);
		} else {
			console.log(stepLists, '........................');
			if (type === 'Summary') {
				//如果时summary
				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value.summary);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
				getLastChat.value.summaryLoading = false;
				nextTick(() => {
					if (isGlobalVoice.value) {
						getLastChat.value.voiceStatus = false;
						voiceEv({
							id: getLastChat.value.id,
							vType: 'play',
						});
					}
				});
				isLoading.value = false;
				getLastChat.value.loading = false;
			} else if (type === 'cardOrMsg') {
				if (getLastChat.value.isIframe) {
					// 去除开头和结尾的 ```json 标记
					let trimmedText;
					// 定义正则表达式匹配第一个 ```json ... ``` 块
					const regex = /```json\s+([\s\S]*?)\s+```/;
					// 使用正则表达式进行匹配
					const match = getLastChat.value.msg.match(regex);
					// 如果匹配成功，返回匹配组中的内容，否则返回 null
					if (match && match[1]) {
						trimmedText = match[1].trim();
					}
					// 解析 JSON 字符串为 JavaScript 对象或数组
					const jsonData = JSON.parse(trimmedText);
					console.log(jsonData);
					if (jsonData.type === 'internalInfo') {
						getLastChat.value.iframeUrl = jsonData.url;
					} else {
						// 提取 params 和 QA
						const { params, url } = jsonData[0];
						// 构建查询字符串
						const queryString = Object.entries(params)
							.map(([key, value]) => `${key}=${value}`)
							.join('&');
						getLastChat.value.iframeUrl = url + '?' + queryString;
					}
					getLastChat.value.msg = jsonData;
					getLastChat.value.isLoading = false;
				} else if (getLastChat.value.msg.includes('```json') && getLastChat.value.msg.includes('[]')) {
					// 查询卡片为空的情况
					getLastChat.value.isLoading = false;
					getLastChat.value.msg = '';

					let ar = chatList.value.filter((ele) => ele.type === 'user');
					let msg = ar?.[ar.length - 1]?.msg;
					setTextMessage({ type: 'cardComSummary', Question: msg });
				} else {
					// 字段afterStopAutoRead等于true说明是每日播报，需要缓存到本地
					if (getLastChat.value.afterStopAutoRead) proxy.$cache.local.setExpirseJSON('customerInsight0Report', getLastChat.value.msg, 1);
					nextTick(() => {
						if (getLastChat.value.afterStopAutoRead && isGlobalVoice.value) {
							getLastChat.value.voiceStatus = false;
							voiceEv({
								id: getLastChat.value.id,
								vType: 'play',
							});
						}
					});
					if (Object.keys(dotcorInfo.value).length > 0) {
						getLastChat.value.loading = false;
						chatList.value.push({
							msg: JSON.parse(JSON.stringify(dotcorInfo.value)),
							type: 'ai',
							dataId: '',
							doctorId: dotcorInfo.value.doctorId,
							templateType: 'docCard',
							id: Date.now() + 892,
							loading: true,
							isBtns: false,
							voiceStatus: true,
							isTarDoc: false,
							zeStatus: '0',
							isCollection: false,
							isMore: false,
						});
						dotcorInfo.value = '';
					}
					isLoading.value = false;
					getLastChat.value.loading = false;
				}
				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value.msg);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
			} else {
				//如果时猜你想问和下一步行动
				// 去除开头和结尾的 ```json 标记
				let trimmedText = getLastChat.value[fixedVariable[type][0]]
					.trim()
					.replace(/^```json\s+/, '')
					.replace(/\s+```$/, '');
				getLastChat.value[fixedVariable[type][1]] = JSON.parse(trimmedText).data;

				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value[fixedVariable[type][1]], type);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
			}
			scrollBottom();
		}
	}
	reader.read().then(processStreamResult);
};
//发送信息
const setTextMessage = async (val) => {
	try {
		let res = await fetchRequest(val);
		handleStream(res, 'cardOrMsg');
	} catch (e) {
		handleError(e);
	}
};
let handleError = (e) => {
	showToast({
		position: 'top',
		message: '请求失败,请重试',
	});
	isLoading.value = false;
	// 清除chatList最后一位
	chatList.value.splice(-1, 1);
	console.error(e);
};
//任务弹框
const actionPopup = ref(false);
const actionPopupTitle = ref('');
const missionList = ref([]);
let transformInfo = {
	执行电话拜访: 'PHONE_VISIT',
	Wecome沟通: 'WECHAT_VISIT',
	发送会议邀请: 'MEETING_INVITATION',
	记录实地拜访: 'OFFLINE_VISIT',
};
const toNextAction = (value) => {
	console.log(value);
	if (value === '查看医生详情') {
		viewDoctors.value = true;
		return;
	} else {
		actionPopup.value = true;
		actionPopupTitle.value = value;
		missionList.value = missionData.value.filter((ele) => {
			return ele.taskInfoType === transformInfo[value];
		});
		console.log(missionList.value);
	}
};
// 关闭任务
const closeTask = (id) => {
	showConfirmDialog({
		title: '提示',
		message: '是否确认关闭该任务？',
		confirmButtonColor: '#0052CC',
	}).then(() => {
		closeTaskInterface({ remark: '' }, [id]).then(() => {
			showToast({
				position: 'top',
				message: '任务已关闭',
			});
			for (const ite of taskData.value) {
				if (ite.id === id) {
					ite.taskInfoStatusType = 'CLOSE';
					break;
				}
			}
		});
	});
};
// 同事列表
const teammateList = ref([]);
const getTeammateList = () => {
	colleagueList(userId, { isBrief: false }).then((res) => {
		if (res.result && res.result.length > 0) {
			res.result = res.result.reduce((acc, cur) => {
				const hasDuplicate = acc.some((item) => item.id === cur.id);
				if (!hasDuplicate) {
					acc.push(cur);
				}
				return acc;
			}, []);
		}
		teammateList.value = res.result || [];
	});
};
// 转发
let forwardId = '';
const showForward = ref(false);
const forwardTaskDialog = (id) => {
	forwardId = id;
	showForward.value = true;
};
const activeTask = (id) => {
	showConfirmDialog({
		title: '提示',
		message: '是否确认转发该任务？',
		confirmButtonColor: '#0052CC',
	}).then(() => {
		taskTransferIn({ recipientId: id, taskInfoIdList: [forwardId] }).then(() => {
			showToast({
				position: 'top',
				message: '任务已转发',
			});
			taskData.value = taskData.value.filter((ele) => ele.id !== forwardId);
		});
	});
};
// 查看或者编辑任务
const goDetail = (item) => {
	router.push({
		name: 'keyTaskDetail',
		query: {
			isTabBar: route.query.isTabBar || '',
			isCreateTask: 'notCreate',
			taskContent: encodeURIComponent(JSON.stringify(item)),
			taskType: item.taskInfoType === 'PHONE_VISIT' ? '电话拜访' : item.taskInfoType === 'WECHAT_VISIT' ? 'Wecom拜访' : item.taskInfoType === 'OFFLINE_VISIT' ? '线下拜访' : '会议邀请',
		},
	});
};
//生成一条ai消息
const createAiMsg = (text) => {
	let message = {
		msg: '',
		dataId: '',
		type: 'ai',
		id: uuid(),
		loading: true,
		isBtns: true,
		reGen: true,
		voiceStatus: true,
		zeStatus: '0',
		isIframe: '',
		templateType: 'text',
	};
	text ? (message.loadingText = t('common.gdprfy')) : '';
	return message;
};
let doctotListFront = ref([]);
let missionData = ref([]);
const init = async (isRegen = false) => {
	isLoading.value = true;
	let data = {};

	// isRegen = false 不是重新生成，需要判断本地是否有缓存，有缓存取缓存数据，没有缓存直接生成
	if (isRegen === false && proxy.$cache.local.getExpirseJSON('customerInsight0Report')) {
		chatList.value.push({ ...createAiMsg(), afterStopAutoRead: true });
		// 每日业绩播报 --- 会话文本
		getLastChat.value.msg = proxy.$cache.local.getExpirseJSON('customerInsight0Report');
		// 每日业绩播报 --- 下一步计划
		getLastChat.value.nextAction = proxy.$cache.local.getExpirseJSON('customerInsight0NextAction');
		// 每日业绩播报 --- 猜你想问
		// getLastChat.value.questionList = proxy.$cache.local.getExpirseJSON('customerInsightQuestionList');
		getLastChat.value.loading = false;
		// 判断是否需要自动语音播报
		nextTick(() => {
			if (getLastChat.value.afterStopAutoRead && isGlobalVoice.value) {
				getLastChat.value.voiceStatus = false;
				voiceEv({
					id: getLastChat.value.id,
					vType: 'play',
				});
			}
		});
	} else {
		// 重新生成，清空每日播报缓存数据
		if (isRegen) {
			// 每日业绩播报 --- 会话文本
			proxy.$cache.local.remove('customerInsight0Report');
			// 每日业绩播报 --- 下一步计划
			proxy.$cache.local.remove('customerInsight0NextAction');
			// 每日业绩播报 --- 猜你想问
			// proxy.$cache.local.remove('customerInsightQuestionList');
		}
		chatList.value.push({ ...createAiMsg(t('common.gdprfy')), afterStopAutoRead: true });

		getLastChat.value.isLoading = true;
		//获取猜你想问
		// getSuggestion({ type: 'cardSuggestion', Question: { ...data } });
	}

	//获取我的卡片数据 res1 res2
	//获取随手记数据 res3 res4
	// 获取医生动态数据
	// 获取系统推荐任务数据 res5
	const { formattedToday, formattedYesterday } = getTodayAndYesterday();
	const params = {
		page: 0,
		size: 20,
		total: 0,
		sort: 'plan_end_time,desc',
	};
	// 待办任务
	const query = {
		taskInfoSourceType: null,
		taskInfoType: [],
		taskInfoStatusType: 'PROGRESS',
		taskInfoSearchType: 'ALL',
	};
	let [res1, res2, res3, res5, res7] = await Promise.all([
		myVisitSummary({ startLocalDate: '2024-06-01', endLocalDate: '2024-06-30' }),
		visitSummaryInfo({ startLocalDate: '2024-06-01', endLocalDate: '2024-06-30' }),
		findVisitReportList({ recordStatus: true, visitType: '线下拜访' }),
		getTaskList(params, query),
		getDoctorDynamics({ page: 0, size: 20 }),
	]);
	missionData.value = res5.result.content?.slice(0, 20);
	let res5Info = res5.result.content?.slice(0, 20).map((ele) => {
		const { taskDescription, taskInfoStatusType, taskInfoType, endTime, choiceDoctorVOList, doctorVO } = ele;
		const doctorInfo =
			choiceDoctorVOList.length > 0
				? choiceDoctorVOList.map(({ doctorId, doctorName, doctorSex, doctorTitle, hospital, department }) => ({
						id: doctorId,
						doctorName: doctorName,
						doctorSex,
						doctorTitle,
						hospital,
						department,
				  }))
				: {
						id: doctorVO.id,
						doctorName: doctorVO.doctorName,
						doctorSex: doctorVO.doctorSex,
						doctorTitle: doctorVO.doctorTitle,
						hospital: doctorVO.hospital,
						department: doctorVO.department,
				  };
		return {
			taskDescription,
			taskInfoStatusType,
			taskInfoType,
			endTime,
			doctorInfo,
		};
	});
	data = {
		拜访状况: {
			拜访数据: { ...res3.result.slice(0, 20) },
			随手记记录解读: { ...res3.result.slice(0, 20) },
		},
		客户动态: { ...res7.result.content },
		跟进建议: res5Info,
	};
	if (relam === 'muqiao') {
		Reflect.deleteProperty(data, '跟进建议');
	}
	// 前端处理医生列表数据
	doctotListFront.value.push(...res5Info.flatMap((item) => (Array.isArray(item.doctorInfo) ? item.doctorInfo : [item.doctorInfo])));

	res7.result.content.forEach((ele) => {
		doctotListFront.value.push({
			id: ele.doctorId,
			doctorName: ele.doctorName,
			doctorSex: ele.doctorSex,
			doctorTitle: ele.doctorTitle,
			hospital: ele.hospital,
			department: ele.department,
		});
	});

	// 去重处理
	const doctorMap = new Map();
	doctotListFront.value.forEach((doc) => {
		doctorMap.set(doc.id, doc);
	});
	doctotListFront.value = Array.from(doctorMap.values());
	if (doctotListFront.value.length > 0) {
		//下一步行动计划
		getLastChat.value.nextAction = ['查看医生详情'];
	}

	if (relam !== 'muqiao') {
		let tran = {
			PHONE_VISIT: '执行电话拜访',
			WECHAT_VISIT: 'Wecome沟通',
			MEETING_INVITATION: '发送会议邀请',
			OFFLINE_VISIT: '记录实地拜访',
		};
		res5.result.content?.slice(0, 20).forEach((ele) => {
			if (tran[ele.taskInfoType] && !getLastChat.value.nextAction.includes(tran[ele.taskInfoType])) {
				getLastChat.value.nextAction.push(tran[ele.taskInfoType]);
			}
		});
	}

	const cachedReport = proxy.$cache.local.getExpirseJSON('customerInsight0Report');
	isLoading.value = false;
	if (isRegen || !cachedReport) {
		setTextMessage({ ...data, type: 'cardDailyReport' });
	}
};
const viewDoctors = ref(false);
// 任务执行
const executeTask = (item) => {
	if (item.taskInfoType === 'PHONE_VISIT') {
		// 电话拜访，给一个提示“是否继续拨打电话”
		showConfirmDialog({
			title: '提示',
			message: '是否继续拨打电话？',
			confirmButtonColor: '#0052CC',
		});
	} else if (item.taskInfoType === 'OFFLINE_VISIT') {
		// 线下拜访，跳转到医生的拜访记录页面，填写完毕后，自动完成此任务
		// let productIdList = '';
		// if (item.productDomainList.length > 0) {
		// 	for (const s of item.productDomainList) {
		// 		productIdList += s.id + ',';
		// 	}
		// 	productIdList = productIdList.slice(0, productIdList.length - 1);
		// }
		// router.push({
		// 	name: 'toReport',
		// 	query: {
		// 		isTabBar: route.query.isTabBar || '',
		// 		taskId: item.id,
		// 		doctorId: item.doctorVO.doctorId,
		// 		visitType: '线下拜访',
		// 		productIdList: productIdList,
		// 		endTime: item.endTime,
		// 	},
		// });
		router.push({
			name: 'mAssistant',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else if (item.taskInfoType === 'WECHAT_VISIT') {
		// wecom拜访，给提示
		showToast({
			message: '请打开企业微信',
			position: 'top',
		});
	} else {
		// 会议邀请
		router.push({
			name: 'toMeet',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	}
};
// 重新生成
const reGen = (id) => {
	// 获取文本信息
	let prevItemMsg = '';
	const index = chatList.value.findIndex((item) => item.id === id);

	if (index !== -1) {
		if (chatList.value[index].afterStopAutoRead) {
			chatList.value.splice(index, 1);
			init(true);
			return;
		}
		prevItemMsg = chatList.value[index - 1]?.msg ?? '';
	}
	try {
		chatList.value.push(createUserMsg(prevItemMsg));
		// 生产一个ai的消息
		chatList.value.push(createAiMsg());
		scrollBottom();
		gd(prevItemMsg);
	} catch (e) {
		console.log(e);
		showToast({
			position: 'top',
			message: '重新生成失败！',
		});
	}
};
const inForUse = ref('');
onMounted(() => {
	getAgentListApi().then((res) => {
		const agentList = res.result.content || [];
		for (const ele of agentList) {
			if (ele.attributes.url.indexOf('customerInsight') > -1) {
				appId = ele.attributes.appID;
				appKey = ele.attributes.appKey;
				inForUse.value = ele.attributes?.inForUse || '';
				agentName.value = ele.name;
				break;
			}
		}
		// 会话记录
		getConversChatList();
		if (route.query.chatId) {
			chatId = route.query.chatId;
			getHistoryList();
		} else {
			init();
			chatId = 'khdc' + getRandomString();
		}
	});
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
});
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		position: fixed;
		z-index: 100;
		top: 0;
		right: 0;
		left: 0;
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
			.history-img {
				margin-left: 12px;
				margin-right: 24px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-j-header {
		width: 100%;
		height: 44px;
	}

	.mc-content {
		display: flex;
		display: -webkit-flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-list {
			flex: 1 0 0px;
			height: 0px;
			padding: 0 6px;
			padding-top: 12px;
			overflow-y: auto;
			.mc-content-list-left {
				display: flex;
				flex-direction: column;
				width: 100%;
				margin-bottom: 12px;
				.mc-content-template {
					border-top-left-radius: 8px;
					border-bottom-right-radius: 8px;
					border-top-right-radius: 8px;
				}
				img {
					width: 26px;
					height: 26px;
					margin-right: 6px;
					margin-top: 6px;
				}

				.content-iframe {
					background-color: var(--ac-bg-color--fff);
					padding: 8px;
					border-top-left-radius: 8px;
					border-bottom-right-radius: 8px;
					border-top-right-radius: 8px;
					display: flex;
					flex-direction: column;
				}

				.content-docList {
					width: 80%;
					background-color: var(--ac-bg-color--fff);
					padding: 8px;
					border-top-left-radius: 8px;
					border-bottom-right-radius: 8px;
					border-top-right-radius: 8px;
					display: flex;
					flex-direction: column;
					.docList-list {
						max-height: 225px;
						overflow-y: auto;
						.docList-list-header {
							display: flex;
							align-items: center;
							justify-content: space-between;
							.docList-list-header__name {
								font-size: 15px;
								font-weight: 500;
							}

							.docList-list-header__right {
								display: flex;
								align-items: center;
								.docList-list-header__right--icon {
									width: 15px;
									height: 15px;
									object-fit: cover;
									margin-top: 0;
								}
								.docList-list-header__right--icon:nth-child(1) {
									margin-right: 8px;
								}
								.docList-list-header__right--icon:nth-child(2) {
									width: 13px;
									height: 13px;
								}
							}
						}
						.docList-list-item {
							background-color: var(--ac-bg-color);
							border-radius: 5px;
							margin-top: 8px;
							position: relative;
							display: flex;
							align-items: center;
							padding: 8px;
							img {
								height: 50px;
								width: 50px;
								margin-right: 8px;
								margin-top: 0;
							}

							.amd-content-item-desc {
								flex: 1;
								display: flex;
								flex-direction: column;
								font-size: 12px;
								.desc-name {
									font-weight: 500;
									font-size: 14px;
								}

								.desc-hp {
									width: 100%;
								}
							}
						}
					}

					.docList-btns {
						display: flex;
						flex-wrap: wrap;
						justify-content: space-between;
						.docList-btns-item {
							background-color: var(--ac-bg-color);
							border-radius: 5px;
							display: flex;
							align-items: center;
							justify-content: space-between;
							padding: 8px 12px;
							width: 48.5%;
							margin-top: 8px;
						}
					}

					.content-docList-num {
						margin-top: 8px;
						display: flex;
						flex-wrap: wrap;
						border-top: 1px solid rgba(221, 221, 221, 0.35);
						padding-top: 12px;
						.num-item {
							margin-bottom: 8px;
							margin-right: 2%;
							width: 49%;
							border-radius: 5px;
							background-color: var(--ac-bg-color);
							padding: 8px;
							display: flex;
							align-items: center;
							justify-content: space-between;
							.num-item-name {
								width: calc(100% - 12px);
							}
						}
						& .num-item:nth-child(2n) {
							margin-right: 0;
						}
					}

					.content-docList-activity {
						margin-top: 8px;
						display: flex;
						flex-direction: column;
						.activity-item {
							width: 100%;
							background-color: var(--ac-bg-color);
							border-radius: 5px;
							padding: 8px;
							margin-bottom: 8px;
							display: flex;
							flex-direction: column;
							.activity-item--name {
								font-size: 15px;
								font-weight: 500;
							}
							.activity-item--desc {
								color: #6b778c;
								font-size: 12px;
							}
						}

						.content-docList-activity-more {
							color: var(--ac-font-color-active);
							text-align: right;
						}
					}
				}

				.mc-content__groupList {
					padding: 8px 0 0 0;
					display: flex;
					margin-left: 32px;
					display: flex;
					overflow-x: auto;
					width: 80%;
					span {
						flex: none;
						margin-right: 8px;
						margin-bottom: 8px;
						color: #0b67e4;
						border-radius: 5px;
						border: 1px solid #0b67e4;
						padding: 3px 6px;
					}
				}
			}

			.mc-content-list-right {
				display: flex;
				width: 95%;
				float: right;
				margin-bottom: 12px;
				justify-content: flex-end;
				.mc-content-template {
					border-top-right-radius: 8px;
					border-bottom-left-radius: 8px;
					border-top-left-radius: 8px;
					color: var(--ac-bg-color--fff);
					background-color: var(--ac-bg-active-color);
					.mc-content-template__edit {
						border-top: solid 1px rgba(223, 225, 230, 0.3);
						margin-top: 2px;
					}
				}
				.mc-content-info {
					width: 70%;
					background-color: var(--ac-bg-color);
					border: solid 1px var(--ac-border-color);
					border-radius: 5px;

					.docList-list-item {
						background-color: var(--ac-bg-color);
						border-radius: 5px;
						margin-top: 8px;
						position: relative;
						display: flex;
						align-items: center;
						padding: 8px;
						img {
							height: 50px;
							width: 50px;
							margin-right: 8px;
							margin-top: 0;
						}

						.amd-content-item-desc {
							flex: 1;
							display: flex;
							flex-direction: column;
							font-size: 12px;
							.desc-name {
								font-weight: 500;
								font-size: 14px;
							}

							.desc-hp {
								width: 100%;
							}
						}
					}
				}
			}

			.mc-content-docInfo {
				display: flex;
				width: 68%;
				float: right;
				margin-bottom: 12px;
				border: solid 1px var(--ac-border-color);
				padding: 12px;
				border-radius: 5px;
				align-items: center;

				img {
					height: 50px;
					width: 50px;
					margin-right: 8px;
				}

				.amd-content-item-desc {
					flex: 1;
					display: flex;
					flex-direction: column;
					font-size: 12px;
					.desc-name {
						font-weight: 500;
						font-size: 14px;
					}

					.desc-hp {
						width: 100%;
					}
				}
			}

			.mc-content-template {
				max-width: calc(95% - 32px);
				background-color: var(--ac-bg-color--fff);
				padding: 12px;
			}

			// 猜你想问
			.mc-content-template__guess {
				padding-top: 10px;
				.guess-header {
					display: flex;
					align-items: center;
					margin-bottom: 3px;
					.guess-header-title {
						font-size: 12px;
						color: var(--ac-colors-myGray-500);
						margin-right: 6px;
					}
					.guess-header-line {
						flex: 1;
						height: 1px;
						background-color: rgba(221, 221, 221, 0.35);
					}
				}
				.guess-list {
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					.guess-list-item {
						font-size: 12px;
						display: flex;
						border-radius: 5px;
						padding: 3px 6px;
						justify-content: center;
						border: 1px solid rgb(239, 240, 241);
						align-items: center;
						margin-bottom: 4px;
						margin-right: 4px;
					}

					.qutoe-list-item {
						width: 100%;
						justify-content: flex-start;
						display: flex;
						align-items: center;
						margin-bottom: 4px;
						position: relative;
						i {
							position: absolute;
							left: 4px;
						}
						span {
							font-size: 12px;
							border-radius: 5px;
							border: 1px solid rgb(239, 240, 241);
							padding: 3px 6px;
							padding-left: 20px;
							background-color: var(--ac-bg-color);
						}
					}
				}
			}
		}
		// 声明
		.mc-declare {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 6px 0px 3px 0px;
			font-size: 12px;
			color: var(--ac-colors-myGray-500);
		}
		// 按钮组
		.mc-btns {
			width: 100%;
			margin: 0px auto;
			padding: 4px 6px;
			overflow-x: auto;
			display: flex;
			white-space: nowrap;
			background-color: var(--ac-bg-color);
			.mc-btns-item {
				margin-right: 6px;
				border: 1px solid var(--ac-border-color);
				padding: 4px 10px;
				border-radius: 20px;
				font-size: 12px;
				color: var(--ac-font-color);
			}
		}

		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 8px;
			padding-bottom: 6px;
			min-height: 64px;
			position: relative;
			display: flex;
			align-items: flex-start;
			.chat-img {
				width: 25px;
				margin-top: 12.5px;
				object-fit: contain;
				margin-left: 15px;
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
	&:deep(.van-popup--bottom).doc {
		padding: 0 !important;
		div {
			padding: 0px 42px;
			height: 49px;
			line-height: 49px;
			color: #172b4d;
			font-weight: bold;
			font-size: 18px;
			position: relative;
			&::before {
				content: '';
				display: inline-block;
				width: 10px;
				height: 10px;
				border-top: 2px solid #172b4d;
				border-right: 2px solid #172b4d;
				position: absolute;
				left: 18px;
				top: 50%;
				transform: translateY(-50%) rotate(-135deg);
			} //
		}
		iframe {
			height: calc(100% - 49px);
		}
	}
}
.btns {
	width: 100%;
	height: 35px;
	line-height: 35px;
	border-radius: 5px;
	background-color: #ffffff;
	color: #172b4d;
	margin-top: 10px;
	padding-left: 12px;
	padding-right: 15px;
	display: flex;
	align-items: center;

	span {
		flex: 1;
	}

	.right {
		width: 8px;
		height: 8px;
		border-top: 1px solid #6b778c;
		border-right: 1px solid #6b778c;
		transform: rotate(45deg);
	}
}
::v-deep(.action-popup) {
	background-color: #f4f5f7;
}
.action {
	padding: 0 15px;

	&-title {
		color: #172b4d;
		font-weight: bold;
		font-size: 15px;
	}
	&-list {
		margin-top: 8px;
		&-item {
			border-radius: 6px;
			background-color: #ffffff;
			padding: 12px;
			margin-bottom: 15px;
			display: flex;
			flex-direction: column;
			.task-item__header {
				padding-bottom: 12px;
				border-bottom: 1px solid #dfe1e6;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.task-item__header-left {
					display: flex;
					align-items: center;
					.left-icon {
						height: 12px;
						display: inline-block;
						width: 4px;
						background-color: #0052cc;
						margin-right: 6px;
					}
					.left-tag {
						margin-left: 6px;
					}
				}
				.task-item__header-right {
					span {
						font-size: 12px;
					}
				}
			}

			.task-item__body {
				padding-top: 12px;
				display: flex;
				flex-direction: column;
				position: relative;
				.task-item__body--img {
					position: absolute;
					right: 5px;
					top: 50%;
					z-index: 9;
					width: 4px;
					height: 8px;
					margin-bottom: 4px;
				}
				.body-item {
					display: flex;
					align-items: flex-start;
					margin-bottom: 10px;
					& span:nth-child(1) {
						width: 75px;
					}
					& span:nth-child(2) {
						flex: 1;
						word-break: break-all;
					}
				}
			}

			.task-item__bottom {
				display: flex;
				justify-content: flex-end;
				.task-item__bottom_btn {
					margin-right: 8px;
					&:deep(.van-button__text) {
						color: #0052cc;
					}
				}
			}
		}
	}
}
</style>
