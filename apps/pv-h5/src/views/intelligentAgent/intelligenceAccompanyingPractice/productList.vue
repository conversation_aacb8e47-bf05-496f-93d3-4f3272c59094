<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ route.query.title }}</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<div class="intelligent">
				<van-row gutter="15">
					<van-col v-for="product in productList" :key="product.id" span="12">
						<product-item :height="102" :item="product"></product-item>
					</van-col>
				</van-row>
			</div>
		</div>
	</div>
</template>
<script setup>
import p9 from '@/assets/img/demo/p/p9.png';
import p10 from '@/assets/img/demo/p/p10.png';
import p11 from '@/assets/img/demo/p/p11.png';
import p12 from '@/assets/img/demo/p/p12.png';
import ProductItem from './components/productItem.vue';

let navList = ['全部', '产品', '技能'];
let currentIndex = ref('全部');
const navListRef = ref(null);
const productList = ref([
	{
		img: p9,
		title: '百悦泽 产品疗效',
		desc: '识问答之旅，从这里开始',
		id: '1',
		appId: '',
		tag: '已学习',
	},
	{
		img: p11,
		title: '百悦泽产品安全性',
		desc: '识问答之旅，从这里开始',
		id: '2',
		appId: '',
	},
	{
		img: p10,
		title: '百悦泽 产品耐久性',
		desc: '识问答之旅，从这里开始',
		id: '2',
		appId: '',
	},
	{
		img: p12,
		title: '百悦泽 耐受性',
		desc: '识问答之旅，从这里开始',
		id: '2',
		appId: '',
	},
]);
const router = useRouter();
const route = useRoute();
const goBack = () => {
	router.go(-1);
};
let changeNav = (item, index, e) => {
	let oldIndex = navList.findIndex((ele) => ele === currentIndex.value);
	console.log(oldIndex, index);
	currentIndex.value = item;

	const rect = e.target.getBoundingClientRect();
	// if (rect.left + rect.width > (window.innerWidth - 300 || document.documentElement.clientWidth - 300) || rect.left < 0) {

	if (index > oldIndex) {
		console.log(rect.left + '|' + window.innerWidth * 0.7);
		if (rect.left + rect.width > window.innerWidth * 0.7) {
			navListRef.value.scrollTo({
				left: navListRef.value.scrollLeft + 200,
				behavior: 'smooth', // 可选，以平滑动画过渡
			});
		}
	} else {
		console.log(rect.left + rect.width + '|' + window.innerWidth * 0.35);

		if (rect.left + rect.width < window.innerWidth * 0.35) {
			navListRef.value.scrollTo({
				left: -200,
				behavior: 'smooth', // 可选，以平滑动画过渡
			});
		}
	}
};
const emit = defineEmits(['sendMsg']);
onMounted(() => {
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
});
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-list {
			flex: 1 0 0px;
			overflow-y: auto;

			// midlle模版
			.mc-content-middle {
				padding: 0 15px;
				display: flex;
				flex-direction: column;
				.mc-content-middle__avater {
					width: 120px;
					height: 120px;
					margin: 0 auto;
					margin-bottom: 15px;
					margin-top: 15px;
				}
				.mc-content-middle__desc {
					border-radius: 5px;
					background-color: var(--ac-bg-color--fff);
					padding: 8px;
					margin-bottom: 15px;
				}

				.mc-content-middle__app {
					border-radius: 5px;
					background-color: var(--ac-bg-color--fff);
					padding: 12px;
					margin-bottom: 10px;

					.app-header {
						display: flex;
						align-items: center;
						margin-bottom: 15px;

						.app-header__img {
							width: 16px;
							height: 16px;
							margin-right: 5.4px;
						}
						.app-header__name {
							font-weight: 500;
							flex: 1;
							width: 0;
						}
						&__more {
							color: #6b778c;
							font-weight: 400;
							font-size: 12px;
						}
					}

					.app-bottom {
						display: flex;
						align-items: flex-start;
						justify-content: space-around;
						margin-top: 12px;
						.app-bottom__item {
							display: flex;
							flex-direction: column;
							align-items: center;

							.item-top {
								width: 40px;
								height: 40px;
								border-radius: 6px;
								background-color: var(--ac-bg-color);
								display: flex;
								align-items: center;
								justify-content: center;
								img {
									width: 20px;
									height: 20px;
									object-fit: cover;
								}
							}

							.item-text {
								font-size: 12px;
								line-height: 1.2;
								margin-top: 8px;
								text-align: center;
							}
						}
					}
				}

				.mc-content-middle__report {
					border-radius: 5px;
					background-color: var(--ac-bg-color--fff);
					padding: 15px 9px;
					margin-bottom: 10px;

					.report-header {
						display: flex;
						align-items: center;
						justify-content: space-between;
						margin-bottom: 12px;
						.report-header__left {
							display: flex;
							align-items: center;
							.report-header__left-img {
								width: 16px;
								height: 16px;
								margin-right: 4px;
							}
							.report-header__left-name {
								font-size: 16px;
								font-weight: 600;
							}
						}
						.report-header__right {
							.report-header__right-name {
								color: #6b778c;
							}
						}
					}

					.report-bottom {
						border-radius: 6px;
						background: linear-gradient(134.62deg, rgba(255, 255, 255, 0.25) 0%, rgba(179, 238, 255, 0.1) 100%), #ffffff;
						padding: 12px;
						display: flex;
						align-items: center;

						.report-bottom__left {
							flex: 1;
							display: flex;
							flex-direction: column;
							padding-right: 8px;
							.report-bottom__left-name {
								font-weight: 600;
								margin-bottom: 8px;
							}

							.report-bottom__left-desc {
								font-size: 12px;
								color: #6b778c;
							}
						}

						.report-bottom__right {
							width: 98px;
							height: 78px;
							background: url('../../assets/img/demo/report_bg.png') no-repeat;
							background-size: cover;
						}
					}
				}

				.mc-content-middle__btnList {
					display: flex;
					margin-bottom: 8px;
					.btnList-item {
						border-radius: 5px;
						border: 1px solid #dfe1e6;
						margin-right: 8px;
						padding: 4px 8px;
						font-weight: 500;
						width: fit-content;
					}
				}
			}

			.mc-content-template {
				max-width: calc(95% - 32px);
				background-color: var(--ac-bg-color--fff);
				padding: 12px;
			}
		}
		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 8px;
			padding-bottom: 6px;
			position: relative;
			height: 64px;
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
.intelligent {
	&-nav {
		display: flex;
		flex-wrap: nowrap;
		padding-left: 15px;
		margin-top: 10px;
		width: 100%;
		overflow-x: scroll;
		scrollbar-width: none; /* firefox */
		-ms-overflow-style: none; /* IE 10+ */
		&::-webkit-scrollbar {
			display: none; /* Chrome Safari */
		}

		&-item {
			text-align: center;
			width: 70px;
			height: 35px;
			line-height: 35px;
			border-radius: 35px;
			background-color: #f4f5f7;
			margin-right: 15px;
			flex-shrink: 0;
			color: #172b4d;
			font-size: 15px;
			transition: all 0.2s;
		}
		.active {
			background-color: #0052cc;
			color: #fff;
		}
	}
	&-list {
		&-item {
			display: flex;
			align-items: center;
			padding: 12px 20px;
			position: relative;
			&-left {
				margin-right: 12px;
				img {
					width: 55px;
				}
			}
			&-right {
				div:nth-child(1) {
					color: #172b4d;
					font-weight: bold;
					font-size: 16px;
				}
				div:nth-child(2) {
					color: #6b778c;
					font-size: 12px;
					margin-top: 2px;
				}
			}
			&-btn {
				// width: 25px;
				position: absolute;
				right: 15px;
				top: 50%;
				transform: translateY(-50%);
				img {
					width: 25px;
				}
			}
		}
	}
}
:deep() {
	.product-item {
		background-color: #f4f5f7;
	}
	.van-row {
		margin: 15px 7.5px;
	}
	.van-col {
		padding: 0 7.5px;
	}
}
</style>
