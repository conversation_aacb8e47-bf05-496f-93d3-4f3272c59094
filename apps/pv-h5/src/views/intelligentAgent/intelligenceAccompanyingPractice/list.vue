<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('chat.intelligenceAccompanyingPractice') }}</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<div class="intelligent">
				<!-- <div class="intelligent-nav" ref="navListRef">
					<div class="intelligent-nav-item" :class="{ active: item === currentIndex }" v-for="(item, index) in navList" :key="index" @click="changeNav(item, index, $event)">{{ item }}</div>
				</div> -->
				<div class="discovery-nav">
					<div class="discovery-nav-list" ref="navListRef">
						<div class="discovery-nav-list-item" :class="{ active: currentIndex === item }" v-for="item in navList" :key="item" @click="navChange(item, $event)">{{ item }}</div>
					</div>
				</div>
				<div class="class-list" v-for="item in list" :key="item">
					<div class="class-list__title">{{ item.class }}</div>
					<van-row gutter="15" class="class-list__content">
						<van-col v-for="product in item.list" :key="product.id" span="12">
							<product-item :height="93" :item="product" :btns="false"></product-item>
						</van-col>
					</van-row>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { showToast } from 'vant';

import c1 from '@/assets/img/demo/p/c1.png';
import c2 from '@/assets/img/demo/p/c2.png';
import c3 from '@/assets/img/demo/p/c3.png';
import c4 from '@/assets/img/demo/p/c4.png';
import c5 from '@/assets/img/demo/p/c5.png';
import c6 from '@/assets/img/demo/p/c6.png';
import c7 from '@/assets/img/demo/p/c7.png';

import ProductItem from './components/productItem.vue';
let navList = ['推荐', '产品'];
let currentIndex = ref('推荐');
const navListRef = ref(null);
//页签切换
const navChange = (item, e) => {
	currentIndex.value = item;
	const rect = e.target.getBoundingClientRect();
	if (rect.left + rect.width > (window.innerWidth || document.documentElement.clientWidth) || rect.left < 0) {
		navListRef.value.scrollTo({
			left: navListRef.value.scrollLeft + (rect.left < 0 ? rect.left : rect.left + rect.width - window.innerWidth),
			behavior: 'smooth', // 可选，以平滑动画过渡
		});
	}
};
const productList = ref([
	{
		class: '产品',
		list: [
			{
				img: 'http://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/50055b40ab1371efbfe01776b3ce0102/snapshots/4aa9fc8e03564bcf8708a3f2218e6398-00005.jpg',
				title: '癫痫药物治疗',
				desc: '识问答之旅，从这里开始',
				id: '1',
				appId: '',
			},
			{
				img: 'http://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/00b35c40ab1371efbfc86eb3690d0102/snapshots/cfe94068a1064c8ab5c9fdc84bbae8a7-00005.jpg',
				title: '癫痫疾病知识',
				desc: '识问答之旅，从这里开始',
				id: '2',
				appId: '',
			},
			{
				img: 'http://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/305d6940ab1371efbfe01776b3ce0102/snapshots/e893a8ed6aa944e58ccab5b301086e5b-00005.jpg',
				title: '癫痫产品知识',
				desc: '识问答之旅，从这里开始',
				id: '3',
				appId: '',
			},
			{
				img: 'http://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/10695a40ab1371efb9871777b3de0102/snapshots/13d31439406d46cfa3de24ed5c50da57-00005.jpg',
				title: '药店癫痫患者管理',
				desc: '识问答之旅，从这里开始',
				id: '4',
				appId: '',
			},
		],
	},
	// {
	// 	class: '技能',
	// 	list: [
	// 		{
	// 			img: c4,
	// 			title: '访前计划',
	// 			desc: '识问答之旅，从这里开始',
	// 			id: '4',
	// 			appId: '',
	// 		},
	// 		{
	// 			img: c5,
	// 			title: '顺利开场',
	// 			desc: '识问答之旅，从这里开始',
	// 			id: '5',
	// 			appId: '',
	// 		},
	// 		{
	// 			img: c6,
	// 			title: '探寻需求',
	// 			desc: '识问答之旅，从这里开始',
	// 			id: '6',
	// 			appId: '',
	// 		},
	// 		{
	// 			img: c7,
	// 			title: '传递关键信息',
	// 			desc: '识问答之旅，从这里开始',
	// 			id: '7',
	// 			appId: '',
	// 		},
	// 	],
	// },
]);
const list = computed(() => {
	if (currentIndex.value === '推荐') return productList.value;
	return productList.value.filter((item) => item.class === currentIndex.value);
});
const goList = (item) => {
	router.push('/iapProductList?title=' + item.title + '&isTabBar=' + (route.query.isTabBar || ''));
};
const goBack = () => {
	router.go(-1);
};
const router = useRouter();
const route = useRoute();
const emit = defineEmits(['sendMsg']);
onMounted(() => {
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
});
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		// border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-list {
			flex: 1 0 0px;
			overflow-y: auto;

			// midlle模版
			.mc-content-middle {
				padding: 0 15px;
				display: flex;
				flex-direction: column;
				.mc-content-middle__avater {
					width: 120px;
					height: 120px;
					margin: 0 auto;
					margin-bottom: 15px;
					margin-top: 15px;
				}
				.mc-content-middle__desc {
					border-radius: 5px;
					background-color: var(--ac-bg-color--fff);
					padding: 8px;
					margin-bottom: 15px;
				}

				.mc-content-middle__app {
					border-radius: 5px;
					background-color: var(--ac-bg-color--fff);
					padding: 12px;
					margin-bottom: 10px;

					.app-header {
						display: flex;
						align-items: center;
						margin-bottom: 15px;

						.app-header__img {
							width: 16px;
							height: 16px;
							margin-right: 5.4px;
						}
						.app-header__name {
							font-weight: 500;
							flex: 1;
							width: 0;
						}
						&__more {
							color: #6b778c;
							font-weight: 400;
							font-size: 12px;
						}
					}

					.app-bottom {
						display: flex;
						align-items: flex-start;
						justify-content: space-around;
						margin-top: 12px;
						.app-bottom__item {
							display: flex;
							flex-direction: column;
							align-items: center;

							.item-top {
								width: 40px;
								height: 40px;
								border-radius: 6px;
								background-color: var(--ac-bg-color);
								display: flex;
								align-items: center;
								justify-content: center;
								img {
									width: 20px;
									height: 20px;
									object-fit: cover;
								}
							}

							.item-text {
								font-size: 12px;
								line-height: 1.2;
								margin-top: 8px;
								text-align: center;
							}
						}
					}
				}

				.mc-content-middle__report {
					border-radius: 5px;
					background-color: var(--ac-bg-color--fff);
					padding: 15px 9px;
					margin-bottom: 10px;

					.report-header {
						display: flex;
						align-items: center;
						justify-content: space-between;
						margin-bottom: 12px;
						.report-header__left {
							display: flex;
							align-items: center;
							.report-header__left-img {
								width: 16px;
								height: 16px;
								margin-right: 4px;
							}
							.report-header__left-name {
								font-size: 16px;
								font-weight: 600;
							}
						}
						.report-header__right {
							.report-header__right-name {
								color: #6b778c;
							}
						}
					}

					.report-bottom {
						border-radius: 6px;
						background: linear-gradient(134.62deg, rgba(255, 255, 255, 0.25) 0%, rgba(179, 238, 255, 0.1) 100%), #ffffff;
						padding: 12px;
						display: flex;
						align-items: center;

						.report-bottom__left {
							flex: 1;
							display: flex;
							flex-direction: column;
							padding-right: 8px;
							.report-bottom__left-name {
								font-weight: 600;
								margin-bottom: 8px;
							}

							.report-bottom__left-desc {
								font-size: 12px;
								color: #6b778c;
							}
						}

						.report-bottom__right {
							width: 98px;
							height: 78px;
							background: url('../../assets/img/demo/report_bg.png') no-repeat;
							background-size: cover;
						}
					}
				}

				.mc-content-middle__btnList {
					display: flex;
					margin-bottom: 8px;
					.btnList-item {
						border-radius: 5px;
						border: 1px solid #dfe1e6;
						margin-right: 8px;
						padding: 4px 8px;
						font-weight: 500;
						width: fit-content;
					}
				}
			}

			.mc-content-template {
				max-width: calc(95% - 32px);
				background-color: var(--ac-bg-color--fff);
				padding: 12px;
			}
		}
		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 8px;
			padding-bottom: 6px;
			position: relative;
			height: 64px;
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
.intelligent {
	&-list {
		&-item {
			display: flex;
			align-items: center;
			padding: 12px 20px;
			position: relative;
			&-left {
				margin-right: 12px;
				img {
					width: 55px;
				}
			}
			&-right {
				div:nth-child(1) {
					color: #172b4d;
					font-weight: bold;
					font-size: 16px;
				}
				div:nth-child(2) {
					color: #6b778c;
					font-size: 12px;
					margin-top: 2px;
				}
			}
			&-btn {
				// width: 25px;
				position: absolute;
				right: 15px;
				top: 50%;
				transform: translateY(-50%);
				img {
					width: 25px;
				}
			}
		}
	}
}
.discovery {
	&-nav {
		// height: 36px;
		position: sticky;
		top: 0;
		z-index: 3;
		&-list {
			display: flex;
			align-items: center;
			height: 100%;
			// overflow-x: scroll;
			width: 100vw;
			// border-bottom: 1px solid #dfe1e6;
			background-color: var(--pv-bgc);
			padding: 7px 0;
			position: relative;
			&::-webkit-scrollbar {
				height: 1px; /* 宽度 */
			}
			&-item {
				// flex: 1;
				font-size: 14px;
				color: var(--pv-no-active-color);
				padding: 0 16px;
				flex-shrink: 0;
				text-align: center;
			}
			.active {
				font-weight: 400;
				color: var(--pv-default-color);
				position: relative;
				font-weight: bold;
			}
			.active::after {
				content: '';
				position: absolute;
				z-index: 1;
				background-color: var(--pv-tabbar-active);
				width: 30px;
				height: 3px;
				left: 50%;
				bottom: -7px;
				transform: translateX(-50%);
				border-radius: 2px;
			}
			&::after {
				content: '';
				position: absolute;
				height: 1px;
				width: 100%;
				background-color: #dfe1e6;
				bottom: 0;
			}
		}
	}
}
.class-list {
	padding: 15px;
	&__title {
		position: relative;
		padding-left: 10px;
		margin-bottom: 10px;
		&::before {
			content: '';
			width: 4px;
			height: 11px;
			background: #0052cc;
			position: absolute;
			left: 0;
			top: 4px;
		}
	}
	:deep() {
		.product-item {
			background-color: #f4f5f7;
		}
		.van-row {
			margin: 0 -7.5px;
		}
		.van-col {
			padding: 0 7.5px;
		}
	}
}
</style>
