<template>
	<div @click="kxkwDetail" class="product-item">
		<div class="product-item__img">
			<img :src="item.img" :style="{ height: height + 'px' }" />
		</div>
		<div class="product-item__text">
			<div class="product-item__text__title ell">{{ item.title }}</div>
			<div class="product-item__text__desc ell">{{ item.desc }}</div>
		</div>
		<!-- <div class="product-item__btns" v-if="btns">
			<van-button size="small" block type="" :to="'/iapProduct?id=' + item.id + '&title=' + item.title + '&isTabBar=' + route.query.isTabBar">快学</van-button>
			<van-button size="small" block type="primary" :to="'/xiaoice?appId=' + item.appId + '&isTabBar=' + route.query.isTabBar">快问</van-button>
		</div> -->
		<div class="product-item__tag" v-if="item.tag">
			<van-tag type="primary" size="medium">{{ item.tag }}</van-tag>
		</div>
	</div>
</template>

<script setup>
const router = useRouter();
const route = useRoute();
const props = defineProps({
	item: Object,
	btns: {
		type: Boolean,
		default: true,
	},
	height: {
		type: Number,
		default: 102,
	},
});
const kxkwDetail = () => {
	router.push({
		name: 'intelligenceAccompanyingPracticeProduct',
		query: {
			id: props.item.id,
			title: props.item.title,
			isTabBar: route.query.isTabBar,
		},
	});
};
</script>

<style lang="scss" scoped>
.product-item {
	display: flex;
	flex-direction: column;
	background-color: #ffffff;
	position: relative;
	margin-bottom: 15px;
	border-radius: 6px;
	&__img {
		margin-bottom: 10px;
		img {
			width: 100%;
			object-fit: cover;
		}
	}
	&__text {
		padding: 0 10px;
		&__title {
			color: #172b4d;
			font-weight: 500;
			font-size: 12px;
		}
		&__desc {
			color: #6b778c;
			font-weight: 400;
			font-size: 10px;
			margin-bottom: 12px;
		}
	}
	&__btns {
		padding: 0 10px 8px;
		display: flex;
		justify-content: space-around;
		:deep() {
			.van-button + .van-button {
				margin-left: 8px;
			}
			.van-button {
				background-color: #e5edf9;
				color: #0052cc;
				border: 0;
			}
			.van-button--primary {
				background-color: #0052cc;
				color: #fff;
			}
		}
	}
	&__tag {
		position: absolute;
		top: 0;
		left: 0;
	}
}
</style>
