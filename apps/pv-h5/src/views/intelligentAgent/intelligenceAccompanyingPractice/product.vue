<template>
	<div class="mc">
		<!-- header -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ sTitle }}</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- content -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<div class="product">
				<div class="product__vedio">
					<div ref="videoRef"></div>
				</div>
				<div class="product__title">{{ sTitle }}</div>
				<!-- <div class="product__desc">肺癌相关知识</div> -->
				<div class="product__author">作者：穆桥</div>
				<div class="product__time">课程日期：2024.11.25</div>
				<div class="product__content">暂无</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import p99 from '@/assets/img/demo/p/p99.png';
import Player from 'xgplayer';
import 'xgplayer/dist/index.min.css';
const route = useRoute();
const router = useRouter();
const videoRef = ref(null);
const videoPlayer = ref(null);
const list = ref([
	{
		img: 'http://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/50055b40ab1371efbfe01776b3ce0102/snapshots/4aa9fc8e03564bcf8708a3f2218e6398-00005.jpg',
		title: '癫痫药物治疗',
		desc: '识问答之旅，从这里开始',
		sUrl: 'https://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/customerTrans/d5848ba8f8ed0c17751860c620dd55ef/804a1d8-19362be61e1-0004-a2e9-c0e-13f4b.mp4',
		id: '1',
		appId: '',
	},
	{
		img: 'http://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/00b35c40ab1371efbfc86eb3690d0102/snapshots/cfe94068a1064c8ab5c9fdc84bbae8a7-00005.jpg',
		title: '癫痫疾病知识',
		desc: '识问答之旅，从这里开始',
		sUrl: 'https://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/customerTrans/d5848ba8f8ed0c17751860c620dd55ef/804a1d8-19362be61e1-0004-a2e9-c0e-13f4b.mp4',
		id: '2',
		appId: '',
	},
	{
		img: 'http://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/305d6940ab1371efbfe01776b3ce0102/snapshots/e893a8ed6aa944e58ccab5b301086e5b-00005.jpg',
		title: '癫痫产品知识',
		desc: '识问答之旅，从这里开始',
		sUrl: 'https://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/305d6940ab1371efbfe01776b3ce0102/9348e9b90d00401e86715a72fcfe9292-0d9b2517fc7385893df2a2e85b925d67-fd.mp4',
		id: '3',
		appId: '',
	},
	{
		img: 'http://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/10695a40ab1371efb9871777b3de0102/snapshots/13d31439406d46cfa3de24ed5c50da57-00005.jpg',
		title: '药店癫痫患者管理',
		desc: '识问答之旅，从这里开始',
		sUrl: 'https://meetting-oss.oss-cn-beijing.aliyuncs.com/vod-4a2a38/10695a40ab1371efb9871777b3de0102/b4a6c03737bf4957ad00d20d5349a1ad-2df07df42ff2e6280a9d098b21ac18f9-fd.mp4',
		id: '4',
		appId: '',
	},
]);
const sUrl = ref('');
const sTitle = ref('');
const pImg = ref('');
const initVedio = () => {
	nextTick(() => {
		videoPlayer.value = new Player({
			el: videoRef.value,
			url: sUrl.value,
			poster: pImg.value,
			width: '100%',
			height: '190px',
			fitVideoSize: 'fixWidth',
		});
		console.log(videoPlayer.value);
	});
};
const goBack = () => {
	router.go(-1);
};
onMounted(() => {
	const id = route.query.id;
	const ll = list.value.filter((item) => item.id === id);
	sUrl.value = ll[0].sUrl;
	sTitle.value = ll[0].title;
	pImg.value = ll[0].img;
	initVedio();
});
</script>
<style lang="scss" scoped>
.mc {
	height: 100vh;
	width: 100%;
	display: flex;
	flex-direction: column;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 50px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid rgb(223, 225, 230);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
		}
	}

	.mc-content {
		height: calc(100vh - 44px);
		overflow-y: scroll;
		img {
			width: 100%;
			object-fit: cover;
		}
	}
}
.product {
	padding: 15px;
	&__vedio {
		margin: -15px -15px 15px;
	}
	&__title {
		color: #172b4d;
		font-weight: 500;
		font-size: 15px;
	}
	&__desc {
		color: #6b778c;
		font-weight: 400;
		font-size: 10px;
		margin-bottom: 15px;
	}
	&__author {
		color: #172b4d;
		font-weight: 400;
		font-size: 12px;
	}
	&__time {
		color: #172b4d;
		font-weight: 400;
		font-size: 12px;
	}
	&__content {
		margin-top: 15px;
		line-height: 1.5;
	}
}
</style>
