<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" :style="{ visibility: route?.query?.isHeaderBackShow === 'false' ? 'hidden' : '' }" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">
				<span v-if="isReach" @click="changeTab('0')" :class="{ span_active: isTabBar === '0' }">{{ $t('manage.reach') }}</span>
				<span v-if="isMetrics" @click="changeTab('1')" :class="{ span_active: isTabBar === '1' }">{{ $t('manage.metrics') }}</span>
			</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<router-view />
		</div>
	</div>
</template>

<script setup>
const router = useRouter();
const route = useRoute();
const isTabBar = ref('0');
onMounted(() => {
	console.log(route);
	document.querySelector('body').style.backgroundColor = '#f4f5f7';
	route.name === 'reach' ? (isTabBar.value = '0') : (isTabBar.value = '1');
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.querySelector('body').removeAttribute('style');
});
const changeTab = (num) => {
	if (isTabBar.value === num) return;
	isTabBar.value = num;
	if (num === '0') {
		router.replace({
			name: 'reach',
			query: {
				isTabBar: route.query.isTabBar || '',
				...route.query,
			},
		});
	} else {
		router.replace({
			name: 'metrics',
			query: {
				isTabBar: route.query.isTabBar || '',
				...route.query,
			},
		});
	}
};
// 返回
const goBack = () => {
	router.go(-1);
};
const isReach = ref(false);
const isMetrics = ref(false);

const list = router.getRoutes();
list.forEach((item) => {
	if (item.name === 'reach') {
		isReach.value = true;
	} else if (item.name === 'metrics') {
		isMetrics.value = true;
	}
});
</script>

<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	color: #172b4d;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		background-color: #ffffff;
		// border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			span {
				font-size: 18px;
				color: #6b778c;
			}
			span.span_active {
				color: #172b4d;
				font-weight: 600;
			}
			span:nth-child(1) {
				margin-right: 7px;
			}
			span:nth-child(2) {
				margin-left: 7px;
			}
		}
	}
	.mc-content {
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		&:deep(.van-tab) {
			font-size: 15px;
			color: #6b778c;
		}
		&:deep(.van-tab--active) {
			color: #172b4d;
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
