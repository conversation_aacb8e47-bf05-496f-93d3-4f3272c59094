<template>
	<div class="my">
		<div>
			<div class="list" v-if="loading">
				<van-skeleton title :row="3" />
				<van-skeleton title :row="3" />
				<van-skeleton title :row="3" />
				<van-skeleton title :row="3" />
				<van-skeleton title :row="3" />
				<van-skeleton title :row="3" />
			</div>
			<div v-else>
				<div class="my-info">
					<div class="my-info-time">
						<span> {{ formatDate(new Date()).slice(0, 10).replace('-', '年').replace('-', '月') + '日' }} </span>&ensp;
						<span>{{ getTimePeriod(new Date()) }}好！</span>
					</div>
				</div>
				<div class="card" v-for="(item, index) in listData" :key="index">
					<component :is="com(item)" :startTime="item.startTime" :endTime="item.endTime" :dataSyncTime="item.dataSyncTime"></component>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { myList } from '@/api/my';
import { formatDate, getTimePeriod } from '@/utils/index';
import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
let { com } = useMyCard();
const loading = ref(true);
let listData = ref([]);
let getDate = async (role) => {
	let res = await myList({ role });
	// 过滤掉reportCd = myVisitAnalysis 的数据
	res.result = res.result.filter((item) => item.reportCd !== 'myVisitAnalysis');
	listData.value = res.result;
	loading.value = false;
};
onMounted(async () => {
	let roles = ['ADMIN', 'BUD', 'RSD', 'RSM', 'DSM', 'MR'];
	let role = userStore.permission.find((ele) => roles.includes(ele));
	getDate(role);
});
</script>
<style scoped lang="scss">
.my {
	padding: 15px;
	.list {
		border-radius: 5px;
		background-color: #ffffff;
	}
	&-info {
		background-color: #ffffff;
		border-top-left-radius: 5px;
		border-top-right-radius: 5px;
		padding: 12px 12px 0 12px;
		&-time {
			color: #172b4d;
			font-weight: bold;
			font-size: 15px;
		}
	}
	.card {
		margin-bottom: 15px;
		&:deep(.my-info-desc) {
			border-bottom-left-radius: 5px;
			border-bottom-right-radius: 5px;
			background-color: #ffffff;
			padding: 8px 12px 12px;
			margin-bottom: 15px;
			margin-top: 0;
		}

		&:deep(.my-card) {
			background-color: #ffffff;
			margin-top: 0;
		}
	}
}
</style>
