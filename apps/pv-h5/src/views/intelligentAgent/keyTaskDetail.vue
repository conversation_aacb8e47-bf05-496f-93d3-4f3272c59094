<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ route.query.taskType || '任务' }}</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<div class="mc-content-item">
				<div class="item-title">
					<span>基本信息</span>
				</div>
				<div class="item-list">
					<div class="item-list-left">
						<span>产品：</span>
					</div>
					<div @click="choiceProduct" class="item-list-right">
						<van-field v-model="query.productNameList" readonly placeholder="请选择..." />
						<img src="@/assets/img/right.png" />
					</div>
				</div>
				<div class="item-list">
					<div class="item-list-left">
						<span style="color: red">*</span>
						<span>客户：</span>
					</div>
					<div @click="choiceCustomer" class="item-list-right">
						<van-field v-model="query.currencyName" readonly placeholder="请选择..." />
						<img src="@/assets/img/right.png" />
					</div>
				</div>
				<div class="item-list">
					<div class="item-list-left">
						<span style="color: red">*</span>
						<span>负责人：</span>
					</div>
					<div @click="choicePerson" class="item-list-right">
						<van-field v-model="query.recipientName" readonly placeholder="请选择..." />
						<img v-if="route?.query?.isNotCharge !== 'noChange'" src="@/assets/img/right.png" />
					</div>
				</div>
				<div class="item-list">
					<div class="item-list-left">
						<span style="color: red">*</span>
						<span>日期：</span>
					</div>
					<div @click="choiceTime" class="item-list-right">
						<van-field v-model="query.endTime" readonly placeholder="请选择..." />
						<img src="@/assets/img/right.png" />
					</div>
				</div>
			</div>
			<div class="mc-content-item">
				<div class="item-title">
					<span style="color: red">*</span>
					<span>任务描述</span>
				</div>
				<van-field :readonly="taskStatus !== 'PROGRESS' && taskStatus !== ''" v-model="query.taskDescription" rows="4" type="textarea" placeholder="请输入..." />
			</div>
			<div v-if="isCreateTask !== 'create'" class="mc-content-item">
				<div class="item-title">
					<span>任务执行</span>
				</div>
				<van-field v-model="query.remark" :readonly="taskStatus !== 'PROGRESS' && taskStatus !== ''" rows="4" type="textarea" placeholder="请输入..." />
			</div>
			<div v-if="isCreateTask !== 'create'" class="mc-content-item">
				<div class="item-title">
					<span>历史</span>
				</div>
				<van-steps direction="vertical" :active="-1">
					<van-step v-for="item in taskLogList" :key="item.id">
						<h3>{{ item.dateStr }}</h3>
						<p>{{ item.log_content }}</p>
					</van-step>
				</van-steps>
			</div>
			<div v-if="(taskStatus === '' || taskStatus === 'PROGRESS') && query.taskType !== 'MEETING_INVITATION'" class="mc-content-bottom">
				<van-button @click="goBack" class="textColor" color="rgb(231, 237, 248)">取消</van-button>
				<van-button @click="confirm" color="#0052cc">确认</van-button>
			</div>
		</div>

		<!-- 同事列表组件 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="showForward" position="bottom" :style="{ height: '90%' }">
			<team-list @close="showForward = false" @activeTask="activeTask" :list="teammateList"></team-list>
		</van-popup>

		<van-popup :safe-area-inset-bottom="true" position="bottom" v-model:show="timeShow">
			<van-date-picker @cancel="timeShow = false" @confirm="onConfirm" v-model="currentDate" title="选择日期" :min-date="minDate" :max-date="maxDate" />
		</van-popup>

		<!-- 更多医生 弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="docMoreVis" position="bottom" :style="{ height: '90%' }">
			<ai-more-doctors @close="docMoreVis = false" @activeDoc="activeDoc"></ai-more-doctors>
		</van-popup>

		<!-- 产品弹框 -->
		<OnelevelCheckbox ref="productVis" :activeData="query.productIdList" :isHas="false" :list="productList" path="任务详情-选择产品" @_confirm="brandConfirm"></OnelevelCheckbox>
	</div>
</template>
<script setup>
import { taskCreate, taskUpdateInfo, taskInfoLog, findProductAll, colleagueList } from '@/api/task';
import { showToast, showLoadingToast } from 'vant';
import dayjs from 'dayjs';
import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
const userId = userStore.userInfo.id;
const router = useRouter();
const route = useRoute();

const currentDate = ref([dayjs().format('YYYY'), dayjs().format('MM'), dayjs().format('DD')]);
const minDate = ref(new Date(2020, 0, 1));
const maxDate = ref(new Date(2030, 5, 1));

// 任务id
const taskId = ref('');
// 任务状态
const taskStatus = ref('');
// 是否新建
const isCreateTask = ref('');
const query = reactive({
	taskRemindTimeType: null,
	times: '',
	taskType: '',
	taskDescription: '',
	taskCreateTimeType: '',
	recipientId: '',
	recipientName: '',
	endTime: '',
	currencyId: '',
	currencyName: '',
	productIdList: [],
	productNameList: '',
	remark: '',
});

const init = () => {
	isCreateTask.value = route.query.isCreateTask;
	if (isCreateTask.value === 'create') {
		// 新建任务
		query.taskType = route.query.taskType === '电话拜访' ? 'PHONE_VISIT' : route.query.taskType === 'Wecom拜访' ? 'WECHAT_VISIT' : 'OFFLINE_VISIT';
		if (route.query.source === 'notes') {
			query.taskDescription = route.query.taskDescription || '';
			query.currencyId = route.query.currencyId || '';
			query.currencyName = route.query.currencyName || '';
			query.recipientId = route.query.recipientId;
			query.recipientName = route.query.recipientName;
			if (route.query.endTime) {
				query.endTime = route.query.endTime.split('T')[0];
				const day = route.query.endTime.split('T')[0];
				if (day === dayjs(Date.now()).format('YYYY-MM-DD')) {
					query.taskCreateTimeType = 'TODAY';
				} else if (day === dayjs().add(1, 'day').format('YYYY-MM-DD')) {
					query.taskCreateTimeType = 'TOMORROW';
				} else {
					query.taskCreateTimeType = 'OTHER';
				}
			}
		}
	} else {
		// 查看任务
		const taskContent = JSON.parse(decodeURIComponent(route.query.taskContent));
		taskId.value = taskContent.id;
		taskStatus.value = taskContent.taskInfoStatusType;
		query.taskType = taskContent.taskInfoType;
		query.taskDescription = taskContent.taskDescription;
		query.recipientId = taskContent.recipientId;
		query.recipientName = taskContent.recipientName;
		query.endTime = taskContent.endTime.split('T')[0];
		if (taskContent.taskInfoType === 'MEETING_INVITATION') {
			query.currencyName = taskContent.doctorName;
		} else {
			query.currencyId = taskContent.doctorVO.doctorId;
			query.currencyName = taskContent.doctorVO.doctorName;
		}
		query.remark = taskContent.remark || '';
		const day = taskContent.endTime.split('T')[0];
		if (day === dayjs(Date.now()).format('YYYY-MM-DD')) {
			query.taskCreateTimeType = 'TODAY';
		} else if (day === dayjs().add(1, 'day').format('YYYY-MM-DD')) {
			query.taskCreateTimeType = 'TOMORROW';
		} else {
			query.taskCreateTimeType = 'OTHER';
		}
		if (taskContent.productDomainList.length > 0) {
			for (const item of taskContent.productDomainList) {
				query.productIdList.push(item.id);
				query.productNameList += item.name + ',';
			}
			query.productNameList = query.productNameList.substring(0, query.productNameList.length - 1);
		}
		// 任务流转历史
		getTaskLog();
	}
	if (taskStatus.value === '' || taskStatus.value === 'PROGRESS') {
		// 产品列表
		getProductList();
		// 同事列表
		getTeammateList();
	}
};

// 任务流转历史
const taskLogList = ref([]);
const getTaskLog = () => {
	taskInfoLog({ page: 0, size: 1000 }, taskId.value).then((res) => {
		if (res.result.content.length > 0) {
			for (const iterator of res.result.content) {
				if (iterator.dateStr.length > 19) {
					iterator.dateStr = iterator.dateStr.substring(0, 19);
				}
			}
			taskLogList.value = res.result.content;
		}
	});
};

// 选择产品
const productVis = ref(null);
const productList = ref([]);
let pl = [];
const getProductList = () => {
	findProductAll().then((res) => {
		pl = res.result || [];
	});
};
const choiceProduct = () => {
	if (taskStatus.value === '' || taskStatus.value === 'PROGRESS') {
		if (productList.value.length === 0) {
			for (const item of pl) {
				productList.value.push({ id: item.id, name: item.name });
			}
		}
		productVis.value.show = true;
	}
};
const brandConfirm = (val) => {
	if (val.length > 0) {
		query.productIdList = val;
		query.productNameList = '';
		for (const item of val) {
			for (const iterator of pl) {
				if (item === iterator.id) {
					query.productNameList += iterator.name + ',';
				}
			}
		}
		query.productNameList = query.productNameList.substring(0, query.productNameList.length - 1);
	} else {
		query.productIdList = [];
		query.productNameList = '';
	}
};

// 选择客户
const docMoreVis = ref(false);
const choiceCustomer = () => {
	if (taskStatus.value === '' || taskStatus.value === 'PROGRESS') {
		docMoreVis.value = true;
	}
};
const activeDoc = (val) => {
	query.currencyId = val.doctorId;
	query.currencyName = val.doctorName;
	docMoreVis.value = false;
};

// 选择负责人
const showForward = ref(false);
const teammateList = ref([]);
const getTeammateList = () => {
	colleagueList(userId, { isBrief: false }).then((res) => {
		if (res.result && res.result.length > 0) {
			res.result = res.result.reduce((acc, cur) => {
				const hasDuplicate = acc.some((item) => item.id === cur.id);
				if (!hasDuplicate) {
					acc.push(cur);
				}
				return acc;
			}, []);
		}
		teammateList.value = res.result || [];
	});
};
const choicePerson = () => {
	if (route.query.isNotCharge !== 'noChange') {
		if (taskStatus.value === '') {
			showForward.value = true;
		}
	}
};
const activeTask = (val) => {
	query.recipientId = val;
	for (const item of teammateList.value) {
		if (item.id === val) {
			query.recipientName = item.firstName;
			break;
		}
	}
};

// 选择截止时间
const timeShow = ref(false);
const choiceTime = () => {
	if (taskStatus.value === '' || taskStatus.value === 'PROGRESS') {
		timeShow.value = true;
	}
};
const onConfirm = (val) => {
	timeShow.value = false;
	const day = val.selectedValues.join('-');
	const cDay = dayjs(dayjs().format('YYYY-MM-DD'));
	// 比较day和cDay的大小
	if (dayjs(day).isBefore(cDay)) {
		showToast({
			position: 'top',
			message: '截止时间不能小于今天',
		});
	} else if (dayjs(day).isAfter(cDay)) {
		query.endTime = day;

		if (dayjs(day).diff(cDay, 'day') === 1) {
			query.taskCreateTimeType = 'TOMORROW';
		} else {
			query.taskCreateTimeType = 'OTHER';
		}
	} else {
		query.endTime = day;
		query.taskCreateTimeType = 'TODAY';
	}
};
// 返回&&取消
const goBack = () => {
	router.go(-1);
};

const confirm = () => {
	// 校验客户
	if (!query.currencyId) {
		showToast({
			position: 'top',
			message: '请选择客户',
		});
		return;
	}
	// 校验负责人
	if (!query.recipientId) {
		showToast({
			position: 'top',
			message: '请选择任务负责人',
		});
		return;
	}
	// 校验日期
	if (!query.endTime) {
		showToast({
			position: 'top',
			message: '请选择任务日期',
		});
		return;
	}
	// 校验任务描述
	if (!query.taskDescription) {
		showToast({
			position: 'top',
			message: '请输入任务描述',
		});
		return;
	}
	const json = {
		times: query.times,
		taskRemindTimeType: query.taskRemindTimeType,
		productIdList: query.productIdList,
		endTime: query.endTime + 'T23:30:00',
		taskCreateTimeType: query.taskCreateTimeType,
		taskType: query.taskType,
		recipientId: query.recipientId,
		taskDescription: query.taskDescription,
		currencyId: query.currencyId,
	};
	// 判断是编辑任务还是新建任务
	if (isCreateTask.value === 'create') {
		showLoadingToast({
			message: '任务创建中...',
			forbidClick: true,
			duration: 0,
		});
		taskCreate(json).then((res) => {
			showToast({
				position: 'top',
				message: '任务创建成功',
			});
			if (route?.query?.sourceType === 'mobilePhone') {
				location.replace(location.origin + '/mobile/offlineVisitPlan');
			} else {
				goBack();
			}
		});
	} else {
		showLoadingToast({
			message: '任务修改中...',
			forbidClick: true,
			duration: 0,
		});
		json.remark = query.remark || '';
		json.taskInfoStatusType = taskStatus.value;
		taskUpdateInfo(json, taskId.value).then((res) => {
			showToast({
				position: 'top',
				message: '任务修改成功',
			});
			if (route?.query?.sourceType === 'mobilePhone') {
				location.replace(location.origin + '/mobile/offlineVisitPlan');
			} else {
				goBack();
			}
		});
	}
};

onMounted(() => {
	document.querySelector('body').style.backgroundColor = '#f4f5f7';
	init();
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.querySelector('body').removeAttribute('style');
});
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	color: #172b4d;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		background-color: #ffffff;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #172b4d;
			font-weight: 600;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		padding: 8px 15px;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;

		.mc-content-item {
			background-color: #ffffff;
			border-radius: 6px;
			padding: 12px;
			display: flex;
			flex-direction: column;
			margin-bottom: 8px;
			.item-title {
				display: flex;
				align-items: center;
				margin-bottom: 8px;
				span {
					font-size: 16px;
					font-weight: 600;
				}
			}

			.item-list {
				display: flex;
				align-items: center;
				margin-bottom: 10px;
				.item-list-left {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					width: 65px;
				}
				.item-list-right {
					flex: 1;
					position: relative;
					img {
						position: absolute;
						right: 10px;
						top: 50%;
						width: 4px;
						height: 8px;
						margin-top: -4px;
					}
					&:deep(.van-field) {
						padding: 6px 18px 6px 8px;
					}
				}
			}

			&:deep(.van-field) {
				background-color: #f4f5f7;
				border-radius: 6px;
			}

			&:deep(.van-step__circle) {
				background-color: #0052cc;
			}

			&:deep(.van-step__title) {
				h3 {
					color: #6b778c;
					font-size: 10px;
				}
				p {
					background-color: #f4f5f7;
					border-radius: 6px;
					padding: 8px;
					font-size: 12px;
				}
			}
		}
		.mc-content-bottom {
			display: flex;
			align-items: center;
			margin-top: 20px;
			margin-bottom: 20px;
			justify-content: space-between;
			.van-button {
				height: 40px;
				width: 169px;
			}

			&:deep(.textColor) {
				.van-button__text {
					color: #0052cc;
				}
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
}
</style>
