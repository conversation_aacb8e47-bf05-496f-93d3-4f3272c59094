<template>
	<div class="ma">
		<!-- header -->
		<div class="ma-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">会议邀请</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- content -->
		<div class="ma-content">
			<img src="@/assets/img/demo/toMeet.jpg" />
		</div>
	</div>
</template>
<script setup>
const router = useRouter();
const goBack = () => {
	router.go(-1);
};
</script>
<style lang="scss" scoped>
.ma {
	height: 100vh;
	width: 100%;
	display: flex;
	flex-direction: column;
	.ma-header {
		width: 100vw;
		display: flex;
		height: 50px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid rgb(223, 225, 230);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
		}
	}

	.ma-content {
		height: calc(100vh - 44px);
		overflow-y: scroll;
		img {
			width: 98%;
			margin-left: 1%;
			object-fit: cover;
		}
	}
}
</style>
