<template>
	<div class="plan">
		<nav-bar-back>
			<!-- 默认插槽 -->
			<span>制定拜访计划</span>
		</nav-bar-back>
		<!-- <div class="plan-title">建议您对以下医生制定拜访计划</div> -->
		<div class="plan-doc">
			<div class="plan-doc-name" @click="docMoreVis = true">
				<div class="left">
					<img v-if="selectedDoctor.doctorSex === '女'" src="@/assets/img/tx1.png" alt="" />
					<img v-else src="@/assets/img/tx2.png" alt="" />
				</div>
				<div v-if="Object.keys(selectedDoctor).length > 0" class="right">
					<div>
						<span>{{ selectedDoctor.doctorName }}</span>
						<span>{{ selectedDoctor.doctorTitle }}</span>
					</div>
					<div>{{ selectedDoctor.hospital }}</div>
				</div>
				<div v-else class="qxz" style="padding-left: 2.75vw; display: flex; align-items: center">请选择</div>

				<img src="@/assets/img/exchange.png" alt="" />
			</div>
			<div class="plan-doc-option" @click="bflx.showPicker = true">
				<div class="left">拜访类型：</div>
				<div class="right">{{ formData.visitType }}</div>
				<img src="@/assets/img/right.png" alt="" />
			</div>
			<div class="plan-doc-option" @click="popupShow = true">
				<div class="left">拜访时间：</div>
				<div class="right">{{ formData.visitTime }}</div>
				<img src="@/assets/img/right.png" alt="" />
			</div>
			<div class="plan-doc-option" @click="bfmd.showPicker = true">
				<div class="left">拜访目的：</div>
				<div class="right">{{ formData.visitPurpose }}</div>
				<img src="@/assets/img/right.png" alt="" />
			</div>
			<div class="plan-doc-option" @click="bfcp.showPicker = true">
				<div class="left">拜访产品：</div>
				<div class="right">{{ formData.product }}</div>
				<img src="@/assets/img/right.png" alt="" />
			</div>
			<div class="plan-doc-option" @click="bfyy.showPicker = true">
				<div class="left">拜访原因：</div>
				<div class="right">{{ formData.visitReason }}</div>
				<img src="@/assets/img/right.png" alt="" />
			</div>
		</div>

		<div class="plan-btn" @click="$router.go(-1)">制定</div>

		<!-- 拜访类型 -->
		<selector ref="bflx" title="拜访类型" :columns="selectOption.visitTypeList" @selectConfirm="visitTypeConfirm"></selector>
		<!-- 拜访类型 -->
		<selector ref="bfmd" title="拜访目的" :columns="selectOption.visitPurpose" @selectConfirm="visitPurposeConfirm"></selector>
		<!-- 拜访产品 -->
		<selector ref="bfcp" title="拜访产品" :columns="selectOption.productList" @selectConfirm="productConfirm"></selector>
		<!-- 拜访原因 -->
		<selector ref="bfyy" title="拜访原因" :columns="selectOption.visitReason" @selectConfirm="visitReasonConfirm"></selector>
		<!-- 拜访时间 -->
		<van-popup v-model:show="popupShow" position="bottom" closeable>
			<div class="title"><span>拜访时间</span></div>
			<div class="flex_box">
				<van-date-picker v-model="dateData.currentDate" :min-date="dateData.minDate" :show-toolbar="false" :formatter="formatterDate" swipe-duration="250" />
				<van-time-picker v-model="dateData.currentTime" :show-toolbar="false" :formatter="formatterDate" :max-minute="dateData.maxMinute" swipe-duration="250" />
			</div>
			<div class="operation">
				<div @click="popupShow = false">取消</div>
				<div @click="visitTimeConfirm">确定</div>
			</div>
		</van-popup>

		<!-- 更多医生 弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="docMoreVis" position="bottom" :style="{ height: '90%' }">
			<ai-more-doctors @close="docMoreVis = false" @activeDoc="activeDoc"></ai-more-doctors>
		</van-popup>
	</div>
</template>
<script setup>
const formData = reactive({
	visitType: '请选择',
	visitTime: '请选择',
	visitPurpose: '请选择',
	product: '请选择',
	visitReason: '请选择',
});
//筛选项列表
const selectOption = reactive({
	visitTypeList: [
		{
			text: '电话拜访',
			value: '电话拜访',
		},
		{
			text: '微信拜访',
			value: '微信拜访',
		},
		{
			text: '线上会议',
			value: '线上会议',
		},
		{
			text: '线下拜访',
			value: '线下拜访',
		},
		{
			text: '内容分享',
			value: '内容分享',
		},
		{
			text: '会议邀请',
			value: '会议邀请',
		},
	],
	visitPurpose: [
		{
			value: '首次联系，邀请加入项目',
			text: '首次联系，邀请加入项目',
		},
		{
			value: '有计划跟进之前覆盖',
			text: '有计划跟进之前覆盖',
		},
		{
			value: '医生潜力探寻',
			text: '医生潜力探寻',
		},
		{
			value: '医生观念探寻',
			text: '医生观念探寻',
		},
		{
			value: '了解医生随访服务需求',
			text: '了解医生随访服务需求',
		},
		{
			value: '关注医生职业规划和需求',
			text: '关注医生职业规划和需求',
		},
	],
	productList: [
		{
			text: '维A酸',
			value: '维A酸',
		},
		{
			text: '替加氟',
			value: '替加氟',
		},
		{
			text: '卡铂',
			value: '卡铂',
		},
		{
			text: '白消安',
			value: '白消安',
		},
		{
			text: '美司钠',
			value: '美司钠',
		},
		{
			text: '来曲唑片',
			value: '来曲唑片',
		},
		{
			text: '布洛芬片',
			value: '布洛芬片',
		},
		{
			text: '双氯芬酸钠缓释胶囊',
			value: '双氯芬酸钠缓释胶囊',
		},
		{
			text: '吲哚美辛栓',
			value: '吲哚美辛栓',
		},
		{
			text: '雷公藤多苷',
			value: '雷公藤多苷',
		},
		{
			text: '心血管产品',
			value: '心血管产品',
		},
		{
			text: '肺癌产品',
			value: '肺癌产品',
		},
		{
			text: '精神科产品',
			value: '精神科产品',
		},
	],
	visitReason: [
		{ text: '近期发表论文', value: '近期发表论文' },
		{ text: '有计划跟进之前覆盖', value: '有计划跟进之前覆盖' },
		{ text: '连续两周末拜访', value: '连续两周末拜访' },
	],
});
const bflx = ref(null);
const visitTypeConfirm = (value) => {
	formData.visitType = value;
};
const bfmd = ref(null);
const visitPurposeConfirm = (value) => {
	formData.visitPurpose = value;
};
const bfcp = ref(null);
const productConfirm = (value) => {
	formData.product = value;
};
const bfyy = ref(null);
const visitReasonConfirm = (value) => {
	formData.visitReason = value;
};
const popupShow = ref(false);
const dateData = reactive({
	currentDate: formattedDate(new Date()).split('-'),
	minDate: new Date(2020, 0, 1),
	maxDate: new Date(),
	currentTime: formattedTime(),
	maxHour: '',
	maxMinute: '',
});
dateData.maxHour = new Date().getHours();
//拜访时间确认
const visitTimeConfirm = () => {
	formData.visitTime = `${dateData.currentDate.join('-')} ${dateData.currentTime.join(':')}`;
	popupShow.value = false;
};
function formattedTime() {
	let now = new Date();
	let hours = now.getHours().toString().padStart(2, '0');
	let minutes = now.getMinutes().toString().padStart(2, '0');
	return [hours, minutes];
}
function formattedDate(date) {
	if (!date) return '';
	const today = date;
	const year = today.getFullYear();
	const month = String(today.getMonth() + 1).padStart(2, '0');
	const day = String(today.getDate()).padStart(2, '0');
	return `${year}-${month}-${day}`;
}
//格式化日期
const formatterDate = (type, option) => {
	if (type === 'year') {
		option.text += '年';
	}
	if (type === 'month') {
		option.text += '月';
	}
	if (type === 'day') {
		option.text += '日';
	}
	if (type === 'hour') {
		option.text += '时';
	}
	if (type === 'minute') {
		option.text += '分';
	}
	return option;
};
const docMoreVis = ref(false);
const selectedDoctor = ref({});
const activeDoc = (value) => {
	console.log(value);
	selectedDoctor.value = value;
};
</script>
<style lang="scss" scoped>
.plan {
	margin: 46px 15px;
	overflow: hidden;
	&-title {
		color: #172b4d;
		font-weight: bold;
		font-size: 14px;
		margin-top: 15px;
		margin-bottom: 15px;
	}
	&-doc {
		border-radius: 5px;
		background-color: #f4f5f7;
		padding: 8px;
		margin-bottom: 10px;
		&-name {
			border-radius: 5px;
			background-color: #ffffff;
			padding: 12px;
			display: flex;
			position: relative;
			.left {
				width: 40px;
				height: 40px;
				margin-right: 5px;
				img {
					width: 100%;
				}
			}
			.right {
				div:nth-child(1) {
					color: #172b4d;
					font-size: 15px;
					span:nth-last-child(1) {
						font-size: 12px;
						margin-left: 4px;
					}
				}
				div:nth-child(2) {
					color: #6b778c;
					font-size: 12px;
					margin-top: 2px;
				}
			}
			& > img {
				width: 23px;
				position: absolute;
				right: 17px;
				top: 50%;
				transform: translateY(-50%);
			}
		}
		&-option {
			margin-top: 5px;
			display: flex;
			align-items: center;
			position: relative;
			.left {
				color: #172b4d;
				font-weight: bold;
				font-size: 14px;
			}
			.right {
				flex: 1;
				border-radius: 5px;
				background-color: #ffffff;
				margin-left: 8px;
				color: #172b4d;
				font-size: 14px;
				padding: 10px 12px;
			}
			img {
				width: 4px;
				position: absolute;
				right: 13px;
			}
		}
	}
	&-btn {
		margin: 0 auto;
		width: 250px;
		height: 40px;
		line-height: 40px;
		border-radius: 25px;
		background-color: #0052cc;
		color: #ffffff;
		font-size: 16px;
		text-align: center;
		margin-top: 44px;
	}
	::v-deep(.van-popup) {
		border-radius: 5px;
		.van-popup__close-icon {
			font-size: 19px;
		}
		.title {
			height: initial;
			color: #353639;
			font-weight: bold;
			font-size: 14px;
			padding: 20px 16px;
			span {
				padding-left: 4px;
			}
		}
		.flex_box {
			display: flex;
			.van-picker:nth-child(1) {
				flex: 2;
			}
			.van-picker:nth-child(2) {
				flex: 1;
			}
			.van-picker-column {
				font-size: 15px;
			}
		}
		.operation {
			padding: 16px 16px 24px;
			display: flex;
			border: 1px solid rgba(151, 151, 151, 0.1);
			box-shadow: 0px 2px 20px 0px rgba(95, 95, 95, 0.2);
			background-color: #ffffff;
			div {
				flex: 1;
				text-align: center;
				padding: 10px;
				border-radius: 5px;
				font-size: 15px;
			}
			div:nth-child(1) {
				color: #0052cc;
				border: 1px solid #0052cc;
				margin-right: 11px;
			}
			div:nth-child(2) {
				background-color: #0052cc;
				border: 1px solid #0052cc;
				color: #ffffff;
			}
		}
	}
}
</style>
