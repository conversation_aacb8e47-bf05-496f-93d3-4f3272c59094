<template>
	<div class="mc">
		<div class="task-item">
			<div class="task-item__header">
				<div class="task-item__header-left">
					<span class="left-icon"></span>
					<span>销售指标</span>
				</div>
				<div class="task-item__header-right">
					<span v-if="zk" @click="zk = !zk" style="color: #0052cc">收起明细</span>
					<span v-else @click="zk = !zk" style="color: #0052cc">展开明细</span>
				</div>
			</div>
			<div class="task-item__body">
				<img class="mc-img-nodata" src="@/assets/img/noData.png" />
				<span class="mc-nodata-text">暂无数据</span>
				<!-- <span>您的医院销量指标{{ formatNumberWan(qInfo.total) }}， </span>
				<div style="word-break: break-all">
					<span v-for="(item, index) in qInfo.products" :key="index">
						<span v-if="index === 0">其中</span>
						<span>{{ item.name }}</span>
						<span>{{ formatNumberWan(item.total) }}</span>
						<span v-if="index !== qInfo.products.length - 1">，</span>
					</span>
				</div> -->
			</div>
			<!-- 明细 -->
			<div v-if="zk" class="task-item__header task-item-title">
				<div class="task-item__header-left">
					<span class="left-icon"></span>
					<span>产品指标额</span>
				</div>
			</div>
			<div v-if="zk" class="task-item__detail">
				<img class="mc-img-nodata" src="@/assets/img/noData.png" />
				<span class="mc-nodata-text">暂无数据</span>
				<!-- <div class="detail-item">
					<div class="item-one item-lines item-bg">
						<span class="item-lines__product">产品</span>
						<span class="item-lines__month">月</span>
					</div>
					<div class="item-two" style="text-align: center" v-for="(item, index) in myAgentInfo.products" :key="index">{{ item.name }}</div>
				</div>
				<div class="detail-item" v-for="month in myAgentInfo.months" :key="month">
					<div class="item-one">{{ month }}</div>
					<div v-for="product in myAgentInfo.products" :key="product.name" class="item-two item-bg">{{ formatNumber(product.values[month]) }}</div>
				</div> -->
			</div>
		</div>

		<div class="task-item">
			<div class="task-item__header">
				<div class="task-item__header-left">
					<span class="left-icon"></span>
					<span>过程指标</span>
				</div>
			</div>
			<div class="task-item__body">
				<!-- <span>A级客户：覆盖率100%/月，拜访次数：8次/月 </span>
				<span>B级客户：覆盖率100%/月，拜访次数：4次/月 </span>
				<span>C级客户：覆盖率50%/月，拜访次数：2次/月</span>
				<span>拜访医生数：拜访10名医生/日</span>
				<span>科室会频率：一场科室会/周</span> -->
				<img class="mc-img-nodata" src="@/assets/img/noData.png" />
				<span class="mc-nodata-text">暂无数据</span>
			</div>
		</div>
		<div class="task-item">
			<div class="task-item__header">
				<div class="task-item__header-left">
					<span class="left-icon"></span>
					<span>财务指标</span>
				</div>
			</div>
			<div class="task-item__body">
				<!-- <span>报销周期不超过45天</span> -->
				<img class="mc-img-nodata" src="@/assets/img/noData.png" />
				<span class="mc-nodata-text">暂无数据</span>
			</div>
		</div>

		<div style="margin-bottom: 20px" class="task-item">
			<div class="task-item__header">
				<div class="task-item__header-left">
					<span class="left-icon"></span>
					<span>HR指标</span>
				</div>
			</div>
			<div class="task-item__body">
				<!-- <span>人均产出150万</span> -->
				<img class="mc-img-nodata" src="@/assets/img/noData.png" />
				<span class="mc-nodata-text">暂无数据</span>
			</div>
		</div>
	</div>
</template>

<script setup>
import { queryList, getMyAgent } from '@/api/sales';
import { formatNumberWan, formatNumber } from '@/utils/index';

const zk = ref(false);

function getQuarterFromDate(dateStr) {
	// 将日期字符串转换为日期对象
	const date = new Date(dateStr);
	// 获取年份
	const year = date.getFullYear();
	// 获取月份，注意 JavaScript 中的月份是从 0 开始的
	const month = date.getMonth() + 1;
	// 计算季度
	const quarter = Math.ceil(month / 3);
	// 返回格式化的季度字符串
	return `${year}Q${quarter}`;
}

function getQuarterDates(dateStr) {
	// 将日期字符串转换为日期对象
	const date = new Date(dateStr);

	// 获取年份
	const year = date.getFullYear();

	// 获取月份，注意 JavaScript 中的月份是从 0 开始的
	const month = date.getMonth() + 1;

	// 计算季度
	const quarter = Math.ceil(month / 3);

	// 计算季度开始日期和结束日期
	let startMonth, endMonth, startDay, endDay;
	switch (quarter) {
		case 1:
			startMonth = 1;
			endMonth = 3;
			startDay = '01';
			endDay = '31';
			break;
		case 2:
			startMonth = 4;
			endMonth = 6;
			startDay = '01';
			endDay = '30';
			break;
		case 3:
			startMonth = 7;
			endMonth = 9;
			startDay = '01';
			endDay = '30';
			break;
		case 4:
			startMonth = 10;
			endMonth = 12;
			startDay = '01';
			endDay = '31';
			break;
	}

	// 格式化月份
	startMonth = startMonth.toString().padStart(2, '0');
	endMonth = endMonth.toString().padStart(2, '0');

	// 返回格式化的开始日期和结束日期
	return {
		startTime: `${year}-${startMonth}-${startDay}`,
		endTime: `${year}-${endMonth}-${endDay}`,
	};
}
let qTime = ref({});
let qInfo = ref({});

let defaultTIme = ref({});
let myAgentInfo = ref({});
onMounted(async () => {
	// let res = await queryList({ reportType: 'CARD', menuId: 'MyAgent' });
	// defaultTIme.value = { startTime: res.result[0].startTime, endTime: res.result[0].endTime, dataSyncTime: res.result[0].dataSyncTime };
	// qTime.value = getQuarterDates(res.result[0].dataSyncTime);
	// let [res1, res2] = await Promise.all([getMyAgent({ startLocalDate: defaultTIme.value.startTime, endLocalDate: defaultTIme.value.endTime }), getMyAgent({ startLocalDate: qTime.value.startTime, endLocalDate: qTime.value.endTime })]);
	// // 获取我的指标数据
	// myAgentInfo.value = res1.result;
	// //获取我的指标当季度数据
	// qInfo.value = res2.result;
	// console.log(res2);
});
</script>

<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	color: #172b4d;

	.task-item {
		border-radius: 6px;
		background-color: #ffffff;
		margin: 15px 15px 0;
		padding: 12px;
		display: flex;
		flex-direction: column;
		.task-item__header {
			padding-bottom: 12px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.task-item__header-left {
				display: flex;
				align-items: center;
				.left-icon {
					height: 12px;
					display: inline-block;
					width: 4px;
					background-color: #0052cc;
					margin-right: 6px;
				}
			}
		}

		.task-item__body {
			border-radius: 6px;
			// background-color: #f4f5f7;
			padding: 11px 8px;
			display: flex;
			flex-direction: column;
			font-size: 12px;
			justify-content: center;
			align-items: center;
		}
		.task-item-title {
			padding-bottom: 6px;
			margin-top: 15px;
		}
		.task-item__detail {
			font-size: 12px;
			color: #172b4d;
			// background-color: #f4f5f7;
			border-radius: 6px;
			// border-top: 1px solid #dfe1e6;
			// border-left: 1px solid #dfe1e6;

			display: flex;
			flex-direction: column;
			align-items: center;
			.detail-item {
				display: flex;
				align-items: center;
				div {
					height: 31px;
					display: flex;
					align-items: center;
					justify-content: center;
					border-bottom: 1px solid #dfe1e6;
					border-right: 1px solid #dfe1e6;
				}
				.item-one {
					flex-shrink: 0;
					width: 80px;
				}
				.item-two {
					flex-shrink: 0;
					width: 120px;
				}
				.item-three {
					flex: 2;
				}
				.item-bg {
					background-color: #ffffff;
				}
				.item-lines {
					background: linear-gradient(to top right, #ffffff, #ffffff 48%, #dfe1e6, #ffffff 51%, #ffffff);
					background: -webkit-linear-gradient(to top right, #ffffff, #ffffff 48%, #dfe1e6, #ffffff 51%, #ffffff);
					position: relative;

					.item-lines__product {
						position: absolute;
						font-size: 10px;
						right: 5px;
						top: 3px;
					}

					.item-lines__month {
						position: absolute;
						font-size: 10px;
						left: 14px;
						bottom: 3px;
					}
				}
			}
		}
	}

	.mc-img-nodata {
		width: 180px;
	}
	.mc-nodata-text {
		color: #6b778c;
	}
}
</style>
