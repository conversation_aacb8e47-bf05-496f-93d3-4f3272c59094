<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('manage.planTask') }}</div>
			<!-- 右侧icon -->
			<van-popover class="mc-header-popover" placement="bottom-end" v-model:show="showPopover" :actions="actions" @select="onSelect">
				<template #reference>
					<div class="mc-header-icon">{{ $t('task.create') }}</div>
				</template>
			</van-popover>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 任务类型切换 -->
			<van-tabs :swipe-threshold="3" color="#0052cc" @change="tabChange" v-model:active="active">
				<van-tab name="全部任务" :title="$t('task.all')"></van-tab>
				<van-tab name="待办" :title="$t('task.todo')"></van-tab>
				<van-tab name="已完成" :title="$t('task.completed')"></van-tab>
				<van-tab name="已过期" :title="$t('task.expired')"></van-tab>
			</van-tabs>
			<!-- 任务列表 -->
			<div v-if="loadingStatus === 'success'" class="mc-content_task">
				<!-- 任务 -->
				<van-list v-model:loading="loading" :finished="finished" :finished-text="$t('task.noMore')" :immediate-check="false" @load="onLoad">
					<div v-for="item in taskData" :key="item.id" class="task-item">
						<div class="task-item__header">
							<div class="task-item__header-left">
								<span class="left-icon"></span>
								<span>任务ID：{{ item.id }}</span>
								<van-tag class="left-tag" :color="item.taskInfoStatusType === 'PROGRESS' ? '#0052cc' : item.taskInfoStatusType === 'CLOSE' ? '#6B778C' : '#FF991F'" type="primary">{{
									item.taskInfoStatusType === 'PROGRESS' ? '处理中' : item.taskInfoStatusType === 'CLOSE' ? '已关闭' : '已完成'
								}}</van-tag>
							</div>
							<div v-if="item.taskInfoStatusType === 'PROGRESS'" class="task-item__header-right">
								<span @click="closeTask(item.id)">{{ $t('task.closeTask') }}</span>
							</div>
						</div>
						<div @click="goDetail(item)" class="task-item__body">
							<img class="task-item__body--img" src="@/assets/img/right.png" alt="" />
							<div class="body-item">
								<span>任务描述：</span>
								<span>{{ item.taskDescription }}</span>
							</div>

							<div class="body-item">
								<span>任务类型：</span>
								<span>{{ item.taskInfoType === 'PHONE_VISIT' ? '电话拜访' : item.taskInfoType === 'WECHAT_VISIT' ? 'Wecom拜访' : item.taskInfoType === 'OFFLINE_VISIT' ? '线下拜访' : '会议邀请' }}</span>
							</div>
							<div class="body-item">
								<span>客户：</span>
								<span>{{ item.doctorName }}</span>
							</div>
							<div v-if="item.hospital" class="body-item">
								<span>医院：</span>
								<span>{{ item.hospital }}</span>
							</div>
							<div class="body-item">
								<span>截止时间：</span>
								<span>{{ item.endTime ? item.endTime.split('T')[0] : '' }}</span>
							</div>
							<div class="body-item">
								<span>发起人：</span>
								<span>{{ item.creatorName }}</span>
							</div>
						</div>
						<div v-if="item.taskInfoStatusType === 'PROGRESS'" class="task-item__bottom">
							<van-button @click="forwardTaskDialog(item.id)" class="task-item__bottom_btn" size="small" color="#e7edf8">{{ $t('task.forward') }}</van-button>
							<van-button @click="executeTask(item)" size="small" color="#0052cc">{{ $t('task.excute') }}</van-button>
						</div>
					</div>
				</van-list>
			</div>
			<!-- 骨架屏 -->
			<div v-else-if="loadingStatus === 'loading'" class="mc-content__skeleton">
				<van-skeleton style="margin-bottom: 8px" title :row="3" />
				<van-skeleton style="margin-bottom: 8px" title :row="3" />
				<van-skeleton style="margin-bottom: 8px" title :row="3" />
				<van-skeleton style="margin-bottom: 8px" title :row="3" />
				<van-skeleton title :row="3" />
			</div>
			<!-- 空数据 -->
			<van-empty v-else description="暂无任务~" />
		</div>

		<!-- 同事列表组件 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="showForward" position="bottom" :style="{ height: '90%' }">
			<team-list @close="showForward = false" @activeTask="activeTask" :list="teammateList"></team-list>
		</van-popup>
	</div>
</template>
<script setup>
import { getTaskList, closeTaskInterface, colleagueList, taskTransferIn } from '@/api/task';
import { showConfirmDialog, showToast } from 'vant';
import useUserStore from '@/store/modules/user';
const userStore = useUserStore();
const userId = userStore.userInfo.id;
const showPopover = ref(false);
const actions = ref([{ text: '电话拜访' }, { text: 'Wecom拜访' }, { text: '线下拜访' }]);
// 新建任务
const onSelect = ({ text }) => {
	router.push({
		name: 'keyTaskDetail',
		query: {
			isTabBar: route.query.isTabBar || '',
			isCreateTask: 'create',
			taskType: text,
		},
	});
};
// 查看或者编辑任务
const goDetail = (item) => {
	router.push({
		name: 'keyTaskDetail',
		query: {
			isTabBar: route.query.isTabBar || '',
			isCreateTask: 'notCreate',
			taskContent: encodeURIComponent(JSON.stringify(item)),
			taskType: item.taskInfoType === 'PHONE_VISIT' ? '电话拜访' : item.taskInfoType === 'WECHAT_VISIT' ? 'Wecom拜访' : item.taskInfoType === 'OFFLINE_VISIT' ? '线下拜访' : '会议邀请',
		},
	});
};
// 关闭任务
const closeTask = (id) => {
	showConfirmDialog({
		title: '提示',
		message: '是否确认关闭该任务？',
		confirmButtonColor: '#0052CC',
	}).then(() => {
		closeTaskInterface({ remark: '' }, [id]).then(() => {
			showToast({
				position: 'top',
				message: '任务已关闭',
			});
			if (active.value === '全部') {
				for (const ite of taskData.value) {
					if (ite.id === id) {
						ite.taskInfoStatusType = 'CLOSE';
						break;
					}
				}
			} else {
				// 清除 taskData.value 里的id等于id的数据
				taskData.value = taskData.value.filter((ele) => ele.id !== id);
			}
		});
	});
};

onMounted(() => {
	document.querySelector('body').style.backgroundColor = '#f4f5f7';
	init();
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.querySelector('body').removeAttribute('style');
});

const loading = ref(false);
const finished = ref(false);
const onLoad = () => {
	loading.value = true;
	query.page++;
	finished.value = false;
	taskList();
};

const loadingStatus = ref('loading');
const taskData = ref([]);

const query = reactive({
	page: 0,
	size: 10,
	total: 0,
	sort: 'end_time,desc',
});

// 待办任务
const data = reactive({
	taskInfoSourceType: null,
	taskInfoType: [],
	taskInfoStatusType: 'PROGRESS',
	taskInfoSearchType: 'ALL',
});

// 返回
const goBack = () => {
	router.go(-1);
};
const active = ref('待办');
const router = useRouter();
const route = useRoute();

const taskList = () => {
	getTaskList(query, data)
		.then((res) => {
			if (res.result.content.length > 0) {
				loadingStatus.value = 'success';
				loading.value = false;
				query.total = res.result.totalElements;
				for (const item of res.result.content) {
					if (item.choiceDoctorVOList.length > 0) {
						let doctorName = '';
						for (const s of item.choiceDoctorVOList) {
							doctorName += s.doctorName + '，';
						}
						item.doctorName = doctorName.slice(0, doctorName.length - 1);
						item.hospital = '';
					} else {
						item.doctorName = item?.doctorVO?.doctorName;
						item.hospital = item?.doctorVO?.hospital;
					}
				}
				taskData.value = taskData.value.concat(res.result.content);
				if (taskData.value.length >= query.total) {
					finished.value = true;
				} else {
					finished.value = false;
				}
			} else {
				loadingStatus.value = 'empty';
				finished.value = true;
			}
		})
		.catch(() => {
			loadingStatus.value = 'empty';
			finished.value = true;
		});
};

// 初始化
const init = () => {
	loading.value = true;
	// 任务列表
	taskList();
	// 同事列表
	getTeammateList();
};
// 同事列表
const teammateList = ref([]);
const getTeammateList = () => {
	colleagueList(userId, { isBrief: false }).then((res) => {
		if (res.result && res.result.length > 0) {
			res.result = res.result.reduce((acc, cur) => {
				const hasDuplicate = acc.some((item) => item.id === cur.id);
				if (!hasDuplicate) {
					acc.push(cur);
				}
				return acc;
			}, []);
		}
		teammateList.value = res.result || [];
	});
};
// 转发
let forwardId = '';
const showForward = ref(false);
const forwardTaskDialog = (id) => {
	forwardId = id;
	showForward.value = true;
};
const activeTask = (id) => {
	showConfirmDialog({
		title: '提示',
		message: '是否确认转发该任务？',
		confirmButtonColor: '#0052CC',
	}).then(() => {
		taskTransferIn({ recipientId: id, taskInfoIdList: [forwardId] }).then(() => {
			showToast({
				position: 'top',
				message: '任务已转发',
			});
			taskData.value = taskData.value.filter((ele) => ele.id !== forwardId);
		});
	});
};

// 任务执行
const executeTask = (item) => {
	if (item.taskInfoType === 'OFFLINE_VISIT' || item.taskInfoType === 'PHONE_VISIT' || item.taskInfoType === 'WECHAT_VISIT') {
		// 线下拜访，跳转到医生的拜访记录页面，填写完毕后，自动完成此任务
		let productIdList = '';
		if (item.productDomainList.length > 0) {
			for (const s of item.productDomainList) {
				productIdList += s.id + ',';
			}
			productIdList = productIdList.slice(0, productIdList.length - 1);
		}
		router.push({
			name: 'toReport',
			query: {
				isTabBar: route.query.isTabBar || '',
				taskId: item.id,
				doctorId: item.doctorVO.doctorId,
				visitType: item.taskInfoType === 'OFFLINE_VISIT' ? '线下拜访' : item.taskInfoType === 'PHONE_VISIT' ? '电话拜访' : 'Wecom拜访',
				productIdList: productIdList,
				endTime: item.endTime,
			},
		});
	} else {
		// 会议邀请
		router.push({
			name: 'toMeet',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	}
};

// 页签切换
const tabChange = (name) => {
	query.page = 0;
	finished.value = true;
	taskData.value = [];
	loadingStatus.value = 'loading';
	if (name === '全部任务') {
		data.taskInfoSearchType = 'ALL';
		data.taskInfoStatusType = null;
	} else if (name === '待办') {
		data.taskInfoSearchType = 'ALL';
		data.taskInfoStatusType = 'PROGRESS';
	} else if (name === '已完成') {
		data.taskInfoSearchType = 'ALL';
		data.taskInfoStatusType = 'COMPLETE';
	} else {
		data.taskInfoSearchType = 'EXPIRED';
		data.taskInfoStatusType = null;
	}
	taskList();
};
</script>

<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	color: #172b4d;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		background-color: #ffffff;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #172b4d;
			font-weight: 600;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			padding-left: 32px;
			color: var(--ac-font-color);
		}
	}
	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		&:deep(.van-tab) {
			font-size: 15px;
			color: #6b778c;
		}
		&:deep(.van-tab--active) {
			color: #172b4d;
		}

		.mc-content__skeleton {
			display: flex;
			flex: 1;
			overflow-y: auto;
			flex-direction: column;
			background-color: #ffffff;
		}

		.mc-content_task {
			padding: 15px;
			display: flex;
			flex: 1;
			overflow-y: auto;
			flex-direction: column;

			.task-item {
				border-radius: 6px;
				background-color: #ffffff;
				padding: 12px;
				margin-bottom: 15px;
				display: flex;
				flex-direction: column;
				.task-item__header {
					padding-bottom: 12px;
					border-bottom: 1px solid #dfe1e6;
					display: flex;
					align-items: center;
					justify-content: space-between;
					.task-item__header-left {
						display: flex;
						align-items: center;
						.left-icon {
							height: 12px;
							display: inline-block;
							width: 4px;
							background-color: #0052cc;
							margin-right: 6px;
						}
						.left-tag {
							margin-left: 6px;
						}
					}
					.task-item__header-right {
						span {
							font-size: 12px;
						}
					}
				}

				.task-item__body {
					padding-top: 12px;
					display: flex;
					flex-direction: column;
					position: relative;
					margin-bottom: 8px;
					.task-item__body--img {
						position: absolute;
						right: 5px;
						top: 50%;
						z-index: 9;
						width: 4px;
						height: 8px;
						margin-bottom: 4px;
					}
					.body-item {
						display: flex;
						align-items: flex-start;
						margin-bottom: 10px;
						& span:nth-child(1) {
							width: 75px;
						}
						& span:nth-child(2) {
							flex: 1;
							word-break: break-all;
						}
					}
				}

				.task-item__bottom {
					display: flex;
					justify-content: flex-end;
					&:deep(.van-button) {
						width: 65px;
						height: 30px;
					}
					.task-item__bottom_btn {
						margin-right: 8px;
						&:deep(.van-button__text) {
							color: #0052cc;
						}
					}
				}
			}
		}
	}
	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
}
</style>
