<template>
	<div id="ap-script-assistant" class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div
				@click="goBack"
				:style="{
					visibility: route?.query?.isHeaderBackShow === 'false' ? 'hidden' : '',
				}"
				class="mc-header-icon"
			>
				<van-icon :size="20" name="arrow-left" />
			</div>
			<div class="mc-header-name">
				<span class="ell">{{ agentName }}</span>
			</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">
				<img @click="acGlobalVoice(false)" v-show="isGlobalVoice" src="@/assets/img/global-voice-active.png" />
				<img @click="acGlobalVoice(true)" v-show="!isGlobalVoice" src="@/assets/img/global-voice.png" />
				<!-- 多会话历史记录 -->
				<img v-if="isMultiChat" @click="getChatHistory" class="history-img" src="@/assets/img/history.png" />
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div id="aiCon" class="mc-content-list clearfix">
				<template v-for="(item, index) in chatList">
					<template v-if="item.type === 'user'">
						<div :key="item.id" class="mc-content-list-right">
							<!-- 用户内容模版 -->
							<div @click="editorMsg(index, item)" class="mc-content-template">
								<p style="white-space: pre-wrap">{{ item.msg }}</p>
								<!-- 是否可以修改 -->
								<div class="mc-content-template__edit" v-if="index === chatList.length - 2"></div>
							</div>
						</div>
					</template>
					<template v-if="item.type === 'FAQ'">
						<div :key="item.id" class="mc-content-list-left">
							<div class="par">
								<div class="mc-content-template">
									<div class="q-content">
										<div class="q-title">
											<span>您可以试着问</span>
											<span @click="moreQ">更多</span>
										</div>
										<div class="q-list" v-for="item in questionsList" :key="item" @click="recommendCardClick(item)">
											<div class="q-list-text">{{ item.name }}</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</template>
					<template v-if="item.type === 'ai'">
						<div :key="item.id" class="mc-content-list-left" @touchstart="currentClickIndex = index">
							<div class="par" style="display: flex">
								<!-- AI 文本 内容模版 -->
								<div class="mc-content-template">
									<loading1 v-if="item.isLoading" :text="item.loadingText"></loading1>
									<!-- 纯文本 -->
									<ai-text v-if="!item.isIframe && !item.isLoading && !item.isApi" :id="item.id" :is-loading="item.loading" :msg="item.msg" :think="item.think || ''" :name="item.name"></ai-text>
									<!-- 卡片 -->
									<div v-if="item.isIframe && !item.isLoading">
										<div v-if="!item.isLoading" style="margin-top: 5px">
											<iframe class="ifr" :style="{ height: item.iframeHeight ? item.iframeHeight : '' }" v-if="item.iframeUrl" :src="item.iframeUrl" :ref="(el) => setRef(el, index)" @load="(event) => iframeLoad(index, event.target)"></iframe>
										</div>
										<!-- 更多可选卡片 -->
										<div v-if="item.recommedCards?.length > 0 && item.iframeLoadingFinished" class="recommend-cards">
											<span v-if="item.recommedCards.filter((i) => i.id !== getLastChat.msg?.[0]?.id).length > 0">更多可选分析：</span>
											<div v-if="item.recommedCards.filter((i) => i.id !== getLastChat.msg?.[0]?.id).length > 0" class="recommend-cards-list">
												<template v-for="son in item.recommedCards" :key="son.id">
													<div class="recommend-cards-item" v-if="son.id !== getLastChat.msg?.[0]?.id" @click="recommendCardClick(son, index)">
														<img :src="son.previewUrl" alt="" />
														<div class="name">{{ son.name }}</div>
													</div>
												</template>
											</div>
										</div>
										<!-- summary -->
										<div v-if="item.iframeLoadingFinished && !item.summary">正在生成点评，请稍后<span class="dots"></span></div>
										<div v-if="item.summary">
											<ai-text :id="item.id" :is-loading="item.summaryLoading" :msg="item.summary"></ai-text>
										</div>
									</div>
									<div v-if="item.isApi">
										<!-- summary -->
										<div v-if="item.summary && !item.isLoading">
											<ai-text :id="item.id" :is-loading="item.summaryLoading" :msg="item.summary"></ai-text>
										</div>
									</div>
									<!-- 下一步行动计划 -->
									<div v-if="item.nextAction && showEtc(item)">
										<div class="btns toPlan" v-for="(item, index) in item.nextAction" :key="index" @click="toNextAction(item)">
											<span class="toPlan">{{ item }}</span>
											<div class="right"></div>
										</div>
									</div>
									<!-- 空显示随机卡片 -->
									<div v-if="item.noCardFound" class="no-found-card">
										<div class="no-found-card-item" v-for="card in cardList" :key="card.name" @click="recommendCardClick(card, index)">
											<img :src="card.previewUrl" alt="" />
											<span>{{ card.name }}</span>
										</div>
									</div>
									<!-- 小按钮组 - 语音播报和复制 -->
									<ai-btns v-if="item.isBtns && !item.loading && !item.isLoading" :id="item.id" :reGen="item.reGen" :zeStatus="item.zeStatus" @_ze="zeEv" @_reGen="reGen" :voiceStatus="item.voiceStatus" @copyText="copyText" @voiceEv="voiceEv"></ai-btns>
								</div>
							</div>
							<!-- 问题列表 -->
							<div class="ql" v-if="item.questionList && item.questionList.length > 0 && showEtc(item)">
								<div class="nk">您可能还想问</div>
								<div class="mc-content__groupList">
									<span v-for="(val, index) in item.questionList" :key="val" @click="qesEv(val, index, item.questionList)">{{ val.name }}</span>
								</div>
							</div>
						</div>
					</template>
				</template>
			</div>
			<!-- 声明 -->
			<div v-if="inForUse" class="mc-declare">{{ inForUse }}</div>
			<!-- 按钮组 -->
			<btn-list :qt-list="qtList" @qesMoreEv="qesMoreEv"></btn-list>
			<!-- 聊天框 -->
			<div ref="chatBox" class="mc-content-chat">
				<ai-textarea :sMenu="false" :ol="true" :toolsList="actions" :editorText="editorText" @_sendMessage="sendMessage" @_changeEdit="changeEdit" @_toolClick="toolClick"></ai-textarea>
			</div>
			<!-- 滚动到底部 -->
			<Teleport to="body">
				<Transition name="slide">
					<div v-if="showScrollDown" class="scroll-down" @click="scrollBottom()">
						<img src="@/assets/img/scrolldown.png" alt="" />
					</div>
				</Transition>
			</Teleport>
			<!-- 筛选 -->
			<van-overlay :show="comFilterShow" z-index="100" :lock-scroll="false">
				<div v-if="comFilterShow" class="wrapper">
					<div
						class="coms"
						:style="{
							paddingLeft: cardInfo.reportCd === 'hospitalAnalysis' ? (isPc ? '13px' : '3.46667vw') : '',
							paddingRight: cardInfo.reportCd === 'hospitalAnalysis' ? (isPc ? '13px' : '3.46667vw') : '',
						}"
					>
						<component ref="comRef" :is="dom[currentCom]" :info="cardInfo" :dinfo="cardInfo" :defaultValue="chatList[currentClickIndex]?.defaultValue"></component>
					</div>
					<div class="operation">
						<div class="cancel" @click="comFilterShow = false">取消</div>
						<div class="confirm" @click="createNewAnalysis">确认</div>
					</div>
				</div>
			</van-overlay>
		</div>
		<!-- 踩的理由 -->
		<van-action-sheet class="cai-action-sheet" v-model:show="caiShow" title="反馈">
			<van-field v-model="zeJson.feedback_reason" type="textarea" placeholder="请输入您觉得回答不满意的地方" />
			<van-button color="#0B67E4" @click="caiSubmit">提交</van-button>
		</van-action-sheet>
		<!-- 使用须知弹框 -->
		<van-popup v-model:show="showUse" position="bottom" :style="{ height: '80%' }">
			<for-use :content="inForUseTextarea" @close="showUse = false"></for-use>
		</van-popup>
		<!-- 历史会话 -->
		<chat-history :historyList="historyList" ref="cHs" @_openChat="openHistoryChat"></chat-history>
		<!-- 问题列表 -->
		<questionListCom v-if="isMultiChat" ref="questionListComRef" @set-q="setQ" @q-c="qClick"></questionListCom>
	</div>
</template>
<script setup>
import { getToken } from '@/utils/auth';
import { showLoadingToast, showToast, closeToast } from 'vant';
import btnList from './components/btnList.vue';
import useUserStore from '@/store/modules/user';
import usePremissionStore from '@/store/modules/premission';
import ClipboardJS from 'clipboard';
import CryptoJS from 'crypto-js';
import AudioPlayer from '@/utils/ap';
import { getAgentListApi } from '@/api/agent-tools';
import axios from 'axios';
import { uuid, formatParamsDates, formatKNumber, getRandomString, ruleAllList, addOrUpdateQueryParameter } from '@/utils/index';
import { useClickAway } from '@vant/use';
import { queryList, branchHospitalSalesOverviewAmount, ddiTransactionSales, salesAchievementStatus, getApi, getTeamSalesTrends } from '@/api/sales';
import { getParentTerritoryInfo, getTerritoryCodeLevel } from '@/api/user';
import { getReports } from '@/api/report';
import Decimal from 'decimal.js';
import { useI18n } from 'vue-i18n';
import questionListCom from './components/questionList.vue';
const { t } = useI18n();
const { proxy } = getCurrentInstance();
const perStore = usePremissionStore();

import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
// 接口地址
const interfaceUrl = enterStore.enterInfo.interfaceAddress;
const relam = enterStore.enterInfo.id;
const domain = enterStore.enterInfo.domain;
const minioAddress = enterStore.enterInfo.minioAddress;
import xsztqk from '@/assets/img/sxztqk.png';
import fyyxs from '@/assets/img/fyyxs.png';
import fcpxs from '@/assets/img/fcpxs.png';
import xspm from '@/assets/img/xspm.png';
import yyjyddi from '@/assets/img/yyjyddi.png';
import zrzt from '@/assets/img/zrzt.png';
import scfe from '@/assets/img/scfe.png';
import cjwt from '@/assets/img/cjwt.png';
import wdzb from '@/assets/img/wdzb.png';
import dcfx from '@/assets/img/dcfx.png';
import hbgl from '@/assets/img/hbgl.png';
import usefilterStore from '@/store/modules/filter';
import { sassFalse } from 'sass';
import { getAgentId } from '@/api/knowledge';
// 屏幕宽度大于600，认为在pc端
const isPc = ref(window.innerWidth >= 600 ? true : false);

const toolClick = (item) => {
	if (item.type === 'path') {
		if (!item.url.includes('http')) {
			if (isPc.value) {
				router.push({
					name: item.url,
					query: {
						hiddenTarbar: true,
					},
				});
			} else {
				router.push({
					name: item.url,
				});
			}
		} else {
			location.href = item.url;
		}
	} else {
		recommendCardClick(item);
	}
};

const qesMoreEv = (i) => {
	if (i.name === '业绩播报') {
		if (!isLoading.value) {
			scrollBottom();
			init();
		} else {
			showToast({
				position: 'top',
				message: '正在为您解读，请耐心等待',
			});
		}
	} else {
		recommendCardClick(i);
	}
};

//全局语音控制
const filterStore = usefilterStore();
const userStore = useUserStore();
// 语言环境
const language = computed(() => {
	return filterStore.language === 'zh' ? '中文' : '英文';
});
const isGlobalVoice = computed(() => {
	return filterStore.isGlobalVoice;
});
const agentName = ref('');
const inForUse = ref('');
const acGlobalVoice = (val) => {
	filterStore.SET_IS_GLOBAL_VOICE(val);
};
const audioPlayer = new AudioPlayer();

const isLoading = ref(false); //页面是否有正在回答的消息
const chatList = ref([]); //消息列表
// 返回
const route = useRoute();
let appUrl = domain;
let imgUrl = minioAddress;
const goBack = () => {
	router.go(-1);
};

// 赞和踩
const zeJson = reactive({
	id: '',
	feedback_reason: '',
});
const caiSubmit = () => {
	if (!zeJson.feedback_reason) {
		showToast({
			message: '请输入您觉得回答不满意的地方',
		});
		return false;
	}
	caiShow.value = false;
	zeEv({
		id: zeJson.id,
		like: '3',
		remark: zeJson.feedback_reason,
	});
};
const caiShow = ref(false);
const zeEv = ({ id, like, remark = '' }) => {
	const j = chatList.value.filter((ele) => ele.id === id)[0];
	const json = {
		username: userStore.userInfo.username,
		message_id: j.dataId,
		feedback_score: '',
		feedback_reason: '',
	};
	if (like === '2') {
		// 踩
		zeJson.id = id;
		zeJson.feedback_reason = '';
		caiShow.value = true;
		return;
	} else if (like === '1') {
		// 赞
		json.feedback_score = 100;
	} else if (like === '3') {
		// 踩
		json.feedback_score = 0;
		json.feedback_reason = remark;
	} else {
		json.feedback_score = -1;
	}
	if (like === '3') {
		j.zeStatus = '2';
	} else {
		j.zeStatus = like;
	}
	axios({
		url: interfaceUrl + '/API-OPENGPT/api/session/history/update_history_feedback',
		method: 'put',
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
		params: json,
	});
};
//生成一条用户消息
const createUserMsg = (text) => {
	return {
		msg: text,
		dataId: '',
		type: 'user',
		id: uuid(),
		loading: false,
		zeStatus: '0',
	};
};
//生成一条ai消息  添加一个参数isZeStatus，用于判断是现实赞和踩，默认显示
const createAiMsg = (text, isZeStatus = true) => {
	let message = {
		msg: '',
		think: '',
		dataId: '',
		type: 'ai',
		id: uuid(),
		loading: true,
		isBtns: true,
		reGen: true,
		voiceStatus: true,
		zeStatus: isZeStatus ? '0' : '-3',
		isIframe: '',
	};
	text ? (message.loadingText = t('common.gdprfy')) : '';
	return message;
};
//猜你想问
const qesEv = (val) => {
	console.log(val);

	recommendCardClick(val);
	// if (!isLoading.value) {
	// 	isLoading.value = true;
	// 	chatList.value.push(createUserMsg(val));
	// 	// 生产一个ai的消息
	// 	chatList.value.push(createAiMsg());
	// 	scrollBottom();
	// 	gd(val);
	// }
};
// 重新生成
const reGen = (id) => {
	if (isLoading.value) {
		stopFetch();
	}
	// 获取文本信息
	let prevItemMsg = '';
	const index = chatList.value.findIndex((item) => item.id === id);

	if (index !== -1) {
		if (chatList.value[index].afterStopAutoRead) {
			chatList.value.splice(index, 1);
			init();
			return;
		}
		prevItemMsg = chatList.value[index - 1]?.msg ?? '';
	}
	try {
		chatList.value.push(createUserMsg(prevItemMsg));
		// 生产一个ai的消息
		chatList.value.push(createAiMsg());
		scrollBottom();
		gd(prevItemMsg);
	} catch (e) {
		console.log(e);
		showToast({
			position: 'top',
			message: '重新生成失败！',
		});
	}
};

// 历史会话记录
const cHs = ref(null);
const getChatHistory = () => {
	cHs.value.showLeft = true;
};
//查询左侧会话记录
const historyList = ref([]);
const getConversChatList = async () => {
	// 获取fastGpt历史会话记录
	try {
		let allList = await getOtherChatList();
		allList = allList.reduce((acc, cur) => {
			const index = acc.findIndex((item) => item.updateTime.split('T')[0] === cur.updateTime.split('T')[0]);
			if (index !== -1) {
				acc[index].children.push(cur);
			} else {
				acc.push({
					updateTime: cur.updateTime,
					name: cur.updateTime.split('T')[0],
					children: [cur],
				});
			}
			return acc;
		}, []);
		// 把allList根据updateTime做个降序
		allList.sort((a, b) => {
			return new Date(b.updateTime) - new Date(a.updateTime);
		});
		for (const item of allList) {
			item.children.sort((a, b) => {
				return new Date(b.updateTime) - new Date(a.updateTime);
			});
		}
		if (allList.length > 0) {
			allList = ruleAllList(allList, language.value);
		}
		historyList.value = allList;
	} catch (error) {
		console.log(error);
		showToast({
			position: 'top',
			zIndex: 8888,
			message: '会话记录获取失败',
		});
	}
};

// 其他模块的会话列表
const getOtherChatList = async () => {
	const agent_id = await getAgentId();
	return new Promise((resolve, reject) => {
		const json = {
			username: userStore.userInfo.username,
			skip: 0,
			limit: 700,
			agent_id: String(agent_id),
		};
		axios({
			url: interfaceUrl + '/API-OPENGPT/api/session/history/user/page',
			method: 'get',
			params: json,
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		})
			.then((res) => {
				// 把 res.data.data 根据 updateTime 拆分成treeList
				let dataList = res.data.data || [];
				for (const cur of dataList) {
					cur.chatType = 'scriptAssistant';
					cur.updateTime = cur.created_time.substring(0, 19);
					cur.chatId = cur.conversation_id;
					cur.title = cur.query;
				}
				resolve(dataList);
			})
			.catch(() => {
				reject('other会话出错了');
			});
	});
};
// 打开历史记录会话
const openHistoryChat = (item) => {
	if (item.chatId === chatId1.value) return;
	if (isLoading.value) {
		showToast({
			message: '正在回答，请稍后...',
			position: 'top',
		});
		return;
	}
	chatList.value = [];
	pageNum = -1;
	historyOver.value = false;
	loadingHistory.value = false;
	chatId1.value = item.chatId;
	const params = new URLSearchParams(window.location.search);
	params.set('chatId', chatId1.value); // 添加或更新 query 参数
	const newUrl = window.location.pathname + '?' + params.toString();
	history.replaceState(null, '', newUrl);
};

// 是否正在加载历史记录
const loadingHistory = ref(false);
// 历史记录是否加载完毕
const historyOver = ref(false);
// 获取历史记录
const getHistoryList = async () => {
	// 正在加载不触发, 加载完毕不触发
	if (loadingHistory.value || historyOver.value) return;
	// 正在加载
	loadingHistory.value = true;
	pageNum++;
	// 分页不等于0，显示loading
	if (pageNum !== 0) {
		showLoadingToast({
			message: '加载中...',
			forbidClick: true,
			duration: 0,
		});
	}
	const json = {
		username: userStore.userInfo.username,
		chat_type: 'scriptAssistant',
		conversation_id: chatId1.value,
		limit,
		skip: pageNum * limit,
	};
	axios({
		url: interfaceUrl + '/API-OPENGPT/api/session/history/find_by_conversation_id',
		method: 'get',
		params: json,
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
	})
		.then((res) => {
			if (res.data.code === 200 && res.data.data.length > 0) {
				// 临时数组
				const arrList = [];
				for (const item of res.data.data) {
					console.log(item);

					// ai
					item.meta_data.dataId = item.id;
					item.meta_data.zeStatus = item?.feedback_score === 100 ? '1' : item?.feedback_score === 0 ? '2' : '0';
					if (import.meta.env.VITE_APP_ENV === 'development') {
						item.meta_data.iframeUrl = item.meta_data.iframeUrl?.replace('https://agentbox-uat.roche.com.cn', 'http://localhost:1245');
					}
					arrList.unshift(item.meta_data);
					// user
					arrList.unshift(createUserMsg(item.query));
				}
				chatList.value = arrList.concat(chatList.value);
				// 如果返回的条数小于limit，表示加载完毕
				if (res.data.data.length < limit) historyOver.value = true;
				return Promise.resolve('1');
			}
		})
		.finally(() => {
			// 加载完毕
			loadingHistory.value = false;
			if (chatList.value.length === 0) {
				historyOver.value = true;
				// 没有记录，显示欢迎语
				chatList.value.push({
					msg: welcomeText,
					type: 'ai',
					id: uuid(),
				});
				//常问问题
				if (isMultiChat.value) {
					chatList.value.push({
						type: 'FAQ',
						id: uuid(),
					});
				}
			}
			if (pageNum === 0) {
				nextTick(() => {
					scrollBottom('auto');
				});
			} else {
				// 加载完毕，滚动条回到原来的位置
				nextTick(() => {
					const aiCon = document.querySelector('#aiCon');
					const scrollTop = aiCon.scrollHeight - aiCon.clientHeight - prevScrollHeight;
					aiCon.scrollTo({
						top: scrollTop,
						behavior: 'auto',
					});
					closeToast();
				});
			}
		});
};

// 把fastgpt的会话记录同步到公司库
const asyncChatLog = async (message, response, metaData, qa = '') => {
	const agent_id = await getAgentId();
	return new Promise((resolve) => {
		axios({
			url: interfaceUrl + '/API-OPENGPT/api/session/history/create',
			method: 'post',
			headers: {
				Authorization: `Bearer ${getToken()}`,
				'Content-Type': 'application/json;charset=utf-8',
			},
			data: {
				meta_data: metaData,
				username: userStore.userInfo.username,
				conversation_id: chatId1.value,
				query: message,
				response: response,
				chat_type: 'scriptAssistant',
				data_temp1: qa,
				agent_id: String(agent_id),
			},
		})
			.then((res) => {
				getLastChat.value.dataId = res.data.data.id;
			})
			.finally(() => {
				resolve();
			});
	});
};

//获取最后一条用户消息
const getLastUser = () => {
	let ar = chatList.value.filter((ele) => ele.type === 'user');
	let msg = ar?.[ar.length - 1]?.msg;
	return msg;
};
//获取最后一条chat
const getLastChat = computed(() => {
	return chatList.value[chatList.value.length - 1];
});
//固定消息
const gd = (value) => {
	getLastChat.value.isLoading = true;
	if (relam === 'roche' && value.includes('指标')) {
		// 查询卡片为空的情况
		getLastChat.value.isLoading = false;
		getLastChat.value.loading = false;
		getLastChat.value.msg = '当前找不到合适的数据来回答您的问题。以下是为您推荐的其他可用分析：';
		getLastChat.value.noCardFound = true;
		isLoading.value = false;
		stopFetch();
		// 没有命中卡片的情况, 直接同步绘画信息
		asyncChatLog(getLastUser(), getLastChat.value.msg, getLastChat.value, 'AI');
		return;
	}
	//获取猜你想问
	getSuggestion({ type: 'cardSuggestion', Question: getLastUser() });
	moreCards({ type: 'cardMore', Question: getLastUser() });
	setTextMessage(value);
};

// 发送消息
const sendMessage = (val) => {
	if (/ppt/i.test(val) && perStore?.reportChildren) {
		// 如果 val 包含 "PPT"，跳转到另一个 URL
		router.push({ name: perStore.reportChildren });
		return;
	}
	if (!isLoading.value) {
		if (val) {
			isLoading.value = true;
			// val = val.replace(/\n/g, '<br/>');
			chatList.value.push(createUserMsg(val));
			// 生产一个ai的消息
			chatList.value.push(createAiMsg());
			scrollBottom();
			gd(val);
		} else {
			showToast({
				position: 'top',
				message: '请输入内容',
			});
		}
	} else {
		if (val) {
			isLoading.value = true;
			stopFetch();
			val = val.replace(/\n/g, '<br/>');
			chatList.value.push(createUserMsg(val));
			// 生产一个ai的消息
			chatList.value.push(createAiMsg());
			scrollBottom();
			gd(val);
		} else {
			showToast({
				position: 'top',
				message: '请输入内容',
			});
		}
	}

	if (!route.query.chatId && isMultiChat.value) {
		//向url中添加query chatId
		const params = new URLSearchParams(window.location.search);
		params.set('chatId', chatId1.value); // 添加或更新 query 参数
		const newUrl = window.location.pathname + '?' + params.toString();
		history.replaceState(null, '', newUrl);
	}
};
//停止当前请求
const stopFetch = () => {
	controllerList.forEach((ele) => ele.abort());
	getLastChat.value.loading = false;
	getLastChat.value.isLoading = false;
	if (getLastChat.value.msg) {
		getLastChat.value.msg = getLastChat.value.msg + '...';
	}
	if (getLastChat.value.summary) {
		getLastChat.value.summary = getLastChat.value.summary + '...';
	}
	if (!getLastChat.value.msg && !getLastChat.value.summary) {
		getLastChat.value.msg = '...';
		getLastChat.value.summary = '...';
	}
	getLastChat.value.summaryLoading = false;
	getLastChat.value.nextAction = '';
	getLastChat.value.temNextAction = '';
	getLastChat.value.questionList = '';
	getLastChat.value.temQuestionList = '';
	// isLoading.value = false;
	controllerList = [];
	nextTick(() => {
		if (getLastChat.value.afterStopAutoRead && isGlobalVoice.value) {
			getLastChat.value.voiceStatus = false;
			voiceEv({
				id: getLastChat.value.id,
				vType: 'play',
			});
		}
	});
};
// 发送的消息是否是二次编辑的消息
const isEdit = ref(false);
const editorText = ref('');
const editorMsg = (index, item) => {
	if (index === chatList.value.length - 2) {
		// 如果请求正在进行需要终止请求
		if (isLoading.value) {
			stopFetch();
			isLoading.value = false;
		}
		let msg = item.msg.replace(/<br\s*\/?\s*>/gi, '\n');
		editorText.value = msg + 'jjjjjj' + new Date().getTime();
		isEdit.value = true;
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isEdit.value = false;
	}
};
const changeEdit = () => {
	isEdit.value = false;
	editorText.value = '';
};
// 文本框失去焦点
const chatBox = ref();
useClickAway(
	chatBox,
	() => {
		if (isEdit.value) {
			editorText.value = '';
		}
		isEdit.value = false;
	},
	{ eventName: 'touchend' }
);

// 会话ID
let chatId1 = ref('');
let appId = '';
let appKey = '';
let pageNum = -1;
let limit = 3;
let prevScrollHeight = 0;
let welcomeText = 'Hi,我是你的智能分析小助手，您可以向我随时提问关于销售业绩的问题，我来帮您解答哦！';
// 默认多会话
const isMultiChat = ref(true);

watch(chatId1, (newVal, oldVal) => {
	if (oldVal && newVal && oldVal !== newVal) {
		getHistoryList();
	}
});

//处理猜你想问和下一步行动计划出现时机
const showEtc = computed(() => {
	return (item) => {
		if (item.isIframe) {
			if (!item.summaryLoading) {
				return true;
			}
		} else {
			if (!item.loading) {
				return true;
			}
		}
	};
});
let controllerList = [];
const fetchRequest = (val, isNotStream) => {
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async (resolve) => {
		try {
			let controller = new AbortController();
			controllerList.push(controller);
			const { signal } = controller;
			const message = val || '';
			let radom1 = uuid();
			let radom2 = uuid();
			let url = enterStore.enterInfo.agentAddress;
			let res = await fetch(`${url}/api/v1/chat/completions`, {
				signal,
				method: 'POST',
				headers: {
					'Content-Type': 'application/json;charset=utf-8',
					Authorization: 'Bearer ' + appKey,
				},
				body: JSON.stringify({
					appId,
					chatId: chatId1.value,
					stream: isNotStream ? false : isStream.value,
					detail: true,
					variables: {
						language: language.value,
						token: `Bearer ${getToken()}`,
						role: getRole.value,
						usename: userStore.userInfo.username,
					},
					messages: [
						{
							role: 'user',
							// content: JSON.stringify({ message, role: getRole.value }),
							content: JSON.stringify(message),
							dataId: radom1,
						},
						// { role: 'assistant', content: '', dataId: radom2 },
					],
				}),
			});
			if (!res?.body || !res?.ok) {
				throw new Error('Request Error');
			}
			resolve(res);
		} catch (error) {
			console.log(error, 'xxxxxxxxxxxxxxxxxxxxxxx');
		}
	});
};
const handleStream = async (res, type, isStream) => {
	let fixedVariable = {
		Suggestion: ['temQuestionList', 'questionList'],
		NextAction: ['temNextAction', 'nextAction'],
		MoreCards: ['temRecommedCards', 'recommedCards'],
	};
	if (isStream) {
		//如果是流式
		const reader = res.body?.getReader();
		const decoder = new TextDecoder('utf-8');
		let buffer = '';
		function processStreamResult(result) {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			buffer += chunk;
			// 逐条解析后端返回数据
			const lines = buffer.split('\n');
			buffer = lines.pop();
			lines.forEach((line) => {
				if (line.trim().length === 0) return;
				// 错误处理
				if (line.includes('event: error')) {
					if (type === 'cardOrMsg') {
						getLastChat.value.msg = '当前系统繁忙，请稍后再试。';
						isLoading.value = false;
						getLastChat.value.zeStatus = 3;
						getLastChat.value.isLoading = false;
						getLastChat.value.loading = false;
						return;
					}
				}

				if (line.indexOf('data:') === -1 || line.split('data:')[1] === ' [DONE]') return;
				const resData = JSON.parse(line.split('data:')[1]);
				if (resData.name) {
					chatList.value[chatList.value.length - 1].name = resData.name;
					if (chatList.value[chatList.value.length - 1].think !== '') {
						chatList.value[chatList.value.length - 1].think += '\n\n';
					}
				}
				if (resData.name === 'AI 对话-每日业绩播报') {
					getLastChat.value.isLoading = false;
				}
				if (resData.isDateValid === '0') {
					getLastChat.value.deleteTime = true;
				}
				if (resData.choices && resData.choices[0].delta.reasoning_content) {
					const text = resData.choices[0].delta.reasoning_content || '';
					chatList.value[chatList.value.length - 1].think += text;
				} else if (resData.choices && resData.choices[0].delta.content) {
					const text = resData.choices[0].delta.content;
					console.log(text);
					if (type === 'Summary') {
						getLastChat.value.summary += text;
					} else if (type === 'cardOrMsg') {
						getLastChat.value.msg += text;

						if (getLastChat.value.msg.includes('```markdown')) {
							getLastChat.value.msg = getLastChat.value.msg.replace('```markdown', '');
						}
						if (getLastChat.value.msg.includes('cardCom')) {
							getLastChat.value.isIframe = true;
							getLastChat.value.summaryLoading = true; //加载summary
							//获取更多卡片
							getLastChat.value.getMoreCards = true;
						}
						// if (!getLastChat.value.msg.includes('```')) {
						// 	getLastChat.value.isLoading = false;
						// }
					} else if (type === 'MoreCards') {
						getLastChat.value.temRecommedCards += text;
					} else {
						getLastChat.value[fixedVariable[type][0]] += text;
					}
				}
			});
			if (!result.done) {
				return reader.read().then(processStreamResult);
			} else {
				if (type === 'Summary') {
					//如果时summary
					console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
					console.log(getLastChat.value.summary);
					console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
					getLastChat.value.summaryLoading = false;
					nextTick(() => {
						if (isGlobalVoice.value) {
							getLastChat.value.voiceStatus = false;
							voiceEv({
								id: getLastChat.value.id,
								vType: 'play',
							});
						}
					});
					isLoading.value = false;
					getLastChat.value.loading = false;

					// 命中卡片的情况, 分析结束后同步信息
					let iframeUrl = getLastChat.value?.iframeUrl ? addOrUpdateQueryParameter(getLastChat.value?.iframeUrl, 'summary', 0) : '';
					asyncChatLog(getLastUser(), getLastChat.value.summary, { ...getLastChat.value, iframeUrl }, 'QA');
				} else if (type === 'cardOrMsg') {
					if (getLastChat.value.isIframe) {
						// 去除开头和结尾的 ```json 标记
						let trimmedText = getLastChat.value.msg
							.trim()
							.replace(/^```json/, '')
							.replace(/```$/, '');
						// 解析 JSON 字符串为 JavaScript 对象或数组
						let jsonData = JSON.parse(trimmedText);
						console.log(JSON.parse(JSON.stringify(JSON.parse(trimmedText))), '卡片元数据');
						jsonData = formatParamsDates(jsonData);

						//isDateValid判断是否有日期没有日期则删除大模型返回的日期
						if (getLastChat.value.deleteTime) {
							delete jsonData[0].params.startTime;
							delete jsonData[0].params.endTime;
						}
						console.log(jsonData, '格式化每个卡片的日期');
						// 提取 params 和 QA
						let { params, url } = jsonData[0];
						if (import.meta.env.VITE_APP_ENV === 'development') {
							url = url.replace('https://demo.pharmbrain.com', 'http://localhost:1245');
						}

						// 构建查询字符串
						let queryString = '';
						if (params) {
							queryString = Object.entries(params)
								.map(([key, value]) => `${key}=${value}`)
								.join('&');
						}
						getLastChat.value.msg = jsonData;
						// getLastChat.value.recommedCards = jsonData.slice(1);
						// console.log('推荐卡片<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
						// console.log(getLastChat.value.recommedCards);
						// console.log('推荐卡片>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
						if (queryString) {
							getLastChat.value.iframeUrl = url + '?' + queryString;
						} else {
							getLastChat.value.iframeUrl = url;
						}
						getLastChat.value.url = url;
						getLastChat.value.isLoading = false;
						//如果有参数是无就不解读
						for (let item of getLastChat.value.msg) {
							for (let i in item.params) {
								if (item.params[i] === '无') {
									getLastChat.value.summaryLoading = false;
									isLoading.value = false;
									getLastChat.value.loading = false;
								}
							}
						}
					} else if (getLastChat.value.msg.includes('```json') && getLastChat.value.msg.includes('[]')) {
						// 查询卡片为空的情况
						getLastChat.value.isLoading = false;
						getLastChat.value.loading = false;
						getLastChat.value.msg = '当前找不到合适的数据来回答您的问题。以下是为您推荐的其他可用分析：';
						getLastChat.value.noCardFound = true;
						isLoading.value = false;
						stopFetch();

						// 没有命中卡片的情况, 直接同步绘画信息
						asyncChatLog(getLastUser(), getLastChat.value.msg, getLastChat.value, 'AI');
						// let ar = chatList.value.filter((ele) => ele.type === 'user');
						// let msg = ar?.[ar.length - 1]?.msg;
						// setTextMessage({ type: 'cardComSummary', Question: msg });
					} else {
						nextTick(() => {
							if (getLastChat.value.afterStopAutoRead && isGlobalVoice.value) {
								getLastChat.value.voiceStatus = false;
								voiceEv({
									id: getLastChat.value.id,
									vType: 'play',
								});
							}
						});

						isLoading.value = false;
						getLastChat.value.loading = false;
					}
					console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
					console.log(getLastChat.value.msg);
					console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
				} else if (type === 'MoreCards') {
					// 去除开头和结尾的 ```json 标记
					let trimmedText = getLastChat.value.temRecommedCards
						.trim()
						.replace(/^```json/, '')
						.replace(/```$/, '');
					// 解析 JSON 字符串为 JavaScript 对象或数组
					let jsonData = JSON.parse(trimmedText);
					console.log(JSON.parse(JSON.stringify(JSON.parse(trimmedText))), '更多卡片元数据');
					jsonData = formatParamsDates(jsonData);

					//isDateValid判断是否有日期没有日期则删除大模型返回的日期
					if (getLastChat.value.deleteTime) {
						jsonData.forEach((ele) => {
							delete ele.params?.startTime;
							delete ele.params?.endTime;
						});
					}
					console.log(jsonData, '格式化更多每个卡片的日期');
					jsonData.forEach((ele) => {
						if (ele.id !== getLastChat.value.msg?.[0].id) {
							getLastChat.value.recommedCards.push(ele);
						}
					});
					console.log(getLastChat.value.recommedCards, '去重后更多卡片的数据');
				} else {
					//如果时猜你想问和下一步行动
					// 去除开头和结尾的 ```json 标记
					let trimmedText = getLastChat.value[fixedVariable[type][0]]
						.trim()
						.replace(/^```json/, '')
						.replace(/```$/, '');
					getLastChat.value[fixedVariable[type][1]] = JSON.parse(trimmedText).data;
					console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
					console.log(getLastChat.value[fixedVariable[type][1]], type);
					console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
				}
				scrollBottom();
			}
		}
		reader.read().then(processStreamResult);
	} else {
		// 检查响应是否成功
		if (!res.ok) {
			console.error(`请求失败，状态码：${res.status}`);
			return null; // 或抛出错误，取决于你的需求
		}
		try {
			// 假设响应是 JSON 格式
			const data = await res.json();
			let resDatalist = data.responseData; //数据流
			let resDataResult = data.choices;

			for (let item of resDatalist) {
				if (item.moduleName === 'AI 对话-每日业绩播报') {
					getLastChat.value.isLoading = false;
				}
				if (item.moduleName === 'AI 对话—日期与指标类型识别' && item.historyPreview.at(-1)?.value) {
					let { isDateValid, intent, intent_type } = JSON.parse(item.historyPreview.at(-1)?.value);
					getLastChat.value.deleteTime = isDateValid === '0';
					getLastChat.value.intent = intent;
					getLastChat.value.intent_type = intent_type;
				}
			}
			if (resDataResult && resDataResult[0]) {
				const content = resDataResult[0]?.message?.content;
				let text = Array.isArray(content) ? content.at(-1)?.text?.content : content;
				if (type === 'Summary') {
					getLastChat.value.summary += text;
					//完成后的逻辑
					//如果时summary
					console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
					console.log(getLastChat.value.summary);
					console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
					getLastChat.value.summaryLoading = false;
					nextTick(() => {
						if (isGlobalVoice.value) {
							getLastChat.value.voiceStatus = false;
							voiceEv({
								id: getLastChat.value.id,
								vType: 'play',
							});
						}
					});
					isLoading.value = false;
					getLastChat.value.loading = false;
					getLastChat.value.isLoading = false;
					// 命中卡片的情况, 分析结束后同步信息
					let iframeUrl = getLastChat.value?.iframeUrl ? addOrUpdateQueryParameter(getLastChat.value?.iframeUrl, 'summary', 0) : '';
					asyncChatLog(getLastUser(), getLastChat.value.summary, { ...getLastChat.value, iframeUrl }, 'QA');
				} else if (type === 'cardOrMsg') {
					getLastChat.value.msg += text;
					if (getLastChat.value.msg.includes('```markdown')) {
						getLastChat.value.msg = getLastChat.value.msg.replace('```markdown', '');
					}
					if (getLastChat.value.msg.includes('cardCom')) {
						getLastChat.value.isIframe = true;
						getLastChat.value.summaryLoading = true; //加载summary
						//获取更多卡片
						getLastChat.value.getMoreCards = true;
					}
					if (getLastChat.value.msg.includes('dataApi')) {
						getLastChat.value.isApi = true;
						getLastChat.value.summaryLoading = true; //加载summary
					}
					//完成后的逻辑
					if (getLastChat.value.isIframe) {
						// 去除开头和结尾的 ```json 标记
						let trimmedText = getLastChat.value.msg
							.trim()
							.replace(/^```json/, '')
							.replace(/```$/, '');
						// 解析 JSON 字符串为 JavaScript 对象或数组
						let jsonData = JSON.parse(trimmedText);
						console.log(JSON.parse(JSON.stringify(JSON.parse(trimmedText))), '卡片元数据');
						jsonData = formatParamsDates(jsonData);

						//isDateValid判断是否有日期没有日期则删除大模型返回的日期
						if (getLastChat.value.deleteTime) {
							delete jsonData[0].params.startTime;
							delete jsonData[0].params.endTime;
						}

						getLastChat.value.intent ? (jsonData[0].intent = getLastChat.value.intent) : '';
						getLastChat.value.intent_type ? (jsonData[0].intent_type = getLastChat.value.intent_type) : '';

						console.log(jsonData, '格式化每个卡片的日期');
						// 提取 params 和 QA
						let { params, url, intent, intent_type } = jsonData[0];
						console.log(jsonData[0]);

						if (import.meta.env.VITE_APP_ENV === 'development') {
							url = url.replace('https://agentbox-uat.roche.com.cn', 'http://localhost:1245');
						}
						if (intent_type && intent_type === 'rank' && intent) {
							// URL 配置
							const roleUrls = {
								DSM: `/card-library/salesRankingDistributionDSM`,
								RSM: `/card-library/salesRankingDistributionRSM`,
								MR: `/card-library/salesRankingDistribution`,
							};
							const roleUrls2 = {
								dsm_rank: `/card-library/salesRankingDistributionDSM`,
								rsm_rank: `/card-library/salesRankingDistributionRSM`,
								mr_rank: `/card-library/salesRankingDistribution`,
							};
							if (intent === 'team_rank') {
								url = '/card-library/salesAnalysisTeam';
							} else if (['self_rank', 'person_rank'].includes(intent)) {
								if (relam === 'roche') {
									//罗氏特殊处理
									url = '/card-library/noRank';
								} else {
									const territoryCode = intent === 'self_rank' ? userStore.territory_code : params.personId;
									const territoryName = intent === 'self_rank' ? userStore.userInfo.username : params.personName;
									const res11 = await getTerritoryCodeLevel({ territoryCode: territoryCode ? territoryCode : userStore.territory_code });
									if (res11.result === '-1' || !roleUrls[res11.result]) {
										url = '/card-library/noRank';
									} else {
										url = `${roleUrls[res11.result]}${url.includes('Asc') ? 'Asc' : ''}`;
										params.personId = territoryCode ? territoryCode : userStore.territory_code;
										params.personName = territoryName ? territoryName : userStore.userInfo.username;
									}
								}
							} else if (['dsm_rank', 'rsm_rank', 'mr_rank'].includes(intent)) {
								url = `${roleUrls2[intent]}${url.includes('Asc') ? 'Asc' : ''}`;
								//如果最后一个用户问题中包含我的
								const territoryCode = getLastUser().includes('我的') ? userStore.territory_code : params.personId;
								const territoryName = getLastUser().includes('我的') ? userStore.userInfo.username : params.personName;
								const res11 = await getTerritoryCodeLevel({ territoryCode: territoryCode ? territoryCode : userStore.territory_code });
								if (res11.result !== '-1') {
									params.personId = territoryCode ? territoryCode : userStore.territory_code;
									params.personName = territoryName ? territoryName : userStore.userInfo.username;
								}
							} else if (intent === 'other') {
								url = '/card-library/noRank';
							}
						}
						// 构建查询字符串
						let queryString = '';
						if (params) {
							queryString = Object.entries(params)
								.map(([key, value]) => `${key}=${value}`)
								.join('&');
						}
						getLastChat.value.msg = jsonData;
						if (queryString) {
							getLastChat.value.iframeUrl = url + '?' + queryString;
						} else {
							getLastChat.value.iframeUrl = url;
						}
						getLastChat.value.iframeIndex = boxRefs.value.length;
						getLastChat.value.url = url;
						getLastChat.value.isLoading = false;
						//如果有参数是无就不解读
						for (let item of getLastChat.value.msg) {
							for (let i in item.params) {
								if (item.params[i] === '无') {
									getLastChat.value.summaryLoading = false;
									isLoading.value = false;
									getLastChat.value.loading = false;
								}
							}
						}
					} else if (getLastChat.value.isApi) {
						// 去除开头和结尾的 ```json 标记
						let trimmedText = getLastChat.value.msg
							.trim()
							.replace(/^```json/, '')
							.replace(/```$/, '');
						// 解析 JSON 字符串为 JavaScript 对象或数组
						let jsonData = JSON.parse(trimmedText);
						console.log(JSON.parse(JSON.stringify(JSON.parse(trimmedText))), '卡片元数据');
						jsonData = formatParamsDates(jsonData);

						//isDateValid判断是否有日期没有日期则删除大模型返回的日期
						if (getLastChat.value.deleteTime) {
							delete jsonData[0].params.startTime;
							delete jsonData[0].params.endTime;
						}
						console.log(jsonData, '格式化每个卡片的日期');
						// 提取 params 和 QA
						const { params, url } = jsonData[0];
						console.log(params, url);
						const cardInfo = cardsInfoList.value.find((ele) => ele.reportCd === params.reportCd); // 使用默认值
						let p = {
							type: params.indicatorType,
							skuCode: params.brandId ? params.brandId.split(',') : [],
							territoryCode: params.personId ? params.personId.split(',') : [],
							hospCode: params.hospitalId ? params.hospitalId.split(',') : [],
							startLocalDate: params.startTime?.slice(0, 10) || cardInfo?.startTime,
							endLocalDate: params.endTime?.slice(0, 10) || cardInfo?.endTime,
							province: params.province ? params.province.split(',') : [],
							city: params.city ? params.city.split(',') : [],
							level: params.level,
							sort: params.sort,
							month: params.month,
							size: params.size,
							currentStatus: params.currentStatus ? params.currentStatus.split(',') : [],
							groupByName: params.groupByName,
							salesContributionRate: params.salesContributionRate,
						};
						getLastChat.value.msg = jsonData;
						getLastChat.value.url = url;
						let res = await getApi(p, getLastChat.value.url);
						console.log(res);
						// getLastChat.value.isLoading = false;
						let ar = chatList.value.filter((ele) => ele.type === 'user');
						let msg = ar?.[ar.length - 1]?.msg;
						if (res.result.isExceededAuthority) {
							getLastChat.value.summary = '暂无数据或权限，请重新提问。';
							getLastChat.value.summaryLoading = false;
							nextTick(() => {
								if (isGlobalVoice.value) {
									getLastChat.value.voiceStatus = false;
									voiceEv({
										id: getLastChat.value.id,
										vType: 'play',
									});
								}
							});
							isLoading.value = false;
							getLastChat.value.loading = false;
							getLastChat.value.isLoading = false;
							asyncChatLog(getLastUser(), getLastChat.value.summary, { ...getLastChat.value }, 'QA');
							return;
						}

						getSummary({
							type: 'cardComSummary',
							Question: msg,
							data: res.result.agentInfo,
							params: {
								brandName: params.brandName,
								startTime: params.startTime?.slice(0, 10) || cardInfo?.startTime,
								endTime: params.endTime?.slice(0, 10) || cardInfo?.endTime,
								hospitalName: params.hospitalName,
								personName: params.personName,
								marketId: params.marketId,
								province: params.province,
								city: params.city,
							},
						});
					} else if (getLastChat.value.msg.includes('```json') && getLastChat.value.msg.includes('[]')) {
						// 查询卡片为空的情况
						getLastChat.value.isLoading = false;
						getLastChat.value.loading = false;
						getLastChat.value.msg = '当前找不到合适的数据来回答您的问题。以下是为您推荐的其他可用分析：';
						getLastChat.value.noCardFound = true;
						isLoading.value = false;
						stopFetch();
						// 没有命中卡片的情况, 直接同步绘画信息
						asyncChatLog(getLastUser(), getLastChat.value.msg, getLastChat.value, 'AI');
					} else {
						nextTick(() => {
							if (getLastChat.value.afterStopAutoRead && isGlobalVoice.value) {
								getLastChat.value.voiceStatus = false;
								voiceEv({
									id: getLastChat.value.id,
									vType: 'play',
								});
							}
						});

						isLoading.value = false;
						getLastChat.value.loading = false;
					}
					console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
					console.log(getLastChat.value.msg);
					console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
				} else if (type === 'MoreCards') {
					getLastChat.value.temRecommedCards += text;
					//完成后的逻辑
					// 去除开头和结尾的 ```json 标记
					let trimmedText = getLastChat.value.temRecommedCards
						.trim()
						.replace(/^```json/, '')
						.replace(/```$/, '');
					// 解析 JSON 字符串为 JavaScript 对象或数组
					let jsonData = JSON.parse(trimmedText);
					console.log(JSON.parse(JSON.stringify(JSON.parse(trimmedText))), '更多卡片元数据');
					jsonData = formatParamsDates(jsonData);

					//isDateValid判断是否有日期没有日期则删除大模型返回的日期
					if (getLastChat.value.deleteTime) {
						jsonData.forEach((ele) => {
							delete ele.params?.startTime;
							delete ele.params?.endTime;
						});
					}
					console.log(jsonData, '格式化更多每个卡片的日期');
					jsonData.forEach((ele) => {
						getLastChat.value.recommedCards.push(ele);
					});
					console.log(getLastChat.value.recommedCards, '去重后更多卡片的数据');
				} else {
					getLastChat.value[fixedVariable[type][0]] += text;
					//如果时猜你想问和下一步行动
					// 去除开头和结尾的 ```json 标记
					let trimmedText = getLastChat.value[fixedVariable[type][0]]
						.trim()
						.replace(/^```json/, '')
						.replace(/```$/, '');
					console.log(trimmedText);

					getLastChat.value[fixedVariable[type][1]] = JSON.parse(trimmedText).data || JSON.parse(trimmedText);
					console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
					console.log(getLastChat.value[fixedVariable[type][1]], type);
					console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
				}
			}
			console.log(data);
		} catch (e) {
			console.error('解析响应失败：', e);
			return null; // 或根据需要处理错误
		}
	}
};
//发送信息
const setTextMessage = async (val, type = '') => {
	let isNotStream = val.type === 'cardDailyReport' ? false : true;
	try {
		let res = await fetchRequest(val, isNotStream);
		handleStream(res, 'cardOrMsg', val.type === 'cardDailyReport' ? isStream.value : false);
	} catch (e) {
		handleError(e);
	}
};
//获取summary
const getSummary = async (data) => {
	try {
		isLoading.value = true;
		getLastChat.value.summary = '';
		let res = await fetchRequest(data);
		handleStream(res, 'Summary', isStream.value);
	} catch (e) {
		handleError(e);
	}
};
//获取猜你想问
const getSuggestion = async (data) => {
	try {
		getLastChat.value.questionList = '';
		getLastChat.value.temQuestionList = '';
		let res = await fetchRequest(data, true);
		handleStream(res, 'Suggestion');
	} catch (e) {
		handleError(e);
	}
};
//获取下一步行动计划
const actionList = ['创建拜访计划', '记录实地拜访', '发送会议邀请', '传递关键资料'];
const getRandomActions = () => {
	const shuffled = actionList.slice().sort(() => 0.5 - Math.random());
	return shuffled.slice(0, Math.floor(Math.random() * 4) + 1);
};
const nextAction = async (data) => {
	try {
		if (relam === 'muqiao' || relam === 'roche') {
			return;
		}
		getLastChat.value.nextAction = [t('chat.cjbfjh')];
		// getLastChat.value.nextAction = '';
		// getLastChat.value.temNextAction = '';
		// let res = await fetchRequest(data);
		// handleStream(res, 'NextAction');
	} catch (e) {
		handleError(e);
	}
};
//获取更多卡片
const moreCards = async (data) => {
	try {
		getLastChat.value.recommedCards = [];
		getLastChat.value.temRecommedCards = '';
		let res = await fetchRequest(data, true);
		handleStream(res, 'MoreCards');
	} catch (e) {
		handleError(e);
	}
};
let handleError = (e) => {
	showToast({
		position: 'top',
		message: '请求失败,请重试',
	});
	isLoading.value = false;
	// 清除chatList最后一位
	chatList.value.splice(-1, 1);
	console.error(e);
};
const recommendCardClick = (card, index) => {
	if (isLoading.value) {
		stopFetch();
	}

	console.log(card);
	chatList.value.push(createUserMsg(card.name));
	// 生产一个ai的消息
	let message = {
		msg: '',
		dataId: '',
		type: 'ai',
		id: uuid(),
		loading: true,
		isBtns: true,
		reGen: true,
		voiceStatus: true,
		zeStatus: '0',
		isIframe: true,
		summaryLoading: true, //加载summary
		isLoading: false,
	};
	// 构建查询字符串
	const { params, url } = card;
	if (params) {
		const queryString = Object.entries(params)
			.map(([key, value]) => `${key}=${value}`)
			.join('&');
		message.iframeUrl = url + '?' + queryString;
	} else {
		message.iframeUrl = `${url}`;
	}
	message.url = url;
	message.iframeIndex = boxRefs.value.length;
	chatList.value.push(message);

	scrollBottom();
	getSuggestion({ type: 'cardSuggestion', Question: getLastUser() });
	// gd(val);
};
const cardList = [
	{
		name: '销售进度总览',
		url: `${appUrl}/card-library/salesAchievementStatus`,
		previewUrl: `${imgUrl}/public/销售进入总揽.png`,
	},
	{
		name: '销售趋势分析',
		url: `${appUrl}/card-library/saleTrend`,
		previewUrl: `${imgUrl}/public/销售趋势分析.png`,
	},
	{
		name: 'TOP医院销售分析',
		url: `${appUrl}/card-library/branchSalesOverview2`,
		previewUrl: `${imgUrl}/public/TOP医院分析.png`,
	},
	{
		name: '销售额排名分析',
		url: `${appUrl}/card-library/salesRankingDistribution`,
		previewUrl: `${imgUrl}/public/销售额排名分析.png`,
	},
];
let router = useRouter();
const toNextAction = (value) => {
	let action = {
		创建拜访计划: 'keyTaskDetail',
		记录实地拜访: 'mAssistant',
		发送会议邀请: 'toMeet',
		传递关键资料: 'visitArticle',
	};
	// 重命名键
	let newKey = t('chat.cjbfjh');
	action[newKey] = action['创建拜访计划'];
	if (action[value] === 'keyTaskDetail' || action[newKey] === 'keyTaskDetail') {
		router.push({
			name: 'keyTaskDetail',
			query: {
				isCreateTask: 'create',
				taskType: '线下拜访',
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else {
		router.push({
			name: action[value],
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	}
};
let isScrollIng = ref(false);
const scrollBottom = (t = 'smooth') => {
	isScrollIng.value = true;
	nextTick(() => {
		const aiCon = document.querySelector('#aiCon');
		aiCon.scrollTo({
			top: aiCon.scrollHeight,
			behavior: t,
		});
		setTimeout(() => {
			isScrollIng.value = false;
		}, 500);
	});
};
let showScrollDown = ref(false);
// 语音相关
const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let ttsWS = null;
const voiceEv = async (val) => {
	// chatList里所有的voiceStatus改位true
	resetAudioVoice();
	// 通过val.id获取chatList的相等元素
	const index = chatList.value.findIndex((item) => item.id === val.id);
	chatList.value[index].voiceStatus = val.vType === 'play' ? false : true;

	ttsWS?.close();
	audioPlayer.reset();
	if (val.vType === 'play') {
		// 开始语音播报
		const url = getWebSocketUrl(API_KEY, API_SECRET);
		if ('WebSocket' in window) {
			ttsWS = new WebSocket(url);
		} else if ('MozWebSocket' in window) {
			ttsWS = new MozWebSocket(url);
		} else {
			showToast({
				position: 'top',
				message: '浏览器不支持WebSocket',
			});
			return;
		}
		// 发送消息
		ttsWS.onopen = () => {
			audioPlayer.start({
				autoPlay: true,
				sampleRate: 16000,
				resumePlayDuration: 1000,
			});
			let text = document.getElementById(val.id).innerText;
			let tte = 'UTF8';
			let params = {
				common: {
					app_id: APPID,
				},
				business: {
					aue: 'raw',
					auf: 'audio/L16;rate=16000',
					vcn: 'x4_lingfeichen_assist', //发音人
					speed: 50, // 语速
					volume: 50, // 音量
					pitch: 50, // 音色
					bgs: 0,
					tte,
				},
				data: {
					status: 2,
					text: encodeText(text, tte),
				},
			};
			ttsWS.send(JSON.stringify(params));
		};

		ttsWS.onmessage = (e) => {
			console.log(e, '讯飞语音状态???????????????????????????????????');
			let jsonData = JSON.parse(e.data);
			// 合成失败
			if (jsonData.code !== 0) {
				showToast({
					position: 'top',
					message: '语音合成失败, 请稍后再试',
				});
				return;
			}
			audioPlayer.postMessage({
				type: 'base64',
				data: jsonData.data.audio,
				isLastData: jsonData.data.status === 2,
			});
			if (jsonData.code === 0 && jsonData.data.status === 2) {
				//0是成功2是最后一次
				ttsWS.close();
			}
		};

		ttsWS.onerror = (e) => {
			console.error(e);
		};
		ttsWS.onclose = () => {};
	}
};
const resetAudioVoice = () => {
	for (const i of chatList.value) {
		if (i.type === 'user') continue;
		if (i.isBtns === false) continue;
		if (i.voiceStatus === true) continue;
		i.voiceStatus = true;
	}
};

// 播放停止把voiceStatus改位true
audioPlayer.onStop = () => {
	resetAudioVoice();
};
const encodeText = (text, type) => {
	if (type === 'unicode') {
		let buf = new ArrayBuffer(text.length * 4);
		let bufView = new Uint16Array(buf);
		for (let i = 0, strlen = text.length; i < strlen; i++) {
			bufView[i] = text.charCodeAt(i);
		}
		let binary = '';
		let bytes = new Uint8Array(buf);
		let len = bytes.byteLength;
		for (let i = 0; i < len; i++) {
			binary += String.fromCharCode(bytes[i]);
		}
		return window.btoa(binary);
	} else {
		return Base64.encode(text);
	}
};
const getWebSocketUrl = (apiKey, apiSecret) => {
	var url = 'wss://tts-api.xfyun.cn/v2/tts';
	var host = location.host;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};

// 收到iframe传入的消息后处理卡片
const comFilterShow = ref(false);
let currentCom = ref('');
let cardInfo = ref({});

//加载异步组件
const { dom } = useChatCard();
console.log(dom);
const afterMessage = (event) => {
	if (event.data.source) return;
	console.log('=======================收到子iframe消息=======================');
	console.log(event);
	currentClickIndex.value = event.data.index;
	//默认值
	if (event.data.defaultValue) {
		chatList.value[event.data.index].defaultValue = event.data.defaultValue;
		console.log(chatList.value[event.data.index].defaultValue, '////');
	}
	//筛选
	if (event.data.type === 'openFilter') {
		console.log(event.data.comName);

		nextTick(async () => {
			currentCom.value = event.data.comName;
			cardInfo.value = cardsInfoList.value.find((ele) => ele.reportCd === event.data.comName);
			comFilterShow.value = true;
		});
	}
	//有明细信息展开按钮的卡片 更新卡片iframe高度
	if (event.data.type === 'updateClientHeight') {
		console.log(event.data.cardHeight);
		let parentIframeIndex = event.data.parentIframeIndex;

		// nextTick(() => {
		let targetIframe = boxRefs.value[parentIframeIndex];
		if (targetIframe) {
			console.log(targetIframe);
			targetIframe.style.height = event.data.cardHeight + 'px';
			chatList.value[event.data.index].iframeHeight = event.data.cardHeight + 'px';
		}
		// });
		return;
	}
	// 初始化获取内嵌页面的高度
	if (!event.data.cardHeight) return;
	const height = event.data.cardHeight + 'px';
	// 设置 iframe 的高度
	boxRefs.value[boxRefs.value.length - 1].style.height = height;
	getLastChat.value.iframeHeight = height;
	/* 	卡片加载完成后获取数据
  有数据进行下一步获取总结和猜你想问、下一步行动计划 */
	if (event.data.data) {
		let params = event.data.params;
		let ar = chatList.value.filter((ele) => ele.type === 'user');
		let msg = ar?.[ar.length - 1]?.msg;
		getSummary({
			type: 'cardComSummary',
			Question: msg,
			data: { ...event.data.data },
			params,
		});
		nextAction({ type: 'cardNextAction', Question: msg });
		getLastChat.value.iframeLoadingFinished = true;
	}
	if (event.data.noSummary) {
		getLastChat.value.summaryLoading = false;
		getLastChat.value.loading = false;
		getLastChat.value.isLoading = false;
		isLoading.value = false;
		let iframeUrl = addOrUpdateQueryParameter(getLastChat.value.iframeUrl, 'summary', 0);
		asyncChatLog(getLastUser(), '暂无数据或权限', { ...getLastChat.value, iframeUrl }, 'QA');
	}
};
//iframe创建成功后进行通信
const iframeLoad = (index, target) => {
	// setTimeout(() => {
	let iframeIndex = chatList.value[index].iframeIndex;
	console.log('当前加载的iframe位于所有iframe里的index', iframeIndex);
	console.log(target.contentWindow, '>>>>>>');
	target.contentWindow.postMessage({ type: 'sendIndex', index, iframeIndex }, '*');
	// }, 1000);
};
//创建新分析
const currentClickIndex = ref(0);
const comRef = ref(null);
const createNewAnalysis = () => {
	comFilterShow.value = false;
	console.log(comRef.value.query);
	// return;
	if (isLoading.value) {
		stopFetch();
	}
	chatList.value.push(createUserMsg('基于新的数据范围帮您分析'));
	// 生产一个ai的消息
	let message = {
		msg: '',
		dataId: '',
		type: 'ai',
		id: uuid(),
		loading: true,
		isBtns: true,
		reGen: true,
		voiceStatus: true,
		zeStatus: '0',
		isIframe: true,
		summaryLoading: true, //加载summary
		isLoading: false,
	};
	// 构建查询字符串
	if (comRef.value.query) {
		let newQuery = JSON.parse(JSON.stringify(comRef.value.query));
		newQuery.startTime = `${newQuery.startDate?.slice(0, 4)}-${newQuery.startDate?.slice(4, 6)}-01`;
		newQuery.endTime = `${newQuery.endDate?.slice(0, 4)}-${newQuery.endDate?.slice(4, 6)}-01`;
		newQuery.brandName = (newQuery.productName ? newQuery.productName : newQuery.brandName) || '';
		newQuery.brandId = (newQuery.productId ? newQuery.productId : newQuery.brandId) || '';

		delete newQuery.startDate;
		delete newQuery.endDate;
		delete newQuery.productName;
		delete newQuery.productId;

		console.log(newQuery);
		let url = chatList.value[currentClickIndex.value].url;
		// 去掉 URL 中的查询参数，获取基础路径
		const baseUrl = url?.includes('?') ? url.split('?')[0] : url;
		const queryString = Object.entries(newQuery)
			.map(([key, value]) => `${key}=${value}`)
			.join('&');
		message.url = baseUrl;
		message.iframeUrl = baseUrl + '?' + queryString;
		message.iframeIndex = boxRefs.value.length;
	}
	chatList.value.push(message);
	scrollBottom();
	getSuggestion({ type: 'cardSuggestion', Question: '基于新的数据范围进行分析' });
};
const init = async () => {
	// 获取当前月的第一天和最后一天（修复时区问题）
	function getCurrentMonthRange() {
		const now = new Date();
		// 当月第一天
		const start = new Date(now.getFullYear(), now.getMonth(), 1);
		const startLocalDate = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-${String(start.getDate()).padStart(2, '0')}`;
		// 当月最后一天
		const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
		const endLocalDate = `${end.getFullYear()}-${String(end.getMonth() + 1).padStart(2, '0')}-${String(end.getDate()).padStart(2, '0')}`;

		return { startLocalDate, endLocalDate };
	}

	// 动态获取日期范围
	const { startLocalDate, endLocalDate } = getCurrentMonthRange();
	let startDate = cardsInfoList.value.filter((item) => item.reportCd === 'salesAchievementStatus')[0]?.startTime || startLocalDate;
	let endDate = cardsInfoList.value.filter((item) => item.reportCd === 'salesAchievementStatus')[0]?.endTime || endLocalDate;

	isLoading.value = true;
	chatList.value.push({
		...createAiMsg(t('common.gdprfy'), false),
		afterStopAutoRead: true,
	});
	let data = {};
	let territoryCode = [];
	if (relam === 'muqiao') {
		//如果是muqiao
		let value = '{"id":"salesTrendsOverview","text":"月度销售趋势分析","filterList":["org"],"filterInfo":[{"id":"org","dataRange":[],"dataDefault":["NCNSC"],"defaultList":[]}]}';
		const filterConfig = JSON.parse(value);
		const result = useOrg(filterConfig.filterInfo);
		if (result.personId) {
			territoryCode.push(result.personId);
		}
	}
	if (!userStore.permission.includes('MR')) {
		let [res3, res4, res5, res6] = await Promise.all([
			salesAchievementStatus({
				nowLocalDateTime: startDate,
				endLocalDate: endDate,
				territoryCode,
			}),
			getTeamSalesTrends({
				startLocalDate: cardsInfoList.value.filter((item) => item.reportCd === 'salesAnalysisTeam')[0]?.startTime || startLocalDate,
				endLocalDate: cardsInfoList.value.filter((item) => item.reportCd === 'salesAnalysisTeam')[0]?.endTime || endLocalDate,
				territoryCode,
			}),
			branchHospitalSalesOverviewAmount({
				type: 'sales',
				startLocalDate: cardsInfoList.value.filter((item) => item.reportCd === 'branchSalesOverview2')[0]?.startTime || startLocalDate,
				endLocalDate: cardsInfoList.value.filter((item) => item.reportCd === 'branchSalesOverview2')[0]?.endTime || endLocalDate,
				territoryCode,
			}),
			ddiTransactionSales(
				{
					startLocalDate: cardsInfoList.value.filter((item) => item.reportCd === 'ddi')[0]?.startTime || startLocalDate,
					endLocalDate: cardsInfoList.value.filter((item) => item.reportCd === 'ddi')[0]?.startTime || endLocalDate,
					territoryCode,
				},
				relam
			),
		]);
		const keyMap2 = {
			salesMtd: '月销售额',
			salesRateMtd: '月销售达成率',
			yoyMtd: '月同比增长率',
			momMtd: '月环比增长率',
			salesRateQtd: '季度销售达成率',
			yoyQtd: '季度同比增长率',
			qoqQtd: '季度环比增长率',
			salesRateYtd: '年销售达成率',
			yoyYtd: '年同比增长率',
			hospSalesRate: '有销售额终端',
		};
		for (const [oldKey, newKey] of Object.entries(keyMap2)) {
			// eslint-disable-next-line no-prototype-builtins
			if (res3.result.hasOwnProperty(oldKey)) {
				res3.result[newKey] = res3.result[oldKey];
				delete res3.result[oldKey];
			}
		}

		const keyMap3 = {
			// buAvg: 'BU整体销售达成率',
			low: 'low',
			medium: 'medium',
			high: 'high',
			summarizeList: 'summarizeLists',
			empName: '下属名称',
			salesRateMtdOrder: '销售达成率排名',
			salesRateMtd: '销售达成率',
			max: '最大销售达成率',
			nextRole: '下属岗位',
		};
		// const renameKeys1 = (obj, keyMap) => {
		// 	if (Array.isArray(obj)) {
		// 		obj.forEach((item) => renameKeys(item, keyMap));
		// 	} else if (typeof obj === 'object' && obj !== null) {
		// 		Object.keys(obj).forEach((key) => {
		// 			if (keyMap[key]) {
		// 				obj[keyMap[key]] = obj[key];
		// 				delete obj[key];
		// 			}
		// 			if (typeof obj[keyMap[key]] === 'object' && obj[keyMap[key]] !== null) {
		// 				renameKeys(obj[keyMap[key]], keyMap);
		// 			}
		// 		});
		// 	}
		// };
		// Reflect.deleteProperty(res4.result, 'buAvg');
		// renameKeys1(res4.result, keyMap3);

		// 创建一个临时的DOM元素
		const tempElement = document.createElement('div');
		// 将HTML字符串设置为临时元素的innerHTML
		tempElement.innerHTML = res5.result.message;

		let res6Info = [];
		for (const item of res6.result.salesBrand) {
			res6Info.push({
				name: item.name,
				salesV: formatKNumber(item.salesV),
				targetV: formatKNumber(item.targetV),
				achievementRate: new Decimal(item.achievementRate * 100).toDecimalPlaces(0) + '%',
				yearOnYear: new Decimal(item.yearOnYear * 100).toDecimalPlaces(0) + '%',
				yearOnYearGrowth: formatKNumber(item.yearOnYearGrowth),
				chain: new Decimal(item.chain * 100).toDecimalPlaces(0) + '%',
				moMGrowth: formatKNumber(item.moMGrowth),
				salesVLm: item.salesVLm,
				salesVLy: item.salesVLy,
			});
		}

		const keyMap4 = {
			achievementRate: '销售达成率',
			chain: '环比增长率',
			moMGrowth: '环比净增长销售额',
			name: '产品名称',
			salesV: '销售金额',
			salesVLm: '上月同期销售金额',
			salesVLy: '去年同期销售金额',
			targetV: '销售目标金额',
			yearOnYear: '同比增长率',
			yearOnYearGrowth: '同比净增长销售额',
		};
		for (const [oldKey, newKey] of Object.entries(keyMap4)) {
			res6Info.forEach((ele) => {
				// eslint-disable-next-line no-prototype-builtins
				if (ele.hasOwnProperty(oldKey)) {
					ele[newKey] = ele[oldKey];
					delete ele[oldKey];
				}
			});
		}
		data = {
			//  ...res1.result, ...res2.result,
			...res3.result,
			...res4.result,
			分医院: tempElement.innerText,
			分产品: res6Info,
		};
		console.log(data);
	} else {
		let [res3, res5, res6] = await Promise.all([
			salesAchievementStatus({
				nowLocalDateTime: startDate,
				endLocalDate: endDate,
				territoryCode,
			}),
			branchHospitalSalesOverviewAmount({
				type: 'sales',
				startLocalDate: cardsInfoList.value.filter((item) => item.reportCd === 'branchSalesOverview2')[0]?.startTime || startLocalDate,
				endLocalDate: cardsInfoList.value.filter((item) => item.reportCd === 'branchSalesOverview2')[0]?.endTime || endLocalDate,
				territoryCode,
			}),
			ddiTransactionSales(
				{
					startLocalDate: cardsInfoList.value.filter((item) => item.reportCd === 'ddi')[0]?.startTime || startLocalDate,
					endLocalDate: cardsInfoList.value.filter((item) => item.reportCd === 'ddi')[0]?.startTime || endLocalDate,
					territoryCode,
				},
				relam
			),
		]);

		const keyMap2 = {
			salesMtd: '月销售额',
			salesRateMtd: '月销售达成率',
			yoyMtd: '月同比增长率',
			momMtd: '月环比增长率',
			salesRateQtd: '季度销售达成率',
			yoyQtd: '季度同比增长率',
			qoqQtd: '季度环比增长率',
			salesRateYtd: '年销售达成率',
			yoyYtd: '年同比增长率',
			hospSalesRate: '有销售额终端',
		};
		for (const [oldKey, newKey] of Object.entries(keyMap2)) {
			// eslint-disable-next-line no-prototype-builtins
			if (res3.result.hasOwnProperty(oldKey)) {
				res3.result[newKey] = res3.result[oldKey];
				delete res3.result[oldKey];
			}
		}

		// 创建一个临时的DOM元素
		const tempElement = document.createElement('div');
		// 将HTML字符串设置为临时元素的innerHTML
		tempElement.innerHTML = res5.result.message;

		let res6Info = [];
		for (const item of res6.result.salesBrand) {
			res6Info.push({
				name: item.name,
				salesV: formatKNumber(item.salesV),
				targetV: formatKNumber(item.targetV),
				achievementRate: new Decimal(item.achievementRate * 100).toDecimalPlaces(0) + '%',
				yearOnYear: new Decimal(item.yearOnYear * 100).toDecimalPlaces(0) + '%',
				yearOnYearGrowth: formatKNumber(item.yearOnYearGrowth),
				chain: new Decimal(item.chain * 100).toDecimalPlaces(0) + '%',
				moMGrowth: formatKNumber(item.moMGrowth),
				salesVLm: item.salesVLm,
				salesVLy: item.salesVLy,
			});
		}

		const keyMap4 = {
			achievementRate: '销售达成率',
			chain: '环比增长率',
			moMGrowth: '环比净增长销售额',
			name: '产品名称',
			salesV: '销售金额',
			salesVLm: '上月同期销售金额',
			salesVLy: '去年同期销售金额',
			targetV: '销售目标金额',
			yearOnYear: '同比增长率',
			yearOnYearGrowth: '同比净增长销售额',
		};
		for (const [oldKey, newKey] of Object.entries(keyMap4)) {
			res6Info.forEach((ele) => {
				// eslint-disable-next-line no-prototype-builtins
				if (ele.hasOwnProperty(oldKey)) {
					ele[newKey] = ele[oldKey];
					delete ele[oldKey];
				}
			});
		}

		data = {
			//  ...res1.result, ...res2.result,
			...res3.result,
			分医院: tempElement.innerText,
			分产品: res6Info,
		};
		console.log(data);
	}
	getLastChat.value.isLoading = true;
	//获取猜你想问
	// let msg = getLastUser();
	getSuggestion({ type: 'cardSuggestion', Question: { ...data } });
	nextAction({ type: 'cardNextAction', Question: { ...data } });
	setTextMessage({ ...data, type: 'cardDailyReport' });
};

const aiConScroll = (e) => {
	const container = e.target;
	const scrollTop = container.scrollTop;
	const scrollHeight = container.scrollHeight;
	const clientHeight = container.clientHeight;
	// 计算可滚动的高度
	const scrollableHeight = scrollHeight - clientHeight;

	// 如果当前滚动的位置小于可滚动的高度，表示页面没有滚动到底部
	const isScrollAtBottom = scrollTop + 50 >= scrollableHeight;
	if (!isScrollAtBottom) {
		// 其他处理滚动事件的代码
		requestAnimationFrame(() => {
			!isScrollIng.value && (showScrollDown.value = true);
		});
	} else {
		showScrollDown.value = false;
	}
};

const handleScroll = (e) => {
	const scrollTop = e.target.scrollTop;
	if (scrollTop === 0) {
		// 记录当前位置，下次滚动时恢复
		prevScrollHeight = e.target.scrollHeight - scrollTop - e.target.clientHeight;
		getHistoryList();
	}
};

let cardsInfoList = ref([]);
let isStream = ref(true);
const actions = ref([]);
function findNodeByPathName(tree, path) {
	// 处理空输入
	if (!tree || !Array.isArray(tree) || !path || typeof path !== 'string') {
		return null;
	}
	// 使用递归查找节点
	function searchNode(nodes) {
		for (const node of nodes) {
			if (node.path === path) {
				return node; // 返回节点本身
			}
			if (node.children && node.children.length > 0) {
				const result = searchNode(node.children);
				if (result) return result;
			}
		}
		return null;
	}
	// 从根节点开始搜索
	return searchNode(tree);
}
// 按钮组数据
const qtList = ref([]);

//更多问题
const questionsList = ref([]);
const questionListComRef = ref(null);
const moreQ = () => {
	questionListComRef.value.openPopup();
};
const setQ = (v) => {
	if (Array.isArray(v) && v.length >= 3) {
		const shuffled = v.sort(() => 0.5 - Math.random());
		questionsList.value = shuffled.slice(0, 3);
	} else {
		questionsList.value = v;
		console.warn('传入数据不足三条或格式不正确:', v);
	}
};
const qClick = (item) => {
	recommendCardClick(item);
};
onMounted(async () => {
	let al = findNodeByPathName(perStore.agentList, 'scriptAssistant1');
	actions.value = al?.children.map((ele) => {
		return {
			img: ele.icon,
			name: ele.name,
			desc: ele.shortName || '',
			url: ele.api === 'path' ? `${ele.path}` : `${appUrl}${ele.path}`,
			type: ele.api || 'card',
		};
	});
	// 常用问题
	qtList.value.push({
		img: '',
		name: '业绩播报',
		desc: '',
		url: '',
	});
	if (actions.value && actions.value.length > 0) {
		for (const ele of actions.value) {
			if (ele.type === 'card' && qtList.value.length < 4) {
				qtList.value.push(ele);
			}
		}
	}
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
	// 智能体基本信息
	const agentList = await getAgentListApi();
	for (const ele of agentList.result.content) {
		if (ele.attributes.url.indexOf('scriptAssistant1') > -1) {
			appId = ele.attributes.appID;
			appKey = ele.attributes.appKey;
			welcomeText = ele.attributes?.welcomeText || welcomeText;
			agentName.value = ele.name;
			inForUse.value = ele.attributes?.inForUse || '';
			ele.attributes.stream ? (isStream.value = JSON.parse(ele.attributes.stream)) : '';
			isMultiChat.value = ele.attributes?.isMultiChat === 'single' ? false : true;
			break;
		}
	}
	//获取所有卡片的开始日期和结束日期
	let results = await queryList({ reportType: 'CARD' });
	let combinedResults = results.result;
	let res = await getReports(['salesTrendsOverview', 'salesAnalysisTeamReport', 'salesTrendBrandReport', 'topHospitalSalesAnalysis', 'hospitalAnalysis']);
	let combinedResults1 = res.result.flatMap((ele) => ele.reportVOList).filter((ele) => ele.reportCd === 'hospitalAnalysis' || ele.reportCd === 'salesPerformanceProductivity');
	cardsInfoList.value = [...combinedResults, ...combinedResults1];
	console.log(cardsInfoList.value);
	const { updateField } = useCardLibraryLocalStorage();
	updateField('cardsInfoList', cardsInfoList.value);
	window.addEventListener('message', afterMessage);
	nextTick(() => {
		const aiCon = document.querySelector('#aiCon');
		aiCon.addEventListener('scroll', aiConScroll);
		aiCon.addEventListener('scroll', handleScroll);
	});

	/*
	 * 1. 如果是多会话，获取历史会话记录
	 * 2. 如果是多会话，随机生成一个会话，单会话取userId
	 */
	if (isMultiChat.value) {
		getConversChatList();
		chatId1.value = route.query.chatId ? route.query.chatId : 'xsdc' + getRandomString();
	} else {
		chatId1.value = userStore.userInfo.username;
	}
	pageNum = -1;
	historyOver.value = false;
	loadingHistory.value = false;
	await getHistoryList();
});
onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
	// 停止当前请求
	stopFetch();
	// 关闭语音
	resetAudioVoice();
	ttsWS?.close();
	audioPlayer.reset();
});

onUnmounted(() => {
	window.removeEventListener('message', afterMessage);
	window.removeEventListener('scroll', aiConScroll);
});
onBeforeRouteLeave(() => {
	const aiCon = document.querySelector('#aiCon');
	aiCon.removeEventListener('scroll', handleScroll);
});

const getRole = computed(() => {
	let roles = ['ADMIN', 'BUD', 'R SD', 'RSM', 'DSM', 'MR'];
	return userStore.permission.find((ele) => roles.includes(ele));
});
const copyText = (id) => {
	const clipboard = new ClipboardJS('.copy-btn', {
		target: function () {
			return document.getElementById(id);
		},
	});

	clipboard.on('success', () => {
		showToast({
			position: 'top',
			message: '复制成功',
		});
		clipboard.destroy();
	});
};

const boxRefs = ref([]);
const setRef = (el, index) => {
	if (el) {
		if (!boxRefs.value.includes(el)) {
			boxRefs.value.push(el);
			el.id = boxRefs.value.length - 1;
		}
	}
};
defineExpose({ getRole });
</script>
<style lang="scss" scoped>
@import '../../style/pc/intelligent/scriptAssistant/index.scss';
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
			.history-img {
				margin-left: 12px;
				margin-right: 24px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
			display: flex;
			align-items: center;
			justify-content: center;
			flex: 1;
			width: 265px;
			span {
				white-space: nowrap;
			}
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-list {
			flex: 1 0 0px;
			height: 0px;
			padding: 0 0;
			padding-top: 12px;
			overflow-y: auto;
			.mc-content-list-left {
				display: flex;
				flex-direction: column;
				width: 100%;
				margin-bottom: 12px;
				.mc-content-template {
					border-top-left-radius: 8px;
					border-bottom-right-radius: 8px;
					border-top-right-radius: 8px;
				}
				img {
					width: 26px;
					height: 26px;
					margin-right: 6px;
					margin-top: 6px;
				}

				.mc-content__groupList {
					padding: 8px 0 0 0;
					padding-left: 15px;
					display: flex;
					display: flex;
					overflow-x: auto;
					width: 100%;
					span {
						flex: none;
						margin-right: 8px;
						margin-bottom: 8px;
						color: #0b67e4;
						border-radius: 5px;
						border: 1px solid #0b67e4;
						padding: 3px 6px;
					}
				}
			}

			.mc-content-list-right {
				padding-right: 15px;
				display: flex;
				margin-bottom: 12px;
				justify-content: flex-end;
				.mc-content-template {
					border-top-right-radius: 8px;
					border-bottom-left-radius: 8px;
					border-top-left-radius: 8px;
					color: var(--ac-bg-color--fff);
					background-color: var(--ac-bg-active-color);
					.mc-content-template__edit {
						border-top: solid 1px rgba(223, 225, 230, 0.3);
						margin-top: 2px;
					}
				}
			}
			.mc-content-template {
				max-width: 330px;
				min-width: 52px;
				background-color: var(--ac-bg-color--fff);
				padding: 10px;
			}
		}

		// 声明
		.mc-declare {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 6px 0px 3px 0px;
			font-size: 12px;
			color: var(--ac-colors-myGray-500);
		}

		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 8px;
			padding-bottom: 6px;
			min-height: 64px;
			position: relative;
			display: flex;
			align-items: flex-start;
		}
	}

	.mc-content__h5 {
		height: calc(100vh - 44px);
	}

	@supports (height: 100dvh) {
		.mc-content__h5 {
			height: calc(100dvh - 11.733333vw); /* 更好适配移动端软键盘变化 */
		}
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
		background-color: #f4f5f7;
	}
	&:deep(.cai-action-sheet) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 0;
		.van-action-sheet__header {
			font-size: 15px;
			color: #172b4d;
			font-weight: 500;
		}
		i {
			font-size: 16px;
			color: #000000;
		}

		.van-cell {
			background-color: #f4f5f7;
			margin: 0 15px 15px;
			width: calc(100% - 30px);
			border-radius: 6px;
			height: 125px;
		}

		.van-button {
			width: 238px;
			height: 40px;
			border-radius: 21px;
			font-size: 17px;
			margin-bottom: 20px;
			margin-left: 68.5px;
		}
	}
}
.btns {
	width: 100%;
	height: 35px;
	line-height: 35px;
	border-radius: 5px;
	background-color: #ffffff;
	color: #172b4d;
	margin-top: 10px;
	padding-left: 12px;
	padding-right: 15px;
	display: flex;
	align-items: center;

	span {
		flex: 1;
	}

	.right {
		width: 8px;
		height: 8px;
		border-top: 1px solid #6b778c;
		border-right: 1px solid #6b778c;
		transform: rotate(45deg);
	}
}
.par {
	margin-left: 15px;
}
.ql {
	margin-top: 12px;
	.nk {
		margin-left: 15px;
		color: #6b778c;
		font-size: 12px;
	}
	.mc-content__groupList {
		scrollbar-width: none; /* firefox */
		-ms-overflow-style: none; /* IE 10+ */
		&::-webkit-scrollbar {
			display: none; /* Chrome Safari */
		}
	}
}
.ifr {
	width: 100%;
	border: none;
	height: 1px;
	border-radius: 5px;
	background-color: var(--ac-bg-color--fff);
	min-width: 310px;
}
.scroll-down {
	position: fixed;
	bottom: calc(65px + constant(safe-area-inset-bottom)); //兼容 IOS<11.2
	bottom: calc(65px + env(safe-area-inset-bottom));
	left: 50%;
	transform: translateX(-50%);
	img {
		width: 29px;
		height: 29px;
	}
}
// 华东动画
.slide-enter-active,
.slide-leave-active {
	transition: all 0.2s ease-in;
}
.slide-enter-from,
.slide-leave-to {
	transform: translate(-50%, 25px);
	transform: translate(-50%, 25px);
	opacity: 0;
}
.recommend-cards {
	margin-bottom: 15px;
	span {
		color: #172b4d;
	}
	&-list {
		display: flex;
		margin-top: 8px;
		justify-content: flex-start;
	}
	&-item {
		flex: 0.33;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-right: 9px;
		img {
			width: 96px !important;
			height: 60px !important;
			margin-right: 0 !important;
			margin-top: 0 !important;
		}
		.name {
			color: #172b4d;
			font-size: 10px;
			margin-top: 4px;
		}
	}
}
.no-found-card {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	&-item {
		display: flex;
		flex-direction: column;
		margin-top: 8px;
		img {
			width: 150px !important;
			height: 94px !important;
			margin-right: 0 !important;
			margin-top: 0 !important;
		}
		span {
			color: #172b4d;
			font-size: 10px;
			margin-top: 8px;
		}
	}
}
::v-deep(.van-overlay) {
	background: rgba(0, 0, 0, 0.5);
	.wrapper {
		border-radius: 6px;
		background-color: #f4f5f7;
		// width: 90%;
		position: relative;
		top: 20%;
		left: 50%;
		transform: translateX(-50%);
		padding-bottom: 18px;
		.card-title {
			display: none;
		}
		.update-box {
			display: none;
		}
		.coms {
			max-height: 400px;
			overflow-y: auto;
		}
		.operation {
			display: flex;
			padding: 0 22px;
			div {
				width: 145px;
				flex: 1;
				line-height: 44px;
				border-radius: 6px;
				text-align: center;
				box-shadow: 0px 2px 9px 0px rgba(0, 82, 204, 0.1);
				font-size: 13px;
			}
			.cancel {
				color: #0052cc;
				background-color: rgba(0, 82, 204, 0.1);
				margin-right: 13px;
			}
			.confirm {
				background-color: #0052cc;
				color: #fff;
			}
		}
		&::before {
			content: '';
			display: block;
			width: 3%;
			height: 100%;
			position: absolute;
			left: 0;
			top: 0;
			background: rgba(0, 0, 0, 0.5);
		}
		&::after {
			content: '';
			display: block;
			width: 3%;
			height: 100%;
			position: absolute;
			right: 0;
			top: 0;
			background: rgba(0, 0, 0, 0.5);
		}
	}
}
@keyframes dotAnimation {
	0% {
		content: '';
	}
	33% {
		content: '.';
	}
	66% {
		content: '..';
	}
	100% {
		content: '...';
	}
}

.dots::after {
	content: '...';
	display: inline-block;
	animation: dotAnimation 1s infinite steps(3);
}
.q-content {
	.q-title {
		display: flex;
		justify-content: space-between;
		margin-bottom: 4px;
		span:nth-child(1) {
			font-weight: bold;
			font-size: 14px;
			color: #172b4d;
		}
		span:nth-child(2) {
			color: #0052cc;
		}
	}
	.q-list {
		// background: #ffffff;
		border-radius: 5px;
		margin-bottom: 8px;
		padding: 9px 12px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-right: 35px;
		color: #0b67e4;
		border: 1px solid #0b67e4;
		&::after {
			content: '';
			width: 7px;
			height: 7px;
			border-top: 1px solid #0b67e4;
			border-right: 1px solid #0b67e4;
			transform: rotate(45deg);
		}
	}
}
</style>
