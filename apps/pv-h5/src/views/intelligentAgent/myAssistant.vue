<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('chat.myAssistant') }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">
				<img @click="acGlobalVoice(false)" v-show="isGlobalVoice" src="@/assets/img/global-voice-active.png" />
				<img @click="acGlobalVoice(true)" v-show="!isGlobalVoice" src="@/assets/img/global-voice.png" />
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div id="aiCon" class="mc-content-list clearfix">
				<template v-for="(item, index) in chatList">
					<template v-if="item.type === 'user'">
						<div :key="item.id" class="mc-content-list-right">
							<!-- 用户内容模版 -->
							<div @click="editorMsg(index, item)" class="mc-content-template">
								<!-- 纯文本 -->
								<p v-html="item.msg"></p>
								<!-- 是否可以修改 -->
								<div class="mc-content-template__edit" v-if="index === chatList.length - 2"></div>
							</div>
						</div>
					</template>
					<template v-else-if="item.type === 'middle'">
						<div :key="item.id" class="mc-content-middle">
							<!-- 头像 -->
							<img class="mc-content-middle__avater" src="@/assets/img/myAssistant.png" />
							<!-- 描述 -->
							<div class="mc-content-middle__desc">Hi，我是您的私人工作助手，您可以下达“我要报销”的指令，也可以随时询问工作中的疑问，我将帮您更快更好的完成日常工作。您现在想做些什么呢？</div>
							<!-- 常用应用 -->
							<div class="mc-content-middle__app">
								<div class="app-header">
									<img class="app-header__img" src="@/assets/img/demo/app_icon.png" />
									<span class="app-header__name">您经常使用的应用</span>
								</div>
								<div class="app-bottom">
									<div class="app-bottom__item" @click="goAppPage('记录拜访')">
										<div class="item-top">
											<img class="item-top__img" src="https://minioprod.pharmbrain.com/demo/icon/拜访记录1718096792.png" />
										</div>
										<span class="item-text">记录拜访</span>
									</div>
									<div class="app-bottom__item" @click="goAppPage('客户360')">
										<div class="item-top">
											<img class="item-top__img" src="https://minioprod.pharmbrain.com/demo/icon/编组 201718096306.png" />
										</div>
										<span class="item-text">客户360</span>
									</div>
									<div class="app-bottom__item" @click="goAppPage('资料推广')">
										<div class="item-top">
											<img class="item-top__img" src="https://minioprod.pharmbrain.com/demo/icon/资料推广1718097178.png" />
										</div>
										<span class="item-text">资料推广</span>
									</div>
									<div class="app-bottom__item" @click="goAppPage('行为概览')">
										<div class="item-top">
											<img class="item-top__img" src="https://minioprod.pharmbrain.com/demo/icon/销售行为概览1718098451.png" />
										</div>
										<span class="item-text">行为概览</span>
									</div>
									<div @click="goOther" class="app-bottom__item">
										<div class="item-top">
											<img src="@/assets/img/demo/app-10.png" />
										</div>
										<span class="item-text">更多</span>
									</div>
								</div>
							</div>
						</div>
					</template>
					<template v-else>
						<div :key="item.id" class="mc-content-list-left">
							<div style="display: flex">
								<!-- AI 头像 -->
								<img src="@/assets/img/demo/ai.png" />
								<!-- AI 文本 内容模版 -->
								<div class="mc-content-template">
									<!-- 纯文本 -->
									<ai-text :id="item.id" :is-loading="item.loading" :msg="item.msg"></ai-text>
									<!-- 小按钮组 - 语音播报和复制 -->
									<ai-btns v-if="item.isBtns && !item.loading" :id="item.id" :zeStatus="item.zeStatus" @_ze="ze" :voiceStatus="item.voiceStatus" @copyText="copyText" @voiceEv="voiceEv"></ai-btns>
								</div>
							</div>
						</div>
					</template>
				</template>
			</div>
			<!-- 声明 -->
			<div v-if="inForUse" class="mc-declare">{{ inForUse }}</div>
			<!-- 按钮组 -->
			<div class="mc-btns">
				<div v-for="(i, index) in qtList" :key="index" @click="qesMoreEv(i)" class="mc-btns-item">
					{{ i }}
				</div>
			</div>
			<!-- 聊天框 -->
			<div ref="chatBox" class="mc-content-chat">
				<ai-textarea :editorText="editorText" :sMenu="false" :ol="false" :toolsList="[]" @_sendMessage="sendMessage" @_changeEdit="changeEdit"></ai-textarea>
			</div>
		</div>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import { getToken } from '@/utils/auth';
import useUserStore from '@/store/modules/user';
import usefilterStore from '@/store/modules/filter';
import ClipboardJS from 'clipboard';
import CryptoJS from 'crypto-js';
import AudioPlayer from '@/utils/ap';
import axios from 'axios';
import { useClickAway } from '@vant/use';
import { getAgentListApi } from '@/api/agent-tools';
const { proxy } = getCurrentInstance();
const audioPlayer = new AudioPlayer();
const filterStore = usefilterStore();
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
// 接口地址
const interfaceUrl = enterStore.enterInfo.interfaceAddress;
const domain = enterStore.enterInfo.domain;
const isGlobalVoice = computed(() => {
	return filterStore.isGlobalVoice;
});
const inForUse = ref('');
const acGlobalVoice = (val) => {
	filterStore.SET_IS_GLOBAL_VOICE(val);
};

const qtList = ref(['我的客户', '我的指标', '计划与任务', '快学快练']);

const qesMoreEv = (val) => {
	if (val === '快学快练') {
		router.push({
			name: 'intelligenceAccompanyingPractice',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else if (val === '我的客户') {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/mobile/doctor/doctorList',
				title: '客户列表',
				module: 'third',
			});
		} else {
			location.href = domain + '/mobile/doctor/doctorList';
		}
	} else if (val === '计划与任务') {
		router.push({
			name: 'keyTask',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else {
		router.push({
			name: 'myMetrics',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	}
};

// 跳转到目标应用
const goAppPage = (name) => {
	switch (name) {
		case '记录拜访':
			router.push({
				name: 'mAssistant',
				query: {
					isTabBar: route.query.isTabBar || '',
				},
			});
			break;
		case '行为概览':
			if (route.query.isTabBar === 'false') {
				proxy.$cHandler.openAppView({
					url: domain + '/demo/conduct/conductView/overview?flag=1&action=0',
					title: '行为概览',
					module: 'third',
				});
			} else {
				location.href = domain + '/demo/conduct/conductView/overview?flag=1&action=0';
			}
			break;
		case '资料推广':
			if (route.query.isTabBar === 'false') {
				proxy.$cHandler.openAppView({
					url: domain + 'mobile/contentCenter/contentCenterIndex/marketKnowledge',
					title: '资料推广',
					module: 'third',
				});
			} else {
				location.href = domain + 'mobile/contentCenter/contentCenterIndex/marketKnowledge';
			}
			break;
		case '客户360':
			if (route.query.isTabBar === 'false') {
				proxy.$cHandler.openAppView({
					url: domain + '/mobile/doctor/doctorList',
					title: '客户360',
					module: 'third',
				});
			} else {
				location.href = domain + '/mobile/doctor/doctorList';
			}
			break;
	}
};

const goOther = () => {
	if (route.query.isTabBar === 'false') {
		proxy.$cHandler.openAppView({
			url: domain + '/mobile/homePage',
			title: '应用中心',
			module: 'third',
		});
	} else {
		location.href = domain + '/mobile/homePage';
	}
};

// 返回
const goBack = () => {
	router.go(-1);
};

const lineVoice = () => {
	showToast({
		position: 'top',
		message: '暂未开通',
	});
};

const route = useRoute();
// 创建拜访计划
const router = useRouter();
const salesQk = () => {
	router.push({
		name: 'visitPlan',
	});
};

const resetAudioVoice = () => {
	for (const i of chatList.value) {
		if (i.type === 'user') continue;
		if (i.isBtns === false) continue;
		if (i.voiceStatus === true) continue;
		i.voiceStatus = true;
	}
};

// 播放停止把voiceStatus改位true
audioPlayer.onStop = () => {
	resetAudioVoice();
};

const userStore = useUserStore();

const isLoading = ref(false);
const chatList = ref([]);

// 赞和踩
const ze = (val) => {
	const j = chatList.value.filter((ele) => ele.id === val.id)[0];
	const json = {
		message_id: j.dataId,
		feedback_score: '',
	};
	j.zeStatus = val.like;
	if (val.like === '2') {
		// 踩
		json.feedback_score = 0;
	} else if (val.like === '1') {
		// 赞
		json.feedback_score = 100;
	} else {
		json.feedback_score = -1;
	}
	axios({
		url: interfaceUrl + '/API-GPT/chat/message/update_history_feedback',
		method: 'get',
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
		params: json,
	});
};

// 发送消息
const sendMessage = (val) => {
	if (isEdit.value) {
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isEdit.value = false;
	}
	if (!isLoading.value) {
		if (val) {
			isLoading.value = true;
			val = val.replace(/\n/g, '<br/>');
			chatList.value.push({
				msg: val,
				type: 'user',
				id: Date.now(),
				loading: false,
			});
			// 生产一个ai的消息
			chatList.value.push({
				msg: '',
				type: 'ai',
				dataId: '',
				templateType: 'text',
				id: Date.now() + 1,
				loading: true,
				isBtns: true,
				zeStatus: '0',
				voiceStatus: true,
			});
			nextTick(() => {
				scrollBottom();
			});
			setAiMessage(val);
		}
	}
};

// 发送的消息是否是二次编辑的消息
const isEdit = ref(false);
const editorText = ref('');
const editorMsg = (index, item) => {
	if (index === chatList.value.length - 2) {
		// 如果请求正在进行需要终止请求
		if (isLoading.value) {
			controller?.abort();
			chatList.value[chatList.value.length - 1].loading = false;
			chatList.value[chatList.value.length - 1].msg = chatList.value[chatList.value.length - 1].msg + '...';
			isLoading.value = false;
		}
		let msg = item.msg.replace(/<br\s*\/?\s*>/gi, '\n');
		editorText.value = msg + 'jjjjjj' + new Date().getTime();
		isEdit.value = true;
	}
};

const changeEdit = () => {
	isEdit.value = false;
	editorText.value = '';
};

// 文本框失去焦点
const chatBox = ref();
useClickAway(
	chatBox,
	() => {
		if (isEdit.value) {
			editorText.value = '';
		}
		isEdit.value = false;
	},
	{ eventName: 'touchend' }
);

let controller = '';
const setAiMessage = async (val) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = val || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/chat/chat_bot?question=${message}&user_id=${userId}&doctor_id=''&conversation_id=${chatId}&stream=true`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					// json解析失败的原因是后端返回了两个json数据，这里需要做合并
					let chunkArr = '[' + chunk + ']';
					chunkArr = mergeJson(chunkArr, '"end":false}}{"');
					let msg = '';
					for (let i = 0; i < chunkArr.length; i++) {
						msg += chunkArr[i].data.output;
					}
					data = chunkArr[chunkArr.length - 1];
					data.data.output = msg;
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 如果打开全局的语音播报，这里要触发语音播报
						if (isGlobalVoice.value && chatList.value[chatList.value.length - 1].msg) {
							nextTick(() => {
								voiceEv({
									id: chatList.value[chatList.value.length - 1].id,
									vType: 'play',
								});
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 合并多个json
const mergeJson = (chunkArr, svg) => {
	let nim = chunkArr.indexOf(svg);
	if (nim > 0) {
		chunkArr = chunkArr.substring(0, nim + 13) + ',' + chunkArr.substring(nim + 13);
		return mergeJson(chunkArr, svg);
	} else {
		chunkArr = JSON.parse(chunkArr);
		return chunkArr;
	}
};

// 请求或者解析失败提示并删除消息
const errorFun = (e) => {
	if (e.message !== 'signal is aborted without reason') {
		showToast({
			position: 'top',
			zIndex: 8888,
			message: '请求失败,请重试',
		});
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isLoading.value = false;
	}
};

const scrollBottom = () => {
	document.querySelector('#aiCon').scrollTo({
		top: document.querySelector('#aiCon').scrollHeight,
		behavior: 'smooth',
	});
};

const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let ttsWS = null;
const voiceEv = async (val) => {
	// chatList里所有的voiceStatus改位true
	resetAudioVoice();
	// 通过val.id获取chatList的相等元素
	const index = chatList.value.findIndex((item) => item.id === val.id);
	chatList.value[index].voiceStatus = val.vType === 'play' ? false : true;

	ttsWS?.close();
	audioPlayer.reset();
	if (val.vType === 'play') {
		// 开始语音播报
		const url = getWebSocketUrl(API_KEY, API_SECRET);
		if ('WebSocket' in window) {
			ttsWS = new WebSocket(url);
		} else if ('MozWebSocket' in window) {
			ttsWS = new MozWebSocket(url);
		} else {
			showToast({
				position: 'top',
				message: '浏览器不支持WebSocket',
			});
			return;
		}
		// 发送消息
		ttsWS.onopen = () => {
			audioPlayer.start({
				autoPlay: true,
				sampleRate: 16000,
				resumePlayDuration: 1000,
			});
			let text = document.getElementById(val.id).innerText;
			let tte = 'UTF8';
			let params = {
				common: {
					app_id: APPID,
				},
				business: {
					aue: 'raw',
					auf: 'audio/L16;rate=16000',
					vcn: 'x4_lingfeichen_assist', //发音人
					speed: 50, // 语速
					volume: 50, // 音量
					pitch: 50, // 音色
					bgs: 0,
					tte,
				},
				data: {
					status: 2,
					text: encodeText(text, tte),
				},
			};
			ttsWS.send(JSON.stringify(params));
		};

		ttsWS.onmessage = (e) => {
			let jsonData = JSON.parse(e.data);
			// 合成失败
			if (jsonData.code !== 0) {
				console.error(jsonData);
				showToast({
					position: 'top',
					message: '语音合成失败, 请稍后再试',
				});
				return;
			}
			audioPlayer.postMessage({
				type: 'base64',
				data: jsonData.data.audio,
				isLastData: jsonData.data.status === 2,
			});
			if (jsonData.code === 0 && jsonData.data.status === 2) {
				ttsWS.close();
			}
		};

		ttsWS.onerror = (e) => {
			console.error(e);
		};
		ttsWS.onclose = () => {};
	}
};

const encodeText = (text, type) => {
	if (type === 'unicode') {
		let buf = new ArrayBuffer(text.length * 4);
		let bufView = new Uint16Array(buf);
		for (let i = 0, strlen = text.length; i < strlen; i++) {
			bufView[i] = text.charCodeAt(i);
		}
		let binary = '';
		let bytes = new Uint8Array(buf);
		let len = bytes.byteLength;
		for (let i = 0; i < len; i++) {
			binary += String.fromCharCode(bytes[i]);
		}
		return window.btoa(binary);
	} else {
		return Base64.encode(text);
	}
};

const getWebSocketUrl = (apiKey, apiSecret) => {
	var url = 'wss://tts-api.xfyun.cn/v2/tts';
	var host = location.host;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};
const chatId = 'wdzs' + userStore.userInfo.username;
onMounted(() => {
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
	getAgentListApi().then((res) => {
		const agentList = res.result.content || [];
		for (const ele of agentList) {
			if (ele.attributes.url.indexOf('myAssistant') > -1) {
				inForUse.value = ele.attributes?.inForUse || '';
				break;
			}
		}
		init();
	});
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
	// 如果正在请求，取消请求
	if (isLoading.value) {
		controller?.abort();
	}
	//语音停止播放
	resetAudioVoice();
	ttsWS?.close();
	audioPlayer.reset();
});

// 初始化
const init = () => {
	chatList.value.push({
		type: 'middle',
		id: Date.now(),
	});
	// 滚动到底部
	nextTick(() => {
		scrollBottom();
	});
	if (route.query.text) {
		sendMessage(route.query.text);
	}
};

const copyText = (id) => {
	const clipboard = new ClipboardJS('.copy-btn', {
		target: function () {
			return document.getElementById(id);
		},
	});

	clipboard.on('success', () => {
		showToast({
			position: 'top',
			message: '复制成功',
		});
		clipboard.destroy();
	});
};
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-list {
			flex: 1 0 0px;
			overflow-y: auto;

			// midlle模版
			.mc-content-middle {
				padding: 0 15px;
				display: flex;
				flex-direction: column;
				.mc-content-middle__avater {
					width: 120px;
					height: 120px;
					margin: 0 auto;
					margin-bottom: 15px;
					margin-top: 15px;
				}
				.mc-content-middle__desc {
					border-radius: 5px;
					background-color: var(--ac-bg-color--fff);
					padding: 8px;
					margin-bottom: 15px;
					color: var(--ac-font-color);
				}

				.mc-content-middle__app {
					border-radius: 5px;
					background-color: #f4f5f7;
					padding: 12px;
					.app-header {
						display: flex;
						align-items: center;

						.app-header__img {
							width: 16px;
							height: 16px;
							margin-right: 5.4px;
						}

						.app-header__name {
							font-weight: 500;
						}
					}

					.app-bottom {
						display: flex;
						align-items: flex-start;
						justify-content: space-around;
						margin-top: 8px;

						.app-bottom__item {
							display: flex;
							flex-direction: column;
							align-items: center;

							.item-top {
								width: 40px;
								height: 40px;
								border-radius: 6px;
								background-color: #ffffff;
								display: flex;
								align-items: center;
								justify-content: center;

								img {
									width: 20px;
									height: 20px;
									object-fit: cover;
								}

								.item-top__img {
									width: 28px;
									height: 28px;
								}
							}

							.item-text {
								font-size: 12px;
								line-height: 1.2;
								margin-top: 8px;
								text-align: center;
							}
						}
					}
				}
			}

			.mc-content-list-left {
				display: flex;
				flex-direction: column;
				width: 100%;
				padding-left: 6px;
				float: left;
				margin-bottom: 12px;
				.mc-content-template {
					border-top-left-radius: 8px;
					border-bottom-right-radius: 8px;
					border-top-right-radius: 8px;
				}
				img {
					width: 26px;
					height: 26px;
					margin-right: 6px;
					margin-top: 6px;
				}
			}

			.mc-content-list-right {
				display: flex;
				width: 95%;
				float: right;
				padding-right: 6px;
				margin-bottom: 12px;
				justify-content: flex-end;
				.mc-content-template {
					border-top-right-radius: 8px;
					border-bottom-left-radius: 8px;
					border-top-left-radius: 8px;
					color: var(--ac-bg-color--fff);
					background-color: var(--ac-bg-active-color);

					.mc-content-template__edit {
						border-top: solid 1px rgba(223, 225, 230, 0.3);
						margin-top: 2px;
					}
				}
			}

			.mc-content-template {
				max-width: calc(95% - 32px);
				background-color: var(--ac-bg-color--fff);
				padding: 12px;
			}
		}
		// 声明
		.mc-declare {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 6px 0px 3px 0px;
			font-size: 12px;
			color: var(--ac-colors-myGray-500);
		}
		// 按钮组
		.mc-btns {
			width: 100%;
			margin: 0px auto;
			padding: 4px 6px;
			overflow-x: auto;
			display: flex;
			white-space: nowrap;
			.mc-btns-item {
				margin-right: 6px;
				border: 1px solid var(--ac-border-color);
				padding: 4px 10px;
				border-radius: 20px;
				font-size: 12px;
				color: var(--ac-font-color);
			}
		}
		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 8px;
			padding-bottom: 6px;
			min-height: 64px;
			position: relative;
			display: flex;
			align-items: flex-start;
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
}
</style>
