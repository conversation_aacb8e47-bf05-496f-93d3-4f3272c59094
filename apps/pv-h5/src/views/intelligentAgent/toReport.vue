<template>
	<div class="ma">
		<!-- header -->
		<div class="ma-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">实地拜访</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- content -->
		<div class="ma-content">
			<iframe :src="url" />
		</div>
	</div>
</template>
<script setup>
import { getToken } from '@/utils/auth';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
const domain = enterStore.enterInfo.domain;
const router = useRouter();
const route = useRoute();

const goBack = () => {
	router.go(-1);
};
const url = ref('');
onMounted(() => {
	const doctorId = route.query.doctorId || '';
	const visitType = route.query.visitType || '';
	const taskId = route.query.taskId || '';
	const endTime = route.query.endTime || '';
	const productIdList = route.query.productIdList || '';
	const visitResult = route.query.visitResult || '';
	const visitMemo = route.query.visitMemo || '';
	const visitPurpose = route.query.visitPurpose || '';
	const visitDuration = route.query.visitDuration || '';
	const visitTime = route.query.visitTime || '';
	url.value = `${domain}/mobile/visitReport/visitReportNew?token=${getToken()}&doctorId=${doctorId}&visitType=${visitType}&taskId=${taskId}&endTime=${endTime}&productIdList=${productIdList}&visitResult=${visitResult}&visitMemo=${visitMemo}&visitPurpose=${visitPurpose}&visitDuration=${visitDuration}&visitTime=${visitTime}`;
});
</script>
<style lang="scss" scoped>
.ma {
	height: 100vh;
	width: 100%;
	display: flex;
	flex-direction: column;
	.ma-header {
		width: 100vw;
		display: flex;
		height: 50px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid rgb(223, 225, 230);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
		}
	}

	.ma-content {
		height: calc(100vh - 44px);
		iframe {
			border: none;
			width: 100%;
			height: 100%;
			overflow-y: scroll;
		}
	}
}
</style>
