<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<div class="mc-header-name">{{ $t('chat.customerInsight1') }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">
				<img @click="acGlobalVoice(false)" v-show="isGlobalVoice" src="@/assets/img/global-voice-active.png" />
				<img @click="acGlobalVoice(true)" v-show="!isGlobalVoice" src="@/assets/img/global-voice.png" />
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div id="aiCon" class="mc-content-list clearfix">
				<div v-for="(item, index) in chatList" :key="item.id">
					<template v-if="item.type === 'user'">
						<div class="mc-content-list-right">
							<!-- 医生卡片 -->
							<div v-if="item.templateType === 'docInfo'" class="mc-content-info">
								<div style="margin-top: 0" class="docList-list-item">
									<img v-if="item.msg.doctorSex === '女'" src="@/assets/img/demo/gril.png" />
									<img v-else src="@/assets/img/demo/men.png" />
									<div class="amd-content-item-desc">
										<div>
											<span class="desc-name" style="margin-right: 8px">{{ item.msg.doctorName }}</span>
											<span>{{ item.msg.doctorTitle }}</span>
										</div>
										<div class="desc-hp ell1">
											<span style="margin-right: 8px">{{ item.msg.hospital }}</span>
											<span style="margin-right: 8px">{{ item.msg.department }}</span>
										</div>
									</div>
								</div>
							</div>
							<!-- 用户内容模版 -->
							<div v-else @click="editorMsg(index, item)" class="mc-content-template">
								<p>{{ item.msg }}</p>
								<!-- 是否可以修改 -->
								<div class="mc-content-template__edit" v-if="index === chatList.length - 2"></div>
							</div>
						</div>
					</template>
					<div v-else-if="item.templateType === 'docCard'" class="content-docList par">
						<div class="docList-list">
							<!-- header -->
							<div class="docList-list-header">
								<span class="docList-list-header__name">医生360</span>
							</div>
							<div style="margin-top: 8px" class="docList-list-item">
								<img v-if="item.msg.doctorSex === '女'" src="@/assets/img/demo/gril.png" />
								<img v-else src="@/assets/img/demo/men.png" />
								<div class="amd-content-item-desc">
									<div>
										<span class="desc-name" style="margin-right: 8px">{{ item.msg.doctorName }}</span>
										<span>{{ item.msg.doctorTitle }}</span>
									</div>
									<div class="desc-hp ell1">
										<span style="margin-right: 8px">{{ item.msg.hospital }}</span>
									</div>
								</div>
							</div>
						</div>
						<div class="docList-btns">
							<div @click="activeDoc(item.msg, '基本信息')" class="docList-btns-item">
								<span>基本信息</span>
								<van-icon size="12" color="#6B778C" name="arrow" />
							</div>
							<div @click="activeDoc(item.msg, '内部信息')" class="docList-btns-item">
								<span>内部信息</span>
								<van-icon size="12" color="#6B778C" name="arrow" />
							</div>
							<div @click="activeDoc(item.msg, '学术文章')" class="docList-btns-item">
								<span>学术文章</span>
								<van-icon size="12" color="#6B778C" name="arrow" />
							</div>
							<div @click="activeDoc(item.msg, '临床研究')" class="docList-btns-item">
								<span>临床研究</span>
								<van-icon size="12" color="#6B778C" name="arrow" />
							</div>
							<div @click="activeDoc(item.msg, '医生动态')" class="docList-btns-item">
								<span>医生动态</span>
								<van-icon size="12" color="#6B778C" name="arrow" />
							</div>
							<div @click="activeDoc(item.msg, '活动参与')" class="docList-btns-item">
								<span>活动参与</span>
								<van-icon size="12" color="#6B778C" name="arrow" />
							</div>
							<div @click="activeDoc(item.msg, '合作动态')" class="docList-btns-item">
								<span>合作动态</span>
								<van-icon size="12" color="#6B778C" name="arrow" />
							</div>
							<div @click="activeDoc(item.msg, '观念画像')" class="docList-btns-item">
								<span>观念画像</span>
								<van-icon size="12" color="#6B778C" name="arrow" />
							</div>
						</div>
					</div>
					<template v-else>
						<div :key="item.id" class="mc-content-list-left">
							<div class="par" style="display: flex">
								<!-- AI 文本 内容模版 -->
								<div class="mc-content-template">
									<loading1 v-if="item.isLoading" :text="item.loadingText"></loading1>
									<!-- 纯文本 -->
									<ai-text v-if="!item.isIframe && !item.isLoading" :id="item.id" :is-loading="item.loading" :msg="item.msg"></ai-text>
									<!-- 卡片 -->
									<div v-if="item.isIframe && !item.isLoading">
										<div v-if="!item.isLoading" style="margin-top: 5px">
											<iframe v-if="item.isInternalInfo" :src="item.iframeUrl" class="ifr1"></iframe>
											<iframe v-else class="ifr" :src="item.iframeUrl" :ref="(el) => setRef(el, index)"></iframe>
										</div>
										<!-- summary -->
										<div v-if="item.summary">
											<ai-text :id="item.id" :is-loading="item.summaryLoading" :msg="item.summary"></ai-text>
										</div>
									</div>
									<!-- 下一步行动计划 -->
									<div v-if="item.nextAction && showEtc(item)">
										<div class="btns toPlan" v-for="(item, index) in item.nextAction" :key="index" @click="toNextAction(item)">
											<span class="toPlan">
												<span v-if="item === 'Wecome沟通'">
													<strong style="color: #0b67e4">{{ item.slice(0, 6) }}</strong>
													<span> {{ item.slice(6) }}</span>
												</span>
												<span v-else>
													<strong style="color: #0b67e4">{{ item.slice(0, 2) }}</strong>
													<span> {{ item.slice(2) }}</span>
												</span>
											</span>
											<div class="right"></div>
										</div>
									</div>
									<!-- 小按钮组 - 语音播报和复制 -->
									<ai-btns v-if="item.isBtns && !item.loading && !item.isLoading" :id="item.id" :reGen="item.reGen" :zeStatus="item.zeStatus" @_ze="zeEv" @_reGen="reGen" :voiceStatus="item.voiceStatus" @copyText="copyText" @voiceEv="voiceEv"></ai-btns>
								</div>
							</div>
							<!-- 问题列表 -->
							<div class="ql" v-if="item.questionList && showEtc(item)">
								<div class="nk">您可能还想问</div>
								<div class="mc-content__groupList">
									<span v-for="val in item.questionList" :key="val" @click="qesEv(val, item)">{{ val }}</span>
								</div>
							</div>
						</div>
					</template>
				</div>
			</div>
			<!-- 按钮组 -->
			<div class="mc-btns">
				<div @click="docMore" class="mc-btns-item">查医生</div>
			</div>
			<!-- 聊天框 -->
			<div ref="chatBox" class="mc-content-chat">
				<!-- 跳转到别的智能体 -->
				<!-- 选择对话场景 -->
				<van-popover v-model:show="showPopover" placement="top-start" :offset="[8, 15]">
					<!-- 聊天类型 列表 -->
					<div class="chat-bottom-popover">
						<div v-for="item in actions" @click="chatTypeEv(item)" :key="item.type" :class="{ 'popover-item': true, 'popover-item__permission': !item.permission, 'popover-item__active': item.type === chatType }">
							<img :src="item.img" />
							<div class="popover-item__desc">
								<span class="popover-item__desc--name">{{ item.name }}</span>
								<span class="popover-item__desc--text">{{ item.desc }}</span>
							</div>
						</div>
					</div>
					<template #reference>
						<img class="chat-img" v-if="showPopover" src="@/assets/img/menu_active.png" />
						<img class="chat-img" v-else src="@/assets/img/menu.png" />
					</template>
				</van-popover>
				<ai-inputs :editorText="editorText" @_sendMessage="sendMessage" @_changeEdit="changeEdit"></ai-inputs>
			</div>
			<!-- 滚动到底部 -->
			<Teleport to="body">
				<Transition name="slide">
					<div v-if="showScrollDown" class="scroll-down" @click="scrollBottom">
						<img src="@/assets/img/scrolldown.png" alt="" />
					</div>
				</Transition>
			</Teleport>
		</div>

		<!-- 更多医生 弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="docMoreVis" position="bottom" :style="{ height: '90%' }">
			<ai-customer-doctors @close="docMoreVis = false" @activeDoc="activeDoc"></ai-customer-doctors>
		</van-popup>

		<!-- 查看医生 弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="viewDoctors" position="bottom" :style="{ height: '90%' }">
			<view-doctor-list :doctotListFront="doctotListFront" @close="viewDoctors = false" @activeDoc="to360"></view-doctor-list>
		</van-popup>

		<!-- 360 -->
		<van-popup v-model:show="showBottom" position="bottom" :style="{ height: '90%' }">
			<iframe style="width: 100%; height: 100%; border: none" :src="docUrl"></iframe>
		</van-popup>

		<!-- 下一步行动计划弹出层-底部弹出 -->
		<van-popup v-model:show="actionPopup" closeable position="bottom" :safe-area-inset-bottom="true" :style="{ height: '70%' }" class="action-popup">
			<div class="action">
				<span class="action-title">{{ actionPopupTitle }}</span>
				<div class="action-list">
					<div class="action-list-item" v-for="item in missionList" :key="item.id">
						<div class="task-item__header">
							<div class="task-item__header-left">
								<span class="left-icon"></span>
								<span>任务ID：{{ item.id }}</span>
								<van-tag class="left-tag" :color="item.taskInfoStatusType === 'PROGRESS' ? '#0052cc' : item.taskInfoStatusType === 'CLOSE' ? '#6B778C' : '#FF991F'" type="primary">{{
									item.taskInfoStatusType === 'PROGRESS' ? '处理中' : item.taskInfoStatusType === 'CLOSE' ? '已关闭' : '已完成'
								}}</van-tag>
							</div>
							<div v-if="item.taskInfoStatusType === 'PROGRESS'" class="task-item__header-right">
								<span @click="closeTask(item.id)">关闭任务</span>
							</div>
						</div>
						<div @click="goDetail(item)" class="task-item__body">
							<img class="task-item__body--img" src="@/assets/img/right.png" alt="" />
							<div class="body-item">
								<span>任务描述：</span>
								<span>{{ item.taskDescription }}</span>
							</div>

							<div class="body-item">
								<span>任务类型：</span>
								<span>{{ item.taskInfoType === 'PHONE_VISIT' ? '电话拜访' : item.taskInfoType === 'WECHAT_VISIT' ? 'Wecom拜访' : item.taskInfoType === 'OFFLINE_VISIT' ? '线下拜访' : '会议邀请' }}</span>
							</div>
							<div class="body-item">
								<span>客户：</span>
								<span>{{ item.doctorName }}</span>
							</div>
							<div v-if="item.hospital" class="body-item">
								<span>医院：</span>
								<span>{{ item.hospital }}</span>
							</div>
							<div class="body-item">
								<span>截止时间：</span>
								<span>{{ item.planEndTime }}</span>
							</div>
							<div class="body-item">
								<span>发起人：</span>
								<span>{{ item.creatorName }}</span>
							</div>
						</div>
						<div v-if="item.taskInfoStatusType === 'PROGRESS'" class="task-item__bottom">
							<van-button @click="forwardTaskDialog(item.id)" class="task-item__bottom_btn" size="small" color="#e7edf8">转发</van-button>
							<van-button size="small" color="#0052cc" @click="executeTask(item)">执行</van-button>
						</div>
					</div>
				</div>
			</div>
		</van-popup>
		<!-- 同事列表组件 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="showForward" position="bottom" :style="{ height: '90%' }">
			<team-list @close="showForward = false" @activeTask="activeTask" :list="teammateList"></team-list>
		</van-popup>
	</div>
</template>
<script setup>
import { getToken } from '@/utils/auth';
import { showToast, showConfirmDialog } from 'vant';
import useUserStore from '@/store/modules/user';
import ClipboardJS from 'clipboard';
import CryptoJS from 'crypto-js';
import AudioPlayer from '@/utils/ap';
import axios from 'axios';
import { uuid, getTodayAndYesterday } from '@/utils/index';
import { myVisitSummary, visitSummaryInfo } from '@/api/my';
import { useClickAway } from '@vant/use';
import { notesList } from '@/api/randomNotes';
import { getTaskList, closeTaskInterface, colleagueList, taskTransferIn } from '@/api/task';
import { getDoctorDynamics } from '@/api/agent-tools';
import cpdc from '@/assets/img/cpdc.png';
import hszs from '@/assets/img/hszs.png';
import khdc from '@/assets/img/khdc.png';
import zlfx from '@/assets/img/zlfx.png';
import khbfxxsj from '@/assets/img/khbfxxsj.png';
import bfbj from '@/assets/img/bfbj.png';

import usefilterStore from '@/store/modules/filter';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
// 接口地址
const interfaceUrl = enterStore.enterInfo.interfaceAddress;
const domain = enterStore.enterInfo.domain;
const filterStore = usefilterStore();

const { proxy } = getCurrentInstance();

// 语言环境
const language = computed(() => {
	return filterStore.language === 'zh' ? '中文' : '英文';
});

const isGlobalVoice = computed(() => {
	return filterStore.isGlobalVoice;
});

const acGlobalVoice = (val) => {
	filterStore.SET_IS_GLOBAL_VOICE(val);
};

// 聊天类型
const showPopover = ref(false);
const actions = ref([
	{
		img: khdc,
		name: '客户洞察2.0',
		desc: '关注客户最新动态，及时采取行动',
		type: 'customerInsight1',
		permission: true,
	},
	{
		img: cpdc,
		name: '客户问题解决方案推荐',
		desc: '基本信息、专业支持、推广策略等',
		type: 'productKnowledgQuery',
		permission: true,
	},
	{
		img: zlfx,
		name: '拜访材料建议与分享',
		desc: '会议，行业动态，法规和资料分享',
		type: '',
		permission: true,
	},
	{
		img: hszs,
		name: '拜访话术准备',
		desc: '拜访话术、文章推送话术等',
		type: 'cloundAssistant',
		permission: true,
	},
	{
		img: khbfxxsj,
		name: '客户拜访信息收集',
		desc: '客户拜访信息收集',
		type: '2',
		permission: true,
	},
	{
		img: bfbj,
		name: '拜访笔记',
		desc: '随时随地记录关键信息',
		type: 'randomNotes',
		permission: true,
	},
]);

// 模块id
const chatType = ref('customerInsight');

const chatTypeEv = (item) => {
	showPopover.value = false;
	if (item.type === '') {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/mobile/contentCenter/contentCenterIndex/marketKnowledge',
				title: '拜访材料建议与分享',
				module: 'third',
			});
		} else {
			location.href = domain + '/mobile/contentCenter/contentCenterIndex/marketKnowledge';
		}
		return;
	}
	if (item.type === '2') {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/mobile/questionResearch',
				title: '客户拜访信息收集',
				module: 'third',
			});
		} else {
			location.href = domain + '/mobile/questionResearch';
		}
		return;
	}
	if (item.type === 'customerInsight1') return;
	if (item.type === 'randomNotes') {
		router.push({
			name: item.type,
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else {
		router.replace({
			name: item.type,
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	}
};

const audioPlayer = new AudioPlayer();
const userStore = useUserStore();
const userId = userStore.userInfo.id;
const isLoading = ref(false); //页面是否有正在回答的消息
const chatList = ref([]); //消息列表
// 返回
const goBack = () => {
	router.go(-1);
};
// 赞和踩
const zeEv = (val) => {
	const j = chatList.value.filter((ele) => ele.id === val.id)[0];
	const json = {
		message_id: j.dataId,
		feedback_score: '',
	};
	j.zeStatus = val.like;
	if (val.like === '2') {
		// 踩
		json.feedback_score = 0;
	} else if (val.like === '1') {
		// 赞
		json.feedback_score = 100;
	} else {
		json.feedback_score = -1;
	}
	axios({
		url: interfaceUrl + '/API-GPT/chat/message/update_history_feedback',
		method: 'get',
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
		params: json,
	});
};
//生成一条用户消息
const createUserMsg = (text) => {
	return {
		msg: text,
		dataId: '',
		type: 'user',
		id: uuid(),
		loading: false,
		zeStatus: '0',
	};
};
//生成一条ai消息
const createAiMsg = (text) => {
	let message = {
		msg: '',
		dataId: '',
		type: 'ai',
		id: uuid(),
		loading: true,
		isBtns: true,
		reGen: true,
		voiceStatus: true,
		zeStatus: '0',
		isIframe: '',
	};
	text ? (message.loadingText = '正在为您生成每日业绩播报') : '';
	return message;
};
//猜你想问
const qesEv = (val, item) => {
	if (!isLoading.value) {
		isLoading.value = true;
		chatList.value.push(createUserMsg(val));

		scrollBottom();
		let msg;
		if (item.templateType === 'doctor') {
			msg = `doctorid=${item.doctorId} ${val}`;
			// 生产一个ai的消息
			chatList.value.push({
				msg: '',
				type: 'ai',
				dataId: '',
				doctorId: item.doctorId,
				templateType: 'doctor',
				id: Date.now() + 1,
				loading: true,
				isBtns: true,
				voiceStatus: true,
				isTarDoc: false,
				zeStatus: '0',
				isLoading: true,
			});
		} else {
			msg = val;
			// 生产一个ai的消息
			chatList.value.push(createAiMsg());
		}
		gd(msg);
	}
};
// 重新生成
const reGen = (id) => {
	// 获取文本信息
	let prevItemMsg = '';
	const index = chatList.value.findIndex((item) => item.id === id);

	if (index !== -1) {
		if (chatList.value[index].afterStopAutoRead) {
			chatList.value.splice(index, 1);
			init(true);
			return;
		}
		prevItemMsg = chatList.value[index - 1]?.msg ?? '';
	}
	try {
		chatList.value.push(createUserMsg(prevItemMsg));
		// 生产一个ai的消息
		chatList.value.push(createAiMsg());
		scrollBottom();
		gd(prevItemMsg);
	} catch (e) {
		console.log(e);
		showToast({
			position: 'top',
			message: '重新生成失败！',
		});
	}
};
//获取最后一条用户消息
const getLastUser = () => {
	let ar = chatList.value.filter((ele) => ele.type === 'user');
	let msg = ar?.[ar.length - 1]?.msg;
	return msg;
};
//获取最后一条chat
const getLastChat = computed(() => {
	return chatList.value[chatList.value.length - 1];
});
//固定消息
const gd = (value) => {
	getLastChat.value.isLoading = true;
	//获取猜你想问
	getSuggestion({ type: 'cardSuggestion', Question: value });
	setTextMessage(value);
};

// 发送消息
const sendMessage = (val) => {
	if (!isLoading.value) {
		if (val) {
			isLoading.value = true;
			chatList.value.push(createUserMsg(val));
			// 生产一个ai的消息
			chatList.value.push(createAiMsg());
			scrollBottom();
			gd(val);
		} else {
			showToast({
				position: 'top',
				message: '请输入内容',
			});
		}
	} else {
		if (val) {
			isLoading.value = true;
			stopFetch();
			chatList.value.push(createUserMsg(val));
			// 生产一个ai的消息
			chatList.value.push(createAiMsg());
			scrollBottom();
			gd(val);
		} else {
			showToast({
				position: 'top',
				message: '请输入内容',
			});
		}
	}
};
//停止当前请求
const stopFetch = () => {
	controllerList.forEach((ele) => ele.abort());
	getLastChat.value.loading = false;
	getLastChat.value.isLoading = false;
	if (getLastChat.value.msg) {
		getLastChat.value.msg = getLastChat.value.msg + '...';
	}
	if (getLastChat.value.summary) {
		getLastChat.value.summary = getLastChat.value.summary + '...';
	}
	getLastChat.value.summaryLoading = false;
	getLastChat.value.nextAction = '';
	getLastChat.value.temNextAction = '';
	getLastChat.value.questionList = '';
	getLastChat.value.temQuestionList = '';
	isLoading.value = false;
	controllerList = [];
	nextTick(() => {
		if (getLastChat.value.afterStopAutoRead && isGlobalVoice.value) {
			getLastChat.value.voiceStatus = false;
			voiceEv({
				id: getLastChat.value.id,
				vType: 'play',
			});
		}
	});
};
// 发送的消息是否是二次编辑的消息
const isEdit = ref(false);
const editorText = ref('');
const editorMsg = (index, item) => {
	if (index === chatList.value.length - 2) {
		// 如果请求正在进行需要终止请求
		if (isLoading.value) {
			stopFetch();
		}
		editorText.value = item.msg + 'jjjjjj' + new Date().getTime();
		isEdit.value = true;
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isEdit.value = false;
	}
};
const changeEdit = () => {
	isEdit.value = false;
	editorText.value = '';
};
// 文本框失去焦点
const chatBox = ref();
useClickAway(
	chatBox,
	() => {
		if (isEdit.value) {
			editorText.value = '';
		}
		isEdit.value = false;
	},
	{ eventName: 'touchend' }
);

// 会话ID
const chatId1 = '7bzlclv2pdop';
const appId = '667cc60c6a0b5dcceac3ed4b';
const appKey = 'Bearer appKey-pOjSX8hPcPkX5VJBPArrUsbcVvAh8FpYsRw7zYsoH2PSNeZvAA25WunGEhYKF';
// const appId = '662de788e79ac9e8c73e0214';
// const appKey = 'Bearer appKey-0F8tLeppAcq0X98bQRvvxMvyLXLoFicNMURoqXosCRqBjcebeZrUCuT555jabG';
//处理猜你想问和下一步行动计划出现时机
const showEtc = computed(() => {
	return (item) => {
		if (item.isIframe) {
			if (!item.summaryLoading) {
				return true;
			}
		} else {
			if (!item.loading) {
				return true;
			}
		}
	};
});
let controllerList = [];
const fetchRequest = (val) => {
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async (resolve) => {
		try {
			let controller = new AbortController();
			controllerList.push(controller);
			const { signal } = controller;
			const message = val || '';
			let radom1 = uuid();
			let radom2 = uuid();
			let url = enterStore.enterInfo.agentAddress;
			let res = await fetch(`${url}/api/v1/chat/completions`, {
				signal,
				method: 'POST',
				headers: {
					'Content-Type': 'application/json;charset=utf-8',
					Authorization: appKey,
				},
				body: JSON.stringify({
					appId,
					chatId: chatId1,
					stream: true,
					detail: true,
					variables: { token: `Bearer ${getToken()}`, language: language.value },
					messages: [
						{ role: 'user', content: JSON.stringify({ message, role: getRole.value }), dataId: radom1 },
						{ role: 'assistant', content: '', dataId: radom2 },
					],
				}),
			});
			if (!res?.body || !res?.ok) {
				throw new Error('Request Error');
			}
			resolve(res);
		} catch (error) {
			console.log(error, 'xxxxxxxxxxxxxxxxxxxxxxx');
		}
	});
};
const handleStream = (res, type) => {
	let fixedVariable = {
		Suggestion: ['temQuestionList', 'questionList'],
		NextAction: ['temNextAction', 'nextAction'],
	};
	const reader = res.body?.getReader();
	const decoder = new TextDecoder('utf-8');
	let buffer = '';
	let aceptInfo = false;
	let stepLists = [];
	function processStreamResult(result) {
		const chunk = decoder.decode(result.value, { stream: !result.done });
		buffer += chunk;
		// 逐条解析后端返回数据
		const lines = buffer.split('\n');
		buffer = lines.pop();
		lines.forEach((line) => {
			if (line.trim().length === 0) return;
			if (line.indexOf('data:') === -1 || line.split('data:')[1] === ' [DONE]') return;
			const resData = JSON.parse(line.split('data:')[1]);
			if (resData.name) {
				stepLists.push(resData.name);
			}
			if (resData.name === 'AI 对话-每日业绩解读') {
				aceptInfo = true;
				getLastChat.value.isLoading = false;
			}
			if (resData.name === 'AI 对话-下一步行动计划') {
				aceptInfo = true;
			}
			if (resData.name === 'AI 对话-卡片解读') {
				aceptInfo = true;
			}
			if (resData.name === 'AI 对话-猜你想问') {
				aceptInfo = true;
			}
			if (resData.name === '查询内部信息') {
				aceptInfo = true;
			}
			if (stepLists.includes('任务调度（工具）')) {
				aceptInfo = true;
				if (stepLists.includes('查询卡片信息')) {
					getLastChat.value.isLoading = true;
				} else {
					getLastChat.value.isLoading = false;
				}
			}

			if (resData.choices && resData.choices[0].delta.content && aceptInfo) {
				const text = resData.choices[0].delta.content;
				// console.log(text);
				if (type === 'Summary') {
					getLastChat.value.summary += text;
				} else if (type === 'cardOrMsg') {
					getLastChat.value.msg += text;
					if (getLastChat.value.msg.includes('cardCom')) {
						getLastChat.value.isIframe = true;
						getLastChat.value.summaryLoading = true; //加载summary
					}
					if (getLastChat.value.msg.includes('internalInfo')) {
						getLastChat.value.isIframe = true;
						getLastChat.value.isInternalInfo = true;
					}
					// if (!getLastChat.value.msg.includes('```')) {
					// 	getLastChat.value.isLoading = false;
					// }
				} else {
					getLastChat.value[fixedVariable[type][0]] += text;
				}
			}
		});
		if (!result.done) {
			return reader.read().then(processStreamResult);
		} else {
			console.log(stepLists, '........................');
			if (type === 'Summary') {
				//如果时summary
				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value.summary);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
				getLastChat.value.summaryLoading = false;
				nextTick(() => {
					if (isGlobalVoice.value) {
						getLastChat.value.voiceStatus = false;
						voiceEv({
							id: getLastChat.value.id,
							vType: 'play',
						});
					}
				});
				isLoading.value = false;
				getLastChat.value.loading = false;
			} else if (type === 'cardOrMsg') {
				if (getLastChat.value.isIframe) {
					// 去除开头和结尾的 ```json 标记
					let trimmedText;
					// 定义正则表达式匹配第一个 ```json ... ``` 块
					const regex = /```json\s+([\s\S]*?)\s+```/;
					// 使用正则表达式进行匹配
					const match = getLastChat.value.msg.match(regex);
					// 如果匹配成功，返回匹配组中的内容，否则返回 null
					if (match && match[1]) {
						trimmedText = match[1].trim();
					}
					// 解析 JSON 字符串为 JavaScript 对象或数组
					const jsonData = JSON.parse(trimmedText);
					console.log(jsonData);
					if (jsonData.type === 'internalInfo') {
						getLastChat.value.iframeUrl = jsonData.url;
					} else {
						// 提取 params 和 QA
						const { params, url } = jsonData[0];
						// 构建查询字符串
						const queryString = Object.entries(params)
							.map(([key, value]) => `${key}=${value}`)
							.join('&');
						getLastChat.value.iframeUrl = url + '?' + queryString;
					}
					getLastChat.value.msg = jsonData;
					getLastChat.value.isLoading = false;
				} else if (getLastChat.value.msg.includes('```json') && getLastChat.value.msg.includes('[]')) {
					// 查询卡片为空的情况
					getLastChat.value.isLoading = false;
					getLastChat.value.msg = '';

					let ar = chatList.value.filter((ele) => ele.type === 'user');
					let msg = ar?.[ar.length - 1]?.msg;
					setTextMessage({ type: 'cardComSummary', Question: msg });
				} else {
					// 字段afterStopAutoRead等于true说明是每日播报，需要缓存到本地
					if (getLastChat.value.afterStopAutoRead) proxy.$cache.local.setExpirseJSON('customerInsightReport', getLastChat.value.msg, 1);
					nextTick(() => {
						if (getLastChat.value.afterStopAutoRead && isGlobalVoice.value) {
							getLastChat.value.voiceStatus = false;
							voiceEv({
								id: getLastChat.value.id,
								vType: 'play',
							});
						}
					});
					if (Object.keys(dotcorInfo.value).length > 0) {
						getLastChat.value.loading = false;
						chatList.value.push({
							msg: JSON.parse(JSON.stringify(dotcorInfo.value)),
							type: 'ai',
							dataId: '',
							doctorId: dotcorInfo.value.doctorId,
							templateType: 'docCard',
							id: Date.now() + 892,
							loading: true,
							isBtns: false,
							voiceStatus: true,
							isTarDoc: false,
							zeStatus: '0',
							isCollection: false,
							isMore: false,
						});
						dotcorInfo.value = '';
					}
					isLoading.value = false;
					getLastChat.value.loading = false;
				}
				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value.msg);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
			} else {
				//如果时猜你想问和下一步行动
				// 去除开头和结尾的 ```json 标记
				let trimmedText = getLastChat.value[fixedVariable[type][0]]
					.trim()
					.replace(/^```json\s+/, '')
					.replace(/\s+```$/, '');
				getLastChat.value[fixedVariable[type][1]] = JSON.parse(trimmedText).data;

				// 字段afterStopAutoRead等于true说明是每日播报，需要把猜你想问缓存到本地
				// if (getLastChat.value.afterStopAutoRead && fixedVariable[type][1] === 'questionList') {
				// 	proxy.$cache.local.setExpirseJSON('customerInsightQuestionList', getLastChat.value[fixedVariable[type][1]], 1);
				// }
				console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
				console.log(getLastChat.value[fixedVariable[type][1]], type);
				console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
			}
			scrollBottom();
		}
	}
	reader.read().then(processStreamResult);
};
//发送信息
const setTextMessage = async (val) => {
	try {
		let res = await fetchRequest(val);
		handleStream(res, 'cardOrMsg');
	} catch (e) {
		handleError(e);
	}
};
//获取summary
const getSummary = async (data) => {
	try {
		getLastChat.value.summary = '';
		let res = await fetchRequest(data);
		handleStream(res, 'Summary');
	} catch (e) {
		handleError(e);
	}
};
//获取猜你想问
const getSuggestion = async (data) => {
	try {
		getLastChat.value.questionList = '';
		getLastChat.value.temQuestionList = '';
		let res = await fetchRequest(data);
		handleStream(res, 'Suggestion');
	} catch (e) {
		handleError(e);
	}
};
//获取下一步行动计划
const nextAction = async (data) => {
	try {
		getLastChat.value.nextAction = '';
		getLastChat.value.temNextAction = '';
		let res = await fetchRequest(data);
		handleStream(res, 'NextAction');
	} catch (e) {
		handleError(e);
	}
};
let handleError = (e) => {
	showToast({
		position: 'top',
		message: '请求失败,请重试',
	});
	isLoading.value = false;
	// 清除chatList最后一位
	chatList.value.splice(-1, 1);
	console.error(e);
};
let router = useRouter();
const route = useRoute();

//任务弹框
const actionPopup = ref(false);
const actionPopupTitle = ref('');
const missionList = ref([]);
let transformInfo = {
	执行电话拜访: 'PHONE_VISIT',
	Wecome沟通: 'WECHAT_VISIT',
	发送会议邀请: 'MEETING_INVITATION',
	记录实地拜访: 'OFFLINE_VISIT',
};
const toNextAction = (value) => {
	console.log(value);
	if (value === '查看医生详情') {
		viewDoctors.value = true;
		return;
	} else {
		actionPopup.value = true;
		actionPopupTitle.value = value;
		missionList.value = missionData.value.filter((ele) => {
			return ele.taskInfoType === transformInfo[value];
		});
		console.log(missionList.value);
	}

	// let action = {
	// 	创建拜访计划: 'visitPlan',
	// 	记录实地拜访: 'toReport',
	// 	发送会议邀请: 'toMeet',
	// 	传递关键资料: 'visitArticle',
	// };
	// router.push({
	// 	name: action[value],
	// 	query: {
	// 		isTabBar: route.query.isTabBar || '',
	// 	},
	// });
};
// 关闭任务
const closeTask = (id) => {
	showConfirmDialog({
		title: '提示',
		message: '是否确认关闭该任务？',
		confirmButtonColor: '#0052CC',
	}).then(() => {
		closeTaskInterface({ remark: '' }, [id]).then(() => {
			showToast({
				position: 'top',
				message: '任务已关闭',
			});
			for (const ite of taskData.value) {
				if (ite.id === id) {
					ite.taskInfoStatusType = 'CLOSE';
					break;
				}
			}
		});
	});
};
// 同事列表
const teammateList = ref([]);
const getTeammateList = () => {
	colleagueList(userId, { isBrief: false }).then((res) => {
		if (res.result && res.result.length > 0) {
			res.result = res.result.reduce((acc, cur) => {
				const hasDuplicate = acc.some((item) => item.id === cur.id);
				if (!hasDuplicate) {
					acc.push(cur);
				}
				return acc;
			}, []);
		}
		teammateList.value = res.result || [];
	});
};
// 转发
let forwardId = '';
const showForward = ref(false);
const forwardTaskDialog = (id) => {
	forwardId = id;
	showForward.value = true;
};
const activeTask = (id) => {
	showConfirmDialog({
		title: '提示',
		message: '是否确认转发该任务？',
		confirmButtonColor: '#0052CC',
	}).then(() => {
		taskTransferIn({ recipientId: id, taskInfoIdList: [forwardId] }).then(() => {
			showToast({
				position: 'top',
				message: '任务已转发',
			});
			taskData.value = taskData.value.filter((ele) => ele.id !== forwardId);
		});
	});
};
// 查看或者编辑任务
const goDetail = (item) => {
	router.push({
		name: 'keyTaskDetail',
		query: {
			isTabBar: route.query.isTabBar || '',
			isCreateTask: 'notCreate',
			taskContent: encodeURIComponent(JSON.stringify(item)),
			taskType: item.taskInfoType === 'PHONE_VISIT' ? '电话拜访' : item.taskInfoType === 'WECHAT_VISIT' ? 'Wecom拜访' : item.taskInfoType === 'OFFLINE_VISIT' ? '线下拜访' : '会议邀请',
		},
	});
};
let isScrollIng = ref(false);
const scrollBottom = () => {
	isScrollIng.value = true;
	nextTick(() => {
		document.querySelector('#aiCon').scrollTo({
			top: document.querySelector('#aiCon').scrollHeight,
			behavior: 'smooth',
		});
		setTimeout(() => {
			isScrollIng.value = false;
		}, 500);
	});
};
let showScrollDown = ref(false);
// 语音相关
const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let ttsWS = null;
const voiceEv = async (val) => {
	// chatList里所有的voiceStatus改位true
	resetAudioVoice();
	// 通过val.id获取chatList的相等元素
	const index = chatList.value.findIndex((item) => item.id === val.id);
	chatList.value[index].voiceStatus = val.vType === 'play' ? false : true;

	ttsWS?.close();
	audioPlayer.reset();
	if (val.vType === 'play') {
		// 开始语音播报
		const url = getWebSocketUrl(API_KEY, API_SECRET);
		if ('WebSocket' in window) {
			ttsWS = new WebSocket(url);
		} else if ('MozWebSocket' in window) {
			ttsWS = new MozWebSocket(url);
		} else {
			showToast({
				position: 'top',
				message: '浏览器不支持WebSocket',
			});
			return;
		}
		// 发送消息
		ttsWS.onopen = () => {
			audioPlayer.start({
				autoPlay: true,
				sampleRate: 16000,
				resumePlayDuration: 1000,
			});
			let text = document.getElementById(val.id).innerText;
			let tte = 'UTF8';
			let params = {
				common: {
					app_id: APPID,
				},
				business: {
					aue: 'raw',
					auf: 'audio/L16;rate=16000',
					vcn: 'x4_lingfeichen_assist', //发音人
					speed: 50, // 语速
					volume: 50, // 音量
					pitch: 50, // 音色
					bgs: 0,
					tte,
				},
				data: {
					status: 2,
					text: encodeText(text, tte),
				},
			};
			ttsWS.send(JSON.stringify(params));
		};

		ttsWS.onmessage = (e) => {
			let jsonData = JSON.parse(e.data);
			console.log(jsonData);
			// 合成失败
			if (jsonData.code !== 0) {
				showToast({
					position: 'top',
					message: '语音合成失败, 请稍后再试',
				});
				return;
			}
			audioPlayer.postMessage({
				type: 'base64',
				data: jsonData.data.audio,
				isLastData: jsonData.data.status === 2,
			});
			if (jsonData.code === 0 && jsonData.data.status === 2) {
				//0是成功2是最后一次
				ttsWS.close();
			}
		};

		ttsWS.onerror = (e) => {
			console.error(e);
		};
		ttsWS.onclose = () => {};
	}
};
const resetAudioVoice = () => {
	for (const i of chatList.value) {
		if (i.type === 'user') continue;
		if (i.isBtns === false) continue;
		if (i.voiceStatus === true) continue;
		i.voiceStatus = true;
	}
};

// 播放停止把voiceStatus改位true
audioPlayer.onStop = () => {
	resetAudioVoice();
};
const encodeText = (text, type) => {
	if (type === 'unicode') {
		let buf = new ArrayBuffer(text.length * 4);
		let bufView = new Uint16Array(buf);
		for (let i = 0, strlen = text.length; i < strlen; i++) {
			bufView[i] = text.charCodeAt(i);
		}
		let binary = '';
		let bytes = new Uint8Array(buf);
		let len = bytes.byteLength;
		for (let i = 0; i < len; i++) {
			binary += String.fromCharCode(bytes[i]);
		}
		return window.btoa(binary);
	} else {
		return Base64.encode(text);
	}
};
const getWebSocketUrl = (apiKey, apiSecret) => {
	var url = 'wss://tts-api.xfyun.cn/v2/tts';
	var host = location.host;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};
// 获取更多目标医生
const docMoreVis = ref(false);
const docMore = () => {
	docMoreVis.value = true;
};
// 点击医生生产消息
let dotcorInfo = ref({});
const activeDoc = (item, msg) => {
	if (isLoading.value) {
		stopFetch();
	}
	isLoading.value = true;
	if (msg) {
		chatList.value.push({
			msg,
			type: 'user',
			id: Date.now(),
			loading: false,
		});
	} else {
		dotcorInfo.value = {
			doctorName: item.doctorName,
			doctorId: item.doctorId,
			doctorSex: item.doctorSex,
			doctorTitle: item.doctorTitle,
			hospital: item.hospital,
			department: item.department,
		};
		chatList.value.push({
			msg: dotcorInfo.value,
			type: 'user',
			templateType: 'docInfo',
			id: Date.now(),
			loading: false,
		});
	}

	// 生产一个ai的消息
	chatList.value.push({
		msg: '',
		type: 'ai',
		dataId: '',
		doctorId: item.doctorId,
		templateType: 'doctor',
		id: Date.now() + 1,
		loading: true,
		isBtns: true,
		voiceStatus: true,
		isTarDoc: false,
		zeStatus: '0',
		isLoading: true,
	});
	scrollBottom();
	//获取猜你想问
	getSuggestion({ type: 'cardSuggestion', Question: `doctorid=${item.doctorId}的${msg ? msg : '基本信息'}` });
	setTextMessage(`doctorid=${item.doctorId}的${msg ? msg : '基本信息'}`);
	// setTemplateMessage(item.doctorName, item.doctorId);
};
const isToken = ref(false);
const articleTitle = ref('');
const quesMoreVis = ref(false);
const qesTitle = ref('');
const qesMoreEv = () => {
	quesMoreVis.value = true;
	qesTitle.value = '文章分享';
};

const pushArticleVis = ref(false);
const activeArticle = () => {
	pushArticleVis.value = true;
};

// 文章详情
const articleDetailVis = ref(false);
const sourceUrl = ref('');
const articleDetail = (item) => {
	isToken.value = true;
	articleTitle.value = '';
	sourceUrl.value = item;
	articleDetailVis.value = true;
};

// 拜访报告
const visitedEv = () => {
	isToken.value = false;
	articleTitle.value = '会后回访报告';
	const baseUrl = domain + '/mobile/';
	sourceUrl.value = baseUrl + 'visitReport/visitReportNew?token=' + getToken();
	articleDetailVis.value = true;
};
// 收到iframe传入的消息后处理卡片
const afterMessage = (event) => {
	if (event.data.source) return;
	console.log('=======================收到子iframe消息=======================');
	console.log(event);
	// 获取内嵌页面的高度
	if (!event.data.cardHeight) return;
	const height = event.data.cardHeight + 'px';
	// 设置 iframe 的高度
	boxRefs.value[boxRefs.value.length - 1].style.height = height;
	//有数据进行下一步获取总结和猜你想问、下一步行动计划
	if (event.data.data) {
		let ar = chatList.value.filter((ele) => ele.type === 'user');
		let msg = ar?.[ar.length - 1]?.msg;
		getSummary({ type: 'cardComSummary', Question: msg, data: { ...event.data.data } });
		nextAction({ type: 'cardNextAction', Question: msg });
	}
};

let doctotListFront = ref([]);
let missionData = ref([]);
const init = async (isRegen = false) => {
	isLoading.value = true;
	let data = {};

	// isRegen = false 不是重新生成，需要判断本地是否有缓存，有缓存取缓存数据，没有缓存直接生成
	if (isRegen === false && proxy.$cache.local.getExpirseJSON('customerInsightReport')) {
		chatList.value.push({ ...createAiMsg(), afterStopAutoRead: true });
		// 每日业绩播报 --- 会话文本
		getLastChat.value.msg = proxy.$cache.local.getExpirseJSON('customerInsightReport');
		// 每日业绩播报 --- 下一步计划
		getLastChat.value.nextAction = proxy.$cache.local.getExpirseJSON('customerInsightNextAction');
		// 每日业绩播报 --- 猜你想问
		// getLastChat.value.questionList = proxy.$cache.local.getExpirseJSON('customerInsightQuestionList');
		getLastChat.value.loading = false;

		// 判断是否需要自动语音播报
		nextTick(() => {
			if (getLastChat.value.afterStopAutoRead && isGlobalVoice.value) {
				getLastChat.value.voiceStatus = false;
				voiceEv({
					id: getLastChat.value.id,
					vType: 'play',
				});
			}
		});
	} else {
		// 重新生成，清空每日播报缓存数据
		if (isRegen) {
			// 每日业绩播报 --- 会话文本
			proxy.$cache.local.remove('customerInsightReport');
			// 每日业绩播报 --- 下一步计划
			proxy.$cache.local.remove('customerInsightNextAction');
			// 每日业绩播报 --- 猜你想问
			// proxy.$cache.local.remove('customerInsightQuestionList');
		}
		chatList.value.push({ ...createAiMsg('正在为您生成每日业绩播报'), afterStopAutoRead: true });

		getLastChat.value.isLoading = true;
		//获取猜你想问
		// getSuggestion({ type: 'cardSuggestion', Question: { ...data } });

		// setTextMessage({ ...data, type: 'cardDailyReport' });
	}

	//获取我的卡片数据 res1 res2
	//获取随手记数据 res3 res4
	// 获取医生动态数据
	// 获取系统推荐任务数据 res5
	const { formattedToday, formattedYesterday } = getTodayAndYesterday();
	const params = {
		page: 0,
		size: 10,
		total: 0,
		sort: 'plan_end_time,desc',
	};
	// 待办任务
	const query = {
		taskInfoSourceType: null,
		taskInfoType: [],
		taskInfoStatusType: 'PROGRESS',
		taskInfoSearchType: 'ALL',
	};
	let [res1, res2, res3, res4, res5, res7] = await Promise.all([myVisitSummary({}), visitSummaryInfo({}), notesList({ dateTime: formattedToday }), notesList({ dateTime: formattedYesterday }), getTaskList(params, query), getDoctorDynamics({ page: 0, size: 3 })]);
	missionData.value = res5.result.content?.slice(0, 3);
	let res5Info = res5.result.content?.slice(0, 3).map((ele) => {
		const { taskDescription, taskInfoStatusType, taskInfoType, endTime, choiceDoctorVOList, doctorVO } = ele;
		const doctorInfo =
			choiceDoctorVOList.length > 0
				? choiceDoctorVOList.map(({ doctorId, doctorName, doctorSex, doctorTitle, hospital, department }) => ({
						id: doctorId,
						doctorName: doctorName,
						doctorSex,
						doctorTitle,
						hospital,
						department,
				  }))
				: {
						id: doctorVO.id,
						doctorName: doctorVO.doctorName,
						doctorSex: doctorVO.doctorSex,
						doctorTitle: doctorVO.doctorTitle,
						hospital: doctorVO.hospital,
						department: doctorVO.department,
				  };
		return {
			taskDescription,
			taskInfoStatusType,
			taskInfoType,
			endTime,
			doctorInfo,
		};
	});
	data = { 拜访状况: { 拜访数据: { ...res1.result, ...res2.result }, 随手记记录解读: { ...res3.result, ...res4.result } }, 客户动态: { ...res7.result.content }, 跟进建议: res5Info };
	console.log(data);

	// 前端处理医生列表数据
	doctotListFront.value.push(...res5Info.flatMap((item) => (Array.isArray(item.doctorInfo) ? item.doctorInfo : [item.doctorInfo])));

	res7.result.content.forEach((ele) => {
		doctotListFront.value.push({ id: ele.doctorId, doctorName: ele.doctorName, doctorSex: ele.doctorSex, doctorTitle: ele.doctorTitle, hospital: ele.hospital, department: ele.department });
	});

	// 去重处理
	const doctorMap = new Map();
	doctotListFront.value.forEach((doc) => {
		doctorMap.set(doc.id, doc);
	});
	doctotListFront.value = Array.from(doctorMap.values());
	if (doctotListFront.value.length > 0) {
		//下一步行动计划
		getLastChat.value.nextAction = ['查看医生详情'];
	}

	let tran = { PHONE_VISIT: '执行电话拜访', WECHAT_VISIT: 'Wecome沟通', MEETING_INVITATION: '发送会议邀请', OFFLINE_VISIT: '记录实地拜访' };
	res5.result.content?.slice(0, 3).forEach((ele) => {
		if (tran[ele.taskInfoType] && !getLastChat.value.nextAction.includes(tran[ele.taskInfoType])) {
			getLastChat.value.nextAction.push(tran[ele.taskInfoType]);
		}
	});

	const cachedReport = proxy.$cache.local.getExpirseJSON('customerInsightReport');

	if (isRegen || !cachedReport) {
		setTextMessage({ ...data, type: 'cardDailyReport' });
	}
};
const viewDoctors = ref(false);
// 任务执行
const executeTask = (item) => {
	if (item.taskInfoType === 'PHONE_VISIT') {
		// 电话拜访，给一个提示“是否继续拨打电话”
		showConfirmDialog({
			title: '提示',
			message: '是否继续拨打电话？',
			confirmButtonColor: '#0052CC',
		});
	} else if (item.taskInfoType === 'OFFLINE_VISIT') {
		// 线下拜访，跳转到医生的拜访记录页面，填写完毕后，自动完成此任务
		// let productIdList = '';
		// if (item.productDomainList.length > 0) {
		// 	for (const s of item.productDomainList) {
		// 		productIdList += s.id + ',';
		// 	}
		// 	productIdList = productIdList.slice(0, productIdList.length - 1);
		// }
		// router.push({
		// 	name: 'toReport',
		// 	query: {
		// 		isTabBar: route.query.isTabBar || '',
		// 		taskId: item.id,
		// 		doctorId: item.doctorVO.doctorId,
		// 		visitType: '线下拜访',
		// 		productIdList: productIdList,
		// 		endTime: item.endTime,
		// 	},
		// });
		router.push({
			name: 'mAssistant',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	} else if (item.taskInfoType === 'WECHAT_VISIT') {
		// wecom拜访，给提示
		showToast({
			message: '请打开企业微信',
			position: 'top',
		});
	} else {
		// 会议邀请
		router.push({
			name: 'toMeet',
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	}
};

const showBottom = ref(false);
const docUrl = ref('');
const to360 = (v) => {
	showBottom.value = true;
	docUrl.value = `${domain}/mobile/doctor/doctorInfo/internalInfo?doctorId=${v.id}`;
};
const aiConScroll = (e) => {
	const container = e.target;
	const scrollTop = container.scrollTop;
	const scrollHeight = container.scrollHeight;
	const clientHeight = container.clientHeight;
	// 计算可滚动的高度
	const scrollableHeight = scrollHeight - clientHeight;

	// 如果当前滚动的位置小于可滚动的高度，表示页面没有滚动到底部
	const isScrollAtBottom = scrollTop + 50 >= scrollableHeight;
	if (!isScrollAtBottom) {
		// 其他处理滚动事件的代码
		requestAnimationFrame(() => {
			!isScrollIng.value && (showScrollDown.value = true);
		});
	} else {
		showScrollDown.value = false;
	}
};
onMounted(() => {
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
	init();
	window.addEventListener('message', afterMessage);
	nextTick(() => {
		document.querySelector('#aiCon').addEventListener('scroll', aiConScroll);
	});
	// 同事列表
	getTeammateList();
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
	// 停止当前请求
	stopFetch();
	// 关闭语音
	resetAudioVoice();
	ttsWS?.close();
	audioPlayer.reset();
});

onUnmounted(() => {
	window.removeEventListener('message', afterMessage);
	window.removeEventListener('scroll', aiConScroll);
});

const getRole = computed(() => {
	let roles = ['ADMIN', 'BUD', 'RSD', 'RSM', 'DSM', 'MR'];
	return userStore.permission.find((ele) => roles.includes(ele));
});
const copyText = (id) => {
	const clipboard = new ClipboardJS('.copy-btn', {
		target: function () {
			return document.getElementById(id);
		},
	});

	clipboard.on('success', () => {
		showToast({
			position: 'top',
			message: '复制成功',
		});
		clipboard.destroy();
	});
};

const boxRefs = ref([]);
const setRef = (el, index) => {
	if (el) {
		if (!boxRefs.value.includes(el)) {
			boxRefs.value.push(el);
		}
	}
};
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-list {
			flex: 1 0 0px;
			height: 0px;
			padding: 0 0;
			padding-top: 12px;
			overflow-y: auto;
			position: relative;
			.mc-content-list-left {
				display: flex;
				flex-direction: column;
				width: 100%;
				margin-bottom: 12px;
				.mc-content-template {
					border-top-left-radius: 8px;
					border-bottom-right-radius: 8px;
					border-top-right-radius: 8px;
				}
				img {
					width: 26px;
					height: 26px;
					margin-right: 6px;
					margin-top: 6px;
				}

				.mc-content__groupList {
					padding: 8px 0 0 0;
					padding-left: 15px;
					display: flex;
					display: flex;
					overflow-x: auto;
					width: 100%;
					span {
						flex: none;
						margin-right: 8px;
						margin-bottom: 8px;
						color: #0b67e4;
						border-radius: 5px;
						border: 1px solid #0b67e4;
						padding: 3px 6px;
					}
				}
			}

			.mc-content-list-right {
				padding-right: 15px;
				display: flex;
				margin-bottom: 12px;
				justify-content: flex-end;
				.mc-content-template {
					border-top-right-radius: 8px;
					border-bottom-left-radius: 8px;
					border-top-left-radius: 8px;
					color: var(--ac-bg-color--fff);
					background-color: var(--ac-bg-active-color);
					.mc-content-template__edit {
						border-top: solid 1px rgba(223, 225, 230, 0.3);
						margin-top: 2px;
					}
				}
			}
			.mc-content-template {
				max-width: 330px;
				min-width: 52px;
				background-color: var(--ac-bg-color--fff);
				padding: 10px;
			}
			.mc-content-info {
				width: 70%;
				background-color: var(--ac-bg-color);
				border: solid 1px var(--ac-border-color);
				border-radius: 5px;

				.docList-list-item {
					background-color: var(--ac-bg-color);
					border-radius: 5px;
					margin-top: 8px;
					position: relative;
					display: flex;
					align-items: center;
					padding: 8px;
					img {
						height: 50px;
						width: 50px;
						margin-right: 8px;
						margin-top: 0;
					}

					.amd-content-item-desc {
						flex: 1;
						display: flex;
						flex-direction: column;
						font-size: 12px;
						.desc-name {
							font-weight: 500;
							font-size: 14px;
						}

						.desc-hp {
							width: 100%;
						}
					}
				}
			}
		}
		// 按钮组
		.mc-btns {
			width: 100%;
			margin: 0px auto;
			padding: 4px 6px;
			overflow-x: auto;
			display: flex;
			white-space: nowrap;
			background-color: var(--ac-bg-color);
			.mc-btns-item {
				margin-right: 6px;
				border: 1px solid var(--ac-border-color);
				padding: 4px 10px;
				border-radius: 20px;
				font-size: 12px;
				color: var(--ac-font-color);
			}
		}
		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 8px;
			padding-bottom: 6px;
			background-color: var(--ac-bg-color);
			z-index: 666;
			position: relative;
			height: 64px;
			display: flex;
			align-items: center;
			.chat-img {
				width: 25px;
				object-fit: contain;
				margin-left: 15px;
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
}

.btns {
	width: 100%;
	height: 35px;
	line-height: 35px;
	border-radius: 5px;
	background-color: #ffffff;
	color: #172b4d;
	margin-top: 10px;
	padding-left: 12px;
	padding-right: 15px;
	display: flex;
	align-items: center;

	span {
		flex: 1;
	}

	.right {
		width: 8px;
		height: 8px;
		border-top: 1px solid #6b778c;
		border-right: 1px solid #6b778c;
		transform: rotate(45deg);
	}
}
.par {
	margin-left: 15px;
}
.ql {
	margin-top: 12px;
	.nk {
		margin-left: 15px;
		color: #6b778c;
		font-size: 12px;
	}
	.mc-content__groupList {
		scrollbar-width: none; /* firefox */
		-ms-overflow-style: none; /* IE 10+ */
		&::-webkit-scrollbar {
			display: none; /* Chrome Safari */
		}
	}
}
.ifr {
	width: 100%;
	border: none;
	height: 1px;
	border-radius: 5px;
	background-color: var(--ac-bg-color--fff);
	min-width: 310px;
}
.ifr1 {
	width: 100%;
	border: none;
	height: 450px;
	border-radius: 5px;
	background-color: var(--ac-bg-color--fff);
	min-width: 310px;
}
.scroll-down {
	position: fixed;
	bottom: calc(65px + constant(safe-area-inset-bottom)); //兼容 IOS<11.2
	bottom: calc(65px + env(safe-area-inset-bottom));
	left: 50%;
	transform: translateX(-50%);
	img {
		width: 29px;
		height: 29px;
	}
}
// 华东动画
.slide-enter-active,
.slide-leave-active {
	transition: all 0.2s ease-in;
}
.slide-enter-from,
.slide-leave-to {
	transform: translate(-50%, 25px);
	transform: translate(-50%, 25px);
	opacity: 0;
}
.content-docList {
	width: 80%;
	background-color: var(--ac-bg-color--fff);
	padding: 8px;
	border-top-left-radius: 8px;
	border-bottom-right-radius: 8px;
	border-top-right-radius: 8px;
	display: flex;
	flex-direction: column;
	margin-bottom: 12px;
	.docList-list {
		max-height: 225px;
		overflow-y: auto;
		.docList-list-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			.docList-list-header__name {
				font-size: 15px;
				font-weight: 500;
			}

			.docList-list-header__right {
				display: flex;
				align-items: center;
				.docList-list-header__right--icon {
					width: 15px;
					height: 15px;
					object-fit: cover;
					margin-top: 0;
				}
				.docList-list-header__right--icon:nth-child(1) {
					margin-right: 8px;
				}
				.docList-list-header__right--icon:nth-child(2) {
					width: 13px;
					height: 13px;
				}
			}
		}
		.docList-list-item {
			background-color: var(--ac-bg-color);
			border-radius: 5px;
			margin-top: 8px;
			position: relative;
			display: flex;
			align-items: center;
			padding: 8px;
			img {
				height: 50px;
				width: 50px;
				margin-right: 8px;
				margin-top: 0;
			}

			.amd-content-item-desc {
				flex: 1;
				display: flex;
				flex-direction: column;
				font-size: 12px;
				.desc-name {
					font-weight: 500;
					font-size: 14px;
				}

				.desc-hp {
					width: 100%;
				}
			}
		}
	}

	.docList-btns {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		.docList-btns-item {
			background-color: var(--ac-bg-color);
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 8px 12px;
			width: 48.5%;
			margin-top: 8px;
		}
	}

	.content-docList-num {
		margin-top: 8px;
		display: flex;
		flex-wrap: wrap;
		border-top: 1px solid rgba(221, 221, 221, 0.35);
		padding-top: 12px;
		.num-item {
			margin-bottom: 8px;
			margin-right: 2%;
			width: 49%;
			border-radius: 5px;
			background-color: var(--ac-bg-color);
			padding: 8px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.num-item-name {
				width: calc(100% - 12px);
			}
		}
		& .num-item:nth-child(2n) {
			margin-right: 0;
		}
	}

	.content-docList-activity {
		margin-top: 8px;
		display: flex;
		flex-direction: column;
		.activity-item {
			width: 100%;
			background-color: var(--ac-bg-color);
			border-radius: 5px;
			padding: 8px;
			margin-bottom: 8px;
			display: flex;
			flex-direction: column;
			.activity-item--name {
				font-size: 15px;
				font-weight: 500;
			}
			.activity-item--desc {
				color: #6b778c;
				font-size: 12px;
			}
		}

		.content-docList-activity-more {
			color: var(--ac-font-color-active);
			text-align: right;
		}
	}
}
::v-deep(.action-popup) {
	background-color: #f4f5f7;
}
.action {
	padding: 0 15px;

	&-title {
		color: #172b4d;
		font-weight: bold;
		font-size: 15px;
	}
	&-list {
		margin-top: 8px;
		&-item {
			border-radius: 6px;
			background-color: #ffffff;
			padding: 12px;
			margin-bottom: 15px;
			display: flex;
			flex-direction: column;
			.task-item__header {
				padding-bottom: 12px;
				border-bottom: 1px solid #dfe1e6;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.task-item__header-left {
					display: flex;
					align-items: center;
					.left-icon {
						height: 12px;
						display: inline-block;
						width: 4px;
						background-color: #0052cc;
						margin-right: 6px;
					}
					.left-tag {
						margin-left: 6px;
					}
				}
				.task-item__header-right {
					span {
						font-size: 12px;
					}
				}
			}

			.task-item__body {
				padding-top: 12px;
				display: flex;
				flex-direction: column;
				position: relative;
				.task-item__body--img {
					position: absolute;
					right: 5px;
					top: 50%;
					z-index: 9;
					width: 4px;
					height: 8px;
					margin-bottom: 4px;
				}
				.body-item {
					display: flex;
					align-items: flex-start;
					margin-bottom: 10px;
					& span:nth-child(1) {
						width: 75px;
					}
					& span:nth-child(2) {
						flex: 1;
						word-break: break-all;
					}
				}
			}

			.task-item__bottom {
				display: flex;
				justify-content: flex-end;
				.task-item__bottom_btn {
					margin-right: 8px;
					&:deep(.van-button__text) {
						color: #0052cc;
					}
				}
			}
		}
	}
}
</style>
