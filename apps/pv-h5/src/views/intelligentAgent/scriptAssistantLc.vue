<template>
	<div id="ap-script-assistant" class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div
				@click="goBack"
				:style="{
					visibility: route?.query?.isHeaderBackShow === 'false' ? 'hidden' : '',
				}"
				class="mc-header-icon"
			>
				<van-icon :size="20" name="arrow-left" />
			</div>
			<div class="mc-header-name">
				<span class="ell">{{ agentName }}</span>
			</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">
				<img @click="acGlobalVoice(false)" v-show="isGlobalVoice" src="@/assets/img/global-voice-active.png" />
				<img @click="acGlobalVoice(true)" v-show="!isGlobalVoice" src="@/assets/img/global-voice.png" />
				<!-- 多会话历史记录 -->
				<img v-if="isMultiChat" @click="openChatHistory" class="history-img" src="@/assets/img/history.png" />
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div id="aiCon" class="mc-content-list clearfix">
				<div v-for="(item, index) in chatList" :key="index">
					<template v-if="item.type === 'user'">
						<div :key="item.id" class="mc-content-list-right">
							<!-- 用户内容模版 -->
							<div @click="editorMsg(index, item)" class="mc-content-template">
								<p style="white-space: pre-wrap">{{ item.msg }}</p>
								<!-- 是否可以修改 -->
								<div class="mc-content-template__edit" v-if="index === chatList.length - 2"></div>
							</div>
						</div>
					</template>
					<template v-if="item.type === 'FAQ'">
						<div :key="item.id" class="mc-content-list-left">
							<div class="par">
								<div class="mc-content-template">
									<div class="q-content">
										<div class="q-title">
											<span>您可以试着问</span>
											<span @click="moreQ">更多</span>
										</div>
										<div class="q-list" v-for="item in questionsList" :key="item" @click="qesEv(item)">
											<div class="q-list-text">{{ item.name }}</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</template>
					<template v-if="item.type === 'ai'">
						<div :key="item.id" class="mc-content-list-left">
							<div class="par" style="display: flex">
								<!-- AI 文本 内容模版 -->
								<div class="mc-content-template">
									<loading1 v-if="item.isLoading" :text="item.loadingText"></loading1>
									<!-- 纯文本 -->
									<ai-text v-if="!item.isIframe && !item.isLoading && item.msg" :id="item.id" :is-loading="item.loading" :msg="item.msg" :think="item.think || ''" :name="item.name"></ai-text>
									<!-- 卡片 -->
									<div v-if="item.isIframe && !item.isLoading">
										<div v-if="!item.isLoading" style="margin-top: 5px">
											<component
												class="mp0"
												:is="dom[item.iframeUrl.id]"
												:info="cardInfoC(item.iframeUrl.id)"
												:dinfo="cardInfoC(item.iframeUrl.id)"
												:summary="!item.noSummary"
												:inChat="true"
												:defaultValue="item.iframeUrl.params"
												:startTime="cardInfoC(item.iframeUrl.id).startTime"
												:endTime="cardInfoC(item.iframeUrl.id).endTime"
												:dataSyncTime="cardInfoC(item.iframeUrl.id).dataSyncTime"
												@summaryIng="afterMessage"
												@openfilter="openfilter"
											></component>
										</div>
										<!-- summary -->
										<div v-if="item.iframeLoadingFinished && !item.summary">正在生成点评，请稍后<span class="dots"></span></div>
										<div v-if="item.summary">
											<ai-text :id="item.id" :is-loading="item.summaryLoading" :msg="item.summary"></ai-text>
										</div>
									</div>
									<div v-if="item.responseType === 'followup' && !item.isLoading">
										<span>{{ item.responseResult.message }}</span>
										<div class="options">
											<div v-for="val in item.responseResult.options" :key="val" @click="sendMessage(val)" class="followup">{{ val.title }}</div>
										</div>
									</div>
									<div v-if="item.responseType === 'card_selection' && !item.isLoading">
										<span>{{ item.responseResult.content }}</span>
										<div>
											<div class="cards">
												<div class="cards-item" v-for="val in item.responseResult.options" :key="val" @click="sendMessage(val)">
													<img :src="val?.properties?.thumbnail" alt="" />
													<div>{{ val.title }}</div>
												</div>
											</div>
										</div>
									</div>
									<div v-if="item.responseType === 'help' && !item.isLoading">
										<span>{{ item.responseResult.introduction }}</span>
									</div>
									<div v-if="item.responseType === 'error' && !item.isLoading">
										<span>{{ item.responseResult.message }}</span>
									</div>
									<!-- 小按钮组 - 语音播报和复制 -->
									<ai-btns v-if="item.isBtns && !item.loading && !item.isLoading" :id="item.id" :reGen="item.reGen" :zeStatus="item.zeStatus" @_ze="zeEv" @_reGen="reGen" :voiceStatus="item.voiceStatus" @copyText="copyText" @voiceEv="voiceEv"></ai-btns>
								</div>
							</div>
							<!-- 问题列表 -->
							<div class="ql" v-if="item.questionList && item.questionList.length > 0 && showEtc(item)">
								<div class="nk">您可能还想问</div>
								<div class="mc-content__groupList">
									<span v-for="(val, index) in item.questionList" :key="val" @click="qesEv(val, index, item.questionList)">{{ val }}</span>
								</div>
							</div>
						</div>
					</template>
				</div>
			</div>
			<!-- 按钮组 -->
			<btn-list :qt-list="qtList" @qesMoreEv="recommendCardClick"></btn-list>
			<!-- 聊天框 -->
			<div class="mc-content-chat">
				<ai-textarea :sMenu="false" :ol="true" :toolsList="actions" :editorText="editorText" @_sendMessage="sendMessage" @_changeEdit="changeEdit" @_toolClick="toolClick"></ai-textarea>
			</div>
			<!-- 滚动到底部 -->
			<Teleport to="body">
				<Transition name="slide">
					<div v-if="showScrollDown" class="scroll-down" @click="scrollBottom()">
						<img src="@/assets/img/scrolldown.png" alt="" />
					</div>
				</Transition>
			</Teleport>
			<!-- 筛选 -->
			<van-overlay :show="comFilterShow" z-index="100" :lock-scroll="false">
				<div v-if="comFilterShow" class="wrapper">
					<div
						class="coms"
						:style="{
							paddingLeft: cardInfo.reportCd === 'topHospitalAnalysis' ? (isPc ? '13px' : '3.46667vw') : '',
							paddingRight: cardInfo.reportCd === 'topHospitalAnalysis' ? (isPc ? '13px' : '3.46667vw') : '',
						}"
					>
						<component ref="comRef" :is="dom[currentCom]" :info="cardInfo" :dinfo="cardInfo" :defaultValue="currentComDefault" :startTime="cardInfo.startTime" :endTime="cardInfo.endTime" :dataSyncTime="cardInfo.dataSyncTime"></component>
					</div>
					<div class="operation">
						<div class="cancel" @click="comFilterShow = false">取消</div>
						<div class="confirm" @click="createNewAnalysis">确认</div>
					</div>
				</div>
			</van-overlay>
		</div>
		<!-- 踩的理由 -->
		<van-action-sheet class="cai-action-sheet" v-model:show="caiShow" title="反馈">
			<van-field v-model="zeJson.feedback_reason" type="textarea" placeholder="请输入您觉得回答不满意的地方" />
			<van-button color="#0B67E4" @click="caiSubmit">提交</van-button>
		</van-action-sheet>
		<!-- 历史会话 -->
		<chat-history :historyList="historyList" ref="cHs" @_openChat="openHistoryChat"></chat-history>
		<!-- 问题列表 -->
		<questionListCom v-if="isMultiChat" ref="questionListComRef" @set-q="setQ" @q-c="qesEv"></questionListCom>
	</div>
</template>
<script setup>
import { showLoadingToast, showToast, closeToast } from 'vant';
import Decimal from 'decimal.js';
import { useI18n } from 'vue-i18n';
import axios, { CancelToken, isCancel } from 'axios';
import { useClickAway } from '@vant/use';

import { getToken } from '@/utils/auth';
import { uuid, formatParamsDates, formatKNumber, getRandomString, ruleAllList, addOrUpdateQueryParameter, getCurrentMonthRange } from '@/utils/index';

import { getHistoryApi, updateChatDescApi, initChatId, sendMsgApi, getChatDetailApi, updateMsgApi, deleteMsgApi, likeMsgApi, cancelLikeMsgApi, dislikeMsgApi, cancelDislikeMsgApi, dislikeFeedbackApi, getInterpretationApi, getGuessQuestionApi, addMsgApi } from '@/api/agent-tools';
import { branchHospitalSalesOverviewAmount, ddiTransactionSales, salesAchievementStatus, getApi, getTeamSalesTrends } from '@/api/sales';
import { getParentTerritoryInfo, getTerritoryCodeLevel } from '@/api/user';
import { getQuestion } from '@/api/filter';

import questionListCom from './components/questionList.vue';
import btnList from './components/btnList.vue';
const { t } = useI18n();
const { proxy } = getCurrentInstance();
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();

import { getAgentId } from '@/api/knowledge';

//会话
import { useScriptAssistant } from '@/hooks/web/useScriptAssistant.js';
let { interfaceUrl, isPc, agentName, route, router, chatList, goBack, createUserMsg, createAiMsg, getLastUser, getLastChat, chatId, isMultiChat, isStream, copyText, isLoading, zeJson, caiShow, cardsInfoList, cardInfoC, actions, qtList, getLastUserChat, relam } = useScriptAssistant();
//界面滚动
import { useScriptAssistantScroll } from '@/hooks/web/useScriptAssistantScroll';
const { showScrollDown, scrollBottom, aiConScroll } = useScriptAssistantScroll();
//语音
import { useScriptAssistantAudioPlayer } from '@/hooks/web/useScriptAssistantAudioPlayer.js';
const { ttsWS, voiceEv, audioPlayer, resetAudioVoice, userStore, isGlobalVoice, acGlobalVoice, language } = useScriptAssistantAudioPlayer(chatList);

// 发送的消息是否是二次编辑的消息
const editorText = ref('');
const editorMsg = (index, item) => {
	if (index === chatList.value.length - 2) {
		// 如果请求正在进行需要终止请求
		if (isLoading.value) {
			stopFetch();
			isLoading.value = false;
		}
		let msg = item.msg.replace(/<br\s*\/?\s*>/gi, '\n');
		editorText.value = msg + 'jjjjjj' + new Date().getTime();
		//删除后一条消息记录
		deleteMsgApi({ message_ids: [getLastChat.value.dataId, getLastUserChat.value.dataId] });
		// 清除chatList最后2位
		chatList.value.splice(-2);
	}
};
const changeEdit = () => {
	editorText.value = '';
};
//聊天工具点击
const toolClick = (item) => {
	if (item.type === 'path') {
		if (!item.url.includes('http')) {
			if (isPc.value) {
				router.push({
					name: item.url,
					query: {
						hiddenTarbar: true,
					},
				});
			} else {
				router.push({
					name: item.url,
				});
			}
		} else {
			location.href = item.url;
		}
	} else {
		recommendCardClick(item);
	}
};
//点踩提交
const caiSubmit = () => {
	if (!zeJson.feedback_reason) {
		showToast({
			message: '请输入您觉得回答不满意的地方',
		});
		return false;
	}
	caiShow.value = false;
	const j = chatList.value.filter((ele) => ele.id === zeJson.id)[0];
	dislikeFeedbackApi(j.dataId, { feedback: zeJson.feedback_reason });
};
//点赞点踩
const zeEv = ({ id, like, remark = '' }) => {
	const j = chatList.value.filter((ele) => ele.id === id)[0];
	if (like === '2') {
		// 踩
		zeJson.id = id;
		zeJson.feedback_reason = '';
		caiShow.value = true;
		dislikeMsgApi(j.dataId);
		j.zeStatus = like;
		return;
	} else if (like === '1') {
		// 赞
		likeMsgApi(j.dataId);
	} else {
		cancelLikeMsgApi(j.dataId);
		cancelDislikeMsgApi(j.dataId);
	}
	j.zeStatus = like;
};
//猜你想问
const qesEv = (val) => {
	if (isLoading.value) {
		stopFetch();
	}
	isLoading.value = true;
	chatList.value.push(createUserMsg(val.name || val));
	// 生产一个ai的消息
	chatList.value.push(createAiMsg());
	scrollBottom();
	setTextMessage(val.name || val);
};
// 重新生成
const reGen = (id) => {
	if (isLoading.value) {
		stopFetch();
	}
	// 获取文本信息
	let prevItemMsg = '';
	const index = chatList.value.findIndex((item) => item.id === id);

	if (index !== -1) {
		prevItemMsg = chatList.value[index - 1]?.msg ?? '';
	}
	try {
		chatList.value.push(createUserMsg(prevItemMsg));
		// 生产一个ai的消息
		chatList.value.push(createAiMsg());
		scrollBottom();
		setTextMessage(prevItemMsg);
	} catch (e) {
		console.log(e);
		showToast({
			position: 'top',
			message: '重新生成失败！',
		});
	}
};
// 发送消息
const sendMessage = (val) => {
	console.log(val);
	// 确保 val 存在且有效
	if (!val) {
		showToast({
			position: 'top',
			message: '请输入内容',
		});
		return;
	}
	// 处理包含 "PPT" 的跳转逻辑
	if (/ppt/i.test(val) && perStore?.reportChildren) {
		router.push({ name: perStore.reportChildren });
		return;
	}
	// 如果正在加载，先停止当前请求
	if (isLoading.value) {
		stopFetch();
	}
	// 设置加载状态
	isLoading.value = true;
	// 添加用户消息和 AI 消息
	const message = typeof val === 'string' ? val : val.title;
	chatList.value.push(createUserMsg(message));
	chatList.value.push(createAiMsg());
	// 滚动到底部并发送消息
	scrollBottom();
	setTextMessage(val);
};
//停止当前请求
const stopFetch = () => {
	while (controllerList.length > 0) {
		controllerList.pop()('取消请求');
	}
	controllerFetchList.forEach((ele) => ele.abort());

	if (!getLastChat.value) {
		controllerList = [];
		controllerFetchList = [];
		return;
	}
	getLastChat.value.loading = false;
	getLastChat.value.isLoading = false;
	if (getLastChat.value.msg) {
		getLastChat.value.msg = getLastChat.value.msg + '...';
	}
	if (getLastChat.value.summary) {
		getLastChat.value.summary = getLastChat.value.summary + '...';
	}
	if (!getLastChat.value.msg && !getLastChat.value.summary && !getLastChat.value.noSummary) {
		getLastChat.value.msg = '您停止生成了本次回答，请重新提交问题...';
		getLastChat.value.summary = '您停止生成了本次回答，请重新提交问题...';
	}
	getLastChat.value.summaryLoading = false;
	getLastChat.value.questionList = '';
	getLastChat.value.temQuestionList = '';
	// isLoading.value = false;

	controllerList = [];
	controllerFetchList = [];

	//更新消息
	getLastChat.value.noSummary = true;
	getLastChat.value.dataId ? updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) }) : '';
	nextTick(() => {
		if (getLastChat.value.afterStopAutoRead && isGlobalVoice.value) {
			getLastChat.value.voiceStatus = false;
			voiceEv({
				id: getLastChat.value.id,
				vType: 'play',
			});
		}
	});
};

//发送信息
const setTextMessage = async (val) => {
	try {
		getLastChat.value.isLoading = true;
		await sendMsgRequest(val);
	} catch (e) {
		handleError(e);
	}
};
let handleError = (e) => {
	showToast({
		position: 'top',
		message: '请求失败,请重试',
	});
	isLoading.value = false;
	// 清除chatList最后一位
	chatList.value.splice(-1, 1);
	console.error(e);
};
/**
 * 辅助函数：根据报告代码从cardsInfoList中获取日期范围
 * @param {string} reportCd - 报告代码
 * @param {{startLocalDate: string, endLocalDate: string}} defaultRange - 默认日期范围
 * @returns {{startTime: string, endTime: string}}
 */
const getReportDateRange = (reportCd, defaultRange) => {
	const cardInfo = cardsInfoList.value.find((item) => item.reportCd === reportCd);
	return {
		startTime: cardInfo?.startTime || defaultRange.startLocalDate,
		endTime: cardInfo?.endTime || defaultRange.endLocalDate,
	};
};

/**
 * 辅助函数：重命名对象或对象数组中的键
 * @param {object | object[]} data - 要处理的数据
 * @param {object} keyMap - 新旧键的映射关系, e.g. { oldKey: newKey }
 * @returns {object | object[]}
 */
const renameKeys = (data, keyMap) => {
	const renameObj = (obj) =>
		Object.keys(obj).reduce((acc, oldKey) => {
			const newKey = keyMap[oldKey] || oldKey;
			acc[newKey] = obj[oldKey];
			return acc;
		}, {});

	if (Array.isArray(data)) {
		return data.map(renameObj);
	}
	return renameObj(data);
};

/**
 * 辅助函数：处理品牌销售数据（格式化和计算）
 * @param {Array<object>} salesBrandItems - 来自API的品牌销售数据数组
 * @returns {Array<object>}
 */
const processSalesBrandData = (salesBrandItems) => {
	return salesBrandItems.map((item) => ({
		name: item.name,
		salesV: formatKNumber(item.salesV),
		targetV: formatKNumber(item.targetV),
		achievementRate: new Decimal(item.achievementRate * 100).toDecimalPlaces(0) + '%',
		yearOnYear: new Decimal(item.yearOnYear * 100).toDecimalPlaces(0) + '%',
		yearOnYearGrowth: formatKNumber(item.yearOnYearGrowth),
		chain: new Decimal(item.chain * 100).toDecimalPlaces(0) + '%',
		moMGrowth: formatKNumber(item.moMGrowth),
		salesVLm: item.salesVLm,
		salesVLy: item.salesVLy,
	}));
};

/**
 * 辅助函数：从HTML字符串中提取纯文本
 * @param {string} htmlString
 * @returns {string}
 */
const extractTextFromHtml = (htmlString) => {
	if (!htmlString) return '';
	const tempElement = document.createElement('div');
	tempElement.innerHTML = htmlString;
	return tempElement.innerText;
};
const init = async () => {
	// 1. 初始化状态
	isLoading.value = true;
	chatList.value.push({
		...createAiMsg(t('common.gdprfy'), false),
		afterStopAutoRead: true,
	});

	// 2. 获取基础参数 (区域代码, 权限)
	let territoryCode = [];
	if (relam === 'muqiao') {
		const value = '{"id":"salesTrendsOverview","text":"月度销售趋势分析","filterList":["org"],"filterInfo":[{"id":"org","dataRange":[],"dataDefault":["NCNSC"],"defaultList":[]}]}';
		const filterConfig = JSON.parse(value);
		const result = useOrg(filterConfig.filterInfo);
		if (result.personId) {
			territoryCode.push(result.personId);
		}
	}
	const isManager = !userStore.permission.includes('MR');
	const defaultDateRange = getCurrentMonthRange();

	// 3. 动态构建API请求列表
	const apiPromises = [];
	const salesStatusDates = getReportDateRange('salesAchievementStatus', defaultDateRange);
	apiPromises.push(
		salesAchievementStatus({
			nowLocalDateTime: salesStatusDates.startTime,
			endLocalDate: salesStatusDates.endTime,
			territoryCode,
		})
	);

	// 根据权限决定是否添加 getTeamSalesTrends 请求
	if (isManager) {
		const teamSalesDates = getReportDateRange('salesAnalysisTeam', defaultDateRange);
		apiPromises.push(
			getTeamSalesTrends({
				startLocalDate: teamSalesDates.startTime,
				endLocalDate: teamSalesDates.endTime,
				territoryCode,
			})
		);
	}

	const branchDates = getReportDateRange('branchSalesOverview2', defaultDateRange);
	apiPromises.push(
		branchHospitalSalesOverviewAmount({
			type: 'sales',
			startLocalDate: branchDates.startTime,
			endLocalDate: branchDates.endTime,
			territoryCode,
		})
	);

	const ddiDates = getReportDateRange('ddi', defaultDateRange);
	apiPromises.push(
		ddiTransactionSales(
			{
				startLocalDate: ddiDates.startTime,
				// [FIXED] 修正了原代码中可能存在的bug, endLocalDate使用了endTime
				endLocalDate: ddiDates.endTime,
				territoryCode,
			},
			relam
		)
	);

	// 4. 并发执行所有API请求
	const results = await Promise.all(apiPromises);

	// 5. 解析和处理API返回结果
	const [res3, res4, res5, res6] = isManager ? [results[0], results[1], results[2], results[3]] : [results[0], null, results[1], results[2]]; // 非Manager时, res4为null

	// 定义键名映射 (只需定义一次)
	const statusKeyMap = { salesMtd: '月销售额', salesRateMtd: '月销售达成率', yoyMtd: '月同比增长率', momMtd: '月环比增长率', salesRateQtd: '季度销售达成率', yoyQtd: '季度同比增长率', qoqQtd: '季度环比增长率', salesRateYtd: '年销售达成率', yoyYtd: '年同比增长率', hospSalesRate: '有销售额终端' };
	const productKeyMap = { achievementRate: '销售达成率', chain: '环比增长率', moMGrowth: '环比净增长销售额', name: '产品名称', salesV: '销售金额', salesVLm: '上月同期销售金额', salesVLy: '去年同期销售金额', targetV: '销售目标金额', yearOnYear: '同比增长率', yearOnYearGrowth: '同比净增长销售额' };

	// 使用辅助函数处理数据
	const processedSalesStatus = renameKeys(res3.result, statusKeyMap);
	const hospitalSalesText = extractTextFromHtml(res5.result.message);
	const processedProductSales = renameKeys(processSalesBrandData(res6.result.salesBrand), productKeyMap);

	// 6. 组合最终数据
	const data = {
		...processedSalesStatus,
		...(isManager && res4.result), // 使用条件展开, 仅在isManager为true时添加res4.result
		分医院: hospitalSalesText,
		分产品: processedProductSales,
	};
	console.log(data);

	// 7. 执行后续操作
	getLastChat.value.isLoading = true;
	await getGuess();
	getReport(data);
};
const recommendCardClick = (card, index) => {
	console.log(card);
	if (card.name === '业绩播报') {
		init();
	}
	return;
	if (isLoading.value) {
		stopFetch();
	}
	console.log(card);
	chatList.value.push(createUserMsg(card.name));
	// 生产一个ai的消息
	let message = {
		msg: '',
		dataId: '',
		type: 'ai',
		id: uuid(),
		loading: true,
		isBtns: true,
		reGen: true,
		voiceStatus: true,
		zeStatus: '0',
		isIframe: true,
		summaryLoading: true, //加载summary
		isLoading: false,
	};
	// 构建查询字符串
	const { params, url } = card;
	if (params) {
		const queryString = Object.entries(params)
			.map(([key, value]) => `${key}=${value}`)
			.join('&');
		message.iframeUrl = url + '?' + queryString;
	} else {
		message.iframeUrl = `${url}`;
	}
	message.url = url;
	chatList.value.push(message);

	scrollBottom();
	// setTextMessage(val);
};

// 收到iframe传入的消息后处理卡片
const comFilterShow = ref(false);
let currentCom = ref('');
let cardInfo = ref({});
let currentComDefault = ref({});
//加载异步组件
const { dom } = useChatCard();
const afterMessage = async (event) => {
	console.log(event);
	/* 	卡片加载完成后获取数据
  有数据进行下一步获取总结和猜你想问、下一步行动计划 */
	if (event.data) {
		isLoading.value = true;
		getLastChat.value.summary = '';
		getSummary({
			type: 'cardComSummary',
			Question: getLastUser.value,
			data: { ...event.data },
			params: event.params,
		});
		getLastChat.value.iframeLoadingFinished = true;
	}
	if (event.noSummary) {
		getLastChat.value.summaryLoading = false;
		getLastChat.value.loading = false;
		getLastChat.value.isLoading = false;
		isLoading.value = false;
		//更新消息
		getLastChat.value.noSummary = true;
		await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
	}
};
const openfilter = (data) => {
	currentComDefault.value = data.defaultValue;
	currentCom.value = data.comName;
	cardInfo.value = cardsInfoList.value.find((ele) => ele.reportCd === data.comName);
	console.log(cardInfo.value);
	comFilterShow.value = true;
};
//创建新分析
const comRef = ref(null);
const comQuery = ref({});
const createNewAnalysis = async () => {
	comFilterShow.value = false;
	comQuery.value = JSON.parse(JSON.stringify(comRef.value.query));
	console.log(comQuery.value);
	// return;
	if (isLoading.value) {
		stopFetch();
	}

	chatList.value.push(createUserMsg('基于新的数据范围帮您分析'));
	let res = await addMsgApi(
		{ conversation_id: chatId.value, content: JSON.stringify(getLastChat.value), role: 'user', message_type: 'string' },
		{
			cancelToken: new CancelToken((c) => controllerList.push(c)),
		}
	);
	getLastChat.value.dataId = res.data.message_id;
	await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
	// 生产一个ai的消息
	let message = {
		msg: '',
		dataId: '',
		type: 'ai',
		id: uuid(),
		loading: true,
		isBtns: true,
		reGen: true,
		voiceStatus: true,
		zeStatus: '0',
		isIframe: true,
		summaryLoading: true, //加载summary
		isLoading: false,
	};
	// 构建查询字符串
	if (comQuery.value) {
		let newQuery = JSON.parse(JSON.stringify(comQuery.value));
		newQuery.startTime = `${newQuery.startDate?.slice(0, 4)}-${newQuery.startDate?.slice(4, 6)}-01`;
		newQuery.endTime = `${newQuery.endDate?.slice(0, 4)}-${newQuery.endDate?.slice(4, 6)}-01`;
		newQuery.brandName = (newQuery.productName ? newQuery.productName : newQuery.brandName) || '';
		newQuery.brandId = (newQuery.productId ? newQuery.productId : newQuery.brandId) || '';

		delete newQuery.startDate;
		delete newQuery.endDate;
		delete newQuery.productName;
		delete newQuery.productId;

		console.log(newQuery);
		message.iframeUrl = {
			id: currentCom.value,
			params: useParams(currentCom.value, cardsInfoList.value, newQuery),
		};
	}
	chatList.value.push(message);
	let res1 = await addMsgApi(
		{ conversation_id: chatId.value, content: JSON.stringify(getLastChat.value), role: 'assistant', message_type: 'string' },
		{
			cancelToken: new CancelToken((c) => controllerList.push(c)),
		}
	);
	getLastChat.value.dataId = res1.data.message_id;
	await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
	scrollBottom();
};

//更多问题
const questionsList = ref([]);
const questionListComRef = ref(null);
const moreQ = () => {
	questionListComRef.value.openPopup();
};
const setQ = (v) => {
	if (Array.isArray(v) && v.length >= 3) {
		const shuffled = v.sort(() => 0.5 - Math.random());
		questionsList.value = shuffled.slice(0, 3);
	} else {
		questionsList.value = v;
		console.warn('传入数据不足三条或格式不正确:', v);
	}
};
//历史会话
const historyList = ref([]);
const cHs = ref(null);
const openHistoryChat = (item) => {
	if (item.chatId === chatId.value) return;
	if (isLoading.value) {
		showToast({
			message: '正在回答，请稍后...',
			position: 'top',
		});
		return;
	}
	chatList.value = [];
	chatId.value = item.chatId;
	const params = new URLSearchParams(window.location.search);
	params.set('chatId', chatId.value); // 添加或更新 query 参数
	const newUrl = window.location.pathname + '?' + params.toString();
	history.replaceState(null, '', newUrl);
	getChatList();
};
//获取历史会话记录
const getHistoryList = async () => {
	let res = await getHistoryApi({ limit: 1000 });
	let allList = res.data.items.filter((ele) => ele.description);
	allList = allList.reduce((acc, cur) => {
		const index = acc.findIndex((item) => item.created_at.split('T')[0] === cur.created_at.split('T')[0]);
		if (index !== -1) {
			acc[index].children.push(cur);
		} else {
			acc.push({
				created_at: cur.created_at,
				name: cur.created_at.split('T')[0],
				children: [cur],
			});
		}
		return acc;
	}, []);
	if (allList.length > 0) {
		allList = ruleAllList(allList, language.value);
	}
	historyList.value = allList;
};
const openChatHistory = () => {
	cHs.value.showLeft = true;
};
//根据会话id获取对话详情
const getChatList = async () => {
	let res = await getChatDetailApi(chatId.value, { limit: 1000 });
	let list = res.data.messages;
	let transformlist = [];
	if (list.length < 1) return;
	for (let item of list) {
		if (item.role === 'user') {
			let data;
			try {
				let content = JSON.parse(item.content);
				data = content;
			} catch (error) {
				data = createUserMsg(item.content);
				data.dataId = item.id;
			}
			transformlist.push(data);
		} else {
			let data = JSON.parse(item.content);
			data.zeStatus = !item.is_disliked && !item.is_liked ? '0' : item.is_liked ? '1' : '2';
			data.summaryLoading = false;
			isLoading.value = false;
			data.loading = false;
			data.noSummary = true;
			transformlist.push(data);
		}
	}
	chatList.value = transformlist.concat(chatList.value);
	scrollBottom();
};
//axios取消
let controllerList = [];
//查卡片
const sendMsgRequest = (val) => {
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async (resolve) => {
		try {
			let res = await sendMsgApi(
				{
					conversation_id: chatId.value,
					user_query: typeof val === 'string' ? val : '',
					selection_type: val.type,
					selection_value: val.value,
				},
				{
					cancelToken: new CancelToken((c) => controllerList.push(c)),
				}
			);
			//初始化会话ID
			if (!route.query.chatId) {
				chatId.value = res.data.conversation_id;
				const params = new URLSearchParams(window.location.search);
				params.set('chatId', chatId.value); // 添加或更新 query 参数
				const newUrl = window.location.pathname + '?' + params.toString();
				history.replaceState(null, '', newUrl);
			}

			console.log(res);
			getLastChat.value.responseType = res.data.type;
			getLastChat.value.responseResult = res.data.payload;
			getLastChat.value.dataId = res.data.metadata.assistant_message_id;
			getLastUserChat.value.dataId = res.data.metadata.user_message_id;
			getLastChat.value.loading = false;
			getLastChat.value.isLoading = false;

			isLoading.value = false;
			//更新会话描述
			if (chatList.value.filter((ele) => ele.type === 'user')?.length === 1) {
				updateChatDescApi(chatId.value, {
					description: getLastUser(),
				});
			}
			//处理卡片逻辑
			if (res.data.type === 'tool' && res.data.payload.category === 'card') {
				handleCard();
			}
			//获取猜你想问
			await getGuess();
			//更新消息
			await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
			resolve(res);
		} catch (error) {
			console.log(error, 'xxxxxxxxxxxxxxxxxxxxxxx');
		}
	});
};
const handleCard = async (type) => {
	try {
		let resData = getLastChat.value.responseResult;
		let resType = getLastChat.value.responseType;

		if (resData) {
			// if (resType === 'card') {
			getLastChat.value.isIframe = true;
			getLastChat.value.summaryLoading = true; //加载summary
			// }
			//完成后的逻辑
			if (getLastChat.value.isIframe) {
				let params = {
					startTime: resData.filters.find((ele) => (ele = ele.key === 'time'))?.value.start_time,
					endTime: resData.filters.find((ele) => (ele = ele.key === 'time'))?.value.end_time,
					brandId: resData.filters.find((ele) => (ele = ele.key === 'product'))?.value.id,
					brandName: resData.filters.find((ele) => (ele = ele.key === 'product'))?.value.name,
					personId: resData.filters.find((ele) => (ele = ele.key === 'organization'))?.value.id,
					personName: resData.filters.find((ele) => (ele = ele.key === 'organization'))?.value.name,
					province: resData.filters.find((ele) => (ele = ele.key === 'region'))?.value.properties?.province,
					city: resData.filters.find((ele) => (ele = ele.key === 'region'))?.value.properties?.city,
					hospitalId: resData.filters.find((ele) => (ele = ele.key === 'hospital'))?.value.id,
					hospitalName: resData.filters.find((ele) => (ele = ele.key === 'hospital'))?.value.name,
					indicatorType: resData.metrics?.[0]?.key,
				};
				let url = resData.route_key;
				if (params) {
					getLastChat.value.iframeUrl = {
						id: url,
						params: useParams(url, cardsInfoList.value, params),
					};
				} else {
					getLastChat.value.iframeUrl = {
						id: url,
					};
				}
				getLastChat.value.isLoading = false;
			}
		}
	} catch (e) {
		console.error('解析响应失败：', e);
		return null; // 或根据需要处理错误
	}
};
// 1. 导入 useApi
import { useApi } from '@/hooks/web/fetchApi';
const { apiFetch } = useApi();
let controllerFetchList = [];
/**
 * 通用的流式API请求处理器
 * @param {object} config - 配置对象
 * @param {string} config.endpoint - API的端点URL
 * @param {object} config.payload - 发送给API的请求体数据
 * @param {function(object): void} config.onData - 每当接收并解析到一条数据时调用的回调函数
 * @param {function(): Promise<void>} config.onDone - (可选) 数据流接收完毕时调用的异步回调函数
 * @param {function(Error): void} [config.onError] - (可选) 发生错误时调用的回调函数
 */
const handleStreamedApiRequest = async (config) => {
	const { endpoint, payload, onData, onDone, onError } = config;
	const controller = new AbortController();
	controllerFetchList.push(controller); // 管理AbortController实例
	try {
		const response = await apiFetch(endpoint, {
			signal: controller.signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
			},
			body: JSON.stringify(payload),
		});

		if (!response.ok || !response.body) {
			throw new Error(`API请求失败，状态码: ${response.status}`);
		}

		const reader = response.body.getReader();
		const decoder = new TextDecoder('utf-8');
		// 创建一个缓冲区来存储不完整的数据
		let buffer = '';
		// 循环读取流
		// eslint-disable-next-line no-constant-condition
		while (true) {
			const { done, value } = await reader.read();
			if (done) {
				console.log('数据流接收完毕。');
				if (onDone) onDone();
				break;
			}
			buffer += decoder.decode(value, { stream: true });
			// SSE消息以 '\n' 分隔
			while (buffer.includes('\n')) {
				const separatorIndex = buffer.indexOf('\n');
				// 1. 提取出完整的一行数据
				const line = buffer.slice(0, separatorIndex).trim();
				// 2. 从缓冲区中移除已经处理过的行和换行符
				buffer = buffer.slice(separatorIndex + 1);
				// 如果行是空的，则跳过（这可以处理连续的换行符或心跳空行）
				if (!line) {
					continue;
				}
				// 3. 直接处理这一行
				if (line.startsWith('data:')) {
					const jsonString = line.substring(5).trim();
					if (jsonString && !jsonString.includes('[DONE]')) {
						try {
							const dataObject = JSON.parse(jsonString);
							// 假设 onData 回调函数存在
							if (onData) onData(dataObject);
						} catch (error) {
							console.error('解析JSON失败:', error, '原始JSON字符串:', jsonString);
						}
					}
				}
			}
		}
	} catch (error) {
		console.error('流式请求或处理过程中发生错误:', error);
		if (onError) onError(error); // 调用错误处理回调
	}
};
// 获取解读（优化后）
const getSummary = async (data) => {
	if (isStream.value) {
		await handleStreamedApiRequest({
			endpoint: `${interfaceUrl}/API-AIP/chat/analyze`,
			payload: {
				stream: true,
				conversation_id: chatId.value,
				data: JSON.stringify(data),
			},
			onData: (dataObject) => {
				if (dataObject.type === 'content') {
					getLastChat.value.summary += dataObject.data.content;
				}
			},
			onDone: async () => {
				getLastChat.value.summaryLoading = false;
				isLoading.value = false;
				getLastChat.value.loading = false;
				getLastChat.value.noSummary = true;
				await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
				scrollBottom();
			},
			onError: () => {
				// 统一处理错误状态
				getLastChat.value.summaryLoading = false;
				isLoading.value = false;
				getLastChat.value.loading = false;
			},
		});
	} else {
		let res = await getInterpretationApi(
			{ conversation_id: chatId.value, data: JSON.stringify(data), stream: true },
			{
				cancelToken: new CancelToken((c) => controllerList.push(c)),
			}
		);
		getLastChat.value.summary = res.data.analysis;
		getLastChat.value.summaryLoading = false;
		isLoading.value = false;
		getLastChat.value.loading = false;
		//更新消息
		getLastChat.value.noSummary = true;
		await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
	}
};

// 获取业绩播报（优化后）
const getReport = async (data) => {
	if (isStream.value) {
		// 注意：这个 isLoading = false 的操作特定于 getReport，所以留在这里
		getLastChat.value.isLoading = false;
		await handleStreamedApiRequest({
			endpoint: `${interfaceUrl}/API-AIP/chat/generic_analysis`,
			payload: {
				stream: true,
				conversation_id: chatId.value,
				data: JSON.stringify(data),
				response_format: 'markdown',
				system_prompt: '基于销售数据生成每日销售播报。',
			},
			onData: (dataObject) => {
				if (dataObject.type === 'content') {
					getLastChat.value.msg += dataObject.data.content;
				}
			},
			onDone: async () => {
				nextTick(() => {
					if (getLastChat.value.afterStopAutoRead && isGlobalVoice.value) {
						getLastChat.value.voiceStatus = false;
						voiceEv({ id: getLastChat.value.id, vType: 'play' });
					}
				});
				isLoading.value = false;
				getLastChat.value.loading = false;
				await updateMsgApi(getLastChat.value.dataId, { content: JSON.stringify(getLastChat.value) });
				scrollBottom();
			},
			onError: () => {
				isLoading.value = false;
				getLastChat.value.loading = false;
			},
		});
	}
	// 原函数没有else分支，这里保持一致
};
//获取问题
let questionListAll = [];
const getQuestionList = async () => {
	const res = await getQuestion({ username: userStore.userInfo.username });
	questionListAll = res.result.content.map((ele) => ele.basicQuestion);
	return Promise.resolve();
};
//获取猜你想问
const getGuess = async () => {
	let res = await getGuessQuestionApi(
		{ data: questionListAll, conversation_id: chatId.value, response_format: 'text', stream: false, system_prompt: '请根据提供的业务数据和用户历史对话，从中选3个相关问题。只返回示例数据中存在的。固定返回格式 ["问题一","问题二","问题三"]' },
		{
			cancelToken: new CancelToken((c) => controllerList.push(c)),
		}
	);
	getLastChat.value.questionList = res.data.analysis ? JSON.parse(res.data.analysis) : [];
};
//处理猜你想问和下一步行动计划出现时机
const showEtc = computed(() => {
	return (item) => {
		if (item.isIframe) {
			if (!item.summaryLoading) {
				return true;
			}
		} else {
			if (!item.loading) {
				return true;
			}
		}
	};
});
onMounted(async () => {
	nextTick(() => {
		const aiCon = document.querySelector('#aiCon');
		aiCon.addEventListener('scroll', aiConScroll);
	});
	//获取会话历史记录
	getHistoryList();
	if (route.query.chatId) {
		chatId.value = route.query.chatId;
		getChatList();
	}
	await getQuestionList();
});
onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
	// 停止当前请求
	stopFetch();
	// 关闭语音
	resetAudioVoice();
	ttsWS?.close();
	audioPlayer.reset();
});

onUnmounted(() => {
	window.removeEventListener('scroll', aiConScroll);
});
defineExpose({ userStore });
</script>
<style lang="scss" scoped>
@import '../../style/pc/intelligent/scriptAssistant/index.scss';
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
			.history-img {
				margin-left: 12px;
				margin-right: 24px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
			display: flex;
			align-items: center;
			justify-content: center;
			flex: 1;
			width: 265px;
			span {
				white-space: nowrap;
			}
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-list {
			flex: 1 0 0px;
			height: 0px;
			padding: 0 0;
			padding-top: 12px;
			overflow-y: auto;
			.mc-content-list-left {
				display: flex;
				flex-direction: column;
				width: 100%;
				margin-bottom: 12px;
				.mc-content-template {
					border-top-left-radius: 8px;
					border-bottom-right-radius: 8px;
					border-top-right-radius: 8px;
				}
				img {
					width: 26px;
					height: 26px;
					margin-right: 6px;
					margin-top: 6px;
				}

				.mc-content__groupList {
					padding: 8px 0 0 0;
					padding-left: 15px;
					display: flex;
					display: flex;
					overflow-x: auto;
					width: 100%;
					span {
						flex: none;
						margin-right: 8px;
						margin-bottom: 8px;
						color: #0b67e4;
						border-radius: 5px;
						border: 1px solid #0b67e4;
						padding: 3px 6px;
					}
				}
			}

			.mc-content-list-right {
				padding-right: 15px;
				display: flex;
				margin-bottom: 12px;
				justify-content: flex-end;
				.mc-content-template {
					border-top-right-radius: 8px;
					border-bottom-left-radius: 8px;
					border-top-left-radius: 8px;
					color: var(--ac-bg-color--fff);
					background-color: var(--ac-bg-active-color);
					.mc-content-template__edit {
						border-top: solid 1px rgba(223, 225, 230, 0.3);
						margin-top: 2px;
					}
				}
			}
			.mc-content-template {
				max-width: 330px;
				min-width: 52px;
				background-color: var(--ac-bg-color--fff);
				padding: 10px;
			}
		}

		// 声明
		.mc-declare {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 6px 0px 3px 0px;
			font-size: 12px;
			color: var(--ac-colors-myGray-500);
		}

		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 8px;
			padding-bottom: 6px;
			min-height: 64px;
			position: relative;
			display: flex;
			align-items: flex-start;
		}
	}

	.mc-content__h5 {
		height: calc(100vh - 44px);
	}

	@supports (height: 100dvh) {
		.mc-content__h5 {
			height: calc(100dvh - 11.733333vw); /* 更好适配移动端软键盘变化 */
		}
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}
	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
		background-color: #f4f5f7;
	}
	&:deep(.cai-action-sheet) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 0;
		.van-action-sheet__header {
			font-size: 15px;
			color: #172b4d;
			font-weight: 500;
		}
		i {
			font-size: 16px;
			color: #000000;
		}

		.van-cell {
			background-color: #f4f5f7;
			margin: 0 15px 15px;
			width: calc(100% - 30px);
			border-radius: 6px;
			height: 125px;
		}

		.van-button {
			width: 238px;
			height: 40px;
			border-radius: 21px;
			font-size: 17px;
			margin-bottom: 20px;
			margin-left: 68.5px;
		}
	}
}
.btns {
	width: 100%;
	height: 35px;
	line-height: 35px;
	border-radius: 5px;
	background-color: #ffffff;
	color: #172b4d;
	margin-top: 10px;
	padding-left: 12px;
	padding-right: 15px;
	display: flex;
	align-items: center;

	span {
		flex: 1;
	}

	.right {
		width: 8px;
		height: 8px;
		border-top: 1px solid #6b778c;
		border-right: 1px solid #6b778c;
		transform: rotate(45deg);
	}
}
.par {
	margin-left: 15px;
}
.ql {
	margin-top: 12px;
	.nk {
		margin-left: 15px;
		color: #6b778c;
		font-size: 12px;
	}
	.mc-content__groupList {
		scrollbar-width: none; /* firefox */
		-ms-overflow-style: none; /* IE 10+ */
		&::-webkit-scrollbar {
			display: none; /* Chrome Safari */
		}
	}
}
.scroll-down {
	position: fixed;
	bottom: calc(65px + constant(safe-area-inset-bottom)); //兼容 IOS<11.2
	bottom: calc(65px + env(safe-area-inset-bottom));
	left: 50%;
	transform: translateX(-50%);
	img {
		width: 29px;
		height: 29px;
	}
}
// 华东动画
.slide-enter-active,
.slide-leave-active {
	transition: all 0.2s ease-in;
}
.slide-enter-from,
.slide-leave-to {
	transform: translate(-50%, 25px);
	transform: translate(-50%, 25px);
	opacity: 0;
}
::v-deep(.van-overlay) {
	background: rgba(0, 0, 0, 0.5);
	.wrapper {
		border-radius: 6px;
		background-color: #f4f5f7;
		// width: 90%;
		position: relative;
		top: 20%;
		left: 50%;
		transform: translateX(-50%);
		padding-bottom: 18px;
		.card-title {
			display: none;
		}
		.update-box {
			display: none;
		}
		.coms {
			max-height: 400px;
			overflow-y: auto;
		}
		.operation {
			display: flex;
			padding: 0 22px;
			div {
				width: 145px;
				flex: 1;
				line-height: 44px;
				border-radius: 6px;
				text-align: center;
				box-shadow: 0px 2px 9px 0px rgba(0, 82, 204, 0.1);
				font-size: 13px;
			}
			.cancel {
				color: #0052cc;
				background-color: rgba(0, 82, 204, 0.1);
				margin-right: 13px;
			}
			.confirm {
				background-color: #0052cc;
				color: #fff;
			}
		}
		&::before {
			content: '';
			display: block;
			width: 3%;
			height: 100%;
			position: absolute;
			left: 0;
			top: 0;
			background: rgba(0, 0, 0, 0.5);
		}
		&::after {
			content: '';
			display: block;
			width: 3%;
			height: 100%;
			position: absolute;
			right: 0;
			top: 0;
			background: rgba(0, 0, 0, 0.5);
		}
	}
}
@keyframes dotAnimation {
	0% {
		content: '';
	}
	33% {
		content: '.';
	}
	66% {
		content: '..';
	}
	100% {
		content: '...';
	}
}

.dots::after {
	content: '...';
	display: inline-block;
	animation: dotAnimation 1s infinite steps(3);
}
.q-content {
	.q-title {
		display: flex;
		justify-content: space-between;
		margin-bottom: 4px;
		span:nth-child(1) {
			font-weight: bold;
			font-size: 14px;
			color: #172b4d;
		}
		span:nth-child(2) {
			color: #0052cc;
		}
	}
	.q-list {
		// background: #ffffff;
		border-radius: 5px;
		margin-bottom: 8px;
		padding: 9px 12px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-right: 35px;
		color: #0b67e4;
		border: 1px solid #0b67e4;
		&::after {
			content: '';
			width: 7px;
			height: 7px;
			border-top: 1px solid #0b67e4;
			border-right: 1px solid #0b67e4;
			transform: rotate(45deg);
		}
	}
}
//新逻辑
:deep(.mp0) {
	width: 310px;
	margin: 0 !important;
	padding: 0 !important;
	.top .card-title {
		.icon {
			display: none;
		}
	}
}
.options {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	& > div {
		flex: 0 0 48%; /* 禁止增长，固定宽度为 50% */
		// margin-right: 2px;
	}
}
.cards {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	&-item {
		flex: 0 0 calc(50% - 4px);
		img {
			width: 100% !important;
			height: auto !important;
			object-fit: cover;
			aspect-ratio: 16 / 9; /* 设置宽高比，例如 16:9 */
		}
		div {
			font-size: 12px;
		}
	}
}
.followup {
	border: 1px solid #0052cc;
	border-radius: 5px;
	color: #0052cc;
	padding: 5px;
	margin-bottom: 5px;
}
</style>
