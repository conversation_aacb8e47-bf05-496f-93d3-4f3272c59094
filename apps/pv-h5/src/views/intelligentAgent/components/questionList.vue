<!-- FAQSearchPopup.vue -->
<template>
	<div id="question-list">
		<!-- 底部弹出的 Popup -->
		<van-popup v-model:show="showPopup" position="bottom" :close-on-click-overlay="true">
			<!-- title -->
			<div class="top">
				<span class="back" @click="showPopup = false"></span>
				<span>您可以试着问</span>
				<img src="@/assets/img/close-ii.png" alt="" @click="showPopup = false" />
			</div>
			<!-- 搜索输入框 -->
			<van-search v-model="searchQuery" placeholder="请输入关键词" @search="onSearch" @clear="onSearch" />
			<van-empty v-if="state.pageStatus === 'loading'" description="加载中..." />
			<van-empty v-if="state.pageStatus === 'empty'" description="暂无数据" />
			<!-- 建议列表 -->
			<van-list v-if="state.pageStatus === 'normal'" v-model:loading="state.isLoading" ref="vantList" :offset="30" :immediate-check="false" :finished="state.finished" finished-text="没有更多了" @load="onLoad">
				<div class="list">
					<div class="list-item" v-for="(item, index) in state.suggestions" :key="index" @click="qClick(item)">{{ item.basicQuestion }}</div>
				</div>
			</van-list>
		</van-popup>
	</div>
</template>
<script setup>
import { getQuestion } from '@/api/filter';
const { proxy } = getCurrentInstance();

// Popup 显示状态
const showPopup = ref(false);

// 搜索关键词
const searchQuery = ref('');

const state = reactive({
	pageStatus: 'normal',
	isLoading: false,
	finished: false,
	page: 0,
	suggestions: [],
});

// 搜索事件处理
const onSearch = (value) => {
	state.pageStatus = 'loading';
	// 这里可以添加搜索逻辑，例如过滤 suggestions 或调用 API
	console.log('搜索关键词:', value);
	state.page = 0;
	state.suggestions = [];
	getData();
};
// 添加首次调用标志，移到函数外部，默认为 true
const isFirstCall = ref(true);
// 暴露方法以便外部控制 Popup 显示
const openPopup = () => {
	showPopup.value = true;
};
const emit = defineEmits(['setQ', 'qC']);
const getData = async () => {
	const query = {
		username: proxy.$parent.userStore.userInfo.username,
		search: searchQuery.value,
	}; //请求传参
	try {
		const res = await getQuestion(query, state.page);
		console.log(res);
		const rows = res.result.content;
		state.isLoading = false;
		state.total = res.result.totalElements;
		if (rows == null || rows.length === 0) {
			state.pageStatus = 'empty';
			state.finished = true;
			return;
		}
		rows.forEach((item) => {
			item.url = item.cardUrl;
			item.name = item.basicQuestion;
		});
		state.suggestions = state.suggestions.concat(rows);
		state.pageStatus = 'normal';
		state.finished = false;
		if (state.suggestions.length >= state.total) {
			state.finished = true;
		}
		// 首次调用时抛出事件，传递第一页数据
		if (isFirstCall.value) {
			emit('setQ', state.suggestions);
			isFirstCall.value = false; // 更新标志，避免后续重复抛出
		}
	} catch (error) {
		state.isLoading = false;
		state.finished = true;
	}
};
const qClick = (item) => {
	showPopup.value = false;
	emit('qC', item);
};
const onLoad = () => {
	state.page++;
	getData();
};
onMounted(() => {
	getData();
});
defineExpose({
	openPopup,
	state,
});
</script>
<style lang="scss" scoped>
:deep(.van-popup) {
	background: #fff !important;
	padding: 28px 16px 20px !important;
	height: 80%;
	.top {
		display: flex;
		align-items: center;
		font-weight: bold;
		font-size: 16px;
		.back {
			width: 10px;
			height: 10px;
			border-top: 1px solid #000;
			border-right: 1px solid #000;
			transform: rotate(-135deg);
			margin-right: 13px;
		}
		img {
			margin-left: auto;
			width: 20px;
		}
	}
	.van-search {
		padding: 0;
		margin-top: 22px;
		.van-search__content {
			padding-left: 0;
			.van-search__field {
				height: 30px;
				padding: 0px 16px;
				.van-icon-search,
				.van-field__control::placeholder {
					color: #6b778c;
				}
			}
		}
	}
	.van-list {
		height: calc(100% - 58px - 20px);
		overflow-y: auto;
	}
	.list {
		&-item {
			padding: 16px 0;
			font-size: 15px;
			color: #172b4d;
			border-bottom: 1px solid rgba(151, 151, 151, 0.3);
		}
	}
}
</style>
