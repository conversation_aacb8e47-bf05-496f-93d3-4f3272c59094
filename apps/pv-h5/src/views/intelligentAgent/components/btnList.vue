<template>
	<div v-if="props.qtList.length > 0" class="mc-btns">
		<div v-for="(i, index) in props.qtList" :key="index" @click="qesMoreEv(i)" class="mc-btns-item">{{ i.name }}</div>
	</div>
</template>
<script setup>
const props = defineProps(['qtList']);

const emits = defineEmits(['qesMoreEv']);
const qesMoreEv = (i) => {
	emits('qesMoreEv', i);
};
</script>

<style lang="scss" scoped>
// 按钮组
.mc-btns {
	width: 100%;
	margin: 0px auto;
	padding: 4px 6px;
	overflow-x: auto;
	display: flex;
	white-space: nowrap;
	.mc-btns-item {
		margin-right: 6px;
		border: 1px solid var(--ac-border-color);
		padding: 4px 10px;
		border-radius: 20px;
		font-size: 12px;
		color: var(--ac-font-color);
	}
}

/* 当屏幕宽度大于600px时，应用以下样式 */
@media screen and (min-width: 600px) {
	// 按钮组
	.mc-btns {
		margin: 0px auto;
		padding: 4px 6px;
		.mc-btns-item {
			margin-right: 6px;
			border-width: 1px;
			padding: 4px 10px;
			border-radius: 20px;
			font-size: 12px;
		}
	}
}
</style>
