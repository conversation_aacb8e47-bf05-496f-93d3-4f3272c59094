<template>
	<div class="amd">
		<!-- header -->
		<div class="amd-header">
			<span>使用须知</span>
			<van-icon size="16" @click="close" name="cross" />
		</div>
		<!-- 内容 -->
		<div class="amd-content">
			<div class="amd-content-content" v-html="props.content"></div>
		</div>
	</div>
</template>
<script setup>
const props = defineProps(['content']);
const emits = defineEmits(['close']);
const close = () => {
	emits('close');
};
</script>

<style lang="scss" scoped>
.amd {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.amd-header {
		display: flex;
		align-items: center;
		padding: 0 12px;
		justify-content: space-between;
		span {
			font-weight: 500;
			font-size: 16px;
		}
	}

	.amd-content {
		width: 100%;
		flex: 1;
		margin-top: 12px;
		padding: 0 12px;
		overflow-y: auto;
		display: flex;
		flex-direction: column;

		.amd-content-content {
			width: 100%;
		}
	}
}
</style>
