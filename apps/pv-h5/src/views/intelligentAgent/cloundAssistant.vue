<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div
				@click="goBack"
				:style="{
					visibility: route?.query?.isHeaderBackShow === 'false' ? 'hidden' : '',
				}"
				class="mc-header-icon"
			>
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('chat.cloundAssistant') }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">
				<img @click="getChatHistory" src="@/assets/img/history.png" />
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div id="aiCon" class="mc-content-list clearfix">
				<template v-for="(item, index) in chatList">
					<template v-if="item.type === 'user'">
						<div :key="item.id" class="mc-content-list-right">
							<!-- 用户内容模版 -->
							<div @click="editorMsg(index, item)" class="mc-content-template">
								<!-- 纯文本 -->
								<div v-html="item.msg"></div>
								<!-- 是否可以修改 -->
								<div class="mc-content-template__edit" v-if="index === chatList.length - 2"></div>
							</div>
						</div>
					</template>
					<template v-else-if="item.type === 'desc'">
						<div :key="item.id" class="mc-content-desc">
							<img v-if="item.msg.img" :src="item.msg.img" />
							<img v-else src="@/assets/img/demo/article-default.png" />
							<div class="desc-info">
								<div class="desc-info-title ell2">{{ item.msg.title }}</div>
								<div class="desc-info-other">{{ item.msg.source }} {{ item.msg.publishDate.split(' ')[0] }}</div>
							</div>
						</div>
					</template>
					<template v-else-if="item.type === 'docCard'">
						<div :key="item.id" class="content-docList">
							<div class="docList-list">
								<div style="margin-top: 0" class="docList-list-item">
									<img v-if="item.msg.doctorSex === '女'" src="@/assets/img/demo/gril.png" />
									<img v-else src="@/assets/img/demo/men.png" />
									<div class="amd-content-item-desc">
										<div>
											<span class="desc-name" style="margin-right: 8px">{{ item.msg.doctorName }}</span>
											<span>{{ item.msg.doctorTitle }}</span>
										</div>
										<div class="desc-hp ell1">
											<span style="margin-right: 8px">{{ item.msg.hospital }}</span>
										</div>
									</div>
								</div>
							</div>
							<div class="docList-btns">
								<div @click="docBtnItem(item.msg, '概览')" class="docList-btns-item">
									<span>概览</span>
									<img class="cus-sj" src="@/assets/img/demo/sj.png" />
								</div>
								<div @click="docBtnItem(item.msg, '学术动态')" class="docList-btns-item">
									<span>学术动态</span>
									<img class="cus-sj" src="@/assets/img/demo/sj.png" />
								</div>
								<div @click="docBtnItem(item.msg, '科研动态')" class="docList-btns-item">
									<span>科研动态</span>
									<img class="cus-sj" src="@/assets/img/demo/sj.png" />
								</div>
								<div @click="docBtnItem(item.msg, '外部活动动态')" class="docList-btns-item">
									<span>外部活动动态</span>
									<img class="cus-sj" src="@/assets/img/demo/sj.png" />
								</div>
								<div @click="docBtnItem(item.msg, '内部活动参与')" class="docList-btns-item">
									<span>内部活动参与</span>
									<img class="cus-sj" src="@/assets/img/demo/sj.png" />
								</div>
								<div @click="docBtnItem(item.msg, '合作')" class="docList-btns-item">
									<span>合作</span>
									<img class="cus-sj" src="@/assets/img/demo/sj.png" />
								</div>
								<!-- <div @click="docBtnItem(item.msg, '认知倾向')" class="docList-btns-item">
									<span>认知倾向</span>
									<img class="cus-sj" src="@/assets/img/demo/sj.png" />
								</div> -->
							</div>
						</div>
					</template>
					<template v-else>
						<div :key="item.id" class="mc-content-list-left">
							<div style="display: flex">
								<!-- AI 头像 -->
								<img src="@/assets/img/demo/ai.png" />
								<!-- AI 文本 内容模版 -->
								<div class="mc-content-template">
									<!-- 纯文本 -->
									<ai-text :id="item.id" :is-loading="item.loading" :msg="item.msg"></ai-text>
									<!-- 小按钮组 - 语音播报和复制 -->
									<ai-btns v-if="item.isBtns && !item.loading" :id="item.id" :reGen="item.reGen" :zeStatus="item.zeStatus" @_ze="zeEv" @_reGen="reGen" :voiceStatus="item.voiceStatus" @copyText="copyText" @voiceEv="voiceEv"></ai-btns>
									<!-- 备注介绍 -->
									<div class="mc-content-template__remark" v-if="item.isRemark">
										<div class="remark-item">您正在准备一次针对该医生的面对面拜访，请根据该医生的**信息设计拜访话题生成150字以内的拜访话术</div>
										<div class="remark-item">
											<span style="font-weight: 500; width: 19.4667vw">话术场景：</span>
											<span>常规拜访的开场环节</span>
										</div>
										<div class="remark-item">
											<span style="font-weight: 500; width: 19.4667vw">拜访目的：</span>
											<span>加强关系、推广产品 </span>
										</div>
										<div class="remark-item">
											<span style="font-weight: 500; width: 19.4667vw">话术要求：</span>
											<span>结构清晰，三段式介绍，分段展现</span>
										</div>
									</div>
								</div>
							</div>
							<div class="mc-content__groupList" v-if="item.btnGroupList && item.btnGroupList.length > 0">
								<span v-for="val in item.btnGroupList" @click="docBtnItem({ doctorId: item.doctorId }, val)" :key="val">{{ val }}</span>
							</div>
						</div>
					</template>
				</template>
			</div>
			<!-- 声明 -->
			<div v-if="inForUse" class="mc-declare">{{ inForUse }}</div>
			<!-- 按钮组 -->
			<div v-if="qtList.length > 0" class="mc-btns">
				<div v-for="(i, index) in qtList" :key="index" @click="qesMoreEv(i)" class="mc-btns-item">
					{{ i }}
				</div>
			</div>
			<!-- 聊天框 -->
			<div ref="chatBox" class="mc-content-chat">
				<!-- 选择对话场景 -->
				<ai-textarea :sMenu="false" :ol="false" :toolsList="[]" :editorText="editorText" @_sendMessage="sendMessage" @_changeEdit="changeEdit"></ai-textarea>
			</div>
		</div>

		<!-- 资料列表 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="zlMoreVis" position="bottom" :style="{ height: '90%' }">
			<ai-more-articles :key="2" :title="zlTitle" @_activeArticle="activeArticle" @_articleDetail="articleDetail" @close="zlMoreVis = false"></ai-more-articles>
		</van-popup>

		<!-- 文章详情弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="articleDetailVis" position="bottom" :style="{ height: '90%' }">
			<ai-article-detail :sourceUrl="sourceUrl" @close="articleDetailVis = false"></ai-article-detail>
		</van-popup>

		<!-- 选择的文章 -->
		<div v-if="articleItem.articleId" class="article-item">
			<div class="article-item__header">
				<img v-if="articleItem.img" :src="articleItem.img" />
				<img v-else src="@/assets/img/demo/article-default.png" />
				<div @click="deleteArticleItem" class="header-close">
					<van-icon color="var(--ac-bg-color)" size="8" name="cross" />
				</div>
			</div>
			<div class="article-item__content ell3">{{ articleItem.title }}</div>
		</div>
		<!-- 更多医生 弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="docMoreVis" position="bottom" :style="{ height: '90%' }">
			<ai-more-doctors @close="docMoreVis = false" @activeDoc="activeDoc"></ai-more-doctors>
		</van-popup>
		<!-- 历史会话 -->
		<chat-history :historyList="historyList" ref="cHs" @_openChat="openHistoryChat"></chat-history>
	</div>
</template>
<script setup>
import { getToken } from '@/utils/auth';
import { showToast } from 'vant';
import useUserStore from '@/store/modules/user';
import ClipboardJS from 'clipboard';
import CryptoJS from 'crypto-js';
import AudioPlayer from '@/utils/ap';
import axios from 'axios';
import { uuid, ruleAllList, getRandomString, getQueryString } from '@/utils/index';
import { useClickAway } from '@vant/use';
import usefilterStore from '@/store/modules/filter';
import useEnterStore from '@/store/modules/enterprise';
import { getAgentListApi } from '@/api/agent-tools';
const filterStore = usefilterStore();
const enterStore = useEnterStore();
// 接口地址
const interfaceUrl = enterStore.enterInfo.interfaceAddress;
const domain = enterStore.enterInfo.domain;
const { proxy } = getCurrentInstance();
import { useI18n } from 'vue-i18n';
const audioPlayer = new AudioPlayer();
import { nextTick } from 'vue';
const userStore = useUserStore();
const { t } = useI18n();
import cpdc from '@/assets/img/cpdc.png';
import khdc from '@/assets/img/khdc.png';
import hszs from '@/assets/img/hszs.png';
import zlfx from '@/assets/img/zlfx.png';
import khbfxxsj from '@/assets/img/khbfxxsj.png';
import bfbj from '@/assets/img/bfbj.png';
const route = useRoute();
const router = useRouter();
// 会话id
let chatId = '';

// 语言环境
const language = computed(() => {
	return filterStore.language === 'zh' ? '中文' : '英文';
});

watch(
	() => route.query.chatId,
	(newVal, oldVal) => {
		if (newVal && !oldVal) {
			editorText.value = '';
			chatId = newVal;
			// 获取历史记录
			getHistoryList();
		}
		// 改变chatId
		if (oldVal && newVal && oldVal !== newVal) {
			editorText.value = '';
			// 清空聊天记录
			chatList.value = [];
			chatId = newVal;
			// 获取历史记录
			getHistoryList();
		}
	}
);

// 更新用户发出的消息
const updateUserMessage = (id, msg) => {
	axios({
		url: interfaceUrl + '/API-GPT/chat/message/update_history_query_meta_data',
		method: 'POST',
		params: {
			message_id: id,
		},
		data: msg,
		headers: {
			Authorization: `Bearer ${getToken()}`,
			'Content-Type': 'application/json;charset=UTF-8',
		},
	});
};

// 获取历史记录
const getHistoryList = () => {
	let cl = [];
	const json = {
		user_id: userStore.userInfo.username,
		chat_type: 'generate_script',
		conversation_id: chatId,
	};
	axios({
		url: interfaceUrl + '/API-GPT/chat/message/history',
		method: 'get',
		params: json,
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
	})
		.then((res) => {
			if (res.data.code === 200 && res.data.data.length > 0) {
				for (const item of res.data.data) {
					if (item.query.indexOf('医生的拜访话术') > -1 && item.response.indexOf('医生的拜访话术') > -1 && item.query_data.type === 'docCard') {
						cl.push({
							msg: {
								department: item.query_data.data.department,
								doctorName: item.query_data.data.doctorName,
								doctorId: item.query_data.data.doctorId,
								doctorSex: item.query_data.data.doctorSex,
								doctorTitle: item.query_data.data.doctorTitle,
								hospital: item.query_data.data.hospital,
							},
							dataId: item.id,
							type: 'docCard',
							id: uuid(),
							loading: false,
							zeStatus: '0',
						});
						continue;
					}
					// 1. 插入用户发出的消息
					if (item.query_meta_data.type && item.query_meta_data.type === 'desc' && item.query_meta_data.data.articleId) {
						cl.push({
							msg: {
								img: item.query_meta_data.data.img,
								title: item.query_meta_data.data.title,
								articleId: item.query_meta_data.data.articleId,
								source: item.query_meta_data.data.source,
								publishDate: item.query_meta_data.data.publishDate + ' 00:00:00',
							},
							dataId: item.id,
							type: 'desc',
							id: uuid(),
							loading: false,
							zeStatus: '0',
						});
					}

					cl.push({
						msg: item.query.replace(/<br\s*\/?\s*>/gi, '\n'),
						type: 'user',
						dataId: item.id,
						id: uuid(),
						loading: false,
						isBtns: false,
						zeStatus: '0',
					});

					// 2. 插入ai回复的消息
					if (item.response.indexOf('reasoning_content') > -1) {
						item.response = fixPseudoJson(item.response);
					} else {
						item.response = {
							content: item.response,
							reasoning_content: '',
						};
					}
					cl.push({
						msg: item.response.content || '',
						dataId: item.id,
						type: 'ai',
						doctorId: item.doctor_id || '',
						apiUrl: item.api_url || '',
						id: uuid(),
						loading: false,
						isBtns: true,
						reGen: true,
						voiceStatus: true,
						zeStatus: item.feedback_score === -1 ? '0' : item.feedback_score === 100 ? '1' : '2',
						btnGroupList: item?.btn_group_list?.btn_group_list || [],
					});
				}
			}
		})
		.finally(() => {
			let json = {
				msg: 'Hi，我是你的AI助理小智，可以帮您生成各种场景下的话术，您可以参考以下指令对小智进行提问哦！',
				type: 'ai',
				dataId: '',
				doctorId: '',
				apiUrl: '',
				id: uuid(),
				loading: false,
				isBtns: false,
				isRemark: true,
				zeStatus: '0',
				btnGroupList: [],
			};
			chatList.value = [json, ...cl];
			nextTick(() => {
				scrollBottom();
			});
		});
};

const fixPseudoJson = (pseudoJsonStr) => {
	try {
		// 1. 去除首尾空格
		let str = pseudoJsonStr.trim();

		// 2. 检查是否已经是有效的JSON
		try {
			return JSON.parse(str);
		} catch (e) {
			// 不是有效JSON，继续处理
		}

		// 3. 处理单引号包围的JSON对象
		if (str.startsWith("{'") && str.endsWith("'}")) {
			// 3.1 替换键的单引号为双引号
			str = str.replace(/'([^']+?)'\s*:/g, '"$1":');

			// 3.2 处理值中的换行符和引号
			let result = '';
			let inQuote = false;

			for (let i = 0; i < str.length; i++) {
				// 处理单引号开始或结束的情况
				if (str[i] === "'" && (i === 0 || str[i - 1] !== '\\')) {
					if (!inQuote) {
						// 开始引号
						inQuote = true;
						result += '"';
					} else {
						// 结束引号
						inQuote = false;
						result += '"';
					}
				} else if (inQuote && str[i] === '\n') {
					// 在引号内的换行符转为 \n
					result += '\\n';
				} else if (inQuote && str[i] === '"' && (i === 0 || str[i - 1] !== '\\')) {
					// 在单引号内的双引号需要转义
					result += '\\"';
				} else {
					// 其他字符直接添加
					result += str[i];
				}
			}

			str = result;
		}

		// 4. 尝试解析修复后的JSON
		return JSON.parse(str);
	} catch (err) {
		console.error('转换失败，请确认格式正确：', err);
		console.error('错误详情：', err.message);
		return null;
	}
};

// 历史记录
const cHs = ref(null);
const getChatHistory = () => {
	cHs.value.showLeft = true;
};
//查询左侧会话记录
const historyList = ref([]);
const getConversChatList = () => {
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async (resolve) => {
		// 获取fastGpt历史会话记录
		try {
			let allList = await getOtherChatList();
			allList = allList.reduce((acc, cur) => {
				const index = acc.findIndex((item) => item.updateTime.split('T')[0] === cur.updateTime.split('T')[0]);
				if (index !== -1) {
					acc[index].children.push(cur);
				} else {
					acc.push({
						updateTime: cur.updateTime,
						name: cur.updateTime.split('T')[0],
						children: [cur],
					});
				}
				return acc;
			}, []);
			// 把allList根据updateTime做个降序
			allList.sort((a, b) => {
				return new Date(b.updateTime) - new Date(a.updateTime);
			});
			for (const item of allList) {
				item.children.sort((a, b) => {
					return new Date(b.updateTime) - new Date(a.updateTime);
				});
			}
			if (allList.length > 0) {
				allList = ruleAllList(allList, language.value);
			}
			historyList.value = allList;
			resolve();
		} catch (error) {
			console.log(error);
			showToast({
				position: 'top',
				zIndex: 8888,
				message: '会话记录获取失败',
			});
			resolve();
		}
	});
};
const getOtherChatList = () => {
	return new Promise((resolve, reject) => {
		const json = {
			user_id: userStore.userInfo.username,
			skip: 0,
			limit: 700,
			chat_type: 'generate_script',
		};
		axios({
			url: interfaceUrl + '/API-GPT/chat/message/history_list_by_chat_type',
			method: 'get',
			params: json,
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		})
			.then((res) => {
				// 把 res.data.data 根据 updateTime 拆分成treeList
				for (const cur of res.data.data) {
					cur.chatType = 'generate_script';
					cur.updateTime = cur.created_time.substring(0, 19);
					cur.chatId = cur.conversation_id;
					cur.title = cur.query;
				}
				resolve(res.data.data);
			})
			.catch(() => {
				reject('other会话出错了');
			});
	});
};
// 打开历史记录会话
const openHistoryChat = (item) => {
	if (item.chatId === chatId) return;
	if (isLoading.value) {
		showToast({
			message: '正在回答，请稍后...',
			position: 'top',
		});
		return;
	}
	chatList.value = [];
	chatId = item.chatId;
	router.replace({
		name: 'cloundAssistant',
		query: {
			chatId: item.chatId,
			isTabBar: route.query.isTabBar || '',
		},
	});
};

const chatType = ref('cloundAssistant');
// 聊天类型
const showPopover = ref(false);
const actions = ref([
	{
		img: khdc,
		name: t('chat.customerVisitSuggestions'),
		desc: t('chat.salesCustomerInsightDesc'),
		type: 'customerInsight',
	},
	{
		img: cpdc,
		name: t('chat.productKnowledgQuery'),
		desc: t('chat.productKnowledgQueryDesc'),
		type: 'productKnowledgQuery',
	},
	{
		img: zlfx,
		name: t('chat.visitMateriaDisplayAndSharing'),
		desc: t('chat.visitMateriaDisplayAndSharingDesc'),
		type: '',
		permission: true,
	},
	{
		img: hszs,
		name: t('chat.cloundAssistant'),
		desc: t('chat.cloundAssistantDesc'),
		type: 'cloundAssistant',
	},
	{
		img: khbfxxsj,
		name: t('chat.khbfxxsj'),
		desc: t('chat.khbfxxsjDesc'),
		type: '2',
		permission: true,
	},
	{
		img: bfbj,
		name: ['msl_demo'].includes(userStore.userInfo.username) ? t('chat.medicalVisitRecords') : t('chat.salesVisitRecords'),
		desc: t('chat.medicalVisitRecordsDesc'),
		type: 'randomNotes',
		permission: true,
	},
]);

const chatTypeEv = (item) => {
	showPopover.value = false;
	if (item.type === '') {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/mobile/contentCenter/contentCenterIndex/marketKnowledge',
				title: '拜访材料建议与分享',
				module: 'third',
			});
		} else {
			location.href = domain + '/mobile/contentCenter/contentCenterIndex/marketKnowledge';
		}
		return;
	}
	if (item.type === '2') {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/mobile/questionResearch',
				title: '客户拜访信息收集',
				module: 'third',
			});
		} else {
			location.href = domain + '/mobile/questionResearch';
		}
		return;
	}
	if (item.type === 'cloundAssistant') return;
	if (item.type === 'randomNotes') {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/app/mobile/random/randomNotes',
				title: '拜访记录',
				module: 'third',
			});
		} else {
			router.push({
				name: item.type,
				query: {
					isTabBar: route.query.isTabBar || '',
				},
			});
		}
	} else {
		router.replace({
			name: item.type,
			query: {
				isTabBar: route.query.isTabBar || '',
			},
		});
	}
};

// 发送的消息是否是二次编辑的消息
const isEdit = ref(false);
const editorText = ref('');
const editorMsg = (index, item) => {
	if (index === chatList.value.length - 2) {
		// 如果请求正在进行需要终止请求
		if (isLoading.value) {
			controller?.abort();
			chatList.value[chatList.value.length - 1].loading = false;
			chatList.value[chatList.value.length - 1].msg = chatList.value[chatList.value.length - 1].msg + '...';
			isLoading.value = false;
		}
		let msg = item.msg.replace(/<br\s*\/?\s*>/gi, '\n');
		editorText.value = msg + 'jjjjjj' + new Date().getTime();
		isEdit.value = true;
	}
};

let controller = '';

const changeEdit = () => {
	isEdit.value = false;
	deleteArticleItem();
	deleteDoctorItem();
	editorText.value = '';
};

// 文本框失去焦点
const chatBox = ref();
useClickAway(
	chatBox,
	() => {
		if (isEdit.value) {
			editorText.value = '';
		}
		isEdit.value = false;
	},
	{ eventName: 'touchend' }
);

// 返回
const goBack = () => {
	router.go(-1);
};

const zlMoreVis = ref(false);
const zlTitle = ref('');

const docMoreVis = ref(false);
const qesMoreEv = (i) => {
	if (i === '推广资料话术') {
		zlMoreVis.value = true;
		zlTitle.value = i.slice(0, -2);
	} else {
		docMoreVis.value = true;
	}
};

const activeDoc = (item) => {
	docMoreVis.value = false;
	if (!isLoading.value) {
		isLoading.value = true;
		deleteArticleItem();
		// 生成一个医生基本信息的模版
		const json = {
			msg: {
				department: item.department,
				doctorName: item.doctorName,
				doctorId: item.doctorId,
				doctorSex: item.doctorSex,
				doctorTitle: item.doctorTitle,
				hospital: item.hospital,
			},
			dataId: '',
			type: 'docCard',
			id: uuid(),
			loading: false,
			zeStatus: '0',
		};
		chatList.value.push(json);
		// 生成一条用户历史记录
		axios({
			url: interfaceUrl + '/API-GPT/chat/message/create_history',
			method: 'post',
			headers: {
				Authorization: `Bearer ${getToken()}`,
				'Content-Type': 'application/json;charset=utf-8',
			},
			data: {
				user_id: userStore.userInfo.username,
				conversation_id: chatId,
				chat_type: 'generate_script',
				query: `${item.doctorName}医生的拜访话术`,
				response: `${item.doctorName}医生的拜访话术`,
				query_data: {
					type: 'docCard',
					data: {
						doctorName: item.doctorName,
						doctorId: item.doctorId,
						doctorSex: item.doctorSex,
						doctorTitle: item.doctorTitle,
						hospital: item.hospital,
						department: item.department,
					},
				},
			},
		}).finally(() => {
			isLoading.value = false;
			activeDoctor(item, '概览');
		});
	}
};

const docBtnItem = (info, msg) => {
	activeDoctor(info, msg);
};

// 文章详情
const articleDetailVis = ref(false);
const sourceUrl = ref('');
const articleDetail = (item) => {
	sourceUrl.value = item;
	articleDetailVis.value = true;
};
// 选择文章
const articleItem = reactive({
	img: '',
	title: '',
	articleId: '',
	source: '',
	publishDate: '',
});
// 激活文章
const activeArticle = (item) => {
	deleteDoctorItem();
	articleItem.img = item.thumbMediaUrl;
	articleItem.title = item.title;
	articleItem.articleId = item.id;
	articleItem.source = item.source;
	articleItem.publishDate = item.publishDate.split(' ')[0];
	editorText.value = '您正在准备一次针对该医生的面对面拜访，请基于此文章信息设计拜访话题生成150字以内的拜访话术\n话术场景：常规拜访的开场环节\n拜访目的：加强关系、推广产品\n话术要求：结构清晰，三段式介绍，分段展现';
};
// 清除文章
const deleteArticleItem = () => {
	articleItem.img = '';
	articleItem.title = '';
	articleItem.articleId = '';
	articleItem.source = '';
	articleItem.publishDate = '';
	editorText.value = '';
};

// 选择医生
const doctorItem = reactive({
	doctorName: '',
	doctorId: '',
	doctorSex: '',
	doctorTitle: '',
	hospital: '',
	department: '',
	apiUrl: '',
	prod: '',
});

// 激活医生
const activeDoctor = (item, type = '概览') => {
	doctorItem.department = item.department || '';
	doctorItem.doctorName = item.doctorName || '';
	doctorItem.doctorId = item.doctorId || '';
	doctorItem.doctorSex = item.doctorSex || '';
	doctorItem.doctorTitle = item.doctorTitle || '';
	doctorItem.hospital = item.hospital || '';
	switch (type) {
		case '概览':
			doctorItem.apiUrl = '/script/hcp_info';
			break;
		case '科研动态':
			doctorItem.apiUrl = '/script/script_hcp_research_info';
			break;
		case '学术动态':
			doctorItem.apiUrl = '/script/script_hcp_paper_info';
			break;
		case '内部活动参与':
			doctorItem.apiUrl = '/script/script_hcp_action_detail_info';
			break;
		case '合作':
			doctorItem.apiUrl = '/script/script_hcp_relation_info';
			break;
		case '外部活动动态':
			doctorItem.apiUrl = '/script/script_hcp_activity_info';
			break;
		case '临床指南':
			doctorItem.apiUrl = '/script/script_hcp_guide_info';
			break;
		case '学术会议':
			doctorItem.apiUrl = '/script/script_hcp_meeting_info';
			break;
		case '学术直播':
			doctorItem.apiUrl = '/script/script_hcp_live_info';
			break;
		case '国家自然科学基金项目':
			doctorItem.apiUrl = '/script/script_hcp_fund_info';
			break;
		case '发明专利':
			doctorItem.apiUrl = '/script/script_hcp_patent_info';
			break;
		// case '认知倾向':
		// 	doctorItem.apiUrl = '/script/script_hcp_image_info';
		// 	doctorItem.prod = '认知倾向';
		// 	break;
		case 'KLK':
			doctorItem.apiUrl = '/script/script_hcp_image_info';
			doctorItem.prod = 'KLK';
			break;
		case 'AKR':
			doctorItem.apiUrl = '/script/script_hcp_image_info';
			doctorItem.prod = 'AKR';
			break;
		case 'UTI':
			doctorItem.apiUrl = '/script/script_hcp_image_info';
			doctorItem.prod = 'UTI';
			break;
		case 'UK':
			doctorItem.apiUrl = '/script/script_hcp_image_info';
			doctorItem.prod = 'UK';
			break;
	}
	editorText.value = '您正在准备一次针对该医生的面对面拜访，请根据该医生的【' + type + '】设计拜访话题生成150字以内的拜访话术\n话术场景：常规拜访的开场环节\n拜访目的：加强关系、推广产品\n观念阶段：信息传递\n话术要求：结构清晰，三段式介绍，分段展现';
};
// 清除医生
const deleteDoctorItem = () => {
	doctorItem.department = '';
	doctorItem.doctorName = '';
	doctorItem.doctorId = '';
	doctorItem.doctorSex = '';
	doctorItem.doctorTitle = '';
	doctorItem.hospital = '';
	doctorItem.apiUrl = '';
	doctorItem.prod = '';
	editorText.value = '';
};

// 赞和踩
const zeEv = (val) => {
	const j = chatList.value.filter((ele) => ele.id === val.id)[0];
	const json = {
		message_id: j.dataId,
		feedback_score: '',
	};
	j.zeStatus = val.like;
	if (val.like === '2') {
		// 踩
		json.feedback_score = 0;
	} else if (val.like === '1') {
		// 赞
		json.feedback_score = 100;
	} else {
		json.feedback_score = -1;
	}
	axios({
		url: interfaceUrl + '/API-GPT/chat/message/update_history_feedback',
		method: 'get',
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
		params: json,
	});
};

// 重新生成
const reGen = (id) => {
	let typeInterface = '';
	let prod = '';
	let apiUrl = '';
	let doctorId = '';
	// 获取文本信息
	const textJson = {
		msg: '',
		dataId: '',
		type: 'user',
		id: uuid(),
		loading: false,
		zeStatus: '0',
	};
	let num = 0;
	for (let i = 0; i < chatList.value.length; i++) {
		if (chatList.value[i].id === id) {
			num = i - 1;
			break;
		}
	}
	textJson.msg = chatList.value[num].msg;
	let snum = num - 1;
	try {
		if (chatList.value[snum].type === 'desc') {
			typeInterface = '文章生成';
			chatList.value.push({
				msg: {
					img: chatList.value[snum].msg.img,
					title: chatList.value[snum].msg.title,
					articleId: chatList.value[snum].msg.articleId,
					source: chatList.value[snum].msg.source,
					publishDate: chatList.value[snum].msg.publishDate + ' 00:00:00',
				},
				dataId: '',
				type: 'desc',
				id: uuid(),
				loading: false,
				zeStatus: '0',
			});
		} else if (chatList.value[num + 1].doctorId && chatList.value[num + 1].doctorId !== '') {
			typeInterface = '医生生成';
			apiUrl = chatList.value[num + 1].apiUrl;
			doctorId = chatList.value[num + 1].doctorId;
			// if (textJson.msg.indexOf('认知倾向') > -1) {
			// 	prod = '认知倾向';
			// }
			if (textJson.msg.indexOf('KLK') > -1) {
				prod = 'KLK';
			}
			if (textJson.msg.indexOf('AKR') > -1) {
				prod = 'AKR';
			}
			if (textJson.msg.indexOf('UTI') > -1) {
				prod = 'UTI';
			}
			if (textJson.msg.indexOf('UK') > -1) {
				prod = 'UK';
			}
		}
		chatList.value.push(textJson);
		// 生产一个ai的消息
		chatList.value.push({
			msg: '',
			dataId: '',
			type: 'ai',
			apiUrl: '',
			doctorId: '',
			id: uuid(),
			loading: true,
			isBtns: true,
			reGen: true,
			voiceStatus: true,
			zeStatus: '0',
			btnGroupList: [],
		});
		nextTick(() => {
			scrollBottom();
		});
		if (typeInterface === '文章生成') {
			setAiMessage(textJson.msg, chatList.value[snum].msg.articleId, chatList.value[snum].msg);
		} else if (typeInterface === '医生生成') {
			switch (apiUrl) {
				// 概览
				case '/script/hcp_info':
					setScriptHcpInfo(textJson.msg, doctorId);
					break;
				// 学术动态
				case '/script/script_hcp_paper_info':
					setScriptHcpPaperInfo(textJson.msg, doctorId);
					break;
				// 临床指南
				case '/script/script_hcp_guide_info':
					setScriptHcpGuideInfo(textJson.msg, doctorId);
					break;
				// 学术直播
				case '/script/script_hcp_live_info':
					setScriptHcpLiveInfo(textJson.msg, doctorId);
					break;
				// 学术会议
				case '/script/script_hcp_meeting_info':
					setScriptHcpMeetingInfo(textJson.msg, doctorId);
					break;
				// 科研动态
				case '/script/script_hcp_research_info':
					setScriptHcpResearchInfo(textJson.msg, doctorId);
					break;
				// 国家自然科学基金
				case '/script/script_hcp_fund_info':
					setScriptHcpFundInfo(textJson.msg, doctorId);
					break;
				// 发明专利
				case '/script/script_hcp_patent_info':
					setScriptHcpPatentInfo(textJson.msg, doctorId);
					break;
				// 内部活动参与
				case '/script/script_hcp_action_detail_info':
					setScriptHcpActionDetailInfo(textJson.msg, doctorId);
					break;
				// 外部活动动态
				case '/script/script_hcp_activity_info':
					setScriptHcpActivityInfo(textJson.msg, doctorId);
					break;
				// 合作
				case '/script/script_hcp_relation_info':
					setScriptHcpRelationInfo(textJson.msg, doctorId);
					break;
				// 认知倾向，klk, akr, uti, uk
				case '/script/script_hcp_image_info':
					setScriptHcpImageInfo(textJson.msg, doctorId, prod);
					break;
			}
		} else {
			setTextMessage(textJson.msg);
		}
	} catch (e) {
		console.log(e);
		showToast({
			position: 'top',
			zIndex: 8888,
			message: '重新生成失败！',
		});
	}
};
const resetAudioVoice = () => {
	for (const i of chatList.value) {
		if (i.type === 'user') continue;
		if (i.isBtns === false) continue;
		if (i.voiceStatus === true) continue;
		i.voiceStatus = true;
	}
};
// 播放停止把voiceStatus改位true
audioPlayer.onStop = () => {
	resetAudioVoice();
};

const isLoading = ref(false);
const chatList = ref([]);

// 发送消息
const sendMessage = async (val) => {
	if (isEdit.value) {
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isEdit.value = false;
	}
	if (!isLoading.value) {
		if (val) {
			isLoading.value = true;
			val = val.replace(/\n/g, '<br/>');
			// 文章话术
			if (articleItem.articleId) {
				chatList.value.push({
					msg: {
						img: articleItem.img,
						title: articleItem.title,
						articleId: articleItem.articleId,
						source: articleItem.source,
						publishDate: articleItem.publishDate,
					},
					dataId: '',
					type: 'desc',
					id: uuid(),
					loading: false,
					zeStatus: '0',
				});
			}
			chatList.value.push({
				msg: val,
				dataId: '',
				type: 'user',
				id: uuid(),
				loading: false,
				zeStatus: '0',
			});
			// 生产一个ai的消息
			chatList.value.push({
				msg: '',
				dataId: '',
				type: 'ai',
				doctorId: doctorItem.doctorId,
				apiUrl: doctorItem.apiUrl,
				id: uuid(),
				loading: true,
				isBtns: true,
				reGen: true,
				voiceStatus: true,
				zeStatus: '0',
				btnGroupList: [],
			});
			nextTick(() => {
				scrollBottom();
			});
			if (articleItem.articleId) {
				setAiMessage(val, articleItem.articleId, articleItem);
			} else if (doctorItem.doctorId) {
				// 3. 调用对应的接口
				switch (doctorItem.apiUrl) {
					// 概览
					case '/script/hcp_info':
						setScriptHcpInfo(val, doctorItem.doctorId);
						break;
					// 学术动态
					case '/script/script_hcp_paper_info':
						setScriptHcpPaperInfo(val, doctorItem.doctorId);
						break;
					// 临床指南
					case '/script/script_hcp_guide_info':
						setScriptHcpGuideInfo(val, doctorItem.doctorId);
						break;
					// 学术直播
					case '/script/script_hcp_live_info':
						setScriptHcpLiveInfo(val, doctorItem.doctorId);
						break;
					// 学术会议
					case '/script/script_hcp_meeting_info':
						setScriptHcpMeetingInfo(val, doctorItem.doctorId);
						break;
					// 科研动态
					case '/script/script_hcp_research_info':
						setScriptHcpResearchInfo(val, doctorItem.doctorId);
						break;
					// 国家自然科学基金
					case '/script/script_hcp_fund_info':
						setScriptHcpFundInfo(val, doctorItem.doctorId);
						break;
					// 发明专利
					case '/script/script_hcp_patent_info':
						setScriptHcpPatentInfo(val, doctorItem.doctorId);
						break;
					// 内部活动参与
					case '/script/script_hcp_action_detail_info':
						setScriptHcpActionDetailInfo(val, doctorItem.doctorId);
						break;
					// 外部活动动态
					case '/script/script_hcp_activity_info':
						setScriptHcpActivityInfo(val, doctorItem.doctorId);
						break;
					// 合作
					case '/script/script_hcp_relation_info':
						setScriptHcpRelationInfo(val, doctorItem.doctorId);
						break;
					// 认知倾向，klk, akr, uti, uk
					case '/script/script_hcp_image_info':
						setScriptHcpImageInfo(val, doctorItem.doctorId, doctorItem.prod);
						break;
				}
			} else {
				setTextMessage(val);
			}
		}
	}
};

// 概览
const setScriptHcpInfo = async (msg, doctorId) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/hcp_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 合并多个json
const mergeJson = (chunkArr, svg) => {
	let nim = chunkArr.indexOf(svg);
	if (nim > 0) {
		chunkArr = chunkArr.substring(0, nim + 13) + ',' + chunkArr.substring(nim + 13);
		return mergeJson(chunkArr, svg);
	} else {
		chunkArr = JSON.parse(chunkArr);
		return chunkArr;
	}
};

// 学术动态
const setScriptHcpPaperInfo = async (msg, doctorId) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/script_hcp_paper_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};
// 临床指南
const setScriptHcpGuideInfo = async (msg, doctorId) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/script_hcp_guide_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 学术直播
const setScriptHcpLiveInfo = async (msg, doctorId) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/script_hcp_live_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 学术会议
const setScriptHcpMeetingInfo = async (msg, doctorId) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/script_hcp_meeting_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 科研动态
const setScriptHcpResearchInfo = async (msg, doctorId) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/script_hcp_research_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 国家自然科学基金项目
const setScriptHcpFundInfo = async (msg, doctorId) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/script_hcp_fund_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 发明专利
const setScriptHcpPatentInfo = async (msg, doctorId) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/script_hcp_patent_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 内部活动参与
const setScriptHcpActionDetailInfo = async (msg, doctorId) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/script_hcp_action_detail_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 外部活动动态
const setScriptHcpActivityInfo = async (msg, doctorId) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/script_hcp_activity_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 合作
const setScriptHcpRelationInfo = async (msg, doctorId) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/script_hcp_relation_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 认知倾向，klk, akr, uk, uti
const setScriptHcpImageInfo = async (msg, doctorId, prod) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = msg.replace(/\n/g, '<br/>') || '';
		const dId = doctorId || '';
		const pd = prod || '';
		deleteDoctorItem();
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/script_hcp_image_info?prompt=您现在是一位专业的医药产品推广代表，${message}&user_id=${userId}&doctor_id=${dId}&conversation_id=${chatId}&prod=${pd}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 文章生成话术
const setAiMessage = async (val, articleId, metaData) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const prompt = val.replace(/\n/g, '<br/>') || '';
		const content_id = articleId;
		const userId = userStore.userInfo.username || '';
		deleteArticleItem();
		let res = await fetch(`${interfaceUrl}/API-GPT/script/generate_article_script?content_id=${content_id}&user_id=${userId}&prompt=您现在是一位专业的医药产品推广代表，${prompt}&doctor_id=''&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						// 改变用户的历史记录信息
						await updateUserMessage(data.data.message_id, JSON.stringify({ type: 'desc', data: articleData }));
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		if (e.message !== 'signal is aborted without reason') {
			showToast({
				position: 'top',
				zIndex: 8888,
				message: '请求失败,请重试',
			});
			isLoading.value = false;
			// 清除chatList最后3位
			chatList.value.splice(-3);
		}
	}
};

// 文本框单独输入生成话术
const setTextMessage = async (val) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const prompt = val.replace(/\n/g, '<br/>') || '';
		const userId = userStore.userInfo.username || '';
		let res = await fetch(`${interfaceUrl}/API-GPT/script/generate_script?user_id=${userId}&prompt=您现在是一位专业的医药产品推广代表，${prompt}&doctor_id=''&conversation_id=${chatId}&stream=true&language=${language.value}`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${getToken()}`,
			},
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Network res was not ok');
		}
		const reader = res.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let data = '';
		const processStreamResult = async (result) => {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			if (!result.done) {
				try {
					data = JSON.parse(chunk);
				} catch (e) {
					try {
						// json解析失败的原因是后端返回了两个json数据，这里需要做合并
						let chunkArr = '[' + chunk + ']';
						chunkArr = mergeJson(chunkArr, '"end":false}}{"');
						let msg = '';
						for (let i = 0; i < chunkArr.length; i++) {
							msg += chunkArr[i].data.output;
						}
						data = chunkArr[chunkArr.length - 1];
						data.data.output = msg;
					} catch (s) {
						errorFun('返回的json不合法');
						throw new Error('返回的json不合法');
					}
				}
				if (data.code === 200) {
					if (!data.data.end) {
						chatList.value[chatList.value.length - 1].msg += data.data.output;
					}
					if (data.data.end) {
						if (!chatList.value[chatList.value.length - 1].msg) {
							chatList.value[chatList.value.length - 1].msg = data.data.output || '';
						}
						chatList.value[chatList.value.length - 1].dataId = data.data.message_id;
						chatList.value[chatList.value.length - 1].apiUrl = data.data.api_url || '';
						chatList.value[chatList.value.length - 1].doctorId = data.data.doctor_id || '';
						chatList.value[chatList.value.length - 1].btnGroupList = data.data.btn_group_list || [];
						chatList.value[chatList.value.length - 1].loading = false;
						isLoading.value = false;
						nextTick(() => {
							scrollBottom();
						});
						if (!getQueryString('chatId')) {
							// 获取历史会话记录
							getConversChatList().then(() => {
								const params = new URLSearchParams(window.location.search);
								params.set('chatId', chatId); // 添加或更新 query 参数
								const newUrl = window.location.pathname + '?' + params.toString();
								history.replaceState(null, '', newUrl);
							});
						}
					} else {
						nextTick(() => {
							scrollBottom();
						});
						return reader.read().then(processStreamResult);
					}
				} else {
					throw new Error('code !== 200');
				}
			}
		};
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 请求或者解析失败提示并删除消息
const errorFun = (e) => {
	if (e.message !== 'signal is aborted without reason') {
		showToast({
			position: 'top',
			zIndex: 8888,
			message: '请求失败,请重试',
		});
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isLoading.value = false;
	}
};

const scrollBottom = () => {
	document.querySelector('#aiCon').scrollTo({
		top: document.querySelector('#aiCon').scrollHeight,
		behavior: 'smooth',
	});
};

const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let ttsWS = null;
const voiceEv = async (val) => {
	// chatList里所有的voiceStatus改位true
	resetAudioVoice();
	// 通过val.id获取chatList的相等元素
	const index = chatList.value.findIndex((item) => item.id === val.id);
	chatList.value[index].voiceStatus = val.vType === 'play' ? false : true;

	ttsWS?.close();
	audioPlayer.reset();
	if (val.vType === 'play') {
		// 开始语音播报
		const url = getWebSocketUrl(API_KEY, API_SECRET);
		if ('WebSocket' in window) {
			ttsWS = new WebSocket(url);
		} else if ('MozWebSocket' in window) {
			ttsWS = new MozWebSocket(url);
		} else {
			showToast({
				position: 'top',
				zIndex: 8888,
				message: '浏览器不支持WebSocket',
			});
			return;
		}
		// 发送消息
		ttsWS.onopen = () => {
			audioPlayer.start({
				autoPlay: true,
				sampleRate: 16000,
				resumePlayDuration: 1000,
			});
			let text = document.getElementById(val.id).innerText;
			let tte = 'UTF8';
			let params = {
				common: {
					app_id: APPID,
				},
				business: {
					aue: 'raw',
					auf: 'audio/L16;rate=16000',
					vcn: import.meta.env.VITE_APP_XF_VOICER, //发音人
					speed: 50, // 语速
					volume: 50, // 音量
					pitch: 50, // 音色
					bgs: 0,
					tte,
				},
				data: {
					status: 2,
					text: encodeText(text, tte),
				},
			};
			ttsWS.send(JSON.stringify(params));
		};

		ttsWS.onmessage = (e) => {
			let jsonData = JSON.parse(e.data);
			// 合成失败
			if (jsonData.code !== 0) {
				showToast({
					position: 'top',
					zIndex: 8888,
					message: '语音合成失败, 请稍后再试',
				});
				return;
			}
			audioPlayer.postMessage({
				type: 'base64',
				data: jsonData.data.audio,
				isLastData: jsonData.data.status === 2,
			});
			if (jsonData.code === 0 && jsonData.data.status === 2) {
				ttsWS.close();
			}
		};

		ttsWS.onerror = (e) => {
			console.error(e);
		};
		ttsWS.onclose = () => {};
	}
};

const encodeText = (text, type) => {
	if (type === 'unicode') {
		let buf = new ArrayBuffer(text.length * 4);
		let bufView = new Uint16Array(buf);
		for (let i = 0, strlen = text.length; i < strlen; i++) {
			bufView[i] = text.charCodeAt(i);
		}
		let binary = '';
		let bytes = new Uint8Array(buf);
		let len = bytes.byteLength;
		for (let i = 0; i < len; i++) {
			binary += String.fromCharCode(bytes[i]);
		}
		return window.btoa(binary);
	} else {
		return Base64.encode(text);
	}
};

const getWebSocketUrl = (apiKey, apiSecret) => {
	var url = 'wss://tts-api.xfyun.cn/v2/tts';
	var host = location.host;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};
const inForUse = ref('');
onMounted(() => {
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
	getAgentListApi().then((res) => {
		const agentList = res.result.content || [];
		for (const ele of agentList) {
			if (ele.attributes.url.indexOf('cloudAssistant') > -1) {
				inForUse.value = ele.attributes?.inForUse || '';
				break;
			}
		}
		getConversChatList();
		init();
	});
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
	if (isLoading.value) {
		controller?.abort();
	}
	// 关闭语音
	resetAudioVoice();
	ttsWS?.close();
	audioPlayer.reset();
});

const init = () => {
	chatId = route.query.chatId || '';
	if (chatId) {
		// 获取历史记录
		getHistoryList();
	} else {
		// 初始化会话
		initChat();
	}
};

const initChat = () => {
	chatId = getRandomString();
	chatList.value.push({
		msg: 'Hi，我是你的AI助理小智，可以帮您生成各种场景下的话术，您可以参考以下指令对小智进行提问哦！',
		type: 'ai',
		dataId: '',
		doctorId: '',
		apiUrl: '',
		id: uuid(),
		loading: false,
		isBtns: false,
		isRemark: true,
		zeStatus: '0',
		btnGroupList: [],
	});
};

// 问题类型
const qtList = ref(['推广资料话术', '拜访话术']);

const copyText = (id) => {
	const clipboard = new ClipboardJS('.copy-btn', {
		target: function () {
			return document.getElementById(id);
		},
	});

	clipboard.on('success', () => {
		showToast({
			position: 'top',
			zIndex: 8888,
			message: '复制成功',
		});
		clipboard.destroy();
	});
};
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-list {
			flex: 1 0 0px;
			height: 0px;
			padding: 0 6px;
			padding-top: 12px;
			overflow-y: auto;
			.mc-content-list-left {
				display: flex;
				flex-direction: column;
				width: 100%;
				margin-bottom: 12px;
				.mc-content-template {
					border-bottom-left-radius: 8px;
					border-bottom-right-radius: 8px;
					border-top-right-radius: 8px;
					.mc-content-template__remark {
						display: flex;
						flex-direction: column;
						background-color: var(--ac-bg-color);
						padding: 8px;
						border-radius: 5px;
						margin-top: 8px;
						.remark-item {
							display: flex;
							align-items: flex-start;
						}
					}
				}
				img {
					width: 26px;
					height: 26px;
					margin-right: 6px;
					margin-top: 3px;
				}

				.mc-content__groupList {
					padding: 8px 0 0 0;
					margin-left: 32px;
					display: flex;
					overflow-x: auto;
					width: calc(95% - 32px);
					span {
						flex: none;
						margin-right: 8px;
						margin-bottom: 8px;
						color: #0b67e4;
						border-radius: 5px;
						border: 1px solid #0b67e4;
						padding: 3px 6px;
					}
				}
			}

			.mc-content-list-right {
				display: flex;
				width: 95%;
				float: right;
				margin-bottom: 12px;
				justify-content: flex-end;
				.mc-content-template {
					border-bottom-right-radius: 8px;
					border-bottom-left-radius: 8px;
					border-top-left-radius: 8px;
					color: var(--ac-bg-color--fff);
					background-color: var(--ac-bg-active-color);
					.mc-content-template__edit {
						border-top: solid 1px rgba(223, 225, 230, 0.3);
						margin-top: 2px;
					}
				}
			}

			.content-docList {
				width: 80%;
				background-color: var(--ac-bg-color--fff);
				padding: 8px;
				border-bottom-right-radius: 8px;
				border-bottom-left-radius: 8px;
				border-top-left-radius: 8px;
				display: flex;
				flex-direction: column;
				float: right;
				margin-bottom: 12px;
				.docList-list {
					max-height: 225px;
					overflow-y: auto;

					.docList-list-item {
						background-color: var(--ac-bg-color);
						border-radius: 5px;
						margin-top: 8px;
						position: relative;
						display: flex;
						align-items: center;
						padding: 8px;
						img {
							height: 50px;
							width: 50px;
							margin-right: 8px;
							margin-top: 0;
						}

						.amd-content-item-desc {
							flex: 1;
							display: flex;
							flex-direction: column;
							font-size: 12px;
							.desc-name {
								font-weight: 500;
								font-size: 14px;
							}

							.desc-hp {
								width: 100%;
							}
						}
					}
				}

				.docList-btns {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					.docList-btns-item {
						background-color: var(--ac-bg-color);
						border-radius: 5px;
						display: flex;
						align-items: center;
						justify-content: space-between;
						padding: 8px 12px;
						width: 48.5%;
						margin-top: 8px;
					}

					.docList-btns-item:active {
						background-color: #f4f5f7;
						border-radius: 5px;
					}
				}

				.content-docList-num {
					margin-top: 8px;
					display: flex;
					flex-wrap: wrap;
					.num-item {
						margin-bottom: 8px;
						margin-right: 2%;
						width: 49%;
						border-radius: 5px;
						background-color: var(--ac-bg-color);
						padding: 8px;
						display: flex;
						align-items: center;
						justify-content: space-between;
						.num-item-name {
							width: calc(100% - 12px);
						}
					}
					& .num-item:nth-child(2n) {
						margin-right: 0;
					}
				}

				.content-docList-activity {
					margin-top: 8px;
					display: flex;
					flex-direction: column;
					.activity-item {
						width: 100%;
						background-color: var(--ac-bg-color);
						border-radius: 5px;
						padding: 8px;
						margin-bottom: 8px;
						display: flex;
						flex-direction: column;
						.activity-item--name {
							font-size: 15px;
							font-weight: 500;
						}
						.activity-item--desc {
							color: #6b778c;
							font-size: 12px;
						}
					}

					.content-docList-activity-more {
						color: var(--ac-font-color-active);
						text-align: right;
					}
				}
			}

			.mc-content-desc {
				float: right;
				width: 80%;
				border: solid 1px var(--ac-border-color);
				padding: 15px 12px;
				display: flex;
				align-items: center;
				border-radius: 5px;
				margin-bottom: 12px;
				img {
					width: 100px;
					height: 65px;
					border-radius: 5px;
					margin-right: 8px;
					object-fit: cover;
				}

				.desc-info {
					display: flex;
					flex-direction: column;
					flex: 1;
					.desc-info-title {
						color: var(--ac-font-color);
						width: 100%;
						font-size: 14px;
					}
					.desc-info-other {
						display: flex;
						align-items: center;
						font-size: 12px;
						color: var(--ac-colors-myGray-500);
					}
				}
			}

			.mc-content-template {
				max-width: calc(95% - 32px);
				background-color: var(--ac-bg-color--fff);
				padding: 12px;
			}

			// 猜你想问
			.mc-content-template__guess {
				padding-top: 10px;
				.guess-header {
					display: flex;
					align-items: center;
					margin-bottom: 3px;
					.guess-header-title {
						font-size: 12px;
						color: var(--ac-colors-myGray-500);
						margin-right: 6px;
					}
					.guess-header-line {
						flex: 1;
						height: 1px;
						background-color: rgba(221, 221, 221, 0.35);
					}
				}
				.guess-list {
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					.guess-list-item {
						font-size: 12px;
						display: flex;
						border-radius: 5px;
						padding: 3px 6px;
						justify-content: center;
						border: 1px solid rgb(239, 240, 241);
						align-items: center;
						margin-bottom: 4px;
						margin-right: 4px;
					}

					.qutoe-list-item {
						width: 100%;
						justify-content: flex-start;
						display: flex;
						align-items: center;
						margin-bottom: 4px;
						position: relative;
						i {
							position: absolute;
							left: 4px;
						}
						span {
							font-size: 12px;
							border-radius: 5px;
							border: 1px solid rgb(239, 240, 241);
							padding: 3px 6px;
							padding-left: 20px;
							background-color: var(--ac-bg-color);
						}
					}
				}
			}

			.mc-content-template__more {
				font-size: 12px;
				color: var(--ac-bg-active-color);
			}
		}
		// 声明
		.mc-declare {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 6px 0px 3px 0px;
			font-size: 12px;
			color: var(--ac-colors-myGray-500);
		}
		// 按钮组
		.mc-btns {
			width: 100%;
			margin: 0px auto;
			padding: 4px 6px;
			overflow-x: auto;
			display: flex;
			white-space: nowrap;
			.mc-btns-item {
				margin-right: 6px;
				border: 1px solid #dfe1e6;
				padding: 4px 8px;
				border-radius: 20px;
				font-size: 14px;
				color: var(--ac-font-color);
			}
		}

		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 8px;
			padding-bottom: 6px;
			min-height: 64px;
			position: relative;
			display: flex;
			align-items: flex-start;
			.chat-img {
				width: 25px;
				margin-top: 12.5px;
				object-fit: contain;
				margin-left: 15px;
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}

	.article-item {
		position: fixed;
		left: 16px;
		bottom: 32%;
		padding-top: 5px;
		width: 80px;
		z-index: 66;
		background-color: var(--ac-bg-color);
		border-radius: 5px;
		border: 1px solid var(--ac-border-color);
		overflow: hidden;
		.article-item__header {
			width: 100%;
			position: relative;
			img {
				width: 100%;
				height: 50px;
				object-fit: cover;
			}
			.header-close {
				width: 15px;
				height: 15px;
				border-radius: 50%;
				background-color: rgba(0, 0, 0, 0.5);
				position: absolute;
				right: 2px;
				top: 0px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
		.article-item__content {
			padding: 4px 4px 2px;
			font-size: 10px;
		}
	}

	.cus-sj {
		width: 5px !important;
		height: 10px !important;
		margin: 0 !important;
	}
}
</style>
