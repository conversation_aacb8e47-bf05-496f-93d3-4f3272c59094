<template>
	<div class="mc">
		<!-- 顶部 -->
		<div v-if="singleChatTools" class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">
				{{ route?.query?.id ? $t('visit.editVisit') : $t('visit.createLog') }}
			</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon"></div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : singleChatTools ? 'mc-content__h5' : 'mc-content__wechat'">
			<!-- 类型 -->
			<van-tabs v-if="!route.query.id" :swipe-threshold="3" color="#0052cc" @change="tabChange" :before-change="beforeChange" v-model:active="active">
				<van-tab v-if="perList.some((item) => item.path === 'visit.medicalInsights')" :name="$t('visit.medicalInsights')" :title="$t('visit.medicalInsights')"></van-tab>
				<van-tab v-if="perList.some((item) => item.path === 'visit.medicalInquiry')" :name="$t('visit.medicalInquiry')" :title="$t('visit.medicalInquiry')"></van-tab>
				<van-tab v-if="perList.some((item) => item.path === 'visit.visit')" :name="$t('visit.visit')" :title="$t('visit.visit')"></van-tab>
				<van-tab v-if="perList.some((item) => item.path === 'visit.visitColl')" :name="$t('visit.visitColl')" :title="$t('visit.visitColl')"></van-tab>
				<van-tab v-if="perList.some((item) => item.path === 'visit.notes')" :name="$t('visit.notes')" :title="$t('visit.notes')"></van-tab>
			</van-tabs>
			<!-- 内容 -->
			<div class="mc-content-cotainer">
				<!-- 语音录入 -->
				<div class="mc-content__voice">
					<img v-if="formData.remark" @click="formData.remark = ''" class="mc-content__voice__close" src="@/assets/img/voice_close.png" />
					<van-field
						v-model="formData.remark"
						type="textarea"
						:placeholder="
							active === $t('visit.medicalInsights')
								? '(who)某肿瘤科专家认为：\n(what)治疗方案A比治疗方案B对于C类患者更好。\n(why)因为某个研究中的数据显示，对于C类患者，A方案相较于B方案可以延长PFS 6个月，生活质量改善有统计学差异。 \n(so what)所以专家会选择在C类患者中优先选择方案A。\n(next)整理方案对比的内容，后续进一步分享'
								: active === $t('visit.medicalInquiry')
								? '请输入...'
								: active === $t('visit.visit')
								? '请输入拜访笔记...'
								: active === $t('visit.visitColl')
								? '请输入协访笔记...'
								: '请输入...'
						"
					/>
					<div class="voice-content">
						<div @click="voiceEv" class="voice-content__left">
							<img src="@/assets/img/voice.png" alt="" />
							<span style="color: #0052cc">语音输入</span>
						</div>
						<van-button @click="setAiUrl" :disabled="formData.remark === ''" size="small" round color="#0052cc">识别</van-button>
					</div>
				</div>

				<!-- 拜访表单 -->
				<div class="mc-content__form">
					<van-form ref="form1">
						<!-- 基本信息 -->
						<div class="form-info">
							<!-- 治疗领域 -->
							<van-field v-if="active === $t('visit.medicalInsights')" class="required" v-model="formData.therapyArea" name="治疗领域" label="治疗领域" placeholder="请选择" is-link readonly :rules="[{ validator: cusValidator, message: '请选择治疗领域' }]" @click="selectTherapyArea"> </van-field>
							<!-- 客户 -->
							<van-field class="required" v-model="formData.ownerName" name="客户" label="客户" placeholder="请选择" :is-link="singleChatTools && route.query.fromSource !== 'docModelInfo'" readonly :rules="[{ validator: cusValidator, message: '请选择客户' }]" @click="selectDoctor"> </van-field>
							<!-- 多点执业 选择医院 -->
							<van-field v-if="formData.multiPointPractice" class="required" v-model="formData.hospital" name="医院" label="医院" placeholder="请选择" is-link readonly :rules="[{ validator: cusValidator, message: '请选择医院' }]" @click="selectHospital"> </van-field>
							<!-- 拜访类型 -->
							<van-field
								v-if="active === $t('visit.visit') || active === $t('visit.visitColl')"
								class="required"
								v-model="formData.visitType"
								name="拜访类型"
								label="拜访类型"
								placeholder="请选择"
								is-link
								readonly
								:rules="[{ validator: cusValidator, message: '请选择拜访类型' }]"
								@click="selectChannel"
							>
							</van-field>
							<!-- 拜访时间 -->
							<van-field class="required" v-model="formData.visitTime" name="拜访时间" label="拜访时间" placeholder="请选择" is-link readonly :rules="[{ required: true, message: '请选择拜访时间' }]" @click="choiceTime"> </van-field>
							<!-- 拜访结果 -->
							<van-field v-if="active === $t('visit.visit') || active === $t('visit.visitColl')" v-model="formData.visitResult" name="拜访结果" label="拜访结果" placeholder="请选择" is-link readonly @click="selectResult"> </van-field>
							<!-- 拜访时长 -->
							<van-field
								v-if="active === $t('visit.visit') || active === $t('visit.visitColl')"
								class="required"
								v-model="formData.visitDuration"
								name="拜访时长"
								label="拜访时长"
								placeholder="请选择"
								is-link
								readonly
								:rules="[{ validator: cusValidator, message: '请选择拜访时长' }]"
								@click="selectDuration"
							>
							</van-field>
							<!-- 协访人 -->
							<van-field v-if="active === $t('visit.visitColl')" v-model="formData.coordinatorName" name="协访人" label="协访人" placeholder="经理" is-link readonly @click="showForwardEv(1)"> </van-field>
							<!-- 被协防人 -->
							<van-field v-if="active === $t('visit.visitColl')" v-model="formData.intervieweeName" name="被协访人" label="被协访人" placeholder="员工" is-link readonly @click="showForwardEv(2)"> </van-field>
							<!-- 拜访目的 -->
							<van-field v-if="active === $t('visit.medicalInsights') || active === $t('visit.visit') || active === $t('visit.visitColl')" class="inline" name="拜访目的" label="拜访目的">
								<template #input>
									<van-checkbox-group direction="horizontal" v-model="formData.visitPurpose">
										<van-checkbox checked-color="#0052cc" v-for="item in visitPurposeList" :key="item.text" :name="item.text" shape="square">{{ item.text }}</van-checkbox>
									</van-checkbox-group>
								</template>
							</van-field>
							<!-- 产品信息关注点 -->
							<van-field v-if="active === $t('visit.medicalInsights')" class="inline" name="产品信息关注点" label="产品信息关注点">
								<template #input>
									<van-checkbox-group direction="horizontal" v-model="formData.productInformationFocus">
										<van-checkbox checked-color="#0052cc" v-for="item in ['疗效', '安全性', '依从性', '费用']" :key="item" :name="item" shape="square">{{ item }}</van-checkbox>
									</van-checkbox-group>
								</template>
							</van-field>
							<!-- 产品疗效认可 -->

							<van-field v-if="active === $t('visit.medicalInsights')" class="inline" name="产品疗效认可" label="产品疗效认可">
								<template #input>
									<van-checkbox-group direction="horizontal" v-model="formData.productEfficacyRecognition">
										<van-checkbox checked-color="#0052cc" v-for="item in ['改善微循环', '减少并发症', '抑酶调控', '快速起效']" :key="item" :name="item" shape="square">{{ item }}</van-checkbox>
									</van-checkbox-group>
								</template>
							</van-field>
							<!-- 患者类型 -->
							<van-field v-if="active === $t('visit.medicalInsights')" class="inline" name="患者类型" label="患者类型">
								<template #input>
									<van-checkbox-group direction="horizontal" v-model="formData.patientType">
										<van-checkbox checked-color="#0052cc" v-for="item in ['伴发高血压', '伴发糖尿病', '溶栓', '血管内治疗', '其他']" :key="item" :name="item" shape="square">{{ item }}</van-checkbox>
									</van-checkbox-group>
								</template>
							</van-field>
							<!-- 产品 -->
							<van-field v-if="active === $t('visit.visit') || active === $t('visit.visitColl')" class="inline-product" name="产品" label="产品" :rules="[{ required: true, message: '请选择产品' }]">
								<template #input>
									<van-checkbox-group direction="horizontal" v-model="formData.product">
										<van-checkbox checked-color="#0052cc" v-for="item in productsList" :key="item.id" :name="item.id" shape="square" @click="productChange($event, item)">{{ item.name }}</van-checkbox>
									</van-checkbox-group>
								</template>
							</van-field>
							<!-- 关键信息 -->
							<template v-if="active === $t('visit.visit')">
								<van-field v-for="(val, index) in keyMessageList" :key="val.id" class="inline-product" :name="val.name + '关键信息'" :label="val.name + '关键信息'" :rules="[{ required: true, message: `请选择${val.name}的关键信息` }]">
									<template #input>
										<van-checkbox-group direction="horizontal" v-model="formData.keyMessage[index].list">
											<van-checkbox checked-color="#0052cc" v-for="item in val.keyMessage" :key="item" :name="item" shape="square">{{ item }}</van-checkbox>
										</van-checkbox-group>
									</template>
								</van-field>
							</template>

							<!-- 邮箱 -->
							<van-field v-if="active === $t('visit.medicalInquiry')" class="required" v-model="formData.email" name="邮箱地址" label="邮箱地址" placeholder="请输入邮箱地址" :rules="[{ validator: validatorEmail }]"> </van-field>
							<!-- 电话 -->
							<van-field v-if="active === $t('visit.medicalInquiry')" class="required" v-model="formData.phone" name="联系电话" label="联系电话" placeholder="请输入联系电话" :rules="[{ validator: validatorPhone }]"> </van-field>
						</div>
						<!-- 描述信息 -->
						<div class="form-desc">
							<span>
								{{ active }}内容
								<span style="color: #de350b">*</span>
							</span>
							<van-field v-model="formData.visitMemo" :placeholder="'请输入' + active + '内容'" type="textarea" :rules="[{ required: true, message: '请输入' + active + '内容' }]" rows="3" autosize> </van-field>
						</div>
					</van-form>
					<!-- 医生签字信息 -->
					<div v-if="active === $t('visit.medicalInquiry')" class="mc-content__sign">
						<span>医生签字</span>
						<div class="sign-content">签字区域</div>
					</div>
					<!-- 行动建议 -->
					<div v-if="active !== $t('visit.medicalInquiry') && active !== $t('visit.visitColl')" @click="adviceEv" class="mc-content__suggest">
						<div class="suggest-header">
							<div class="suggest-header__left">
								<span class="active">行动建议</span>
								<img src="@/assets/img/s5.png" alt="" />
							</div>
							<div class="suggest-header__right">
								<img v-if="obSuggest.isZk" src="@/assets/img/s4.png" alt="" />
								<img v-if="!obSuggest.isZk" src="@/assets/img/s3.png" alt="" />
							</div>
						</div>
						<van-field v-if="obSuggest.isZk" v-model="obSuggest.adviceMsg" rows="3" autosize readonly type="textarea" placeholder="行动建议" />
						<div v-if="obSuggest.isZk" class="mc-content-list">
							<!-- <div v-if="obSuggest.isVisitPlan" @click.stop="goPage('创建拜访计划')" class="list-item">
								<img src="@/assets/img/ass-4.png" alt="" />
								<span>创建拜访计划</span>
							</div> -->
							<div v-if="obSuggest.isMeetingInvite" @click.stop="goPage('发送会议邀请')" class="list-item">
								<img src="@/assets/img/ass-2.png" alt="" />
								<span>发送会议邀请</span>
							</div>
							<div v-if="obSuggest.isKeyData" @click.stop="goPage('传递关键资料')" class="list-item">
								<img src="@/assets/img/ass-1.png" alt="" />
								<span>传递关键资料</span>
							</div>
						</div>
					</div>
					<!-- 取消和确认 -->
					<div class="mc-content__btn">
						<van-button v-if="singleChatTools" class="size-color" color="#dde4f2" @click="goBack">取消</van-button>
						<van-button :style="!singleChatTools ? 'width: 100%' : ''" @click="confirmCreate('')" color="#0052cc">确认</van-button>
					</div>
				</div>
			</div>
		</div>
		<van-popup :safe-area-inset-bottom="true" v-model:show="visitPlanDialog" position="bottom" :style="{ height: '90%' }">
			<ai-doctors @close="visitPlanDialog = false" @activeDoc="activeDoc" :doctorList="doctorList"></ai-doctors>
		</van-popup>
		<!-- 更多医生 弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="docMoreVis" position="bottom" :style="{ height: '90%' }">
			<ai-customer-doctors @close="docMoreVis = false" @activeDoc="activeDoc"></ai-customer-doctors>
		</van-popup>
		<!-- 医院 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="hosMoreVis" position="bottom" :style="{ height: '80%' }">
			<ai-hospital @close="hosMoreVis = false" @activeHos="activeHos" :hosList="hosList"></ai-hospital>
		</van-popup>
		<!-- 时间选择器 -->
		<van-popup class="visitTimePicker" :safe-area-inset-bottom="true" position="bottom" v-model:show="timeShow">
			<div class="title"><span>拜访时间</span></div>
			<div class="flex_box">
				<van-date-picker v-model="dateData.currentDate" :min-date="dateData.minDate" :max-date="dateData.maxDate" :show-toolbar="false" @change="dateChange" :formatter="formatterDate" swipe-duration="250" />
				<van-time-picker v-model="dateData.currentTime" :show-toolbar="false" @change="timeChange" :formatter="formatterDate" :max-hour="dateData.maxHour" :max-minute="dateData.maxMinute" swipe-duration="250" />
			</div>

			<div class="operation">
				<div @click="timeShow = false">取消</div>
				<div @click="onConfirm">确定</div>
			</div>
		</van-popup>
		<!-- 拜访结果 -->
		<van-popup v-model:show="visitResultDialog" round position="bottom">
			<van-picker :columns="visitResultList" @cancel="visitResultDialog = false" @confirm="onConfirmResult" />
		</van-popup>
		<!-- 领域 -->
		<van-popup v-model:show="therapyAreaDialog" round position="bottom">
			<van-picker :columns="selectTherapyAreaList" @cancel="therapyAreaDialog = false" @confirm="onConfirmArea" />
		</van-popup>
		<!-- 拜访时长 -->
		<van-popup v-model:show="visitDurationDialog" round position="bottom">
			<van-picker :columns="visitDurationList" @cancel="visitDurationDialog = false" @confirm="onConfirmDuration" />
		</van-popup>
		<!-- 拜访类型 -->
		<van-popup v-model:show="visitTypeDialog" round position="bottom">
			<van-picker :columns="visitChannelList" @cancel="visitTypeDialog = false" @confirm="onConfirmChannel" />
		</van-popup>
		<!-- 语音录入 -->
		<van-popup :close-on-popstate="true" :close-on-click-overlay="false" v-model:show="showCenter" round>
			<div class="popup__voice">
				<span class="voice-title">语音识别</span>
				<span class="voice-desc">（仅支持单条识别）</span>
				<div class="voice-gif">
					<img src="@/assets/img/visit.gif" alt="" />
				</div>
				<van-button color="#0052cc" @click="voiceDone">说完了</van-button>
			</div>
		</van-popup>
		<!-- 同事列表组件 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="showForward" position="bottom" :style="{ height: '90%' }">
			<team-list @close="showForward = false" @activeTask="activeTask" :list="teammateList"></team-list>
		</van-popup>
	</div>
</template>
<script setup>
import { showToast, showLoadingToast, showConfirmDialog, closeToast } from 'vant';
import { getTargetDoctor, getTreatmentArea } from '@/api/user';
import { dictData } from '@/api/login';
import { findVisitReportDetail, createVisitReport, updateVisitReport, getDoctorDetail } from '@/api/randomNotes';
import { findProductAll, colleagueList } from '@/api/task';
import useUserStore from '@/store/modules/user';
import CryptoJS from 'crypto-js';
import RecorderManager from '@/utils/tts';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
// 接口地址
const domain = enterStore.enterInfo.domain;
const relam = enterStore.enterInfo.id;
const recorder = new RecorderManager(`${domain}/app/mobile`);
const userStore = useUserStore();
const userId = userStore.userInfo.id;
import dayjs from 'dayjs';
import { uuid, dateISO, formattedTime } from '@/utils/index';
import { useI18n } from 'vue-i18n';
import usePremissionStore from '@/store/modules/premission';
const perStore = usePremissionStore();
const { t } = useI18n();
const singleChatTools = ref(true);
//格式化日期
const formatterDate = (type, option) => {
	if (type === 'year') {
		option.text += '年';
	}
	if (type === 'month') {
		option.text += '月';
	}
	if (type === 'day') {
		option.text += '日';
	}
	if (type === 'hour') {
		option.text += '时';
	}
	if (type === 'minute') {
		option.text += '分';
	}
	return option;
};

// 语言环境
const perList = computed(() => {
	return perStore.visitList;
});

// 语音录入
const showCenter = ref(false);

const timeShow = ref(false);
const dateData = reactive({
	currentDate: [dayjs().format('YYYY'), dayjs().format('MM'), dayjs().format('DD')],
	minDate: new Date(2022, 0, 1),
	maxDate: new Date(),
	currentTime: formattedTime(),
	maxHour: new Date().getHours(),
	maxMinute: new Date().getMinutes(),
});
const choiceTime = () => {
	timeShow.value = true;
};

const dateChange = (val) => {
	const dt1 = val.selectedValues.join('-');
	const dt2 = dayjs().format('YYYY-MM-DD');
	if (dt1 === dt2) {
		dateData.maxHour = new Date().getHours();
		dateData.maxMinute = new Date().getMinutes();
	} else {
		dateData.maxHour = 23;
		dateData.maxMinute = 59;
	}
	dateData.currentTime = formattedTime();
};

const timeChange = (val) => {
	if (val.columnIndex === 0) {
		const hour1 = val.selectedValues[0];
		const hour2 = dayjs().format('HH');
		if (hour1 === hour2) {
			dateData.maxMinute = new Date().getMinutes();
		} else {
			dateData.maxMinute = 59;
		}
	}
};
// 选择产品
const keyMessageList = ref([]);
const productChange = (event, item) => {
	if (item.keyMessage && item.keyMessage.length > 0) {
		if (formData.product.includes(item.id)) {
			keyMessageList.value.push(item);
			formData.keyMessage.push({
				id: item.id,
				name: item.name,
				list: [],
			});
		} else {
			keyMessageList.value = keyMessageList.value.filter((i) => i.id !== item.id);
			formData.keyMessage = formData.keyMessage.filter((i) => i.id !== item.id);
		}
	}
};

// 拜访类型
const visitTypeDialog = ref(false);
const selectChannel = () => {
	visitTypeDialog.value = true;
};
const onConfirmChannel = (val) => {
	visitTypeDialog.value = false;
	formData.visitType = val.selectedValues[0];
};

// 选择时长
const visitDurationDialog = ref(false);
const selectDuration = () => {
	visitDurationDialog.value = true;
};
const onConfirmDuration = (val) => {
	visitDurationDialog.value = false;
	formData.visitDuration = val.selectedValues[0];
};
// 选择结果
const visitResultDialog = ref(false);
const selectResult = () => {
	visitResultDialog.value = true;
};
const onConfirmResult = (val) => {
	visitResultDialog.value = false;
	formData.visitResult = val.selectedValues[0];
};
// 确认时间
const onConfirm = () => {
	formData.visitTime = `${dateData.currentDate.join('-')} ${dateData.currentTime.join(':')}`;
	timeShow.value = false;
};

// 类型切换
const active = ref('');
const isFormValite = () => {
	if (active.value === t('visit.medicalInsights')) {
		// 校验表单是否都是空的
		if (arePropertiesEmpty(formData, ['visitMemo', 'ownerName', 'therapyArea', 'visitTime', 'visitPurpose', 'patientType', 'productInformationFocus', 'productEfficacyRecognition'])) {
			return 3;
		} else {
			// 校验必填项是否已填
			if (arePropertiesNotEmpty(formData, ['visitMemo', 'ownerName', 'therapyArea', 'visitTime'])) {
				return 2;
			} else {
				return 1;
			}
		}
	} else if (active.value === t('visit.medicalInquiry')) {
		// 校验表单是否都是空的
		if (arePropertiesEmpty(formData, ['visitMemo', 'visitTime', 'ownerName', 'email', 'phone'])) {
			return 3;
		} else {
			// 校验必填项是否已填
			if (arePropertiesNotEmpty(formData, ['visitMemo', 'visitTime', 'ownerName', 'email', 'phone'])) {
				return 2;
			} else {
				return 1;
			}
		}
	} else if (active.value === t('visit.visit')) {
		// 校验表单是否都是空的
		if (arePropertiesEmpty(formData, ['visitMemo', 'ownerName', 'visitTime', 'visitResult', 'visitPurpose', 'product', 'visitDuration', 'visitType', 'keyMessage'])) {
			return 3;
		} else {
			// 校验必填项是否已填
			if (arePropertiesNotEmpty(formData, ['visitMemo', 'ownerName', 'visitTime', 'visitDuration', 'product', 'visitType', 'keyMessage'])) {
				return 2;
			} else {
				return 1;
			}
		}
	} else if (active.value === t('visit.visitColl')) {
		// 校验表单是否都是空的
		if (arePropertiesEmpty(formData, ['visitMemo', 'ownerName', 'visitTime', 'visitResult', 'visitPurpose', 'product', 'visitDuration', 'visitType', 'coordinatorId', 'intervieweeId'])) {
			return 3;
		} else {
			// 校验必填项是否已填
			if (arePropertiesNotEmpty(formData, ['visitMemo', 'ownerName', 'visitTime', 'visitDuration', 'product', 'visitType'])) {
				return 2;
			} else {
				return 1;
			}
		}
	} else {
		// 校验表单是否都是空的
		if (arePropertiesEmpty(formData, ['visitMemo', 'visitTime', 'ownerName'])) {
			return 3;
		} else {
			// 校验必填项是否已填
			if (arePropertiesNotEmpty(formData, ['visitMemo', 'visitTime', 'ownerName'])) {
				return 2;
			} else {
				return 1;
			}
		}
	}
};
const arePropertiesEmpty = (obj, props) => {
	return props.every(function (prop) {
		return obj[prop] == null || obj[prop] === '' || obj[prop].length === 0;
	});
};
const arePropertiesNotEmpty = (obj, props) => {
	return props.every(function (prop) {
		if (Array.isArray(obj[prop])) {
			return obj[prop].length > 0;
		} else {
			return obj[prop] !== '';
		}
	});
};
const beforeChange = async () => {
	const isValite = isFormValite();
	if (isValite === 1) {
		return showConfirmDialog({
			title: '提示',
			message: '当前拜访记录不完善',
			confirmButtonText: '清空并跳转',
			confirmButtonColor: '#0052CC',
		})
			.then(() => {
				return true;
			})
			.catch(async () => {
				// 校验表单
				try {
					await form1.value.validate();
				} catch (e) {
					console.log(e);
				}
				return false;
			});
	} else if (isValite === 2) {
		return showConfirmDialog({
			title: '提示',
			message: '是否保存当前拜访记录?',
			confirmButtonText: '保存并跳转',
			confirmButtonColor: '#0052CC',
		})
			.then(() => {
				return switchSaveVisit().then(() => {
					return true;
				});
			})
			.catch(() => {
				return false;
			});
	} else {
		return true;
	}
};

const tabChange = () => {
	// 这里需要做校验
	obSuggest.isZk = false;
	obSuggest.isVisitPlan = false;
	obSuggest.isMeetingInvite = false;
	obSuggest.isKeyData = false;
	obSuggest.adviceMsg = '';
	// 清空表单
	formData.visitMemo = '';
	formData.ownerId = '';
	formData.ownerName = '';
	formData.multiPointPractice = false;
	formData.hospitalId = '';
	formData.hospital = '';
	formData.therapyArea = '';
	formData.visitPurpose = [];
	formData.productInformationFocus = [];
	formData.productEfficacyRecognition = [];
	formData.patientType = [];
	formData.product = [];
	formData.email = '';
	formData.phone = '';
	formData.visitTime = '';
	formData.remark = '';
	formData.visitDuration = '';
	formData.visitResult = '';
	formData.visitType = '';
	formData.coordinatorId = '';
	formData.intervieweeId = '';
	formData.coordinatorName = '';
	formData.intervieweeName = '';
	formData.keyMessage = [];
	keyMessageList.value = [];
	// 表单校验状态重置
	form1.value.resetValidation();

	nextTick(() => {
		// 重新初始化
		init();
	});
};

// 行为建议
const obSuggest = reactive({
	isZk: false,
	adviceMsg: '',
	isVisitPlan: false,
	isMeetingInvite: false,
	isKeyData: false,
});
const adviceEv = async () => {
	if (obSuggest.adviceMsg) {
		obSuggest.isZk = !obSuggest.isZk;
		return;
	}
	try {
		await form1.value.validate();
		const json = {
			doctorId: formData.ownerId || '',
			hospitalId: formData.hospitalId || '',
			doctorName: formData.ownerName || '',
			hospital: formData.hospital || '',
			visitMemo: formData.visitMemo || '',
			visitTime: formData.visitTime || '',
		};
		if (active.value === t('visit.medicalInsights')) {
			json.therapyArea = formData.therapyArea || '';
			json.visitPurpose = formData.visitPurpose.length > 0 ? formData.visitPurpose.join(',') : '';
			json.productInformationFocus = formData.productInformationFocus.length > 0 ? formData.productInformationFocus.join(',') : '';
			json.productEfficacyRecognition = formData.productEfficacyRecognition.length > 0 ? formData.productEfficacyRecognition.join(',') : '';
			json.patientType = formData.patientType.length > 0 ? formData.patientType.join(',') : '';
		}
		if (active.value === t('visit.visit') || active.value === t('visit.visitColl')) {
			json.visitPurpose = formData.visitPurpose.length > 0 ? formData.visitPurpose.join(',') : '';
			json.product = formData.product.length > 0 ? formData.product.join(',') : '';
			json.visitDuration = formData.visitDuration || '';
			json.visitResult = formData.visitResult || '';
			json.visitType = formData.visitType || '';
		}
		if (active.value === t('visit.visitColl')) {
			json.coordinatorId = formData.coordinatorId || '';
			json.intervieweeId = formData.intervieweeId || '';
			json.coordinatorName = formData.coordinatorName || '';
			json.intervieweeName = formData.intervieweeName || '';
		}
		setAiMessage(json);
	} catch (e) {
		console.log(e);
		showToast({
			position: 'top',
			message: '当前无法提供下一步建议，请完善拜访记录',
		});
	}
};
const formData = reactive({
	id: '',
	memoGenerationType: '手工填写',
	visitChannel: '',
	visitMemo: '',
	ownerType: 'DOCTOR',
	recordStatus: true,
	ownerId: '',
	ownerName: '',
	multiPointPractice: false,
	hospitalId: '',
	hospital: '',
	therapyArea: '',
	visitDuration: '',
	visitResult: '',
	visitPurpose: [],
	productInformationFocus: [],
	productEfficacyRecognition: [],
	patientType: [],
	product: [],
	email: '',
	phone: '',
	typeKinds: '',
	visitTime: '',
	remark: '',
	visitType: '',
	coordinatorId: '',
	intervieweeId: '',
	coordinatorName: '',
	intervieweeName: '',
	keyMessage: [],
});
const resetAdvice = () => {
	obSuggest.isZk = false;
	obSuggest.isVisitPlan = false;
	obSuggest.isMeetingInvite = false;
	obSuggest.isKeyData = false;
	obSuggest.adviceMsg = '';
};
// 监听客户id
watch(
	() => formData.ownerId,
	() => {
		resetAdvice();
	}
);

// 监听拜访时间
watch(
	() => formData.visitTime,
	() => {
		resetAdvice();
	}
);

// 监听见解
watch(
	() => formData.visitMemo,
	() => {
		resetAdvice();
	}
);

// 监听拜访时长
watch(
	() => formData.visitDuration,
	() => {
		resetAdvice();
	}
);

// 监听拜访类型
watch(
	() => formData.visitType,
	() => {
		resetAdvice();
	}
);

// 监控拜访结果
watch(
	() => formData.visitResult,
	() => {
		resetAdvice();
	}
);

// 监控产品
watch(
	() => formData.product,
	() => {
		resetAdvice();
	}
);

// 拜访类型
const visitChannelList = ref([]);

// 拜访时长
const visitDurationList = ref([]);

// 拜访结果
const visitResultList = ref([]);

// 拜访目的
const visitPurposeList = ref([]);

// 选择治疗领域
const therapyAreaDialog = ref(false);
const selectTherapyAreaList = ref([]);

const getOtherInfo = async () => {
	try {
		const res = await dictData(['visitType', 'visitResults', 'visitDuration', 'visitPurpose']);
		visitChannelList.value = res.result.visitType?.map((ele) => ({
			text: ele.name,
			value: ele.name,
		}));
		visitResultList.value = res.result.visitResults?.map((ele) => ({
			text: ele.name,
			value: ele.name,
		}));
		visitDurationList.value = res.result.visitDuration?.map((ele) => ({
			text: ele.name,
			value: ele.name,
		}));
		visitPurposeList.value = res.result.visitPurpose?.map((ele) => ({
			text: ele.name,
			value: ele.name,
		}));
	} catch (error) {
		console.error(error);
	}
};

// 同事列表
const teammateList = ref([]);
const getTeammateList = () => {
	colleagueList(userId, { isBrief: false }).then((res) => {
		if (res.result && res.result.length > 0) {
			res.result = res.result.reduce((acc, cur) => {
				const hasDuplicate = acc.some((item) => item.id === cur.id);
				if (!hasDuplicate) {
					acc.push(cur);
				}
				return acc;
			}, []);
		}
		teammateList.value = res.result || [];
	});
};
const showForward = ref(false);
let isNum = 0;
const showForwardEv = (val) => {
	isNum = val;
	showForward.value = true;
};
const activeTask = (val) => {
	showForward.value = false;
	const info = teammateList.value.filter((ele) => ele.id === val)[0];
	if (isNum === 1) {
		formData.coordinatorId = info.username;
		formData.coordinatorName = info.firstName;
	} else {
		formData.intervieweeId = info.username;
		formData.intervieweeName = info.firstName;
	}
};

const getTherapyAreaList = async () => {
	let res = await getTreatmentArea({ page: 0, size: 1000, keyWord: '' });
	selectTherapyAreaList.value = res.result?.content?.map((ele) => {
		return { text: ele.name, value: ele.name };
	});
};
const selectTherapyArea = () => {
	therapyAreaDialog.value = true;
};
const onConfirmArea = (val) => {
	formData.therapyArea = val.selectedValues[0];
	therapyAreaDialog.value = false;
};

// 选择客户
const selectDoctor = () => {
	if (singleChatTools.value && route.query.fromSource !== 'docModelInfo') {
		docMoreVis.value = true;
	}
};
// 选择医院
const hosMoreVis = ref(false);
const selectHospital = () => {
	hosMoreVis.value = true;
};
// 客户校验函数
const cusValidator = (val) => {
	if (val) return true;
	return false;
};
// 邮箱校验函数
const validatorEmail = (val) => {
	if (!val) return '请输入邮箱地址';
	// 正则校验邮箱地址
	const reg = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
	if (!reg.test(val)) {
		return '请输入正确的邮箱地址';
	}
	return true;
};

// 电话校验函数
const validatorPhone = (val) => {
	if (!val) return '请输入手机号';
	// 正则校验手机号
	const reg = /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/;
	if (!reg.test(val)) {
		return '请输入正确的手机号';
	}
	return true;
};

// 确认函数
const form1 = ref(null);
const switchSaveVisit = () => {
	return new Promise((resolve) => {
		const form2Data = toRaw(formData);
		const query = {
			memoGenerationType: '手工填写',
			visitChannel: active.value,
			ownerType: 'DOCTOR',
			recordStatus: true,
			ownerId: form2Data.ownerId,
			hospitalId: form2Data.hospitalId,
			hospital: form2Data.hospital,
			remark: form2Data.remark || '',
			memorandum: form2Data.visitMemo || '',
			typeKinds: active.value,
			visitTime: dateISO(form2Data.visitTime.replace(' ', 'T') + ':00'),
		};
		if (active.value === t('visit.medicalInquiry')) {
			query.email = form2Data.email;
			query.phone = form2Data.phone;
		} else if (active.value === t('visit.medicalInsights')) {
			query.visitPurpose = form2Data.visitPurpose.length > 0 ? form2Data.visitPurpose.join(',') : '';
			query.therapyArea = form2Data.therapyArea;
			query.productInformationFocus = form2Data.productInformationFocus.length > 0 ? form2Data.productInformationFocus.join(',') : '';
			query.productEfficacyRecognition = form2Data.productEfficacyRecognition.length > 0 ? form2Data.productEfficacyRecognition.join(',') : '';
			query.patientType = form2Data.patientType.length > 0 ? form2Data.patientType.join(',') : '';
		} else if (active.value === t('visit.visit') || active.value === t('visit.visitColl')) {
			query.visitDuration = form2Data.visitDuration;
			query.visitType = form2Data.visitType;
			query.visitResult = form2Data.visitResult;
			query.visitPurpose = form2Data.visitPurpose.length > 0 ? form2Data.visitPurpose.join(',') : '';
			query.productId = form2Data.product.length > 0 ? form2Data.product.join(',') : '';
		}
		if (active.value === t('visit.visit')) {
			query.keyMessage = form2Data.keyMessage.length > 0 ? JSON.stringify(form2Data.keyMessage) : '';
		}
		if (active.value === t('visit.visitColl')) {
			query.intervieweeId = form2Data.intervieweeId || '';
			query.intervieweeName = form2Data.intervieweeName || '';
			query.coordinatorId = form2Data.coordinatorId || '';
			query.coordinatorName = form2Data.coordinatorName || '';
		}
		createVisitReport(query);
		resolve();
	});
};
// 校验录入是否合规
const validateRule = (svg) => {
	return new Promise((resolve, reject) => {
		controller = new AbortController();
		const { signal } = controller;
		let radom1 = uuid();
		let radom2 = uuid();
		fetch(`${enterStore.enterInfo.agentAddress}/api/v1/chat/completions`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: 'Bearer appKey-kYAc4Iillds98lrt0O6moJExoCkMwpQkWY1rJ1nw0CdHy3AC5S8AkZjcwHRc',
			},
			body: JSON.stringify({
				chatId: chatId,
				stream: false,
				detail: false,
				variables: {
					type: 'compliance_inspection',
				},
				messages: [
					{ role: 'user', content: svg, dataId: radom1 },
					{ role: 'assistant', content: '', dataId: radom2 },
				],
			}),
		})
			.then((res) => {
				if (!res.ok) {
					throw new Error('Request Error');
				}
				return res.json();
			})
			.then(async (data) => {
				let startIndex = data.choices[0].message.content.indexOf('{');
				let endIndex = data.choices[0].message.content.lastIndexOf('}');
				let result = JSON.parse(data.choices[0].message.content.substring(startIndex, endIndex + 1));
				if (result.result && (result.result === '0' || result.result === 0)) {
					// 不合规
					showToast({
						message: result.reason || '录入内容不合规，请重新录入',
						position: 'top',
					});
					reject();
				} else {
					resolve();
				}
			})
			.catch((e) => {
				if (e.message !== 'signal is aborted without reason') {
					showToast({
						message: '请求失败,请检查网络',
						position: 'top',
					});
				} else {
					showToast({
						message: '合规校验失败,请检查网络',
						position: 'top',
					});
				}
				reject();
			});
	});
};
const confirmCreate = async (svg) => {
	try {
		await form1.value.validate();
		showLoadingToast({
			message: route?.query?.id ? '修改中...' : '创建中...',
			duration: 0,
			forbidClick: true,
		});
		// await validateRule(formData.visitMemo);
		const query = {
			memoGenerationType: '手工填写',
			visitChannel: active.value,
			ownerType: 'DOCTOR',
			recordStatus: true,
			ownerId: formData.ownerId,
			hospitalId: formData.hospitalId,
			hospital: formData.hospital,
			remark: formData.remark || '',
			memorandum: formData.visitMemo || '',
			typeKinds: active.value,
			visitTime: dateISO(formData.visitTime.replace(' ', 'T') + ':00'),
		};
		if (active.value === t('visit.medicalInquiry')) {
			query.email = formData.email;
			query.phone = formData.phone;
		} else if (active.value === t('visit.medicalInsights')) {
			query.visitPurpose = formData.visitPurpose.length > 0 ? formData.visitPurpose.join(',') : '';
			query.therapyArea = formData.therapyArea;
			query.productInformationFocus = formData.productInformationFocus.length > 0 ? formData.productInformationFocus.join(',') : '';
			query.productEfficacyRecognition = formData.productEfficacyRecognition.length > 0 ? formData.productEfficacyRecognition.join(',') : '';
			query.patientType = formData.patientType.length > 0 ? formData.patientType.join(',') : '';
		} else if (active.value === t('visit.visit') || active.value === t('visit.visitColl')) {
			query.visitDuration = formData.visitDuration;
			query.visitType = formData.visitType;
			query.visitResult = formData.visitResult;
			query.visitPurpose = formData.visitPurpose.length > 0 ? formData.visitPurpose.join(',') : '';
			query.productId = formData.product.length > 0 ? formData.product.join(',') : '';
		}
		if (active.value === t('visit.visit')) {
			query.keyMessage = formData.keyMessage.length > 0 ? JSON.stringify(formData.keyMessage) : '';
		}
		if (active.value === t('visit.visitColl')) {
			query.coordinatorId = formData.coordinatorId;
			query.coordinatorName = formData.coordinatorName;
			query.intervieweeName = formData.intervieweeName;
			query.intervieweeId = formData.intervieweeId;
		}
		if (route?.query?.id) {
			// 编辑
			query.id = route.query.id;
			updateVisitReport(query).then(() => {
				showToast({
					position: 'top',
					message: '修改成功',
				});
				// 返回上一页
				if (svg === '') {
					goBack();
				} else {
					leaveName(svg);
				}
			});
		} else {
			// 新增
			createVisitReport(query).then((res) => {
				showToast({
					position: 'top',
					message: '创建成功',
				});
				// 返回上一页
				if (!singleChatTools.value) {
					if (svg) {
						leaveName(svg);
					} else {
						// 清空表单信息
						tabChange();
					}
				} else if (svg === '') {
					goBack();
				} else {
					leaveName(svg);
				}
			});
		}
	} catch (e) {
		console.log(e);
	}
};

// 语音录入
const voiceEv = () => {
	// 激活讯飞在线语音api
	initVoice();
	showCenter.value = true;
};
const voiceDone = () => {
	recorder?.stop();
	showCenter.value = false;
};
// 返回
const goBack = () => {
	router.go(-1);
};

// 创建拜访计划
const router = useRouter();
const route = useRoute();

const goPage = async (name) => {
	// 提示用户是否保存当前拜访记录
	try {
		await form1.value.validate();
		showConfirmDialog({
			title: '提示',
			message: '是否保存当前拜访记录？',
			confirmButtonColor: '#0052CC',
		})
			.then(() => {
				confirmCreate(name);
			})
			.catch(() => {
				leaveName(name);
			});
	} catch (e) {
		leaveName(name);
	}
};
const leaveName = (name) => {
	if (name === '发送会议邀请') {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/mobile/meeting',
				title: '会议管理',
				module: 'third',
			});
		} else {
			location.href = domain + '/mobile/meeting';
		}
	} else if (name === '传递关键资料') {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/mobile/contentCenter/contentCenterIndex/marketKnowledge',
				title: '资料推广',
				module: 'third',
			});
		} else {
			location.href = domain + '/mobile/contentCenter/contentCenterIndex/marketKnowledge';
		}
	} else if (name === '创建拜访计划') {
		router.push({
			name: 'keyTaskDetail',
			query: {
				isTabBar: route.query.isTabBar || '',
				isCreateTask: 'create',
				taskType: '线下拜访',
				source: 'notes',
				taskDescription: obSuggest.adviceMsg || '',
				currencyId: formData.ownerId || '',
				currencyName: formData.ownerName || '',
				endTime: formData.visitTime ? formData.visitTime.replace(' ', 'T') + ':00' : '',
			},
		});
	} else {
		if (route.query.isTabBar === 'false') {
			proxy.$cHandler.openAppView({
				url: domain + '/mobile/questionResearch',
				title: '客户拜访信息收集',
				module: 'third',
			});
		} else {
			location.href = domain + '/mobile/questionResearch';
		}
	}
};
// 校验是否合规
let controller = '';
const chatId = 'ssjil' + userStore.userInfo.username;
const setAiMessage = (json) => {
	showLoadingToast({
		message: '生成中...',
		forbidClick: true,
		duration: 0,
	});
	controller = new AbortController();
	const { signal } = controller;
	const val = `['visit_plan']`;
	let radom1 = uuid();
	let radom2 = uuid();
	fetch(`${enterStore.enterInfo.agentAddress}/api/v1/chat/completions`, {
		signal,
		method: 'POST',
		headers: {
			'Content-Type': 'application/json;charset=utf-8',
			Authorization: 'Bearer appKey-kYAc4Iillds98lrt0O6moJExoCkMwpQkWY1rJ1nw0CdHy3AC5S8AkZjcwHRc',
		},
		body: JSON.stringify({
			chatId: chatId,
			stream: false,
			detail: false,
			variables: {
				intent_type: val,
				type: 'intent_recognition',
			},
			messages: [
				{ role: 'user', content: JSON.stringify(json), dataId: radom1 },
				{ role: 'assistant', content: '', dataId: radom2 },
			],
		}),
	})
		.then((res) => {
			if (!res.ok) {
				throw new Error('Request Error');
			}
			return res.json();
		})
		.then((data) => {
			closeToast();
			let startIndex = data.choices[0].message.content.indexOf('{');
			let endIndex = data.choices[0].message.content.indexOf('}');
			let result = JSON.parse(data.choices[0].message.content.substring(startIndex, endIndex + 1));
			if (Object.keys(result).length === 0) {
				showToast({
					message: '暂无行动建议',
					position: 'top',
				});
				return;
			}
			obSuggest.adviceMsg = result.reason || '';
			for (const item of result.intent_type) {
				// 显示哪个按钮
				if (item === 'deliver_key_materials') {
					obSuggest.isKeyData = true;
				} else if (item === 'send_meeting_invitation') {
					obSuggest.isMeetingInvite = true;
				} else if (item === 'visit_plan') {
					obSuggest.isVisitPlan = true;
				}
			}
			obSuggest.isZk = !obSuggest.isZk;
		})
		.catch((e) => {
			if (e.message !== 'signal is aborted without reason') {
				showToast({
					message: '请求失败,请检查网络',
					position: 'top',
				});
			}
		});
};
const doctorList = ref([]);
const setAiUrl = () => {
	controller = new AbortController();
	const { signal } = controller;
	let val = '';
	if (active.value === t('visit.visit') || active.value === t('visit.visitColl')) {
		val = `['sales_visite']`;
	} else {
		val = `['medical_visit']`;
	}
	showLoadingToast({
		message: '识别中...',
		forbidClick: true,
		duration: 0,
	});
	let radom1 = uuid();
	let radom2 = uuid();
	fetch(`${enterStore.enterInfo.agentAddress}/api/v1/chat/completions`, {
		signal,
		method: 'POST',
		headers: {
			'Content-Type': 'application/json;charset=utf-8',
			Authorization: 'Bearer appKey-kYAc4Iillds98lrt0O6moJExoCkMwpQkWY1rJ1nw0CdHy3AC5S8AkZjcwHRc',
		},
		body: JSON.stringify({
			chatId: chatId,
			stream: false,
			detail: false,
			variables: {
				cTime: dayjs().format('YYYY-MM-DD HH:mm:ss') + ' ' + dayjs().format('dddd'),
				intent_type: val,
				type: 'extract_params',
				therapeutic_area: `-therapyArea 治疗领域 (治疗领域有:${selectTherapyAreaList.value?.map((ele) => ele.text).join('、')})`,
			},
			messages: [
				{ role: 'user', content: formData.remark, dataId: radom1 },
				{ role: 'assistant', content: '', dataId: radom2 },
			],
		}),
	})
		.then((res) => {
			if (!res.ok) {
				throw new Error('Request Error');
			}
			return res.json();
		})
		.then(async (data) => {
			let startIndex = data.choices[0].message.content.indexOf('{');
			let endIndex = data.choices[0].message.content.lastIndexOf('}');
			let result = JSON.parse(data.choices[0].message.content.substring(startIndex, endIndex + 1));
			if (Object.keys(result).length === 0) {
				showToast({
					message: '暂无匹配结果',
					position: 'top',
				});
				return;
			}
			if (result.type && result.type === 'is_compliant') {
				// 不合规
				showToast({
					message: result.message || '识别内容不合规，请修改后重试',
					position: 'top',
				});
				return;
			}
			formData.visitMemo = result.params.visitMemo || '';
			// 拜访时间
			if (result.params.visitTime) {
				formData.visitTime = result.params.visitTime.split(' ')[0] + ' 00:00' || '';
			} else {
				formData.visitTime = '';
			}
			if (active.value === t('visit.medicalInsights')) {
				// 领域
				formData.therapyArea = result.params.therapyArea || '';
				// 拜访目的
				if (result.params.visitPurpose) {
					formData.visitPurpose = result.params.visitPurpose.split(',');
				} else {
					formData.visitPurpose = [];
				}
				if (result.params.productInformationFocus) {
					formData.productInformationFocus = result.params.productInformationFocus.split(',');
				} else {
					formData.productInformationFocus = [];
				}
				if (result.params.productEfficacyRecognition) {
					formData.productEfficacyRecognition = result.params.productEfficacyRecognition.split(',');
				} else {
					formData.productEfficacyRecognition = [];
				}
				if (result.params.patientType) {
					formData.patientType = result.params.patientType.split(',');
				} else {
					formData.patientType = [];
				}
			} else if (active.value === t('visit.medicalInquiry')) {
				formData.email = result.params.email || '';
				formData.phone = result.params.phone || '';
			} else if (active.value === t('visit.visit') || active.value === t('visit.visitColl')) {
				// 拜访目的
				if (result.params.visitPurpose) {
					formData.visitPurpose = [result.params.visitPurpose];
				} else {
					formData.visitPurpose = [];
				}
				// 拜访结果
				formData.visitResult = result.params.visitResult || '';
				// 拜访时长
				formData.visitDuration = result.params.visitDuration || '';
				// 拜访类型 visitChannel不改成visitType因为ai返回
				formData.visitType = result.params.visitChannel || '';
			}
			// 判断是否返回来医生名字
			doctorList.value = [];
			formData.ownerId = '';
			formData.ownerName = '';
			formData.multiPointPractice = false;
			if (result.params.doctorName) {
				await getMyDoctorList(result.params.doctorName);
			}
			if (doctorList.value.length === 1) {
				formData.ownerId = doctorList.value[0].doctorId;
				formData.ownerName = doctorList.value[0].doctorName;
				formData.multiPointPractice = doctorList.value[0]?.servingHospital?.length > 0 ? true : false;
				if (formData.multiPointPractice) {
					formData.hospitalId = '';
					formData.hospital = '';
					getHosList(doctorList.value[0]?.servingHospital, doctorList.value[0]);
				}
			} else if (doctorList.value.length > 1) {
				visitPlanDialog.value = true;
			} else {
				if (result.params.doctorName_pinyin) {
					await getMyDoctorList(result.params.doctorName_pinyin);
				}
				if (doctorList.value.length > 0) {
					visitPlanDialog.value = true;
				} else {
					// 没有识别出的医生, 弹出医生列表让用户选择
					if (route.query.queryText) {
						selectDoctor();
					}
				}
			}
			closeToast();
		})
		.catch((e) => {
			if (e.message !== 'signal is aborted without reason') {
				showToast({
					message: '请求失败,请检查网络',
					position: 'top',
				});
			}
		});
};

// 获取我的医生
const getMyDoctorList = (val) => {
	return new Promise((resolve) => {
		const json = {
			search: val,
			allClient: false,
			isFocus: false,
			page: 1,
			size: 50,
			total: 0,
		};
		getTargetDoctor(json)
			.then((res) => {
				doctorList.value = res.result.doctors || [];
			})
			.finally(() => {
				if (doctorList.value.length > 0) {
					resolve('ok');
				} else {
					resolve('no');
				}
			});
	});
};
// 获取全部医生
const getAllDoctorList = (val) => {
	return new Promise((resolve) => {
		const json = {
			search: val,
			allClient: true,
			isFocus: false,
			page: 1,
			size: 50,
			total: 0,
		};
		getTargetDoctor(json)
			.then((res) => {
				doctorList.value = res.result.doctors || [];
			})
			.finally(() => {
				resolve('ok');
			});
	});
};

const visitPlanDialog = ref(false);
const docMoreVis = ref(false);
const activeDoc = (item) => {
	formData.ownerId = item.doctorId;
	formData.ownerName = item.doctorName;
	// 需要获取医生是否是多点执业
	formData.multiPointPractice = item?.servingHospital?.length > 0 ? true : false;
	if (formData.multiPointPractice) {
		formData.hospitalId = '';
		formData.hospital = '';
		getHosList(item.servingHospital, item);
	}
};

// 医院列表
const hosList = ref([]);
const getHosList = (item, info) => {
	const json = {
		doctorIdMulti: info.hospitalId,
		hospital: info.hospital,
		department: '未知/' + info.department,
	};
	hosList.value = [json, ...item];
};

const activeHos = (item) => {
	formData.hospitalId = item.doctorIdMulti;
	formData.hospital = item.hospital;
};

onMounted(async () => {
	if (localStorage.getItem('entry') === 'single_chat_tools') {
		singleChatTools.value = false;
	}
	active.value = t(perList.value[0].path);
	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
	document.querySelector('body').style.backgroundColor = '#f4f5f7';
	// 产品列表
	await getProductsList();
	// 同事列表
	getTeammateList();
	// 其他的一些信息
	getOtherInfo();
	// 治疗领域
	getTherapyAreaList();
	init();
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
	if (controller) controller?.abort();
	closeToast();
});

// 产品列表
const productsList = ref([]);
const getProductsList = () => {
	return new Promise((resolve) => {
		findProductAll().then((res) => {
			if (res.result && res.result.length > 0) {
				// 企业如果是穆桥，只显示“开浦兰” “维派特”两个产品
				if (relam === 'muqiao') {
					res.result = res.result.filter((item) => item.code === '002' || item.code === '005' || item.code === '001');
				}
			}
			productsList.value = res.result || [];
			resolve();
		});
	});
};

// 获取拜访详情
const getVisitDetail = () => {
	findVisitReportDetail(formData.id).then((res) => {
		formData.memoGenerationType = '手工填写';
		formData.visitChannel = res.result.visitChannel || '';
		formData.recordStatus = true;
		formData.visitMemo = res.result.memorandum || '';
		formData.remark = res.result.remark || '';
		formData.ownerId = res.result?.doctorVO.doctorId || '';
		formData.ownerName = res.result?.doctorVO.doctorName || '';
		formData.hospitalId = res.result?.hospitalId || '';
		formData.hospital = res.result?.hospital || '';
		if (formData.hospitalId) {
			formData.multiPointPractice = true;
			getDoctorDetail(formData.ownerId).then((l) => {
				getHosList(l.result[0]?.servingHospital, l.result[0]);
			});
		} else {
			formData.multiPointPractice = false;
		}
		// 拜访时间
		formData.visitTime = res.result?.visitTime.split('T')[0] + ' ' + res.result?.visitTime.split('T')[1].slice(0, -3) || '';
		if (res.result.typeKinds === t('visit.medicalInquiry')) {
			active.value = t('visit.medicalInquiry');
			formData.typeKinds = t('visit.medicalInquiry');
			formData.email = res.result?.email || '';
			formData.phone = res.result?.phone || '';
		} else if (res.result.typeKinds === t('visit.notes')) {
			active.value = t('visit.notes');
			formData.typeKinds = t('visit.notes');
		} else if (res.result.typeKinds === t('visit.visitColl')) {
			active.value = t('visit.visitColl');
			formData.typeKinds = t('visit.visitColl');
			// 拜访结果
			formData.visitResult = res.result?.visitResult || '';
			// 拜访时长
			formData.visitDuration = res.result?.visitDuration || '';
			// 拜访类型
			formData.visitType = res.result?.visitType || '';
			// 协访人
			formData.coordinatorId = res.result?.coordinatorId || '';
			formData.coordinatorName = res.result?.coordinatorName || '';
			// 被协防人
			formData.intervieweeId = res.result?.intervieweeIds || '';
			formData.intervieweeName = res.result?.intervieweeName || '';
			// 产品
			if (res.result.productVOS && res.result.productVOS.length > 0) {
				res.result.productVOS.forEach((item) => {
					formData.product.push(item.id);
				});
			}
			// 拜访目的
			if (res.result.visitPurpose) formData.visitPurpose = res.result.visitPurpose.split(',');
		} else if (res.result.typeKinds === t('visit.visit')) {
			active.value = t('visit.visit');
			formData.typeKinds = t('visit.visit');
			// 拜访结果
			formData.visitResult = res.result?.visitResult || '';
			// 拜访时长
			formData.visitDuration = res.result?.visitDuration || '';
			// 拜访类型
			formData.visitType = res.result?.visitType || '';
			// 产品
			if (res.result.productVOS && res.result.productVOS.length > 0) {
				res.result.productVOS.forEach((item) => {
					formData.product.push(item.id);
				});
			}
			// 拜访目的
			if (res.result.visitPurpose) formData.visitPurpose = res.result.visitPurpose.split(',');

			// 产品的关键信息
			if (res.result.keyMessage) {
				let ll = JSON.parse(res.result.keyMessage);
				formData.keyMessage = ll;
				for (const ele of ll) {
					keyMessageList.value.push(productsList.value.filter((item) => item.id === ele.id)[0]);
				}
			}
		} else {
			active.value = t('visit.medicalInsights');
			formData.typeKinds = t('visit.medicalInsights');
			formData.therapyArea = res.result?.therapyArea || '';
			if (res.result.visitPurpose) formData.visitPurpose = res.result.visitPurpose.split(',');
			if (res.result.productInformationFocus) formData.productInformationFocus = res.result.productInformationFocus.split(',');
			if (res.result.productEfficacyRecognition) formData.productEfficacyRecognition = res.result.productEfficacyRecognition.split(',');
			if (res.result.patientType) formData.patientType = res.result.patientType.split(',');
		}
	});
};

// 初始化
const init = () => {
	if (route.query.id) {
		formData.id = route.query.id;
		// 获取拜访详情
		getVisitDetail();
	} else {
		// 显示那个页签
		if (route.query.jType && route.query.jType === 'sales_visit') {
			// 激活拜访页签
			active.value = t('visit.visit');
			formData.typeKinds = t('visit.visit');
		}
		// url是否存在queryText
		if (route.query.queryText) {
			formData.remark = route.query.queryText;
			// url是否存在doctorId
			if (route.query.doctorId) {
				formData.ownerId = route.query.doctorId;
				getDoctorDetail(route.query.doctorId).then((res) => {
					formData.ownerName = res.result[0].doctorName;
					formData.multiPointPractice = res.result[0]?.servingHospital?.length > 0 ? true : false;
					if (formData.multiPointPractice) {
						formData.hospitalId = '';
						formData.hospital = '';
						getHosList(res.result[0]?.servingHospital, res.result[0]);
					}
				});
			}
			setAiUrl();
		}

		if ((!singleChatTools.value && route.query.doctorId) || (route.query.doctorId && route.query.fromSource === 'docModelInfo')) {
			formData.ownerId = route.query.doctorId;
			getDoctorDetail(route.query.doctorId).then((res) => {
				formData.ownerName = res.result[0].doctorName;
				formData.multiPointPractice = res.result[0]?.servingHospital?.length > 0 ? true : false;
				if (formData.multiPointPractice) {
					formData.hospitalId = '';
					formData.hospital = '';
					getHosList(res.result[0]?.servingHospital, res.result[0]);
				}
			});
		}
	}
};

// 语音识别
let iatWS = null;
let resultText = '';
let resultTextTemp = '';
const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let btnStatus = 'UNDEFINED'; // "UNDEFINED" "CONNECTING" "OPEN" "CLOSING" "CLOSED"

const initVoice = () => {
	resultText = '';
	resultTextTemp = '';
	if (btnStatus === 'UNDEFINED' || btnStatus === 'CLOSED') {
		connectWebSocket();
	}
};

const connectWebSocket = () => {
	const websocketUrl = getWebSocketUrl();
	if ('WebSocket' in window) {
		iatWS = new WebSocket(websocketUrl);
	} else if ('MozWebSocket' in window) {
		iatWS = new MozWebSocket(websocketUrl);
	} else {
		showToast({
			position: 'top',
			message: '浏览器不支持WebSocket',
		});
		fromData.remark = '';
		return;
	}
	changeBtnStatus('CONNECTING');
	iatWS.onopen = () => {
		// 开始录音
		recorder.start({
			sampleRate: 16000,
			frameSize: 1280,
		});

		var params = {
			common: {
				app_id: APPID,
			},
			business: {
				language: 'zh_cn',
				domain: 'iat',
				accent: 'mandarin',
				vad_eos: 3000,
				dwa: 'wpgs',
			},
			data: {
				status: 0,
				format: 'audio/L16;rate=16000',
				encoding: 'raw',
			},
		};
		iatWS.send(JSON.stringify(params));
	};

	iatWS.onmessage = (e) => {
		renderResult(e.data);
	};
	iatWS.onerror = (e) => {
		console.error(e);
		recorder.stop();
		changeBtnStatus('CLOSED');
	};
	iatWS.onclose = (e) => {
		console.error(e);
		recorder.stop();
		changeBtnStatus('CLOSED');
	};
};

const renderResult = (resultData) => {
	// 识别结束
	let jsonData = JSON.parse(resultData);
	if (jsonData.data && jsonData.data.result) {
		let data = jsonData.data.result;
		let str = '';
		let ws = data.ws;
		for (let i = 0; i < ws.length; i++) {
			str = str + ws[i].cw[0].w;
		}
		if (data.pgs) {
			if (data.pgs === 'apd') {
				// 将resultTextTemp同步给resultText
				resultText = resultTextTemp;
			}
			// 将结果存储在resultTextTemp中
			resultTextTemp = resultText + str;
		} else {
			resultText = resultText + str;
		}
		// 第一次识别
		if (jsonData.data.status === 0 && formData.remark) {
			resultTextTemp = formData.remark + resultTextTemp;
			resultText = formData.remark + resultText;
		}
		formData.remark = resultTextTemp || resultText || '';
	}
	if (jsonData.code === 0 && jsonData.data.status === 2) {
		iatWS.close();
	}
	if (jsonData.code !== 0) {
		iatWS.close();
		console.error(jsonData);
	}
};

const getWebSocketUrl = () => {
	// 请求地址根据语种不同变化
	var url = 'wss://iat-api.xfyun.cn/v2/iat';
	var host = location.host;
	var apiKey = API_KEY;
	var apiSecret = API_SECRET;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/iat HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};

const changeBtnStatus = (status) => {
	btnStatus = status;
};
const toBase64 = (buffer) => {
	var binary = '';
	var bytes = new Uint8Array(buffer);
	var len = bytes.byteLength;
	for (var i = 0; i < len; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	return window.btoa(binary);
};
recorder.onStart = () => {
	changeBtnStatus('OPEN');
};

recorder.onFrameRecorded = ({ isLastFrame, frameBuffer }) => {
	if (iatWS.readyState === iatWS.OPEN) {
		iatWS.send(
			JSON.stringify({
				data: {
					status: isLastFrame ? 2 : 1,
					format: 'audio/L16;rate=16000',
					encoding: 'raw',
					audio: toBase64(frameBuffer),
				},
			})
		);
		if (isLastFrame) {
			changeBtnStatus('CLOSING');
		}
	}
};
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;

	.mc-header {
		width: 100vw;
		display: flex;
		background-color: #ffffff;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow-y: auto;

		&:deep(.van-tab) {
			font-size: 15px;
			color: #6b778c;
		}
		&:deep(.van-tab--active) {
			color: #172b4d;
		}
		.mc-content-cotainer {
			display: flex;
			flex-direction: column;
			padding: 0 15px;
			margin-bottom: 20px;
			.mc-content__voice {
				display: flex;
				flex-direction: column;
				background-color: #ffffff;
				padding: 10px;
				margin-top: 12px;
				border-radius: 5px;
				position: relative;
				.mc-content__voice__close {
					position: absolute;
					top: 10px;
					right: 10px;
					width: 13px;
					height: 13px;
					z-index: 99;
				}
				&:deep(.van-field) {
					padding: 0 15px 12px 0;

					textarea {
						color: #172b4d;
						height: 132px !important;
						overflow-y: auto;
						white-space: pre-wrap;
						line-height: 1.5;
					}
					textarea::placeholder {
						font-size: 11px;
						color: #c3c7d1;
						line-height: 1.5;
					}
				}
				&:deep(.van-cell:after) {
					border-bottom: none;
				}

				.voice-content {
					display: flex;
					justify-content: space-between;
					align-items: center;
					.voice-content__left {
						display: flex;
						align-items: center;
						img {
							width: 16px;
							height: 16px;
							margin-right: 5px;
						}
					}
					&:deep(.van-button) {
						height: 26px;
						width: 60px;
					}
				}
			}

			// 拜访表单
			.mc-content__form {
				display: flex;
				flex-direction: column;

				.form-info {
					background-color: #ffffff;
					padding: 0 15px;
					margin-top: 12px;
					border-radius: 5px;
					&:deep(.van-field) {
						padding: 12px 0;
						display: flex;
						.van-cell__title {
							label {
								color: #172b4d;
								font-weight: 600;
							}
						}
					}
					.required {
						&:deep(.van-field__label::after) {
							content: '*';
							color: #de350b;
							transform: scale(1.2) translate(1px, 0px);
							display: inline-block;
						}
					}
					.inline {
						display: block;
						&:deep(.van-cell__title) {
							margin-bottom: 8px;
						}
						&:deep(.van-field__label) {
							width: 100%;
						}
						&:deep(.van-checkbox) {
							margin-bottom: 8px;
						}
					}

					.inline-product {
						display: block;
						&:deep(.van-cell__title) {
							margin-bottom: 8px;
						}
						&:deep(.van-checkbox) {
							margin-bottom: 8px;
						}
						&:deep(.van-field__label) {
							width: 100%;
						}
						&:deep(.van-field__label::after) {
							content: '*';
							color: #de350b;
							transform: scale(1.2) translate(1px, 0px);
							display: inline-block;
						}
					}
				}

				.form-desc {
					background-color: #ffffff;
					padding: 15px;
					margin-top: 12px;
					border-radius: 5px;
					span {
						font-weight: 600;
						color: #172b4d;
					}
					&:deep(.van-field) {
						background-color: #f4f5f7;
						margin-top: 8px;
						border-radius: 5px;
					}
				}

				// 签字信息
				.mc-content__sign {
					display: flex;
					flex-direction: column;
					background-color: #ffffff;
					padding: 15px;
					margin-top: 12px;
					border-radius: 5px;
					span {
						color: #172b4d;
						font-weight: 600;
						margin-bottom: 10px;
					}

					.sign-content {
						border-radius: 5px;
						background-color: #f4f5f7;
						height: 100px;
						display: flex;
						align-items: center;
						justify-content: center;
						color: #dfe1e6;
						font-size: 40px;
					}
				}

				// 按钮
				.mc-content__btn {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 12px;
					&:deep(.van-button) {
						border-radius: 5px;
						height: 40px;
						width: 169px;
					}

					&:deep(.size-color) {
						color: #0052cc !important;
					}
				}
			}

			.mc-content__suggest {
				display: flex;
				flex-direction: column;
				background-color: #ffffff;
				padding: 15px;
				margin-top: 12px;
				border-radius: 5px;
				.suggest-header {
					display: flex;
					align-items: center;
					justify-content: space-between;
					.suggest-header__left {
						display: flex;
						align-items: center;
						span {
							color: #6b778c;
						}
						& span.active {
							color: #172b4d;
						}
						img {
							width: 27px;
							height: 15px;
							margin-left: 5px;
						}
					}
					.suggest-header__right {
						display: flex;
						align-items: center;
						img {
							width: 16px;
							height: 16px;
							margin-left: 5px;
						}
					}
				}
				&:deep(.van-field) {
					padding: 12px;
					background-color: #f4f5f7;
					border-radius: 5px;
					margin-bottom: 15px;
					margin-top: 15px;
					textarea {
						color: #172b4d;
					}
				}

				.mc-content-list {
					width: 100%;
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					.list-item {
						width: 150px;
						height: 45px;
						border-radius: 5px;
						background-color: #f3f6fc;
						margin-bottom: 8px;
						display: flex;
						align-items: center;
						justify-content: center;
						img {
							width: 16px;
							height: 16px;
							margin-right: 8px;
						}
						span {
							color: #172b4d;
							font-size: 14px;
						}
					}
				}
			}
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}
	.mc-content__wechat {
		height: 100dvh;
	}
	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}

	&:deep(.van-picker__confirm) {
		color: #0052cc;
	}

	&:deep(.visitTimePicker) {
		.title {
			height: initial;
			color: #353639;
			font-weight: bold;
			font-size: 14px;
			padding: 0px 16px 16px;
			span {
				padding-left: 4px;
			}
		}
		.flex_box {
			display: flex;
			.van-picker:nth-child(1) {
				flex: 2;
			}
			.van-picker:nth-child(2) {
				flex: 1;
			}
			.van-picker-column {
				font-size: 15px;
			}
		}
		.operation {
			padding: 16px 16px 24px;
			display: flex;
			border: 1px solid rgba(151, 151, 151, 0.1);
			box-shadow: 0px 2px 20px 0px rgba(95, 95, 95, 0.2);
			background-color: #ffffff;
			div {
				flex: 1;
				text-align: center;
				padding: 10px;
				border-radius: 5px;
				font-size: 15px;
			}
			div:nth-child(1) {
				color: #0052cc;
				border: 1px solid #0052cc;
				margin-right: 11px;
			}
			div:nth-child(2) {
				background-color: #0052cc;
				border: 1px solid #0052cc;
				color: #ffffff;
			}
		}
	}
}
:deep(.popup__voice) {
	padding: 30px 0;
	width: 225px;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	align-items: center;
	.voice-title {
		color: #172b4d;
		font-weight: 500;
		font-size: 18px;
	}
	.voice-desc {
		margin: 15px 0;
		color: #6b778c;
		font-size: 12px;
	}
	.voice-gif {
		margin-top: 9px;
		margin-bottom: 28px;
		width: 150px;
		height: 59px;
		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	.van-button {
		width: 116px;
		height: 40px;
		border-radius: 20px;
	}
}
</style>
