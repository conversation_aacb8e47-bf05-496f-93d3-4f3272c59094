<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 左侧icon -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('chat.dkActivity') }}</div>
			<!-- 右侧icon -->
			<div class="mc-header-icon">
				<img @click="getChatHistory" src="@/assets/img/history.png" />
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 聊天列表 -->
			<div id="aiCon" class="mc-content-list clearfix">
				<template v-for="(item, index) in chatList">
					<template v-if="item.type === 'user'">
						<div :key="item.id" class="mc-content-list-right">
							<!-- 用户内容模版 -->
							<div @click="editorMsg(index, item)" class="mc-content-template">
								<!-- 纯文本 -->
								<p v-html="item.msg"></p>
								<!-- 是否可以修改 -->
								<div class="mc-content-template__edit" v-if="index === chatList.length - 2"></div>
							</div>
						</div>
					</template>
					<template v-else>
						<div :key="item.id" class="mc-content-list-left">
							<div style="display: flex">
								<!-- AI 头像 -->
								<img src="@/assets/img/demo/ai.png" />
								<!-- AI 文本 内容模版 -->
								<div v-if="item.templateType === 'text'" class="mc-content-template">
									<!-- 纯文本 -->
									<ai-text :id="item.id" :is-loading="item.loading" :msg="item.msg"></ai-text>
									<!-- 猜你想问问题列表 -->
									<!-- <div v-if="item.guessList.length > 0" class="mc-content-template__guess">
										<div class="guess-header">
											<span class="guess-header-title">智能推荐</span>
											<span class="guess-header-line"></span>
										</div>
										<div class="guess-list">
											<span @click="qeMoreEv" class="guess-list-item" style="background-color: var(--ac-bg-color)">文章分享</span>
										</div>
									</div> -->
									<!-- 引用 -->
									<div v-if="item.filesList && item.filesList.length > 0" class="mc-content-template__guess">
										<div class="guess-header">
											<span class="guess-header-title">引用</span>
											<span class="guess-header-line"></span>
										</div>
										<div class="guess-list">
											<div v-for="i in item.filesList" :key="i.sourceId" class="qutoe-list-item">
												<img src="@/assets/img/demo/pdf.png" />
												<!-- 内容 -->
												<span class="ell">{{ i.sourceName }}</span>
											</div>
										</div>
									</div>
									<!-- 小按钮组 - 语音播报和复制 -->
									<ai-btns v-if="item.isBtns && !item.loading" :id="item.id" :zeStatus="item.zeStatus" @_ze="zeEv" :voiceStatus="item.voiceStatus" @copyText="copyText" @voiceEv="voiceEv"></ai-btns>
								</div>
								<!-- AI 问题 内容模版 -->
								<div v-else class="mc-content-template">
									<ai-question :itemData="item" @quesEv="quesEv">
										<!-- 插槽 更多 -->
										<span @click="qesMoreEv('全部')" class="mc-content-template__more">更多</span>
									</ai-question>
								</div>
							</div>
						</div>
					</template>
				</template>
			</div>
			<!-- 声明 -->
			<div v-if="inForUse" class="mc-declare">{{ inForUse }}</div>
			<!-- 按钮组 -->
			<div v-if="qtList.length > 0" class="mc-btns">
				<div v-for="(i, index) in qtList" :key="index" @click="qesMoreEv(i)" class="mc-btns-item">
					{{ i }}
				</div>
			</div>
			<!-- 聊天框 -->
			<div ref="chatBox" class="mc-content-chat">
				<ai-textarea :editorText="editorText" :ol="false" :toolsList="[]" :sMenu="false" @_sendMessage="sendMessage" @_changeEdit="changeEdit"></ai-textarea>
			</div>
		</div>

		<!-- 更多问题 弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="quesMoreVis" position="bottom" :style="{ height: '90%' }">
			<ai-more-questions @close="quesMoreVis = false" @activeQes="quesEv" :title="qesTitle"></ai-more-questions>
		</van-popup>

		<!-- 文章列表弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="queMoreVis" position="bottom" :style="{ height: '90%' }">
			<ai-more-articles :title="qeTitle" @_activeArticle="activeArticle" @_articleDetail="articleDetail" @close="queMoreVis = false"></ai-more-articles>
		</van-popup>

		<!-- 文章推送 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="pushArticleVis" position="bottom" :style="{ height: '90%' }">
			<ai-push-articles @close="pushArticleVis = false"></ai-push-articles>
		</van-popup>

		<!-- 文章详情弹框 -->
		<van-popup :safe-area-inset-bottom="true" v-model:show="articleDetailVis" position="bottom" :style="{ height: '90%' }">
			<ai-article-detail :sourceUrl="sourceUrl" :isToken="isToken" :title="articleTitle" @close="articleDetailVis = false"></ai-article-detail>
		</van-popup>
		<!-- 历史会话 -->
		<chat-history :historyList="historyList" ref="cHs" @_openChat="openHistoryChat"></chat-history>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import { getToken } from '@/utils/auth';
import useUserStore from '@/store/modules/user';
import ClipboardJS from 'clipboard';
import CryptoJS from 'crypto-js';
import AudioPlayer from '@/utils/ap';
import { uuid, ruleAllList, getRandomString, getQueryString } from '@/utils/index';
import { getQuestionType } from '@/api/user';
const audioPlayer = new AudioPlayer();
import axios from 'axios';
import { nextTick } from 'vue';
import { getAgentListApi } from '@/api/agent-tools';
import { useClickAway } from '@vant/use';
import usefilterStore from '@/store/modules/filter';
const filterStore = usefilterStore();
// 返回
const route = useRoute();
const router = useRouter();
const inForUse = ref('');
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
// 接口地址
const interfaceUrl = enterStore.enterInfo.interfaceAddress;

// 历史记录
const cHs = ref(null);
const getChatHistory = () => {
	cHs.value.showLeft = true;
};

watch(
	() => route.query.chatId,
	(newVal, oldVal) => {
		if (newVal && !oldVal) {
			editorText.value = '';
			chatId = newVal;
			// 获取历史记录
			getHistoryList();
		}
		// 改变chatId
		if (oldVal && newVal && oldVal !== newVal) {
			editorText.value = '';
			// 清空聊天记录
			chatList.value = [];
			chatId = newVal;
			// 获取历史记录
			getHistoryList();
		}
	}
);

//查询左侧会话记录
const historyList = ref([]);
const getConversChatList = async () => {
	// 获取fastGpt历史会话记录
	try {
		let allList = await getOtherChatList();
		allList = allList.reduce((acc, cur) => {
			const index = acc.findIndex((item) => item.updateTime.split('T')[0] === cur.updateTime.split('T')[0]);
			if (index !== -1) {
				acc[index].children.push(cur);
			} else {
				acc.push({
					updateTime: cur.updateTime,
					name: cur.updateTime.split('T')[0],
					children: [cur],
				});
			}
			return acc;
		}, []);
		// 把allList根据updateTime做个降序
		allList.sort((a, b) => {
			return new Date(b.updateTime) - new Date(a.updateTime);
		});
		for (const item of allList) {
			item.children.sort((a, b) => {
				return new Date(b.updateTime) - new Date(a.updateTime);
			});
		}
		if (allList.length > 0) {
			allList = ruleAllList(allList, language.value);
		}
		historyList.value = allList;
	} catch (error) {
		console.log(error);
		showToast({
			position: 'top',
			zIndex: 8888,
			message: '会话记录获取失败',
		});
	}
};
// 其他模块的会话列表
const getOtherChatList = () => {
	return new Promise((resolve, reject) => {
		const json = {
			username: userStore.userInfo.username,
			skip: 0,
			limit: 70,
			agent_id: '006',
		};
		axios({
			url: interfaceUrl + '/API-OPENGPT/api/session/history/user/page',
			method: 'get',
			params: json,
			headers: {
				Authorization: `Bearer ${getToken()}`,
			},
		})
			.then((res) => {
				for (const cur of res.data.data) {
					cur.chatType = 'dkActivity';
					cur.updateTime = cur.created_time.substring(0, 19);
					cur.chatId = cur.conversation_id;
					cur.title = cur.query;
				}
				resolve(res.data.data);
			})
			.catch(() => {
				reject('other会话出错了');
			});
	});
};
// 打开历史记录会话
const openHistoryChat = (item) => {
	if (item.chatId === chatId) return;
	if (isLoading.value) {
		showToast({
			message: '正在回答，请稍后...',
			position: 'top',
		});
		return;
	}
	chatList.value = [];
	chatId = item.chatId;
	router.replace({
		name: item.chatType,
		query: {
			chatId: item.chatId,
			isTabBar: route.query.isTabBar || '',
		},
	});
};

const goBack = () => {
	router.go(-1);
};

// 语言环境
const language = computed(() => {
	return filterStore.language === 'zh' ? '中文' : '英文';
});

const userStore = useUserStore();
const quesMoreVis = ref(false);
const qesTitle = ref('');
const qesMoreEv = (i) => {
	quesMoreVis.value = true;
	qesTitle.value = i;
};

const isToken = ref(false);
const articleTitle = ref('');
const queMoreVis = ref(false);
const qeTitle = ref('');
const qeMoreEv = () => {
	queMoreVis.value = true;
	qeTitle.value = '文章分享';
};

const pushArticleVis = ref(false);
const activeArticle = () => {
	pushArticleVis.value = true;
};

// 文章详情
const articleDetailVis = ref(false);
const sourceUrl = ref('');
const articleDetail = (item) => {
	isToken.value = true;
	articleTitle.value = '';
	sourceUrl.value = item;
	articleDetailVis.value = true;
};

// 会话ID
let chatId = '';
// 赞和踩
const zeEv = ({ id, like }) => {
	const j = chatList.value.filter((ele) => ele.id === id)[0];
	const json = {
		username: userStore.userInfo.username,
		message_id: j.dataId,
		feedback_score: '',
		feedback_reason: '',
	};
	if (like === '2') {
		// 踩
		json.feedback_score = 0;
	} else if (like === '1') {
		// 赞
		json.feedback_score = 100;
	} else {
		json.feedback_score = -1;
	}
	j.zeStatus = like;
	axios({
		url: interfaceUrl + '/API-OPENGPT/api/session/history/update_history_feedback',
		method: 'put',
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
		params: json,
	});
};
const resetAudioVoice = () => {
	for (const i of chatList.value) {
		if (i.type === 'user') continue;
		if (i.isBtns === false) continue;
		if (i.voiceStatus === true) continue;
		i.voiceStatus = true;
	}
};

// 播放停止把voiceStatus改位true
audioPlayer.onStop = () => {
	resetAudioVoice();
};

const isLoading = ref(false);
const chatList = ref([]);

// 发送的消息是否是二次编辑的消息
const isEdit = ref(false);
const editorText = ref('');
const editorMsg = (index, item) => {
	if (index === chatList.value.length - 2) {
		// 如果请求正在进行需要终止请求
		if (isLoading.value) {
			controller?.abort();
			chatList.value[chatList.value.length - 1].loading = false;
			chatList.value[chatList.value.length - 1].msg = chatList.value[chatList.value.length - 1].msg + '...';
			isLoading.value = false;
		}
		let msg = item.msg.replace(/<br\s*\/?\s*>/gi, '\n');
		editorText.value = msg + 'jjjjjj' + new Date().getTime();
		isEdit.value = true;
	}
};

// 把fastgpt的会话记录同步到公司库
const asyncChatLog = (chatId, message, response, metaData, qa = '') => {
	return new Promise((resolve) => {
		axios({
			url: interfaceUrl + '/API-OPENGPT/api/session/history/create',
			method: 'post',
			headers: {
				Authorization: `Bearer ${getToken()}`,
				'Content-Type': 'application/json;charset=utf-8',
			},
			data: {
				meta_data: metaData,
				username: userStore.userInfo.username,
				conversation_id: chatId,
				chat_type: 'dkActivity',
				query: message,
				response: response,
				data_temp1: qa,
				agent_id: '006',
			},
		})
			.then((res) => {
				chatList.value[chatList.value.length - 1].dataId = res.data.data.id;
			})
			.finally(() => {
				resolve();
			});
	});
};

const changeEdit = () => {
	isEdit.value = false;
	editorText.value = '';
};

// 文本框失去焦点
const chatBox = ref();
useClickAway(
	chatBox,
	() => {
		if (isEdit.value) {
			editorText.value = '';
		}
		isEdit.value = false;
	},
	{ eventName: 'touchend' }
);

// 发送消息
const sendMessage = (val) => {
	if (isEdit.value) {
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isEdit.value = false;
	}
	if (!isLoading.value) {
		if (val) {
			isLoading.value = true;
			val = val.replace(/\n/g, '<br/>');
			chatList.value.push({
				msg: val,
				dataId: '',
				type: 'user',
				chatId: chatId,
				id: uuid(),
				loading: false,
				zeStatus: '0',
				isGuess: true,
			});
			// 生产一个ai的消息
			chatList.value.push({
				msg: '',
				dataId: '',
				type: 'ai',
				chatId: chatId,
				templateType: 'text',
				id: uuid(),
				loading: true,
				isBtns: true,
				voiceStatus: true,
				zeStatus: '0',
				isGuess: true,
				guessList: [],
				filesList: [],
			});
			nextTick(() => {
				scrollBottom();
			});
			setAiMessage(val);
		}
	}
};
let appId = '';
let appKey = '';
let controller = '';
const setAiMessage = async (val) => {
	controller = new AbortController();
	const { signal } = controller;
	try {
		const message = val || '';
		let radom1 = uuid();
		let radom2 = uuid();
		let res = await fetch(`${enterStore.enterInfo.agentAddress}/api/v1/chat/completions`, {
			signal,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=utf-8',
				Authorization: `Bearer ${appKey}`,
			},
			body: JSON.stringify({
				appId: appId,
				chatId: chatId,
				stream: true,
				detail: true,
				variables: {
					language: language.value,
				},
				messages: [
					{ role: 'user', content: message, dataId: radom1 },
					{ role: 'assistant', content: '', dataId: radom2 },
				],
			}),
		});
		if (!res?.body || !res?.ok) {
			throw new Error('Request Error');
		}
		// 插入 dataId
		chatList.value[chatList.value.length - 2].dataId = radom1;
		chatList.value[chatList.value.length - 1].dataId = radom2;

		const reader = res.body?.getReader();
		const decoder = new TextDecoder('utf-8');
		let buffer = '';
		function processStreamResult(result) {
			const chunk = decoder.decode(result.value, { stream: !result.done });
			buffer += chunk;
			// 逐条解析后端返回数据
			const lines = buffer.split('\n');
			buffer = lines.pop();
			lines.forEach((line) => {
				if (line.trim().length > 0) {
					if (line.indexOf('data:') > -1 && line.split('data:')[1] !== ' [DONE]') {
						const resData = JSON.parse(line.split('data:')[1]);
						// eslint-disable-next-line no-debugger
						console.log(resData);
						if (resData.choices && resData.choices[0].delta.content) {
							const text = resData.choices[0].delta.content;
							chatList.value[chatList.value.length - 1].msg += text;
						} else if (Array.isArray(resData)) {
							const filesList = [];
							if (resData.length > 1 && resData[1].quoteList) {
								for (const k of resData[1].quoteList) {
									if (k.sourceId && !filesList.some((ele) => ele.sourceId === k.sourceId)) {
										filesList.push({
											sourceId: k.sourceId,
											sourceName: k.sourceName,
										});
									}
								}
							}
							chatList.value[chatList.value.length - 1].filesList = filesList;
						}
						nextTick(() => {
							scrollBottom();
						});
					}
				}
			});
			if (!result.done) {
				return reader.read().then(processStreamResult);
			} else {
				isLoading.value = false;
				chatList.value[chatList.value.length - 1].loading = false;
				// 请求完毕后判断url是否存在chatId
				if (!getQueryString('chatId')) {
					asyncChatLog(chatId, chatList.value[chatList.value.length - 2].msg, chatList.value[chatList.value.length - 1].msg, chatList.value[chatList.value.length - 1], '').then(() => {
						// 获取历史会话记录
						getConversChatList();
						const params = new URLSearchParams(window.location.search);
						params.set('chatId', chatId); // 添加或更新 query 参数
						const newUrl = window.location.pathname + '?' + params.toString();
						history.replaceState(null, '', newUrl);
					});
				} else {
					asyncChatLog(chatId, chatList.value[chatList.value.length - 2].msg, chatList.value[chatList.value.length - 1].msg, chatList.value[chatList.value.length - 1], '');
				}
			}
		}
		return reader.read().then(processStreamResult);
	} catch (e) {
		errorFun(e);
	}
};

// 请求或者解析失败提示并删除消息
const errorFun = (e) => {
	if (e.message !== 'signal is aborted without reason') {
		showToast({
			position: 'top',
			zIndex: 8888,
			message: '请求失败,请重试',
		});
		// 清除chatList最后2位
		chatList.value.splice(-2);
		isLoading.value = false;
	}
};

const scrollBottom = () => {
	document.querySelector('#aiCon').scrollTo({
		top: document.querySelector('#aiCon').scrollHeight,
		behavior: 'smooth',
	});
};

const APPID = '56d35e48';
const API_SECRET = 'NDFkMmEwYTQ0ODA5ZGM4Y2JiNTM0NTVk';
const API_KEY = '93d27369dabb708f1b9a826d03a49200';
let ttsWS = null;
const voiceEv = async (val) => {
	// chatList里所有的voiceStatus改位true
	resetAudioVoice();
	// 通过val.id获取chatList的相等元素
	const index = chatList.value.findIndex((item) => item.id === val.id);
	chatList.value[index].voiceStatus = val.vType === 'play' ? false : true;

	ttsWS?.close();
	audioPlayer.reset();
	if (val.vType === 'play') {
		// 开始语音播报
		const url = getWebSocketUrl(API_KEY, API_SECRET);
		if ('WebSocket' in window) {
			ttsWS = new WebSocket(url);
		} else if ('MozWebSocket' in window) {
			ttsWS = new MozWebSocket(url);
		} else {
			showToast({
				position: 'top',
				message: '浏览器不支持WebSocket',
			});
			return;
		}
		// 发送消息
		ttsWS.onopen = () => {
			audioPlayer.start({
				autoPlay: true,
				sampleRate: 16000,
				resumePlayDuration: 1000,
			});
			let text = document.getElementById(val.id).innerText;
			let tte = 'UTF8';
			let params = {
				common: {
					app_id: APPID,
				},
				business: {
					aue: 'raw',
					auf: 'audio/L16;rate=16000',
					vcn: 'x4_lingfeichen_assist', //发音人
					speed: 50, // 语速
					volume: 50, // 音量
					pitch: 50, // 音色
					bgs: 0,
					tte,
				},
				data: {
					status: 2,
					text: encodeText(text, tte),
				},
			};
			ttsWS.send(JSON.stringify(params));
		};

		ttsWS.onmessage = (e) => {
			let jsonData = JSON.parse(e.data);
			// 合成失败
			if (jsonData.code !== 0) {
				showToast({
					position: 'top',
					message: '语音合成失败, 请稍后再试',
				});
				return;
			}
			audioPlayer.postMessage({
				type: 'base64',
				data: jsonData.data.audio,
				isLastData: jsonData.data.status === 2,
			});
			if (jsonData.code === 0 && jsonData.data.status === 2) {
				ttsWS.close();
			}
		};

		ttsWS.onerror = (e) => {
			console.error(e);
		};
		ttsWS.onclose = () => {};
	}
};

const encodeText = (text, type) => {
	if (type === 'unicode') {
		let buf = new ArrayBuffer(text.length * 4);
		let bufView = new Uint16Array(buf);
		for (let i = 0, strlen = text.length; i < strlen; i++) {
			bufView[i] = text.charCodeAt(i);
		}
		let binary = '';
		let bytes = new Uint8Array(buf);
		let len = bytes.byteLength;
		for (let i = 0; i < len; i++) {
			binary += String.fromCharCode(bytes[i]);
		}
		return window.btoa(binary);
	} else {
		return Base64.encode(text);
	}
};

const getWebSocketUrl = (apiKey, apiSecret) => {
	var url = 'wss://tts-api.xfyun.cn/v2/tts';
	var host = location.host;
	var date = new Date().toGMTString();
	var algorithm = 'hmac-sha256';
	var headers = 'host date request-line';
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
};

const getHistoryList = () => {
	// 初始化会话的聊天记录
	let cl = [];
	const json = {
		username: userStore.userInfo.username,
		chat_type: 'dkActivity',
		conversation_id: chatId,
		limit: 10,
		skip: 0,
	};
	axios({
		url: interfaceUrl + '/API-OPENGPT/api/session/history/find_by_conversation_id',
		method: 'get',
		params: json,
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
	})
		.then((res) => {
			if (res.data.code === 200 && res.data.data.length > 0) {
				for (const item of res.data.data) {
					cl.push({
						msg: item.query,
						type: 'user',
						dataId: '',
						chatId,
						templateType: 'text',
						id: uuid(),
						loading: false,
						isBtns: false,
						voiceStatus: false,
					});
					cl.push({
						msg: item.response,
						type: 'ai',
						dataId: item.id,
						chatId,
						templateType: 'text',
						id: uuid(),
						loading: false,
						isBtns: true,
						voiceStatus: true,
						isGuess: false,
						guessList: [],
						filesList: item.meta_data.filesList,
						reGen: item.meta_data.reGen,
						zeStatus: item.feedback_score === -1 ? '0' : item.feedback_score === 100 ? '1' : '2',
					});
				}
			}
		})
		.finally(() => {
			chatList.value = chatList.value.concat(cl);
			chatList.value.unshift({
				msg: '您好，我是大咖助手，您有什么需要了解的？',
				type: 'ai',
				dataId: uuid(),
				templateType: 'text',
				id: uuid(),
				chatId: chatId,
				loading: false,
				isBtns: false,
				voiceStatus: false,
				zeStatus: '0',
				isGuess: false,
				guessList: [],
				filesList: [],
			});
			nextTick(() => {
				document.querySelector('#aiCon').scrollTo({
					top: document.querySelector('#aiCon').scrollHeight,
				});
			});
		});
};

onMounted(() => {
	getAgentListApi().then((res) => {
		const agentList = res.result.content || [];
		for (const ele of agentList) {
			if (ele.attributes.url.indexOf('dkActivity') > -1) {
				appId = ele.attributes.appID;
				appKey = ele.attributes.appKey;
				inForUse.value = ele.attributes?.inForUse || '';
				break;
			}
		}
		init();
		getConversChatList();
	});

	// 给body设置overflow:hidden,position:relative
	document.body.style.overflow = 'hidden';
	document.body.style.position = 'relative';
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.body.removeAttribute('style');
	if (isLoading.value) {
		controller?.abort();
	}
	// 关闭语音
	resetAudioVoice();
	ttsWS?.close();
	audioPlayer.reset();
});

const init = () => {
	chatId = route.query.chatId || '';
	if (chatId) {
		getHistoryList();
	} else {
		chatId = getRandomString();
		chatList.value.push({
			msg: '您好，我是大咖助手，您有什么需要了解的？',
			type: 'ai',
			dataId: uuid(),
			templateType: 'text',
			id: uuid(),
			chatId: chatId,
			loading: false,
			isBtns: false,
			voiceStatus: false,
			zeStatus: '0',
			isGuess: false,
			guessList: [],
			filesList: [],
		});
		nextTick(() => {
			document.querySelector('#aiCon').scrollTo({
				top: document.querySelector('#aiCon').scrollHeight,
			});
		});
	}
};

// 问题类型
const qtList = ref([]);
const qesType = () => {
	getQuestionType({ product: '' }).then((res) => {
		if (res.result.length > 0) {
			qtList.value = ['全部'].concat(res.result);
		}
	});
};

// 点击问题生成消息
const quesEv = (item) => {
	if (!isLoading.value) {
		isLoading.value = true;
		chatList.value.push({
			msg: item.msg,
			dataId: '',
			type: 'user',
			chatId: chatId,
			id: uuid(),
			loading: false,
			zeStatus: '0',
			isGuess: true,
		});
		// 生产一个ai的消息
		chatList.value.push({
			msg: '',
			dataId: '',
			type: 'ai',
			templateType: 'text',
			chatId: chatId,
			id: uuid(),
			loading: true,
			isBtns: true,
			voiceStatus: true,
			zeStatus: '0',
			isGuess: true,
			guessList: [],
			filesList: [],
		});
		nextTick(() => {
			scrollBottom();
		});
		setAiMessage(item.msg);
	}
};

const copyText = (id) => {
	const clipboard = new ClipboardJS('.copy-btn', {
		target: function () {
			return document.getElementById(id);
		},
	});

	clipboard.on('success', () => {
		showToast({
			position: 'top',
			message: '复制成功',
		});
		clipboard.destroy();
	});
};
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--ac-border-color);

		.mc-header-icon {
			width: 32px;
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		.mc-content-list {
			flex: 1 0 0px;
			height: 0px;
			padding: 0 6px;
			padding-top: 12px;
			overflow-y: auto;
			.mc-content-list-left {
				display: flex;
				flex-direction: column;
				width: 100%;
				margin-bottom: 12px;
				.mc-content-template {
					border-top-left-radius: 8px;
					border-bottom-right-radius: 8px;
					border-top-right-radius: 8px;
				}
				img {
					width: 26px;
					height: 26px;
					margin-right: 6px;
					margin-top: 6px;
				}
			}

			.mc-content-list-right {
				display: flex;
				width: 95%;
				float: right;
				margin-bottom: 12px;
				justify-content: flex-end;
				.mc-content-template {
					border-top-right-radius: 8px;
					border-bottom-left-radius: 8px;
					border-top-left-radius: 8px;
					color: var(--ac-bg-color--fff);
					background-color: var(--ac-bg-active-color);
					.mc-content-template__edit {
						border-top: solid 1px rgba(223, 225, 230, 0.3);
						margin-top: 2px;
					}
				}
			}

			.mc-content-template {
				max-width: calc(95% - 32px);
				background-color: var(--ac-bg-color--fff);
				padding: 12px;
			}

			// 猜你想问
			.mc-content-template__guess {
				padding-top: 10px;
				.guess-header {
					display: flex;
					align-items: center;
					margin-bottom: 3px;
					.guess-header-title {
						font-size: 12px;
						color: var(--ac-colors-myGray-500);
						margin-right: 6px;
					}
					.guess-header-line {
						flex: 1;
						height: 1px;
						background-color: rgba(221, 221, 221, 0.35);
					}
				}
				.guess-list {
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					.guess-list-item {
						font-size: 12px;
						display: flex;
						border-radius: 5px;
						padding: 3px 6px;
						justify-content: center;
						border: 1px solid rgb(239, 240, 241);
						align-items: center;
						margin-bottom: 4px;
						margin-right: 4px;
					}

					.qutoe-list-item {
						width: 100%;
						justify-content: flex-start;
						display: flex;
						align-items: center;
						margin-bottom: 4px;
						border-radius: 5px;
						padding: 8px;
						position: relative;
						background-color: var(--ac-bg-color);
						img {
							width: 12px;
							height: 14px;
							object-fit: cover;
							margin: 0;
							margin-right: 8px;
						}

						span {
							font-size: 12px;
						}
					}
				}
			}

			.mc-content-template__more {
				font-size: 12px;
				color: var(--ac-bg-active-color);
			}
		}
		// 声明
		.mc-declare {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 6px 0px 3px 0px;
			font-size: 12px;
			color: var(--ac-colors-myGray-500);
		}
		// 按钮组
		.mc-btns {
			width: 100%;
			margin: 0px auto;
			padding: 4px 6px;
			overflow-x: auto;
			display: flex;
			white-space: nowrap;
			.mc-btns-item {
				margin-right: 6px;
				border: 1px solid var(--ac-border-color);
				padding: 4px 10px;
				border-radius: 20px;
				font-size: 12px;
				color: var(--ac-font-color);
			}
		}

		.mc-content-chat {
			margin: 0px auto;
			width: 100%;
			padding-inline: 0px;
			padding-top: 8px;
			padding-bottom: 6px;
			min-height: 64px;
			position: relative;
			display: flex;
			align-items: flex-start;
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-popup--bottom) {
		border-top-right-radius: 20px;
		border-top-left-radius: 20px;
		padding: 16px 0;
	}
}
</style>
