<template>
	<div class="mc">
		<!-- 顶部 -->
		<div class="mc-header">
			<!-- 历史记录 -->
			<div @click="goBack" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
			<!-- 中间名称 -->
			<div class="mc-header-name">{{ $t('summary.workSummary') }}</div>
			<!-- 右侧icon -->
			<div style="visibility: hidden" class="mc-header-icon">
				<van-icon :size="20" name="arrow-left" />
			</div>
		</div>
		<!-- 内容 -->
		<div class="mc-content" :class="route?.query?.isTabBar === 'false' ? 'mc-content__app' : 'mc-content__h5'">
			<!-- 任务类型切换 -->
			<van-tabs v-if="orgList.length > 0" :swipe-threshold="3" color="#0052cc" @change="tabChange" v-model:active="active">
				<van-tab :name="$t('summary.mySummary')" :title="$t('summary.mySummary')"></van-tab>
				<van-tab :name="$t('summary.teamSummary')" :title="$t('summary.teamSummary')"></van-tab>
			</van-tabs>
			<!-- 人员组件 -->
			<div v-if="active == $t('summary.teamSummary')" class="content-item">
				<div @click="openOrg" class="q-width-100 content-item__personal">
					<span class="item-title ell">{{ depInfo.text }}</span>
					<!-- icon -->
					<svg-icon class="icon" icon-class="down2"></svg-icon>
				</div>
			</div>

			<!-- 日总结 -->
			<div class="content-item">
				<van-collapse v-model="dsum">
					<van-collapse-item name="1">
						<template #title>
							<div>
								<img src="@/assets/img/one.png" />
								<span>{{ $t('summary.dailySummary') }}</span>
							</div>
						</template>
						<div class="item-container">
							<ai-text :msg="dailyText"></ai-text>
						</div>
					</van-collapse-item>
				</van-collapse>
			</div>
			<!-- 周总结 -->
			<div class="content-item">
				<van-collapse v-model="wsum">
					<van-collapse-item name="1">
						<template #title>
							<div>
								<img src="@/assets/img/two.png" />
								<span>{{ $t('summary.weeklySummary') }}</span>
							</div>
						</template>
						<div class="item-container">
							<ai-text :msg="weeklyText"></ai-text>
						</div>
					</van-collapse-item>
				</van-collapse>
			</div>
			<!-- 月总结 -->
			<div class="content-item content-item--mb">
				<van-collapse v-model="msum">
					<van-collapse-item name="1">
						<template #title>
							<div>
								<img src="@/assets/img/three.png" />
								<span>{{ $t('summary.monthlySummary') }}</span>
							</div>
						</template>
						<div class="item-container">
							<ai-text :msg="monthlyText"></ai-text>
						</div>
					</van-collapse-item>
				</van-collapse>
			</div>
		</div>
		<!-- 部门架构 -->
		<van-popup v-model:show="orgZ" round position="bottom">
			<van-picker :columns-field-names="customFieldName" :columns="orgList" @cancel="orgZ = false" @confirm="onConfirm" />
		</van-popup>
	</div>
</template>
<script setup>
import axios from 'axios';
import { getOrgByUser } from '@/api/login';
import { getToken } from '@/utils/auth';
const router = useRouter();
const route = useRoute();
import useUserStore from '@/store/modules/user';
import useEnterStore from '@/store/modules/enterprise';
const enterStore = useEnterStore();
// 接口地址
const interfaceUrl = enterStore.enterInfo.interfaceAddress;
const userStore = useUserStore();
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
// 返回&&取消
const goBack = () => {
	router.go(-1);
};
const customFieldName = reactive({
	text: 'firstName',
	value: 'username',
});
const depInfo = reactive({
	text: '',
	value: '',
});
// 组织列表
const orgList = ref([]);
const orgZ = ref(false);
const openOrg = () => {
	orgZ.value = true;
};

const onConfirm = ({ selectedOptions }) => {
	depInfo.text = selectedOptions[0].firstName;
	depInfo.value = selectedOptions[0].username;
	orgZ.value = false;
	dailyReportSummary(depInfo.value);
	weeklyReportSummary(depInfo.value);
	monthlyReportSummary(depInfo.value);
};
// 日总结
const dsum = ref(['1']);
const dailyText = ref('');
const dailyReportSummary = async (id) => {
	const query = {
		user_id: id,
		type: 'person',
		frequency: 'daily',
	};
	try {
		const [data] = await handelRequest(query);
		if (data && data.summary) {
			dailyText.value = data.summary;
		} else {
			dailyText.value = t('summary.noDaylySummary');
		}
	} catch (e) {
		dailyText.value = t('summary.failSummary');
	}
};
// 周总结
const wsum = ref([]);
const weeklyText = ref('');
const weeklyReportSummary = async (id) => {
	const query = {
		user_id: id,
		type: 'person',
		frequency: 'weekly',
	};
	try {
		const [data] = await handelRequest(query);
		if (data && data.summary) {
			weeklyText.value = data.summary;
		} else {
			weeklyText.value = t('summary.noWeeklySummary');
		}
	} catch (e) {
		weeklyText.value = t('summary.failSummary');
	}
};
// 月总结
const msum = ref([]);
const monthlyText = ref('');
const monthlyReportSummary = async (id) => {
	const query = {
		user_id: id,
		type: 'person',
		frequency: 'monthly',
	};
	try {
		const [data] = await handelRequest(query);
		if (data && data.summary) {
			monthlyText.value = data.summary;
		} else {
			monthlyText.value = t('summary.noMonthlySummary');
		}
	} catch (e) {
		monthlyText.value = t('summary.failSummary');
	}
};

// 请求体
const handelRequest = (data) => {
	return new Promise((resolve, reject) => {
		axios({
			url: interfaceUrl + '/API-GPT/job_summary/get_job_summary',
			method: 'post',
			headers: {
				Authorization: `Bearer ${getToken()}`,
				'Content-Type': 'application/json;charset=utf-8',
			},
			params: data,
		})
			.then((res) => {
				resolve(res.data.data);
			})
			.catch(() => {
				reject();
			});
	});
};

// 页签切换
const tabChange = (name) => {
	if (name === t('summary.mySummary')) {
		init();
	} else {
		// 团队总结
		if (!depInfo.value) {
			depInfo.value = orgList.value[0].username;
			depInfo.text = orgList.value[0].firstName;
		}
		dailyReportSummary(depInfo.value);
		weeklyReportSummary(depInfo.value);
		monthlyReportSummary(depInfo.value);
	}
};
const init = () => {
	const userId = userStore.userInfo.username;
	dailyReportSummary(userId);
	weeklyReportSummary(userId);
	monthlyReportSummary(userId);
};

const getInterOrgByUser = () => {
	const id = userStore.userInfo.groups[0].id;
	return new Promise((resolve) => {
		getOrgByUser(id)
			.then((res) => {
				let arr = res.result.filter((ele) => ele.username !== userStore.userInfo.username);
				resolve(arr);
			})
			.catch(() => {
				resolve([]);
			});
	});
};

onMounted(async () => {
	document.querySelector('body').style.backgroundColor = '#f4f5f7';
	orgList.value = await getInterOrgByUser();
	init();
});

onBeforeUnmount(() => {
	// 删除body的属性
	document.querySelector('body').removeAttribute('style');
});
const active = ref(t('summary.mySummary'));
</script>
<style lang="scss" scoped>
.mc {
	position: relative;
	overflow: hidden auto;
	width: 100vw;
	height: 100%;
	color: #172b4d;
	.mc-header {
		width: 100vw;
		display: flex;
		height: 44px;
		padding: 0 6px;
		align-items: center;
		justify-content: space-between;
		background-color: #ffffff;
		border-bottom: 1px solid var(--ac-border-color);
		.mc-header-icon {
			height: 32px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #172b4d;
			font-weight: 600;
			img {
				width: 15px;
			}
		}

		.mc-header-name {
			font-size: 16px;
			font-weight: bolder;
			color: var(--ac-font-color);
		}
	}

	.mc-content {
		overflow-y: auto;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		&:deep(.van-tab) {
			font-size: 15px;
			color: #6b778c;
		}
		&:deep(.van-tab--active) {
			color: #172b4d;
		}

		.content-item {
			background-color: #ffffff;
			border-radius: 6px;
			margin: 8px 15px 0;
			overflow: hidden;

			.content-item__personal {
				height: 32px;
				line-height: 32px;
				text-align: center;
				background-color: #dfe1e6;
				position: relative;
				.item-title {
					width: 85%;
					display: inline-block;
				}
				svg {
					position: absolute;
					right: 10px;
					color: #fff;
					font-size: 12px;
					top: 10px;
				}
			}

			.item-container {
				background-color: #f4f5f7;
				padding: 8px;
				border-radius: 6px;
				display: flex;
				flex-direction: column;

				.item-container__content {
					color: #172b4d;
					font-size: 12px;
				}
			}

			&:deep(.van-cell__title) {
				div {
					display: flex;
					align-items: center;
				}

				img {
					width: 20px;
					height: 20px;
					margin-right: 8px;
				}
				span {
					color: #172b4d;
					font-size: 14px;
				}
			}
		}

		.content-item--mb {
			margin-bottom: 20px;
		}
	}

	.mc-content__h5 {
		height: calc(100dvh - 44px);
	}

	.mc-content__app {
		height: calc(100vh - 44px);
	}

	&:deep(.van-picker__confirm) {
		color: #0052cc;
	}
}
</style>
