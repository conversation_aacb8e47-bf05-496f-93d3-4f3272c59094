<template>
	<div class="sales-achievement-status">
		<div class="card-echart">
			<div class="ym-month">
				<span>{{ '2024-02' }}</span>
			</div>
			<div class="ym-quarter">
				<span>{{ '2024-01' }} 至 {{ '2024-03' }}</span>
			</div>
			<div class="sales-achievement-status-echarts" :id="`sales-achievement-status-echarts-${echartsId}`"></div>
			<div class="value1">
				<span>0</span>
				<span>{{ chartData.month && formatNumber(chartData.month.target) }}</span>
			</div>
			<div class="value2">
				<span>0</span>
				<span>{{ chartData.quarter && formatNumber(chartData.quarter.target) }}</span>
			</div>
			<div class="card-echart-detail">
				<div class="card-echart-detail-item">
					<div class="other">
						月度销售：<span>{{ chartData.month && formatNumber(chartData.month.sale) }}</span>
					</div>
					<div class="other">
						月度达成：<span>{{ chartData.month && new Decimal(chartData.month?.ach).toDecimalPlaces(0) + '%' }}</span>
					</div>
					<div class="other">
						月度同比：<span>{{ chartData.month && new Decimal(chartData.month?.yoy).toDecimalPlaces(0) + '%' }}</span>
						<span v-if="chartData.month?.yoy > 0"><svg-icon class="icon" icon-class="up"></svg-icon></span>
						<span v-else><svg-icon class="icon" icon-class="down"></svg-icon></span>
					</div>
					<div class="other">
						月度环比：<span>{{ chartData.month && new Decimal(chartData.month?.mom).toDecimalPlaces(0) + '%' }}</span>
						<span v-if="chartData.month?.mom > 0"><svg-icon class="icon" icon-class="up"></svg-icon></span>
						<span v-else><svg-icon class="icon" icon-class="down"></svg-icon></span>
					</div>
				</div>
				<div class="card-echart-detail-item">
					<div class="other">
						季度销售：<span>{{ chartData.quarter && formatNumber(chartData.quarter.sale) }}</span>
					</div>
					<div class="other">
						季度达成：<span>{{ chartData.quarter && new Decimal(chartData.quarter?.ach).toDecimalPlaces(0) }}%</span>
					</div>
					<div class="other">
						季度同比：<span>{{ chartData.quarter && new Decimal(chartData.quarter?.yoy).toDecimalPlaces(0) }}%</span>
						<span v-if="chartData.quarter?.yoy > 0"><svg-icon class="icon" icon-class="up"></svg-icon></span>
						<span v-else><svg-icon class="icon" icon-class="down"></svg-icon></span>
					</div>
					<div class="other">
						季度环比：<span>{{ chartData.quarter && new Decimal(chartData.quarter?.mom).toDecimalPlaces(0) }}%</span>
						<span v-if="chartData.quarter?.mom > 0"><svg-icon class="icon" icon-class="up"></svg-icon></span>
						<span v-else><svg-icon class="icon" icon-class="down"></svg-icon></span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { salesAchievementStatus } from '@/api/sales';
import { formatNumber, generateRandomNumber, translateFont, getNamesByIdsInArray } from '@/utils/index';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
let props = defineProps(['info', 'isRecommend']);
let myEcharts = echarts;
let chart; //图实例
let chartData = ref({}); //图数据
let filterStore = usefilterStore();

// 计算当季度开始月份与结束月份
const salesDate = reactive({
	startYm: '',
	endYm: '',
});
let month = 0;

// 筛选条件
const query = reactive({
	brandId: '',
	brandName: '全部产品',
	personId: '',
	personName: '全部人员',
	hospitalId: '',
	hospitalName: '全部医院',
});
//初始化
let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#sales-achievement-status-echarts-${echartsId}`));
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};
//设置
let setOption = () => {
	let option = {
		series: [
			{
				type: 'gauge',
				startAngle: 180,
				endAngle: 0,
				min: 0,
				max: 100,
				splitNumber: 1,
				center: [translateFont(80), translateFont(90)],
				radius: translateFont(66),
				itemStyle: {
					color: '#0074f9',
				},
				progress: {
					show: true,
					roundCap: false,
					width: translateFont(18),
				},
				pointer: {
					show: true,
					icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
					length: '60%',
					width: translateFont(8),
					offsetCenter: [0, '5%'],
				},
				axisLine: {
					roundCap: false,
					lineStyle: {
						width: translateFont(18),
						color: [[1, '#E5EDF9']],
					},
				},
				axisTick: {
					show: false,
				},
				splitLine: {
					length: 0,
				},
				axisLabel: {
					show: false,
				},
				detail: {
					show: false,
				},
				data: [
					{
						value: chartData.value.month.ach,
					},
				],
			},
			{
				type: 'gauge',
				startAngle: 180,
				endAngle: 0,
				min: 0,
				max: 100,
				splitNumber: 1,
				center: [translateFont(250), translateFont(90)],
				radius: translateFont(66),
				itemStyle: {
					color: '#00EFFF',
				},
				progress: {
					show: true,
					roundCap: false,
					width: translateFont(18),
				},
				pointer: {
					show: true,
					icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
					length: '60%',
					width: translateFont(8),
					offsetCenter: [0, '5%'],
				},
				axisLine: {
					roundCap: false,
					lineStyle: {
						width: translateFont(18),
						color: [[1, '#E5EDF9']],
					},
				},
				axisTick: {
					show: false,
				},
				splitLine: {
					length: 0,
				},
				axisLabel: {
					show: false,
				},
				title: {
					show: false,
				},
				detail: {
					show: false,
				},
				data: [
					{
						value: chartData.value.quarter.ach,
					},
				],
			},
		],
	};

	chart.setOption(option);
};
//获取数据
let getData = async () => {
	try {
		let res = await salesAchievementStatus({
			skuCode: query.brandId,
			territoryCode: query.personId,
			hospCode: query.hospitalId,
			nowLocalDateTime: '2024-02-01',
			endLocalDate: '2024-02-29',
		});
		chartData.value = res.result;
		return Promise.resolve();
	} catch (error) {
		return Promise.reject(error);
	}
};

const emit = defineEmits(['focus']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let echartsId = generateRandomNumber();
let positionSeason = computed(() => {
	if (chartData.value.quarter) {
		return new Decimal(chartData.value.quarter?.ach).toDecimalPlaces(0) >= 50 ? 'right' : 'left';
	}
	return 'left';
});
onMounted(async () => {
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.sales-achievement-status {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	&-tip {
		padding: 5px 5px 0;
		font-size: 12px;
		color: #7c88a6;
	}
	.card-echart {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		overflow: hidden;
		padding-top: 7px;
		position: relative;
		.ym-month {
			position: absolute;
			color: var(--pv-default-color);
			left: 24%;
			margin-left: -50px;
			top: 10px;
			width: 100px;
			text-align: center;
			font-size: 12px;
		}
		.ym-quarter {
			position: absolute;
			color: var(--pv-default-color);
			right: 23%;
			margin-right: -60px;
			top: 10px;
			width: 120px;
			text-align: center;
			font-size: 12px;
		}
		.info1 {
			position: absolute;
			left: 45px;
			top: 43px;
			text-align: center;
			line-height: 18px;
			width: 70px;
			div:nth-child(1) {
				font-size: 10px;
				color: #6b778c;
			}
			div:nth-child(2) {
				font-size: 13px;
				font-weight: bold;
			}
		}
		.info2 {
			position: absolute;
			left: 215px;
			top: 43px;
			text-align: center;
			line-height: 18px;
			width: 70px;
			div:nth-child(1) {
				font-size: 10px;
				color: #6b778c;
			}
			div:nth-child(2) {
				font-size: 13px;
				font-weight: bold;
			}
		}
		.value1 {
			font-size: 10px;
			position: absolute;
			width: 140px;
			display: flex;
			justify-content: space-between;
			left: 20px;
		}
		.value2 {
			font-size: 10px;
			position: absolute;
			width: 137px;
			display: flex;
			justify-content: space-between;
			left: 189px;
		}
		&-info {
			font-size: 12px;
			position: relative;
			span:nth-child(1) {
				position: absolute;
				left: 23px;
			}
			span:nth-child(2) {
				position: absolute;
				left: 50px;
			}
			span:nth-child(3) {
				position: absolute;
				left: 268px;
				font-weight: bold;
				width: 55px;
				word-break: break-all;
			}
			.month {
				position: absolute;
				left: 50%;
				top: -75px;
				transform: translateX(-50%);
				text-align: center;
				div:nth-child(1) {
					font-size: 30px;
					font-weight: bold;
				}
				div:nth-child(2) {
					font-size: 12px;
					color: var(--pv-no-active-color);
					top: -30px;
				}
			}
			.season {
				font-size: 12px;
				position: absolute;
				top: -140px;
				text-align: center;
				div:nth-child(1) {
					color: var(--pv-no-active-color);
				}
				&.left {
					left: 20px;
				}
				&.right {
					right: 20px;
				}
			}
		}
		&-detail {
			margin: 18px 11px 8px;
			display: flex;
			gap: 8px;
			&-item {
				flex: 1;
				border: 1px dashed #dfe1e6;
				padding: 3px 6px;
				border-radius: 5px;
				.title {
					font-weight: bold;
				}
				.other {
					color: var(--pv-no-active-color);
					margin-top: 5px;
					font-size: 12px;
					span {
						color: var(--pv-default-color);
					}
					.icon {
						font-size: 15px;
					}
				}
			}
		}
	}
	&-echarts {
		width: 100%;
		height: 90px;
	}
}
</style>
