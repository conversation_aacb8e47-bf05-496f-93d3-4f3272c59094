<template>
	<div class="visit-management">
		<div class="visit-management-echarts-card">
			<div class="visit-management-echarts" :id="`visit-management-echarts-${echartsId}`"></div>
		</div>
	</div>
</template>
<script setup>
import { showToast } from 'vant';
import * as echarts from 'echarts';
import { visitManagement } from '@/api/report';
import { generateRandomNumber, getFilterTime, translateFont } from '@/utils/index';
import Decimal from 'decimal.js';
import usefilterStore from '@/store/modules/filter';
let filterStore = usefilterStore();
let props = defineProps(['info', 'hiddenStar']);

const brandData = ref([]);
for (const item of filterStore.productInfo) {
	brandData.value.push({ id: item.productCode, name: item.productCn });
}

// 筛选条件
const query = reactive({
	startDate: getFilterTime(props.info?.startTime),
	endDate: getFilterTime(props.info?.endTime),
	productId: brandData.value.length > 0 ? brandData.value[0].id : '',
	productName: brandData.value.length > 0 ? brandData.value[0].name : '',
	personId:
		'XPRSD04,ONRSM28,ONDSM145,ONDSM214,ONDSM147,ONDSM103,ONDSM104,ONDSM224,ONMR659,XPMR010,ONMR1421,ONMR1180,ONMR912,ONMR660,ONMR1162,ONMR1422,ONMR1420,ONMR675,ONMR996,ONMR1312,ONMR679,ONMR676,ONMR920,ONMR923,ONMR1459,ONMR1205,ONMR1457,ONMR1166,ONMR1463,ONMR671,ONMR673,ONMR1167,ONMR672,ONMR997,ONMR678,ONMR677,ONMR1496,ONMR1184,ONMR918,ONMR1455,XPMR015,ONMR1204,ONMR1038,ONMR922,ONMR1456',
	personName: '全部人员',
});
let myEcharts = echarts;
let chart;

let initChart = () => {
	chart = myEcharts.init(document.querySelector(`#visit-management-echarts-${echartsId}`));
	setOption();
	window.addEventListener('resize', function () {
		chart.resize();
		setOption();
	});
};

let setOption = () => {
	let ab = visitList.value.ab.map((ele) => {
		return new Decimal(ele.abDoctorAch === 'NaN' ? 0 : ele.abDoctorAch).toString();
	});
	let ach = visitList.value.ach.map((ele) => {
		return new Decimal(ele.visitAvh === 'NaN' ? 0 : ele.visitAvh).toString();
	});
	let option = {
		color: colors,
		grid: [
			{ top: translateFont(70), left: translateFont(10), right: translateFont(10), height: translateFont(65), show: true, borderColor: '#eee' },
			{ top: translateFont(145), left: translateFont(10), right: translateFont(10), bottom: translateFont(20), show: true, borderColor: '#eee' },
		],
		legend: {
			itemGap: translateFont(12),
			itemWidth: translateFont(9),
			itemHeight: translateFont(9),
			left: 'center',
			top: translateFont(10),
			textStyle: {
				color: '#6B778C',
				fontSize: translateFont(12),
			},
			data: [
				{
					name: 'A级客户拜访频次',
					icon: 'rect',
				},
				{
					name: 'B级客户拜访频次',
					icon: 'rect',
				},
				{
					name: 'C级客户拜访频次',
					icon: 'rect',
				},
				{
					name: 'AB客户覆盖率',
				},
				{
					name: '拜访计划执行率',
				},
			],
		},
		xAxis: [
			{
				type: 'category',
				data: ['A', 'B', 'C', 'D', 'E', 'a'],
				gridIndex: 0,
				axisLabel: { show: false },
				axisTick: { show: false },
				splitLine: {
					show: false,
				},
				axisLine: { lineStyle: { color: '#eee' } },
			},
			{ type: 'category', data: visitList.value.axisMonth, gridIndex: 1, axisLabel: { show: true, color: '#6B778C', fontSize: translateFont(9) }, axisTick: { show: false }, splitLine: { show: true, lineStyle: { color: '#eee' }, interval: 0 }, axisLine: { lineStyle: { color: '#eee' } } },
		],
		yAxis: [
			{
				type: 'value',
				gridIndex: 0,
				axisLabel: { show: false },
				axisTick: { show: false },
				splitLine: {
					show: false,
				},
			},
			{ type: 'value', gridIndex: 1, axisLabel: { show: false }, axisTick: { show: false }, splitLine: { show: false } },
		],
		series: [
			{
				name: 'A级客户拜访频次',
				type: 'bar',
				data: abcList.value[0],
				xAxisIndex: 1,
				yAxisIndex: 1,
				barWidth: 11,
				label: {
					show: true,
					position: 'top',
					distance: 1,
					color: '#172B4D',
					fontSize: translateFont(7),
				},
			},
			{
				name: 'B级客户拜访频次',
				type: 'bar',
				data: abcList.value[1],
				xAxisIndex: 1,
				yAxisIndex: 1,
				barWidth: 11,
				label: {
					show: true,
					position: 'top',
					distance: 1,
					color: '#172B4D',
					fontSize: translateFont(7),
				},
			},
			{
				name: 'C级客户拜访频次',
				type: 'bar',
				data: abcList.value[2],
				xAxisIndex: 1,
				yAxisIndex: 1,
				barWidth: 11,
				label: {
					show: true,
					position: 'top',
					distance: 1,
					color: '#172B4D',
					fontSize: translateFont(7),
				},
			},
			{
				name: 'AB客户覆盖率',
				type: 'line',
				lineStyle: {
					width: 1,
				},
				data: ab,
				xAxisIndex: 0,
				yAxisIndex: 0,
				label: {
					show: true,
					distance: 2,
					fontSize: translateFont(7),
					color: '#172B4D',
					formatter: ({ value }) => {
						return value + '%';
					},
				},
				symbolSize: translateFont(5),
			},
			{
				name: '拜访计划执行率',
				type: 'line',
				lineStyle: {
					width: 1,
				},
				data: ach,
				xAxisIndex: 0,
				yAxisIndex: 0,
				label: {
					show: true,
					distance: 2,
					fontSize: translateFont(7),
					color: '#172B4D',
					formatter: ({ value }) => {
						return value + '%';
					},
				},
				symbolSize: translateFont(5),
			},
		],
	};

	chart.setOption(option);
};

let visitList = ref([]);
let abcList = ref([]);
let getData = async () => {
	let res = await visitManagement({
		productCode: 'P106',
		territoryCode: 'ONDSM200,ONMR638',
		startLocalDate: `2023-07-01`,
		endLocalDate: `2023-12-01`,
	});
	visitList.value = res.result;
	abcList.value = [];
	for (const item of res.result.frequencyData) {
		let ach = item.data.map((ele) => {
			return new Decimal(ele.visitAch === 'NaN' ? 0 : ele.visitAch).toString();
		});
		abcList.value.push(ach);
	}
};

const emit = defineEmits(['focus']);
const focus = () => {
	emit('focus', { id: props.info.reportCd, name: props.info.nameCn, isLike: !props.info.isLike });
};
let colors = ['#0052CC', '#0379FF', '#79E1F2', '#FF991F', '#01A2BF'];
let echartsId = generateRandomNumber();
onMounted(async () => {
	await getData();
	initChart();
});
onUnmounted(() => {
	chart && chart.dispose();
});
</script>
<style lang="scss" scoped>
.visit-management {
	background-color: var(--pv-card-bgc);
	margin: 10px 15px;
	border-radius: 5px;
	padding: 8px 8px;
	.top {
		font-size: 15px;
	}

	&-echarts-card {
		background-color: var(--pv-bgc);
		margin-top: 8px;
		border-radius: 5px;
		padding: 0 0px 15px;
	}
	&-echarts {
		width: 100%;
		height: 240px;
	}
}
</style>
