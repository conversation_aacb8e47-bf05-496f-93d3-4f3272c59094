<template>
	<div class="box">
		<div class="title">
			<b>Agent Box</b>
		</div>
		<div class="input">
			<van-field v-model="username" clearable clear-icon="close" left-icon="user-o" :placeholder="$t('login.username')" />
			<van-field v-model="password" clearable clear-icon="close" left-icon="shield-o" :right-icon="isPwd === false ? 'eye-o' : 'closed-eye'" :type="isPwd === false ? 'password' : 'text'" :placeholder="$t('login.password')" @click-right-icon="pwdEv" />
		</div>

		<div class="btn">
			<!-- <van-button type="info" size="small" round @click="isLogin">登录</van-button> -->
			<div class="loginBtn" @click="isLogin">{{ $t('common.login') }}</div>
		</div>
	</div>
</template>
<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import { setToken, setReToken } from '@/utils/auth';
import { showToast, showLoadingToast, closeToast } from 'vant';
import { useRouter, useRoute } from 'vue-router';
import { login } from '@/api/login';
import { jwtDecode } from 'jwt-decode';
import { useI18n } from 'vue-i18n';
import useEnterStore from '@/store/modules/enterprise';
export default {
	name: 'Index',
	setup(_props, _context) {
		const isPwd = ref(false);
		const { t } = useI18n();
		const router = useRouter();
		const route = useRoute();
		const state = reactive({
			username: '',
			password: '',
		});
		const enterStore = useEnterStore();
		const { proxy } = getCurrentInstance();
		const isLogin = () => {
			if (state.username && state.password) {
				const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/login' : enterStore.enterInfo.authAddress;
				showLoadingToast({
					message: t('login.loading'),
					forbidClick: true,
					duration: 0,
				});
				login(
					{
						username: state.username,
						password: state.password,
						grant_type: 'password',
						login_type: 'password',
						client_id: enterStore.enterInfo.clientId,
						// client_secret: 'U9TIjKa6n214mOrbEJEV0xtFwAsHP3Df'
					},
					baseUrl,
					enterStore.enterInfo.id
				)
					.then(
						(response) => {
							console.log(response);
							let decodedToken = jwtDecode(response.access_token);
							localStorage.setItem('pv-email', decodedToken.email);
							setToken(response.access_token, response.expires_in);
							setReToken(response.refresh_token, response.refresh_expires_in);
							// proxy.$store.dispatch('GetUserInfo').then((res) => {
							router.push('/intelligentAgent/home');
							// });
						},
						(err) => {
							console.log(err);
						}
					)
					.finally(() => {
						setTimeout(() => {
							closeToast();
						}, 1000);
					});
			} else {
				showToast(t('login.toast'));
			}
		};
		const pwdEv = () => {
			isPwd.value = !isPwd.value;
		};
		onMounted(async () => {
			// 获取用户名
			const userName = route.query.userName || '';
			// 获取密码
			const pwd = route.query.password || '';

			if (userName && pwd) {
				state.username = userName;
				state.password = pwd;
				isLogin();
			}
		});
		return {
			...toRefs(state),
			isLogin,
			pwdEv,
			isPwd,
		};
	},
};
</script>
<style lang="scss" scoped>
.box {
	height: 100vh;
	background: #fff;
	padding: 0 50px;
	text-align: center;
	overflow: hidden;
	.title {
		margin-top: 150px;
		color: #4f8fcd;
		b {
			font-size: 28px;
		}
	}
	.input {
		margin-top: 70px;
	}
	.van-cell {
		margin-top: 10px;
		padding: 5px 10px;
		border-radius: 30px;
		border: 1px solid #4f8fcd;
		::v-deep .van-icon {
			color: #4f8fcd;
		}
	}
	.van-button {
		margin-top: 70px;
		width: 90px;
		color: #fff;
		background: #4f8fcd;
		border-color: #4f8fcd;
	}
}
.btn {
	width: 100%;
	margin-top: 70px;
	.loginBtn {
		width: 90px;
		background: #4f8fcd;
		margin: 0 auto;
		padding: 5px 20px;
		border-radius: 30px;
		color: #fff;
	}
}
</style>
