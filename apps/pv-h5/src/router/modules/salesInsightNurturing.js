const salesInsightNurturing = [
	{
		path: '/salesInsightNurturing',
		name: 'salesInsightNurturing',
		component: () => import('@/views/salesInsightNurturing/index'),
		meta: {
			title: '销售智能培养',
		},
		children: [],
	},
	{
		path: '/salesInsightNurturing/setting',
		name: 'setting',
		component: () => import('@/views/salesInsightNurturing/setting'),
		meta: {
			title: '销售智能培养',
		},
	},
	{
		path: '/salesInsightNurturing/list',
		name: 'list',
		component: () => import('@/views/salesInsightNurturing/list'),
		meta: {
			title: '销售智能培养',
		},
	},
	{
		path: '/salesInsightNurturing/detail',
		name: 'detail',
		component: () => import('@/views/salesInsightNurturing/detail'),
		meta: {
			title: '销售智能培养',
		},
	},
	{
		path: '/salesInsightNurturing/q-list',
		name: 'qList',
		component: () => import('@/views/salesInsightNurturing/qList'),
		meta: {
			title: '销售智能培养',
		},
	},
	{
		path: '/salesInsightNurturing/q-detail',
		name: 'qDetail',
		component: () => import('@/views/salesInsightNurturing/qDetail'),
		meta: {
			title: '销售智能培养',
		},
	},
	{
		path: '/salesInsightNurturing/c-list',
		name: 'cList',
		component: () => import('@/views/salesInsightNurturing/cList'),
		meta: {
			title: '销售智能培养',
		},
	},
	{
		path: '/salesInsightNurturing/c-detail',
		name: 'cDetail',
		component: () => import('@/views/salesInsightNurturing/cDetail'),
		meta: {
			title: '销售智能培养',
		},
	},
	{
		path: '/salesInsightNurturing/recommend',
		name: 'recommend',
		component: () => import('@/views/salesInsightNurturing/recommend'),
		meta: {
			title: '销售智能培养',
		},
	},
	{
		path: '/salesInsightNurturing/new-recommend',
		name: 'newRecommend',
		component: () => import('@/views/salesInsightNurturing/newRecommend'),
		meta: {
			title: '销售智能培养',
		},
	},
	{
		path: '/salesInsightNurturing/recommend-list',
		name: 'recommendList',
		component: () => import('@/views/salesInsightNurturing/recommendList'),
		meta: {
			title: '销售智能培养',
		},
	},
];
export default salesInsightNurturing;
