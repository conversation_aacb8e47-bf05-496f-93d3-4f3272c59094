import layout from '@/layout/index.vue';
const asyncRouter = [
	// 对话
	{
		path: '/intelligentAgent',
		name: 'intelligentAgent',
		redirect: '/intelligentAgent/home',
		component: layout,
		children: [
			{
				path: 'home',
				name: 'home',
				component: () => import('@/views/intelligentAgent/myReactorAssistant/index.vue'),
				meta: {
					title: 'route.chat',
					isHiddenNavBar: true,
				},
			},
			{
				path: 'intelligentList',
				name: 'intelligentList',
				component: () => import('@/views/intelligent/intelligentList'),
				meta: {
					title: 'route.find',
				},
			},
		],
		meta: {
			title: 'common.chat',
			icon: 'intelligent',
			isTabar: true,
		},
	},
	// 洞察
	{
		path: '/observe',
		name: 'observe',
		redirect: '/observe/discovery',
		component: layout,
		children: [
			{
				path: 'focus',
				name: 'focus',
				component: () => import('@/views/observe/focus'),
				meta: {
					title: 'route.focus',
				},
			},
			{
				path: 'discovery',
				name: 'discovery',
				component: () => import('@/views/observe/discovery'),
				meta: {
					title: 'route.find',
				},
			},
		],
		meta: {
			title: 'common.observe',
			icon: 'observe',
			isTabar: true,
		},
	},
	// 客户
	{
		path: '/customer',
		name: 'customer',
		redirect: '/customer/customerList',
		component: layout,
		children: [
			{
				path: 'customerList',
				name: 'customerList',
				component: () => import('@/views/customer/index'),
				meta: {
					title: 'route.customer',
					hiddenNav: true,
				},
			},
		],
		meta: {
			title: 'common.customer',
			icon: 'customer',
			isTabar: true,
		},
	},
	// 拜访
	{
		path: '/random',
		name: 'random',
		redirect: '/random/randomNotes',
		children: [
			{
				path: 'randomNotes',
				name: 'randomNotes',
				component: () => import('@/views/randomNotes/index'),
				meta: {
					title: '拜访',
					hiddenNav: true,
				},
			},
		],
		meta: {
			title: 'common.visit',
		},
	},
	// 报告
	{
		path: '/report',
		name: 'report',
		redirect: '/report/standardReport',
		component: layout,
		children: [
			{
				path: 'myReport',
				name: 'myReport',
				component: () => import('@/views/report/myReport'),
				meta: {
					title: 'route.myReport',
				},
			},
			{
				path: 'reportDetail',
				name: 'reportDetail',
				component: () => import('@/views/report/reportDetail'),
				meta: {
					title: 'route.reportDetail',
					notNavShow: true,
					showWxMenu: true,
				},
			},
		],
		meta: {
			title: 'common.report',
			icon: 'report',
			isTabar: true,
		},
	},
	{
		path: '/myMetrics',
		redirect: '/myMetrics/reach',
		name: 'myMetrics',
		component: () => import('@/views/intelligentAgent/myMetrics.vue'),
		meta: {
			title: '我的指标',
		},
		children: [
			{
				path: 'reach',
				name: 'reach',
				component: () => import('@/views/intelligentAgent/reach'),
				meta: {
					title: '达成',
				},
			},
			{
				path: 'metrics',
				name: 'metrics',
				component: () => import('@/views/intelligentAgent/metrics'),
				meta: {
					title: '目标',
				},
			},
		],
	},
	// 我的
	{
		path: '/manage',
		name: 'manage',
		redirect: '/manage/my',
		component: layout,
		children: [
			{
				path: 'my',
				name: 'my',
				component: () => import('@/views/manage/my'),
				meta: {
					title: '个人总结',
				},
			},
		],
		meta: {
			title: 'common.mine',
			icon: 'manage',
			isTabar: true,
			hiddenNav: true,
		},
	},
];
export default asyncRouter;
