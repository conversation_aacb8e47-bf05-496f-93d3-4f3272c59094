const intelligentAgent = [
	{
		path: '/mAssistant',
		name: 'mAssistant',
		component: () => import('@/views/intelligentAgent/mAssistant'),
		meta: {
			title: '我的助手',
		},
	},
	{
		path: '/myAssistant',
		name: 'myAssistant',
		component: () => import('@/views/intelligentAgent/myReactorAssistant/index.vue'),
		meta: {
			title: '我的助手',
		},
	},
	{
		path: '/customerInsight',
		name: 'customerInsight',
		component: () => import('@/views/intelligentAgent/customerInsight'),
		meta: {
			title: '智能拜访',
		},
	},
	{
		path: '/customerInsight1',
		name: 'customerInsight1',
		component: () => import('@/views/intelligentAgent/customerInsight1'),
		meta: {
			title: '客户洞察2.0',
		},
	},
	{
		path: '/visitPlan',
		name: 'visitPlan',
		component: () => import('@/views/intelligentAgent/visitPlan.vue'),
		meta: {
			title: '制定拜访计划',
		},
	},
	{
		path: '/toMeet',
		name: 'toMeet',
		component: () => import('@/views/intelligentAgent/toMeet'),
		meta: {
			title: 'toMeet',
		},
	},
	{
		path: '/toReport',
		name: 'toReport',
		component: () => import('@/views/intelligentAgent/toReport'),
		meta: {
			title: 'toReport',
		},
	},
	{
		path: '/visitArticle',
		name: 'visitArticle',
		component: () => import('@/views/intelligentAgent/toArticle'),
		meta: {
			title: 'visitArticle',
		},
	},
	{
		path: '/scriptAssistant1',
		name: 'scriptAssistant1',
		component: () => import('@/views/intelligentAgent/scriptAssistantLc'),
		meta: {
			title: '销售洞察1',
		},
	},
	{
		path: '/scriptAssistant2',
		name: 'scriptAssistant2',
		component: () => import('@/views/intelligentAgent/scriptAssistant2'),
		meta: {
			title: '销售洞察2',
		},
	},
	{
		path: '/policyInsights',
		name: 'policyInsights',
		component: () => import('@/views/intelligentAgent/policyInsights'),
		meta: {
			title: '政策洞察',
		},
	},
	{
		path: '/policyInsightsTest',
		name: 'policyInsightsTest',
		component: () => import('@/views/intelligentAgent/policyInsightsTest'),
		meta: {
			title: '政策洞察-测试',
		},
	},
	{
		path: '/itLearnCube',
		name: 'itLearnCube',
		component: () => import('@/views/intelligentAgent/itLearnCube'),
		meta: {
			title: 'IT 学立方',
		},
	},
	{
		path: '/insurancePayment',
		name: 'insurancePayment',
		component: () => import('@/views/intelligentAgent/insurancePayment'),
		meta: {
			title: '医保支付政策洞察',
		},
	},
	{
		path: '/productKnowledgQuery',
		name: 'productKnowledgQuery',
		component: () => import('@/views/intelligentAgent/productKnowledgQuery'),
		meta: {
			title: '产品洞察',
		},
	},
	{
		path: '/dkActivity',
		name: 'dkActivity',
		component: () => import('@/views/intelligentAgent/dkActivity'),
		meta: {
			title: 'dkActivity',
		},
	},
	{
		path: '/cloundAssistant',
		name: 'cloundAssistant',
		component: () => import('@/views/intelligentAgent/cloundAssistant'),
		meta: {
			title: 'cloundAssistant',
		},
	},
	{
		path: '/medicineAgent',
		name: 'medicineAgent',
		component: () => import('@/views/intelligentAgent/medicineAgent'),
		meta: {
			title: '医保与准入政策洞察',
		},
	},
	{
		path: '/medicalIntelligenceAccompanyingPractice',
		name: 'medicalIntelligenceAccompanyingPractice',
		component: () => import('@/views/intelligentAgent/medicalIAccPractice'),
		meta: {
			title: '智能陪练',
		},
	},
	{
		path: '/businessInsights',
		name: 'businessInsights',
		component: () => import('@/views/intelligentAgent/businessInsights'),
		meta: {
			title: '经营分析',
		},
	},
	{
		path: '/ssjIntelligentAgent',
		name: 'ssjIntelligentAgent',
		component: () => import('@/views/intelligentAgent/ssjIntelligentAgent'),
		meta: {
			title: '随手记智能体',
		},
	},
	{
		path: '/xiaoiceList',
		name: 'xiaoiceList',
		component: () => import('@/views/xiaoice/xiaoiceList'),
		meta: {
			title: '智能陪练',
		},
	},
	{
		path: '/xiaoice',
		name: 'xiaoice',
		component: () => import('@/views/xiaoice/xiaoice'),
		meta: {
			title: '智能陪练',
		},
	},
	{
		path: '/xiaoiceHistory',
		name: 'xiaoiceHistory',
		component: () => import('@/views/xiaoice/xiaoiceHistory'),
		meta: {
			title: '陪练历史',
		},
	},
	{
		path: '/xiaoiceDetail',
		name: 'xiaoiceDetail',
		component: () => import('@/views/xiaoice/xiaoiceDetail'),
		meta: {
			title: '陪练历史',
		},
	},
	{
		path: '/intelligenceAccompanyingPractice',
		name: 'intelligenceAccompanyingPractice',
		component: () => import('@/views/intelligentAgent/intelligenceAccompanyingPractice/index.vue'),
		meta: {
			title: '快学快问',
		},
	},
	{
		path: '/iapList',
		name: 'intelligenceAccompanyingPracticeList',
		component: () => import('@/views/intelligentAgent/intelligenceAccompanyingPractice/list.vue'),
		meta: {
			title: '智能陪练',
		},
	},
	{
		path: '/iapProductList',
		name: 'intelligenceAccompanyingPracticeProductList',
		component: () => import('@/views/intelligentAgent/intelligenceAccompanyingPractice/productList.vue'),
		meta: {
			title: '智能陪练',
		},
	},
	{
		path: '/iapProduct',
		name: 'intelligenceAccompanyingPracticeProduct',
		component: () => import('@/views/intelligentAgent/intelligenceAccompanyingPractice/product.vue'),
		meta: {
			title: '智能陪练',
		},
	},
	{
		path: '/keyTask',
		name: 'keyTask',
		component: () => import('@/views/intelligentAgent/keyTask.vue'),
		meta: {
			title: '关键任务',
		},
	},
	{
		path: '/keyTaskDetail',
		name: 'keyTaskDetail',
		component: () => import('@/views/intelligentAgent/keyTaskDetail.vue'),
		meta: {
			title: '关键任务',
		},
	},
	{
		path: '/dailySummary',
		name: 'dailySummary',
		component: () => import('@/views/intelligentAgent/dailySummary.vue'),
		meta: {
			title: '工作总结',
		},
	},
];
export default intelligentAgent;
