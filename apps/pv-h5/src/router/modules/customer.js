const doctor = [
	{
		path: '/labelList',
		name: 'labelList',
		component: () => import('@/views/customer/labelList'),
		meta: {
			title: '标签列表',
		},
	},

	{
		path: '/addPersonalTag',
		name: 'addPersonalTag',
		component: () => import('@/views/customer/addPersonalTag'),
		meta: {
			title: '新建标签',
		},
	},

	{
		path: '/tagManage',
		name: 'tagManage',
		component: () => import('@/views/customer/tagManage/index.vue'),
		meta: {
			zhName: '标签管理',
		},
	},

	{
		path: '/doctorAll',
		name: 'doctorAll',
		component: () => import('@/views/customer/doctorAll'),
		meta: {
			title: '选择目标医生',
		},
	},

	{
		path: '/doctorTag',
		name: 'doctorTag',
		component: () => import('@/views/customer/docTag'),
		meta: {
			title: '选择目标医生',
		},
	},

	{
		path: '/newDoctor',
		name: 'newDoctor',
		component: () => import('@/views/customer/newDoctor'),
		meta: {
			title: '编辑医生',
		},
	},

	// 医生详情
	{
		path: '/doctorInfo',
		name: 'doctorInfo',
		component: () => import('@/views/customer/doctorDetail/doctorInfo'),
		redirect: '/doctorInfo/internalInfo',
		meta: {
			title: '医生详情',
		},
		children: [
			{
				path: 'internalInfo',
				name: 'internalInfo',
				component: () => import('@/views/customer/doctorDetail/internalInfo/internalInfo.vue'),
				meta: {
					title: '内部信息',
				},
			},
			{
				path: 'publicInfo',
				name: 'publicInfo',
				component: () => import('@/views/customer/doctorDetail/publicInfo/index.vue'),
				meta: {
					zhName: '公示信息',
				},
			},
			{
				path: 'CustomerLabel',
				name: 'CustomerLabel',
				component: () => import('@/views/customer/doctorDetail/CustomerLabel/index.vue'),
				meta: {
					title: '客户标签',
				},
			},
			{
				path: 'interactiveDynamic',
				name: 'interactiveDynamic',
				component: () => import('@/views/customer/doctorDetail/interactiveDynamic/index.vue'),
				meta: {
					title: '动态一览',
				},
			},
		],
	},
	{
		path: '/wxworkInfo/:id', // 企业微信绑定列表页面
		name: 'wxworkInfo',
		component: () => import('@/views/customer/doctorDetail/internalInfo/wxworkInfo.vue'),
		meta: {
			title: '绑定外部联系人',
		},
	},
];

export default doctor;
