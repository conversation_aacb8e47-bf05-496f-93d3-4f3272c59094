const manage = [
	{
		path: '/editor',
		name: 'editor',
		component: () => import('@/views/manage/editor'),
		meta: {
			title: '个人资料',
		},
	},
	{
		path: '/aboutUs',
		name: 'aboutUs',
		component: () => import('@/views/manage/aboutUs'),
		meta: {
			title: '关于智体视界',
		},
	},
	{
		path: '/ys',
		name: 'ys',
		component: () => import('@/views/manage/ys'),
		meta: {
			title: '隐私政策',
		},
	},
	{
		path: '/fedBack',
		name: 'fedBack',
		component: () => import('@/views/manage/fedBack'),
		meta: {
			title: '意见反馈',
		},
	},
	{
		path: '/aLanguage',
		name: 'aLanguage',
		component: () => import('@/views/manage/aLanguage'),
		meta: {
			title: '语言设置',
		},
	},
	// 问卷
	{
		path: '/survey',
		name: 'survey',
		component: () => import('@/views/manage/survey'),
		meta: {
			title: 'common.survey',
		},
	},
	// 新建问卷
	{
		path: '/surveyCreate',
		name: 'surveyCreate',
		component: () => import('@/views/manage/surveyCreate'),
		meta: {
			title: '新建问卷',
		},
	},
];
export default manage;
