import { createWebHistory, createRouter } from 'vue-router';
import intelligentAgent from './modules/intelligentAgent';
import manage from './modules/manage';
import medicalKnowledgeMaintained from './modules/medicalKnowledgeMaintained';
import salesInsightNurturing from './modules/salesInsightNurturing';
import asyncRouter from './modules/asyncRouter';
import customer from './modules/customer';

// 路由表
export const routes = [
	{
		path: '/',
		redirect: '/manage',
	},
	{
		path: '/login',
		component: () => import('@/views/login/index.vue'),
	},
	{
		path: '/logout',
		component: () => import('@/views/login/logout.vue'),
	},
	...intelligentAgent, // 智体视界
	...manage, //管理
	...medicalKnowledgeMaintained, // 医学知识维护
	...salesInsightNurturing, // 销售智能培养
	...customer, // 医生
	{
		path: '/401',
		component: () => import('@/views/error/401'),
	},
	{
		path: '/:pathMatch(.*)*',
		component: () => import('@/views/error/404'),
	},
	{
		path: '/ppt',
		component: () => import('@/views/report/components/ppt/hospitalAnalysisPPT.vue'),
	},
	{
		path: '/noExist',
		component: () => import('@/views/error/noExist'),
	},
	{
		path: '/singleCard',
		component: () => import('@/views/extra/singleCard.vue'),
	},
	{
		path: '/singleCard2',
		component: () => import('@/views/extra/singleCard2.vue'),
	},
	{
		path: '/singleCard3',
		component: () => import('@/views/extra/singleCard3.vue'),
	},
	{
		path: '/singleCard4',
		component: () => import('@/views/extra/singleCard4.vue'),
	},
	{
		path: '/test',
		component: () => import('@/views/test/test.vue'),
	},
];
// 异步路由
export const asyncRoutes = [...asyncRouter];

const router = createRouter({
	history: createWebHistory('/app/mobile/'),
	routes,
	scrollBehavior(to, from, savedPosition) {
		if (savedPosition) {
			return savedPosition;
		} else {
			return { top: 0 };
		}
	},
});

export default router;
