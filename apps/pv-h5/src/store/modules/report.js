const useReport = defineStore('report', {
	state: () => ({
		reportName: '',
		btTime: [], //饼图时间
		zxtTime: [], //折线图时间
		bfhzTime: [], //拜访汇总时间
		bfbxTime: [], //拜访表现时间
		reportInfo: [],
		globalFilterInfo: {
			time: {
				startDate: '',
				endDate: '',
			},
			area: {
				countyName: '',
				city: '',
				province: '',
				provinceData: '',
			},
			brand: {
				brandId: '',
				brandName: '',
			},
			people: {
				personName: '',
				personId: '',
			},
			hospital: {
				hospitalName: '',
				hospitalId: '',
			},
		},
	}),
	actions: {
		setReportName(data) {
			this.reportName = data;
		},
		setTime(data) {
			const timeMap = {
				competitiveMarketShare: 'btTime',
				marekShare: 'zxtTime',
				visitSummary: 'bfhzTime',
				visitManagementAndPerformance: 'bfbxTime',
			};
			for (const item of data) {
				const timeKey = timeMap[item.name];
				if (timeKey) {
					this[timeKey] = [item.startTime, item.endTime];
				}
			}
		},
		setReportInfo(data) {
			//如果reportInfo中有name和data.name相同的则替换，否则插入
			if (data.name) {
				let reportIndex = this.reportInfo.findIndex((ele) => ele.name === data.name);
				if (reportIndex === -1) {
					this.reportInfo.push(data);
				} else {
					this.reportInfo.splice(reportIndex, 1, data);
				}
			}
		},
		setGlobalFilterInfo(type, data) {
			this.globalFilterInfo[type] = data;
		},
	},
	getters: {
		toMeetingList() {
			return this.permission.some((item) => item.path === 'meetingList');
		},
	},
});

export default useReport;
