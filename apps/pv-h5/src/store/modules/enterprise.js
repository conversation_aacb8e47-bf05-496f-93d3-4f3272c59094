import { getEnterpriseInfo } from '@/api/login';

const useEnterStore = defineStore('Enter', {
	state: () => ({
		enterInfo: {},
	}),
	actions: {
		SET_ENTERINFO(data) {
			this.enterInfo = data;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('enterInfo', data);
		},
		async GET_ENTERINFO() {
			const domainurl = import.meta.env.VITE_APP_ENV === 'development' ? import.meta.env.VITE_APP_DEFAULT : `${location.protocol}//${location.host}`;
			const baseUrl = websiteConfig?.baseBackEndUrl || window.location.origin;
			const res = await getEnterpriseInfo(baseUrl, domainurl);
			res.result[0].appId = res.result[0].wechatWorkCropId || '';
			const attributes = res.result[0]?.attributes || [];
			for (const i of attributes) {
				if (i.name === 'agentId') {
					res.result[0].agentId = i.value || '';
				}
				if (i.name === 'siteId') {
					res.result[0].dkSiteId = i.value || '';
				}
				if (i.name === 'miniProgramId') {
					res.result[0].shareAppId = i.value || '';
				}
				if (i.name === 'minioAddress') {
					res.result[0].minioAddress = i.value || '';
				}
				if (i.name === 'isProxy') {
					res.result[0].isProxy = i.value || '';
				}
			}
			// 开发环境， 解决跨域
			if (import.meta.env.VITE_APP_ENV === 'development') {
				res.result[0].interfaceAddress = '/api';
				res.result[0].agentAddress = '/ai';
			}
			res.result[0].authAddress = res.result[0].authAddress.slice(0, -5);
			this.SET_ENTERINFO(res.result[0]);
		},
	},
});

export default useEnterStore;
