import { routes, asyncRoutes } from '@/router';
import { permissionList } from '@/api/login';
import { findChildrenById } from '@/utils/index';
import { getAgentListApi } from '@/api/agent-tools';
const usePremissionStore = defineStore('Premission', {
	state: () => ({
		routers: routes,
		addRouters: [],
		asyncRouters: [],
		visitList: [],
		discoveryList: [],
		reportList: [],
		systemBtnList: [],
		activityReportList: [],
		reportChildren: '',
		agentList: [],
		userAgent: [],
		cardList: [],
	}),
	actions: {
		SET_ROUTERS(data) {
			this.routers = routes.concat(data);
		},
		SET_ADD_ROUTERS(data) {
			this.addRouters = data;
		},
		// 异步菜单
		SET_ASYNC_ROUTERS(data) {
			this.asyncRouters = data;
		},
		// 拜访模块权限
		SET_VISIT_LIST(data) {
			this.visitList = data;
		},
		// 洞察-发现模块权限
		SET_DISCOVERY_LIST(data) {
			this.discoveryList = data;
		},
		// 报告模块权限
		SET_REPORT_LIST(data) {
			this.reportList = data;
		},
		SET_REPORT_CHILDREN(data) {
			this.reportChildren = data;
		},
		// 元素权限
		SET_SYSTEM_BTN_LIST(data) {
			this.systemBtnList = data;
		},
		// 报告的动态菜单
		SET_ACTIVITY_REPORT_ROUTERS(data) {
			this.activityReportList = data;
		},
		SET_AGENT_LIST(data) {
			this.agentList = data;
		},
		SET_CARDLIST(list) {
			this.cardList = list;
		},
		async GET_PERMISSION() {
			const { result: list } = await permissionList();
			let cardList = getAllUniqueItems(list);
			this.SET_CARDLIST(cardList);
			const { updateField } = useCardLibraryLocalStorage();
			updateField('permissionTree', list);
			// 存储 asyncRoutes
			this.SET_ASYNC_ROUTERS(list);
			const routeList = getRouteListPath(list);
			let commonRouter = filterAsyncRouter(asyncRoutes, routeList);
			// 获取报告模块的权限
			const reportList = getModulePermissionTag(list, 'report', 'MENU');
			// 清除我的报告和报告详情
			const activityReportList = reportList.filter((ele) => ele.path !== 'myReport' && ele.path !== 'reportDetail');
			this.SET_ACTIVITY_REPORT_ROUTERS(activityReportList);
			// 新建异步菜单
			commonRouter = addAsyncRouter(commonRouter, activityReportList, 'report');
			this.SET_ROUTERS(commonRouter);
			this.SET_ADD_ROUTERS(commonRouter);
			// 获取报告模块下第一个有权限的菜单
			const firstReportName = commonRouter.filter((item) => item.path === '/report');
			if (firstReportName.length > 0 && firstReportName[0].children.length > 0) {
				this.SET_REPORT_CHILDREN(firstReportName[0].children[0].name);
			}
			// 获取拜访模块的权限
			const visitList = getModulePermissionTag(list, 'randomNotes', 'TAG');
			this.SET_VISIT_LIST(visitList);

			// 获取洞察-发现模块的权限
			const discoveryList = getModulePermissionTag(list, 'discovery', 'TAG');
			this.SET_DISCOVERY_LIST(discoveryList);

			this.SET_REPORT_LIST(reportList);

			// 获取系统的特殊权限(button集合)
			const systemBtnList = getModulePermissionButton(list);
			this.SET_SYSTEM_BTN_LIST(systemBtnList);
			//获取智能体菜单
			const intelligentAgentList = getModulePermissionTag(list, 'intelligentAgent', 'MENU');
			this.SET_AGENT_LIST(intelligentAgentList);
		},
		GET_USER_AGENT() {
			return new Promise((resolve) => {
				if (this.userAgent && this.userAgent.length) {
					return resolve(this.userAgent);
				} else {
					getAgentListApi().then((res) => {
						this.userAgent = res.result.content;
						return resolve(this.userAgent);
					});
				}
			});
		},
	},
});

export default usePremissionStore;

/**
 * ⚠️⚠️⚠️  这里现在只支持二级路由嵌套，如果是三级路由需要重新写逻辑
 * 过滤异步路由表，返回符合用户角色权限的路由表
 * @param {asyncRouterMap} 需要过滤的异步路由表
 * @param {rolesRouter} 当前用户有权限的路由名称
 * @returns {Array} 过滤后的路由表
 */
function filterAsyncRouter(asyncRouterMap, rolesRouter, constRouter = []) {
	for (const iterator of asyncRouterMap) {
		if (iterator.children && iterator.children.length) {
			const lItem = filterChildRouter(rolesRouter, iterator);
			if (lItem) {
				constRouter.push(lItem);
			}
		} else {
			if (filterRolesRouter(rolesRouter, iterator)) {
				constRouter.push(iterator);
			}
		}
	}
	return constRouter;
}
/**
 * 新建异步菜单
 */
function addAsyncRouter(commonRouter, activityList, svg) {
	for (const ele of commonRouter) {
		if (ele.name === svg) {
			let list = [];
			for (const item of activityList) {
				list.push({
					path: item.path,
					name: item.path,
					meta: {
						title: item.name,
						isNotI18n: true,
					},
					component: () => import('@/views/report/standardReport'),
				});
			}
			ele.children = list.concat(ele.children);
		}
	}
	return commonRouter;
}
/**
 * 取出routeList里的path
 */
function getRouteListPath(list) {
	const routeList = [];
	list.forEach((item) => {
		if (item.children && item.children.length) {
			item.children.forEach((child) => {
				if (child.view.indexOf('mobile') > -1) {
					routeList.push(child.path);
				}
			});
		} else if (item.view.indexOf('mobile') > -1) {
			routeList.push(item.path);
		}
	});
	return routeList;
}
/**
 * 遍历用户返回的有权限的路由并匹配
 */
function filterRolesRouter(rolesRouter, iterator) {
	return rolesRouter.some((ele) => {
		return ele === iterator.path;
	});
}

/**
 * 遍历路由表的二级路由并匹配
 */
function filterChildRouter(rolesRouter, iterator) {
	iterator.children = iterator.children.filter((ele) => {
		return rolesRouter.includes(ele.path);
	});
	if (iterator.children.length > 0) {
		return iterator;
	} else {
		return false;
	}
}

/**
 *  获取button权限
 */
function getModulePermissionButton(data) {
	let result = [];
	// 遍历每一项
	for (let item of data) {
		// 如果是 type 为 "button" 的元素，添加到结果数组
		if (item.type === 'BUTTON') {
			result.push(item);
		}
		// 如果有 children，则递归调用
		if (item.children) {
			result = result.concat(getModulePermissionButton(item.children));
		}
	}
	return result;
}

/**
 * 获取指定模块的权限
 */
function getModulePermissionTag(list, module, tag) {
	const perList = [];
	const larr = findChildrenById(list, module);
	if (tag === 'TAG') {
		for (const ele of larr) {
			if (ele.type === tag) {
				perList.push(ele);
			}
		}
	} else if (tag === 'BUTTON') {
		for (const ele of larr) {
			if (ele.type === 'BUTTON' || ele.type === null) {
				perList.push(ele);
			}
		}
	} else if (tag === 'MENU') {
		for (const ele of larr) {
			if (ele.type === tag) {
				perList.push(ele);
			}
		}
	}
	return perList;
}
function getAllUniqueItems(tree) {
	// 使用Set来去重
	const uniqueItems = new Set();

	// 递归函数
	function traverse(node) {
		if (!node) return;

		// 如果是数组，遍历每个元素
		if (Array.isArray(node)) {
			node.forEach((item) => traverse(item));
			return;
		}

		// 添加当前节点到Set（这里假设整个对象作为item）
		let filterValue = node.attributes.find((ele) => ele.name === 'filterConfig');
		if (filterValue && filterValue.value) {
			uniqueItems.add(node);
		}

		// 如果有children，递归处理
		if (node.children && Array.isArray(node.children)) {
			node.children.forEach((child) => traverse(child));
		}
	}

	// 开始遍历
	traverse(tree);

	// 将Set转换为数组返回
	return Array.from(uniqueItems);
}
