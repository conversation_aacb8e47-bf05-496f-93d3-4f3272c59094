const useViewStore = defineStore('taskView', {
	state: () => ({
		cacheViews: [],
	}),
	actions: {
		ADD_CACHED_VIEW(view) {
			if (this.cacheViews.includes(view.name)) {
				return;
			}
			if (view.meta.keepAlive) {
				this.cacheViews.push(view.name);
			}
		},
		DEL_CACHED_VIEW(view) {
			return new Promise((resolve) => {
				const index = this.cacheViews.indexOf(view.name);
				index > -1 && this.cacheViews.splice(index, 1);
				resolve(this.cacheViews);
			});
		},
		DEL_ALL_CACHED_VIEWS() {
			return new Promise((resolve) => {
				this.cacheViews = [];
				resolve();
			});
		},
	},
});

export default useViewStore;
