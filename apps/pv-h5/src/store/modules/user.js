import { getUserInfo, findUserIsExistPV } from '@/api/user';

const useUserStore = defineStore('User', {
	state: () => ({
		userInfo: {},
		permission: false,
		isExist: '',
		territory_code: '',
	}),
	actions: {
		SET_USERINFO(data) {
			this.userInfo = data;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('userInfo', data);
		},
		SET_FIRSTNAME(data) {
			this.userInfo.firstName = data;
		},
		SET_AVATAR(data) {
			this.userInfo.avatar = data;
		},
		SET_PERMISSION(data) {
			this.permission = data;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('permission', data);
		},
		async GET_USERINFO() {
			//获取用户信息
			let [{ result: userInfo }] = await Promise.all([getUserInfo()]);
			this.SET_USERINFO(userInfo);
			this.SET_PERMISSION(userInfo?.roles?.map((ele) => ele.name));
			// this.SET_PERMISSION(permission);
		},
		getUserIsExist() {
			findUserIsExistPV().then((res) => {
				this.isExist = res.result.isExist || '';
				this.territory_code = res.result.territory_code || '';
			});
		},
	},
	getters: {
		toMeetingList() {
			return this.permission.some((item) => item.path === 'meetingList');
		},
	},
});

export default useUserStore;
