import { isWeChat } from '@/utils/index';
import { wechatCrop } from '@/api/login';
import wxApi from '@/api/wxApi.js';

const useWxMenu = defineStore('Menu', {
	state: () => ({
		isShowMenu: null,
	}),
	actions: {
		SET_IS_SHOW_MENU(data) {
			this.isShowMenu = data;
		},
		async SET_WX_MENU(data) {
			if (isWeChat() === '企业微信') {
				if ([null, true].includes(this.isShowMenu)) {
					this.isShowMenu = false;
					let res = await wechatCrop({ url: window.location.href.split('#')[0] });
					await wxApi.wxRegister(res.result);
					wxApi.hideAllNonBaseMenuItem();
				}
			}
		},
	},
	getters: {},
});

export default useWxMenu;
