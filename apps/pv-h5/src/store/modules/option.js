const useOption = defineStore('option', {
	state: () => ({
		currentIndex: '我的',
	}),
	actions: {
		setCurrentIndex(data) {
			this.currentIndex = data;
		},
	},
	persist: {
		// 按需存储 state/ref
		// 修改存储中使用的键名称，默认为当前 Store的 id
		key: 'optionStorekey',
		// 修改为 sessionStorage，默认为 localStorage
		storage: window.sessionStorage,
		// 🎉按需持久化，默认不写会存储全部
		// paths: ['nested.data'],
	},
});

export default useOption;
