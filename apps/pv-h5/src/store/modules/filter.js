import { filterBrandInfo, filterSkuInfo, filterSkuInfoTree, filterProductInfo, filterOrganizationTree, filterProvince, filterTa, filterMarketShare, filterHospital, filterProductInfoMq, getProvinceTree } from '@/api/filter';
import { getcubejsData, getcubejsHierarchicalData } from '@/api/sales';
import useEnterStore from '@/store/modules/enterprise';
const usefilterStore = defineStore('Filter', {
	state: () => ({
		brandInfo: [],
		skuInfo: [],
		skuInfoTree: [],
		productInfo: [],
		productMQInfo: [],
		treeInfo: [],
		provinceList: [],
		taList: [],
		msList: [],
		hospitalList: [],
		hospitalTotal: 0,
		isGlobalVoice: localStorage.getItem('globalVoice') === 'true' ? true : false,
		language: localStorage.getItem('language') || 'zh',
		agentList: [],
		doctorInfo: {},
		cubeOrgTreeInfo: [],
		cubeSkuTreeInfo: [],
		cubeProvinceCityTreeInfo: [],
		cubeProvinceCityInfo: [],

		provinceTree: [],
	}),
	actions: {
		SET_AGENTLIST(data) {
			this.agentList = data;
		},
		SET_IS_GLOBAL_VOICE(data) {
			localStorage.setItem('globalVoice', data);
			this.isGlobalVoice = data;
		},
		SET_LANGUAGE(data) {
			this.language = data;
		},
		SET_FILTERINFO(pInfo) {
			// this.brandInfo = bInfo;
			this.productInfo = pInfo;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('productInfo', pInfo);
		},
		SET_FILTER_MQ_INFO(data) {
			this.productMQInfo = data;
		},
		SET_SKUINFO(info) {
			this.skuInfo = info;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('skuInfo', info);
		},
		SET_SKUINFOTREE(info) {
			this.skuInfoTree = info;
		},
		SET_PROVINCE(list) {
			this.provinceList = list;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('provinceList', list);
		},
		SET_TA(list) {
			this.taList = list;
		},
		SET_MARKETSHARE(list) {
			this.msList = list;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('msList', list);
		},
		SET_HOSPITAL(list) {
			this.hospitalList = list;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('hospitalList', list);
		},
		SET_TREEINFO(list) {
			this.treeInfo = list;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('treeInfo', list);
		},
		SET_DOCTOR_INFO(data) {
			this.doctorInfo = data;
		},
		SET_PROVINCETree(list) {
			this.provinceTree = list;
		},
		async GET_ORGINFO() {
			//获取用户信息
			try {
				let [{ result: treeInfo }] = await Promise.all([filterOrganizationTree()]);
				this.SET_TREEINFO(treeInfo);
				return true;
			} catch (err) {
				console.log(err);
				return true;
			}
		},
		async GET_SKUINFO() {
			//获取sku
			let [{ result: skuInfo }] = await Promise.all([filterSkuInfo()]);
			this.SET_SKUINFO(skuInfo);
		},
		async GET_SKUINFOTREE() {
			//获取sku
			let { result: skuInfo } = await filterSkuInfoTree();
			this.SET_SKUINFOTREE(skuInfo);
		},
		async GET_FILTERINFO() {
			let [{ result: productInfo }] = await Promise.all([filterProductInfo()]);
			this.SET_FILTERINFO(productInfo);
			const enterpriseInfo = useEnterStore();
			if (enterpriseInfo.enterInfo.id === 'muqiao') {
				filterProductInfoMq().then((res) => {
					this.SET_FILTER_MQ_INFO(res.result || []);
				});
			}
		},

		async GET_PROVINCE() {
			//获取省份信息
			let [{ result: provinceList }, res] = await Promise.all([filterProvince(), getProvinceTree()]);
			this.SET_PROVINCE(provinceList);
			this.SET_PROVINCETree(res.result);
		},
		async GET_TA() {
			//获取ta信息
			let [{ result: taList }] = await Promise.all([filterTa()]);
			this.SET_TA(taList);
		},
		async GET_MARKETSHARE() {
			//获取市场信息
			let [{ result: msList }] = await Promise.all([filterMarketShare()]);
			this.SET_MARKETSHARE(msList);
		},
		async GET_HOSPITAL() {
			//获取市场信息
			let res = await filterHospital({ size: 50 });
			this.hospitalTotal = res.result.totalElements;
			this.SET_HOSPITAL(res.result.content);
		},
		// cubejs相关筛选器
		//获取组织架构筛选器
		async GET_ORG_FILTER() {
			let { data } = await getcubejsHierarchicalData({
				cube_query: {
					query: {
						dimensions: ['pv_ads_organization_filter.territory_code', 'pv_ads_organization_filter.employee_name', 'pv_ads_organization_filter.user_id', 'pv_ads_organization_filter.parent_user_id', 'pv_ads_organization_filter.employee_id', 'pv_ads_organization_filter.level_tag'],
					},
				},
				id_field: 'pv_ads_organization_filter.user_id',
				parent_id_field: 'pv_ads_organization_filter.parent_user_id',
			});
			this.SET_ORG_FILTER(buildOrgTree(data));
		},
		//存入组织架构筛选器
		SET_ORG_FILTER(data) {
			this.cubeOrgTreeInfo = data;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('cubeOrgTreeInfo', data);
		},
		//获取SKU筛选器
		async GET_SKU_FILTER() {
			let { data } = await getcubejsHierarchicalData({
				cube_query: {
					query: {
						dimensions: ['pv_ads_product_filter.product_code', 'pv_ads_product_filter.product_cn', 'pv_ads_product_filter.parent_product_code', 'pv_ads_product_filter.level_tag'],
					},
				},
				id_field: 'pv_ads_product_filter.product_code',
				parent_id_field: 'pv_ads_product_filter.parent_product_code',
			});
			this.SET_SKU_FILTER(buildSkuTree(data));
		},
		SET_SKU_FILTER(data) {
			this.cubeSkuTreeInfo = data;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('cubeSkuTreeInfo', data);
		},
		//获取省市筛选器
		async GET_PC_FILTER() {
			let { data } = await getcubejsData({
				query: {
					dimensions: ['pv_ads_location_filter.location_name', 'pv_ads_location_filter.location_id', 'pv_ads_location_filter.parent_location_id', 'pv_ads_location_filter.level_tag'],
				},
			});
			this.SET_PC_FILTER(buildPCTree(data), data);
		},
		SET_PC_FILTER(data, info) {
			this.cubeProvinceCityTreeInfo = data;
			this.cubeProvinceCityInfo = info;
			const { updateField } = useCardLibraryLocalStorage();
			updateField('cubeProvinceCityTreeInfo', data);
			updateField('cubeProvinceCityInfo', info);
		},
	},
	getters: {},
});
export default usefilterStore;

/**
 * 将扁平数据转为树形结构
 * @param {Array} data - 扁平数据数组，包含 pv_ads_organization_filter 嵌套字段
 * @param {string|null} parentId - 父级 user_id，默认为 null（从根节点开始）
 * @returns {Array} 树形结构数组
 */
function buildOrgTree(data) {
	// 递归处理每一层
	return data.map((item) => ({
		level_tag: item['pv_ads_organization_filter.level_tag'],
		user_id: item['pv_ads_organization_filter.user_id'],
		employee_name: item['pv_ads_organization_filter.employee_name'],
		parent_user_id: item['pv_ads_organization_filter.parent_user_id'],
		territory_code: item['pv_ads_organization_filter.territory_code'],
		// 如果有 children，就递归调用 buildOrgTree
		children: item.children ? buildOrgTree(item.children) : [],
	}));
}
function buildSkuTree(data) {
	// 展平数据，简化字段名（仅执行一次）
	return data.map((item) => ({
		levelNumber: item['pv_ads_product_filter.level_tag'],
		product_cn: item['pv_ads_product_filter.product_cn'],
		parent_product_code: item['pv_ads_product_filter.parent_product_code'],
		product_code: item['pv_ads_product_filter.product_code'],
		// 如果有 children，就递归调用 buildOrgTree
		children: item.children ? buildSkuTree(item.children) : [],
	}));
}
function buildPCTree(data) {
	// 展平数据，简化字段名（仅执行一次）
	const formattedData = data.map((item) => ({
		levelNumber: item['pv_ads_location_filter.level_tag'],
		location_id: item['pv_ads_location_filter.location_id'],
		name: item['pv_ads_location_filter.location_name'],
		parent_location_id: item['pv_ads_location_filter.parent_location_id'],
	}));

	// 所有节点的 id 用于判断哪些是“缺失父节点的根”
	const idSet = new Set(formattedData.map((item) => item.location_id));

	// 找出在数据中没有父节点的项，作为根节点集合
	const potentialRoots = formattedData.filter((item) => !idSet.has(item.parent_location_id));

	// 构建树（递归）
	function constructTree(parentId) {
		return formattedData
			.filter((item) => item.parent_location_id === parentId)
			.map((item) => ({
				...item,
				child: constructTree(item.location_id),
			}));
	}

	// 多根节点构成森林
	return potentialRoots.map((root) => ({
		...root,
		child: constructTree(root.location_id),
	}));
}
