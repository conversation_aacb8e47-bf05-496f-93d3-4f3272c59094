import { defineConfig, loadEnv } from 'vite';
import path from 'path';
import createVitePlugins from './vite/plugins';
import px2viewport from 'postcss-px-to-viewport';

export default defineConfig(({ mode, command }) => {
	const env = loadEnv(mode, process.cwd());
	return {
		base: env.VITE_APP_ENV === 'development' ? '/' : '/app/mobile/',
		build: {
			outDir: 'dist',
			minify: 'terser',
			sourcemap: mode === 'development' ? 'inline' : false,
			terserOptions: {
				compress: {
					pure_funcs: ['console.log', 'console.info', 'console.debug'],
					drop_debugger: true,
				},
			},
			// 不转换资源为base64
			assetsInlineLimit: 0,
			rollupOptions: {
				output: {
					manualChunks(id, { getModuleIds, getModuleInfo }) {
						if (id.includes('src')) {
							return 'index';
						}
					},
					// experimentalMinChunkSize: 100000, // 最小 Chunk 尺寸（100KB），小于这个大小会尝试合并
				},
			},
		},
		plugins: createVitePlugins(env, command === 'build'),
		css: {
			postcss: {
				plugins: [
					// 页面
					px2viewport({
						unitToConvert: 'px',
						viewportWidth: 375,
						unitPrecision: 6,
						propList: ['*'],
						viewportUnit: 'vw',
						fontViewportUnit: 'vw',
						selectorBlackList: ['ignore-', 'van-circle__layer'],
						minPixelValue: 0.1,
						mediaQuery: false,
						replace: true,
						exclude: [/node_modules\/vant/i, /src\/style\/pc/i],
						landscape: false,
					}),
					// 三方组件
					px2viewport({
						unitToConvert: 'px',
						viewportWidth: 375,
						unitPrecision: 6,
						propList: ['*'],
						viewportUnit: 'vw',
						fontViewportUnit: 'vw',
						selectorBlackList: ['ignore-', 'van-circle__layer'],
						minPixelValue: 0.1,
						mediaQuery: false,
						replace: true,
						exclude: [/^(?!.*node_modules\/vant)/, /src\/style\/pc/i],
						landscape: false,
					}),
				],
			},
			devSourcemap: mode === 'development' ? true : false,
		},
		resolve: {
			alias: {
				// 设置路径
				'~': path.resolve(__dirname, './'),
				// 设置别名
				'@': path.resolve(__dirname, './src'),
			},
			extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
		},
		// vite 相关配置
		server: {
			debug: true, // 开启 debug 日志
			port: 1288,
			host: true,
			open: true,
			proxy: {
				'/api': {
					target: 'https://qdgateway.pharmbrain.com',
					changeOrigin: true,
					ws: true,
					headers: {
						host: 'https://qdgateway.pharmbrain.com',
						origin: 'https://qdgateway.pharmbrain.com',
					},
					rewrite: (path) => path.replace(/^\/api/, ''),
					logLevel: 'debug',
					configure: (proxy, options) => {
						// 监听代理请求发送时的事件
						proxy.on('proxyReq', (proxyReq, req, res) => {
							console.log('发送请求到目标：', req.method, req.url);
							console.log('目标地址：', proxyReq.getHeader('host'), proxyReq.path);
						});

						// 监听代理响应接收时的事件
						proxy.on('proxyRes', (proxyRes, req, res) => {
							console.log('收到目标响应：', proxyRes.statusCode, req.url);
							// console.log('响应头：', JSON.stringify(proxyRes.headers));
						});

						// 可选：监听错误事件
						proxy.on('error', (err, req, res) => {
							console.error('代理出错：', err);
						});
					},
				},
				'/login': {
					target: 'https://qdkeycloak.pharmbrain.com',
					changeOrigin: true,
					ws: true,
					headers: {
						host: 'https://qdkeycloak.pharmbrain.com',
						origin: 'https://qdkeycloak.pharmbrain.com',
					},
					rewrite: (path) => path.replace(/^\/login/, ''),
					logLevel: 'debug',
				},
				'/ai': {
					target: 'https://uat-agent-demo.pharmbrain.com',
					changeOrigin: true,
					ws: true,
					headers: {
						host: 'https://uat-agent-demo.pharmbrain.com',
						origin: 'https://uat-agent-demo.pharmbrain.com',
					},
					rewrite: (path) => path.replace(/^\/ai/, ''),
					logLevel: 'debug',
					configure: (proxy, options) => {
						// 监听代理请求发送时的事件
						proxy.on('proxyReq', (proxyReq, req, res) => {
							console.log('发送请求到目标：', req.method, req.url);
							console.log('目标地址：', proxyReq.getHeader('host'), proxyReq.path);
						});

						// 监听代理响应接收时的事件
						proxy.on('proxyRes', (proxyRes, req, res) => {
							console.log('收到目标响应：', proxyRes.statusCode, req.url);
							// console.log('响应头：', JSON.stringify(proxyRes.headers));
						});

						// 可选：监听错误事件
						proxy.on('error', (err, req, res) => {
							console.error('代理出错：', err);
						});
					},
				},
				'/a48': {
					target: 'https://qdfastgpt48.pharmbrain.com',
					changeOrigin: true,
					ws: true,
					headers: {
						host: 'https://qdfastgpt48.pharmbrain.com',
						origin: 'https://qdfastgpt48.pharmbrain.com',
					},
					rewrite: (path) => path.replace(/^\/a48/, ''),
					logLevel: 'debug',
				},
			},
		},
	};
});
