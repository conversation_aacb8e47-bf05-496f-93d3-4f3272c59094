{"name": "pv-h5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"prepare": "husky install", "dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --fix --ext .vue,.js,.jsx", "prettier": "prettier --write .", "svgo": "svgo -f ./src/assets/icons/beforeSvg -o ./src/assets/icons/svg", "docker:build": "docker build --platform linux/amd64 -t harbor.pharmbrain.com/pharmbrain/app/mobile:DEV.2025.07.23.B .", "docker:push": "docker push harbor.pharmbrain.com/pharmbrain/app/mobile:DEV.2025.07.23.B", "docker:prod": "npm run build && npm run docker:build && npm run docker:push", "postinstall": "patch-package"}, "dependencies": {"@dsb-norge/vue-keycloak-js": "^2.4.0", "@superset-ui/embedded-sdk": "^0.1.0-alpha.12", "@vueuse/components": "^10.11.0", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "css-vars-ponyfill": "^2.4.8", "decimal.js": "^10.4.3", "hammerjs": "^2.0.8", "highlight.js": "^11.9.0", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "jwt-decode": "^4.0.0", "markdown-it": "^13.0.2", "nprogress": "^0.2.0", "pptxgenjs": "3.12.0", "vconsole": "^3.15.1", "virtua": "^0.29.1", "vue-pdf-embed": "^1.1.6", "xgplayer": "^3.0.18"}, "devDependencies": {"consola": "^3.2.3"}, "lint-staged": {"*.{vue,js,jsx,css,scss}": ["prettier --write", "git add"], "*.{js,jsx,vue}": ["eslint --fix", "git add -A"]}}