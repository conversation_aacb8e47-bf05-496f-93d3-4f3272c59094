module.exports = {
	extends: ['@commitlint/config-conventional'],
	rules: {
		'type-enum': [2, 'always', ['feat', 'fix', 'perf', 'refactor', 'docs', 'style', 'revert', 'merge', 'test', 'chore']],
		'type-case': [0],
		'type-empty': [0],
		'scope-empty': [0],
		'scope-case': [0],
		'subject-full-stop': [0, 'never'],
		'subject-case': [0, 'never'],
		'header-max-length': [0, 'always', 72],
	},
};

// 规则说明
/**
 * feat 新增功能
 * fix  bug 修复
 * perf 性能优化 体验优化
 * refactor 代码的重构 修改原有的功能
 * docs 文档更新
 * style 修改空白字符 格式缩紧 补全缺失的分号； 不该变代码逻辑
 * revert 回滚到之前的提交
 * merge 合并分支
 * test 测试相关
 * chore 不属于以上的类型，都可以用 chore
 */

// commit 举例
// git commit -m "fix: 客户中心-招募代描述显示不全"
// git commit -m "perf: 优化系统图片加载-提供图片懒加载"
