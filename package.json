{"name": "monorepo", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "turbo run dev --parallel", "dev:pv-h5": "turbo run dev --filter=pv-h5", "build": "turbo run build", "build:pv-h5": "turbo run build --filter=pv-h5"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.1", "dependencies": {"@vant/use": "^1.6.0", "@vueuse/core": "^9.4.0", "@wsfe/vue-tree": "^4.1.0", "axios": "^1.1.3", "dayjs": "^1.11.11", "echarts": "^5.4.0", "js-cookie": "^3.0.1", "lodash-es": "^4.17.21", "pinia": "^2.0.23", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.11.0", "vant": "^4.6.3", "vue": "^3.2.37", "vue-i18n": "^9.3.0-beta.6", "vue-router": "^4.1.6", "vuejs-tree": "^3.0.2", "vxe-table": "4.5.20"}, "devDependencies": {"@babel/core": "^7.19.6", "@babel/eslint-parser": "^7.19.1", "@commitlint/cli": "^17.2.0", "@commitlint/config-conventional": "^17.2.0", "@vitejs/plugin-vue": "^3.1.0", "@vitejs/plugin-vue-jsx": "^2.0.1", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.7.0", "husky": "^8.0.1", "lint-staged": "^13.0.3", "patch-package": "^8.0.0", "postcss-px-to-viewport": "^1.1.1", "prettier": "^2.7.1", "rollup-plugin-visualizer": "^5.8.3", "sass": "^1.55.0", "terser": "^5.39.0", "turbo": "^2.5.4", "unplugin-auto-import": "^0.11.4", "unplugin-vue-components": "^0.22.9", "vite": "^3.1.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0"}}