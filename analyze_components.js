const fs = require('fs');
const path = require('path');

// 指标类型映射
const indicatorTypeMap = {
    formalMedicineEntryRate: '正式准入率(%)',
    admissionRate: '准入率(%)',
    sales: '销售(K)',
    ach: '按达成%',
    growth: '按同比增长%',
    growthAmount: '同比增长值(K)',
    growthAch: '同比增量贡献(%)',
    mom: '环比(%)',
    momAmount: '环比增长值(K)',
    momAch: '环比增量贡献(%)',
};

// 过滤器名称映射
const filterNameMap = {
    time: '时间范围',
    org: '组织',
    brand: '产品',
    city: '区域',
    hospital: '医院',
    person: '人员'
};

// 过滤器类型映射
const filterTypeMap = {
    time: 'time',
    org: 'organization',
    brand: 'product',
    city: 'region',
    hospital: 'hospital',
    person: 'person'
};

// 分析自定义过滤器UI
function analyzeCustomFilters(content, result) {
    // 检查是否有自定义时间过滤器
    if (content.includes('query.startDate') && content.includes('query.endDate')) {
        const hasTimeFilter = result.filters.some(f => f.key === 'time');
        if (!hasTimeFilter) {
            result.filters.push({
                key: 'time',
                name: '时间范围',
                type: 'time',
                required: true,
                default: '本月'
            });
        }
    }

    // 检查产品过滤器
    if (content.includes('query.productName') || content.includes('query.brandName')) {
        const hasBrandFilter = result.filters.some(f => f.key === 'brand');
        if (!hasBrandFilter) {
            result.filters.push({
                key: 'brand',
                name: '产品',
                type: 'product',
                required: false
            });
        }
    }

    // 检查人员/组织过滤器
    if (content.includes('query.personName')) {
        const hasOrgFilter = result.filters.some(f => f.key === 'org');
        if (!hasOrgFilter) {
            result.filters.push({
                key: 'org',
                name: '组织',
                type: 'organization',
                required: false
            });
        }
    }

    // 检查医院过滤器
    if (content.includes('query.hospitalName')) {
        const hasHospitalFilter = result.filters.some(f => f.key === 'hospital');
        if (!hasHospitalFilter) {
            result.filters.push({
                key: 'hospital',
                name: '医院',
                type: 'hospital',
                required: false
            });
        }
    }

    // 检查城市/区域过滤器
    if (content.includes('query.countyName') || content.includes('query.cityName')) {
        const hasCityFilter = result.filters.some(f => f.key === 'city');
        if (!hasCityFilter) {
            result.filters.push({
                key: 'city',
                name: '区域',
                type: 'region',
                required: false
            });
        }
    }
}

function analyzeVueComponent(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath, '.vue');

        const result = {
            key: fileName,
            filters: [],
            tabs: []
        };

        // 分析 globalFilter - 更精确的正则表达式
        const globalFilterRegex = /<globalFilter[^>]*:filters="\[([^\]]*)\]"[^>]*>/s;
        const match = content.match(globalFilterRegex);

        if (match) {
            const filtersStr = match[1];
            // 提取过滤器配置 - 更精确的匹配
            const filterMatches = filtersStr.match(/{\s*[^}]*}/g);

            if (filterMatches) {
                filterMatches.forEach(filterMatch => {
                    const nameMatch = filterMatch.match(/name:\s*'([^']+)'/);
                    const singleMatch = filterMatch.match(/single:\s*true/);

                    if (nameMatch) {
                        const filterName = nameMatch[1];
                        const filter = {
                            key: filterName,
                            name: filterNameMap[filterName] || filterName,
                            type: filterTypeMap[filterName] || filterName,
                            required: filterName === 'time' || !!singleMatch
                        };

                        if (filterName === 'time') {
                            filter.default = '本月';
                        }

                        // 清理 required 字段
                        if (!filter.required) {
                            filter.required = false;
                        }

                        result.filters.push(filter);
                    }
                });
            }
        }

        // 分析自定义过滤器 UI（如 visitDetails.vue 中的过滤器）
        analyzeCustomFilters(content, result);

        // 分析 filterList 和 tabs
        const filterListRegex = /let\s+filterList\s*=\s*ref\(\[(.*?)\]\)/s;
        const filterListMatch = content.match(filterListRegex);
        
        if (filterListMatch) {
            const filterListContent = filterListMatch[1];
            const items = filterListContent.match(/'([^']+)'/g);
            
            if (items) {
                items.forEach(item => {
                    const value = item.replace(/'/g, '');
                    // 查找对应的 key
                    const key = Object.keys(indicatorTypeMap).find(k => indicatorTypeMap[k] === value);
                    
                    if (key) {
                        result.tabs.push({
                            id: key,
                            category: 'tab',
                            name: value
                        });
                    }
                });
            }
        }

        return result;
    } catch (error) {
        console.error(`Error analyzing ${filePath}:`, error.message);
        return null;
    }
}

function analyzeAllComponents() {
    const componentsDir = './apps/pv-h5/src/views/observe/compoents';
    const results = {};
    
    try {
        const files = fs.readdirSync(componentsDir);
        const vueFiles = files.filter(file => file.endsWith('.vue'));
        
        vueFiles.forEach(file => {
            const filePath = path.join(componentsDir, file);
            const analysis = analyzeVueComponent(filePath);
            
            if (analysis && (analysis.filters.length > 0 || analysis.tabs.length > 0)) {
                results[analysis.key] = analysis;
            }
        });
        
        return results;
    } catch (error) {
        console.error('Error reading components directory:', error.message);
        return {};
    }
}

// 执行分析
const results = analyzeAllComponents();

// 保存到文件
fs.writeFileSync('./components_analysis.json', JSON.stringify(results, null, 2));
console.log('分析完成，结果已保存到 components_analysis.json');
console.log(`共分析了 ${Object.keys(results).length} 个组件`);

// 也输出到控制台
console.log(JSON.stringify(results, null, 2));
