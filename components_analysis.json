{"admissionHospitals": {"key": "admissionHospitals", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "admissionSalesHospital": {"key": "admissionSalesHospital", "filters": [{"key": "monthName", "name": "monthName", "type": "monthName", "required": false}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}, {"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}], "tabs": []}, "admissionSituation": {"key": "admissionSituation", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "admissionSituationCity": {"key": "admissionSituationCity", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "admissionRate", "category": "tab", "name": "准入率(%)"}, {"id": "formalMedicineEntryRate", "category": "tab", "name": "正式准入率(%)"}]}, "admissionSituationDSM": {"key": "admissionSituationDSM", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "admissionRate", "category": "tab", "name": "准入率(%)"}, {"id": "formalMedicineEntryRate", "category": "tab", "name": "正式准入率(%)"}]}, "admissionSituationProvince": {"key": "admissionSituationProvince", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "admissionRate", "category": "tab", "name": "准入率(%)"}, {"id": "formalMedicineEntryRate", "category": "tab", "name": "正式准入率(%)"}]}, "admissionSituationRSM": {"key": "admissionSituationRSM", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "admissionRate", "category": "tab", "name": "准入率(%)"}, {"id": "formalMedicineEntryRate", "category": "tab", "name": "正式准入率(%)"}]}, "admissionSituationSales": {"key": "admissionSituationSales", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "admissionSituationSubordinateTeam": {"key": "admissionSituationSubordinateTeam", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "admissionRate", "category": "tab", "name": "准入率(%)"}, {"id": "formalMedicineEntryRate", "category": "tab", "name": "正式准入率(%)"}]}, "admissionSituationTeam": {"key": "admissionSituationTeam", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "admissionRate", "category": "tab", "name": "准入率(%)"}, {"id": "formalMedicineEntryRate", "category": "tab", "name": "正式准入率(%)"}]}, "branchSalesOverview": {"key": "branchSalesOverview", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "sales", "category": "tab", "name": "销售(K)"}, {"id": "ach", "category": "tab", "name": "按达成%"}, {"id": "growth", "category": "tab", "name": "按同比增长%"}]}, "branchSalesOverview2": {"key": "branchSalesOverview2", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "sales", "category": "tab", "name": "销售(K)"}, {"id": "ach", "category": "tab", "name": "按达成%"}, {"id": "growth", "category": "tab", "name": "按同比增长%"}, {"id": "growthAmount", "category": "tab", "name": "同比增长值(K)"}, {"id": "growthAch", "category": "tab", "name": "同比增量贡献(%)"}, {"id": "mom", "category": "tab", "name": "环比(%)"}, {"id": "momAmount", "category": "tab", "name": "环比增长值(K)"}, {"id": "mom<PERSON><PERSON>", "category": "tab", "name": "环比增量贡献(%)"}]}, "businessSalesAnalytics": {"key": "businessSalesAnalytics", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}], "tabs": []}, "byDetail": {"key": "byDetail", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}], "tabs": []}, "channelSalesComparisonAnalysis": {"key": "channelSalesComparisonAnalysis", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "channelSalesTrendAnalysis": {"key": "channelSalesTrendAnalysis", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "citySalesOverview": {"key": "citySalesOverview", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "sales", "category": "tab", "name": "销售(K)"}, {"id": "ach", "category": "tab", "name": "按达成%"}, {"id": "growth", "category": "tab", "name": "按同比增长%"}, {"id": "growthAmount", "category": "tab", "name": "同比增长值(K)"}, {"id": "growthAch", "category": "tab", "name": "同比增量贡献(%)"}, {"id": "mom", "category": "tab", "name": "环比(%)"}, {"id": "momAmount", "category": "tab", "name": "环比增长值(K)"}, {"id": "mom<PERSON><PERSON>", "category": "tab", "name": "环比增量贡献(%)"}]}, "competitiveMarketShare": {"key": "competitiveMarketShare", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "market", "name": "market", "type": "market", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "competitiveMarketShareRegion": {"key": "competitiveMarketShareRegion", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "market", "name": "market", "type": "market", "required": false}], "tabs": []}, "ddi-zx": {"key": "ddi-zx", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "ddi": {"key": "ddi", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "listing": {"key": "listing", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "marekShare": {"key": "marekShare", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "market", "name": "market", "type": "market", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "mom", "category": "tab", "name": "环比(%)"}]}, "marekShareQuarter": {"key": "marekShareQuarter", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "market", "name": "market", "type": "market", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "mom", "category": "tab", "name": "环比(%)"}]}, "marekShareQuarterRegion": {"key": "marekShareQuarterRegion", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "market", "name": "market", "type": "market", "required": false}], "tabs": [{"id": "mom", "category": "tab", "name": "环比(%)"}]}, "marekShareRegion": {"key": "marekShareRegion", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "market", "name": "market", "type": "market", "required": false}], "tabs": []}, "provinceSalesOverview": {"key": "provinceSalesOverview", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "sales", "category": "tab", "name": "销售(K)"}, {"id": "ach", "category": "tab", "name": "按达成%"}, {"id": "growth", "category": "tab", "name": "按同比增长%"}, {"id": "growthAmount", "category": "tab", "name": "同比增长值(K)"}, {"id": "growthAch", "category": "tab", "name": "同比增量贡献(%)"}, {"id": "mom", "category": "tab", "name": "环比(%)"}, {"id": "momAmount", "category": "tab", "name": "环比增长值(K)"}, {"id": "mom<PERSON><PERSON>", "category": "tab", "name": "环比增量贡献(%)"}]}, "rankingOfContractedSpeakers": {"key": "rankingOfContractedSpeakers", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}], "tabs": []}, "recruitmentRankingOfDoctors": {"key": "recruitmentRankingOfDoctors", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}], "tabs": []}, "saleTrend": {"key": "saleTrend", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "salesAchievementStatus": {"key": "salesAchievementStatus", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}], "tabs": []}, "salesAchievementStatusBar": {"key": "salesAchievementStatusBar", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "salesAnalysisTeam": {"key": "salesAnalysisTeam", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "salesRankingDistribution": {"key": "salesRankingDistribution", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "sales", "category": "tab", "name": "销售(K)"}, {"id": "ach", "category": "tab", "name": "按达成%"}, {"id": "growth", "category": "tab", "name": "按同比增长%"}, {"id": "growthAch", "category": "tab", "name": "同比增量贡献(%)"}, {"id": "mom", "category": "tab", "name": "环比(%)"}, {"id": "mom<PERSON><PERSON>", "category": "tab", "name": "环比增量贡献(%)"}]}, "salesRankingDistributionAsc": {"key": "salesRankingDistributionAsc", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "sales", "category": "tab", "name": "销售(K)"}, {"id": "ach", "category": "tab", "name": "按达成%"}, {"id": "growth", "category": "tab", "name": "按同比增长%"}, {"id": "growthAch", "category": "tab", "name": "同比增量贡献(%)"}, {"id": "mom", "category": "tab", "name": "环比(%)"}, {"id": "mom<PERSON><PERSON>", "category": "tab", "name": "环比增量贡献(%)"}]}, "salesRankingDistributionDSM": {"key": "salesRankingDistributionDSM", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "sales", "category": "tab", "name": "销售(K)"}, {"id": "ach", "category": "tab", "name": "按达成%"}, {"id": "growth", "category": "tab", "name": "按同比增长%"}, {"id": "growthAch", "category": "tab", "name": "同比增量贡献(%)"}, {"id": "mom", "category": "tab", "name": "环比(%)"}, {"id": "mom<PERSON><PERSON>", "category": "tab", "name": "环比增量贡献(%)"}]}, "salesRankingDistributionDSMAsc": {"key": "salesRankingDistributionDSMAsc", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "sales", "category": "tab", "name": "销售(K)"}, {"id": "ach", "category": "tab", "name": "按达成%"}, {"id": "growth", "category": "tab", "name": "按同比增长%"}, {"id": "growthAch", "category": "tab", "name": "同比增量贡献(%)"}, {"id": "mom", "category": "tab", "name": "环比(%)"}, {"id": "mom<PERSON><PERSON>", "category": "tab", "name": "环比增量贡献(%)"}]}, "salesRankingDistributionRSM": {"key": "salesRankingDistributionRSM", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "sales", "category": "tab", "name": "销售(K)"}, {"id": "ach", "category": "tab", "name": "按达成%"}, {"id": "growth", "category": "tab", "name": "按同比增长%"}, {"id": "growthAch", "category": "tab", "name": "同比增量贡献(%)"}, {"id": "mom", "category": "tab", "name": "环比(%)"}, {"id": "mom<PERSON><PERSON>", "category": "tab", "name": "环比增量贡献(%)"}]}, "salesRankingDistributionRSMAsc": {"key": "salesRankingDistributionRSMAsc", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": [{"id": "sales", "category": "tab", "name": "销售(K)"}, {"id": "ach", "category": "tab", "name": "按达成%"}, {"id": "growth", "category": "tab", "name": "按同比增长%"}, {"id": "growthAch", "category": "tab", "name": "同比增量贡献(%)"}, {"id": "mom", "category": "tab", "name": "环比(%)"}, {"id": "mom<PERSON><PERSON>", "category": "tab", "name": "环比增量贡献(%)"}]}, "salesShop": {"key": "salesShop", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}], "tabs": []}, "salesTrendBrand": {"key": "salesTrendBrand", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "salesTrendProduct": {"key": "salesTrendProduct", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "city", "name": "区域", "type": "region", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "visitDetails": {"key": "visitDetails", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "org", "name": "组织", "type": "organization", "required": false}, {"key": "hospital", "name": "医院", "type": "hospital", "required": false}], "tabs": []}, "visitManagementAndPerformance": {"key": "visitManagementAndPerformance", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "org", "name": "组织", "type": "organization", "required": false}], "tabs": []}, "visitSummary": {"key": "visitSummary", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}, {"key": "brand", "name": "产品", "type": "product", "required": false}, {"key": "org", "name": "组织", "type": "organization", "required": false}], "tabs": []}, "visitSummaryPPT": {"key": "visitSummaryPPT", "filters": [{"key": "time", "name": "时间范围", "type": "time", "required": true, "default": "本月"}], "tabs": []}}